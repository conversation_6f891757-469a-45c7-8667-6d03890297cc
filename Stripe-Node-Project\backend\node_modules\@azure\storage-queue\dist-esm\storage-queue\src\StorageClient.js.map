{"version": 3, "file": "StorageClient.js", "sourceRoot": "", "sources": ["../../../src/StorageClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,OAAO,EAAE,oBAAoB,EAAE,yBAAyB,EAAE,MAAM,YAAY,CAAC;AAC7E,OAAO,EAAE,qBAAqB,EAAE,MAAM,sBAAsB,CAAC;AAgB7D;;GAEG;AACH,MAAM,OAAgB,aAAa;IA4BjC;;;;OAIG;IACH,YAAsB,GAAW,EAAE,QAAkB;QACnD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,uBAAuB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7E,IAAI,CAAC,UAAU,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;CACF;AAaD;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,GAAW,EAAE,QAAkB;IACrE,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAiC,CAAC;IACnE,4CAA4C;IAC5C,iFAAiF;IACjF,2GAA2G;IAC3G,IAAI,eAAe,CAAC,YAAY,KAAK,SAAS,EAAE,CAAC;QAC/C,eAAe,CAAC,YAAY,GAAG;YAC7B,cAAc,EAAE,EAAE,GAAG,IAAI;SAC1B,CAAC;IACJ,CAAC;SAAM,IAAI,eAAe,CAAC,YAAY,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;QACpE,eAAe,CAAC,YAAoB,CAAC,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC;IACnE,CAAC;IACD,OAAO,IAAI,oBAAoB,CAAC,GAAG,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { StorageClient as StorageClientContext } from \"./generated/src/\";\nimport { StorageContextClient } from \"./StorageContextClient\";\nimport type { Pipeline, StoragePipelineOptions } from \"./Pipeline\";\nimport { getCoreClientOptions, getCredentialFromPipeline } from \"./Pipeline\";\nimport { getAccountNameFromUrl } from \"./utils/utils.common\";\nimport type { OperationTracingOptions } from \"@azure/core-tracing\";\nimport type { AnonymousCredential } from \"../../storage-blob/src/credentials/AnonymousCredential\";\nimport type { StorageSharedKeyCredential } from \"../../storage-blob/src/credentials/StorageSharedKeyCredential\";\nimport type { TokenCredential } from \"@azure/core-auth\";\n\n/**\n * An interface for options common to every remote operation.\n */\nexport interface CommonOptions {\n  /**\n   * Options to configure spans created when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n}\n\n/**\n * A StorageClient represents a based client class for {@link QueueServiceClient}, {@link QueueClient} and etc.\n */\nexport abstract class StorageClient {\n  /**\n   * URL string value.\n   */\n  public readonly url: string;\n  public readonly accountName: string;\n\n  /**\n   * Request policy pipeline.\n   *\n   * @internal\n   */\n  protected readonly pipeline: Pipeline;\n\n  /**\n   * Credential factory in the pipeline to authenticate requests to the service, such as AnonymousCredential, StorageSharedKeyCredential.\n   * Initialized to an AnonymousCredential if not able to retrieve it from the pipeline.\n   *\n   * @internal\n   */\n  protected readonly credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential;\n\n  /**\n   * StorageClientContext is a reference to protocol layer operations entry, which is\n   * generated by AutoRest generator.\n   */\n  protected readonly storageClientContext: StorageClientContext;\n\n  /**\n   * Creates an instance of StorageClient.\n   * @param url -\n   * @param pipeline -\n   */\n  protected constructor(url: string, pipeline: Pipeline) {\n    this.url = url;\n    this.accountName = getAccountNameFromUrl(url);\n    this.pipeline = pipeline;\n    this.storageClientContext = getStorageClientContext(this.url, this.pipeline);\n    this.credential = getCredentialFromPipeline(pipeline);\n  }\n}\n\n// The following type is introduced to avoid a breaking change during the\n// migration of V6 SDK generator. Can be removed later when a really\n// breaking change is released.\n\n/**\n * Defines values for ListQueuesIncludeType.\n * Possible values include: 'metadata'\n * @readonly\n */\nexport type ListQueuesIncludeType = \"metadata\";\n\n/**\n * @internal\n */\nexport function getStorageClientContext(url: string, pipeline: Pipeline): StorageClientContext {\n  const pipelineOptions = pipeline.options as StoragePipelineOptions;\n  // Set maximum timeout for queue operations.\n  // This was previously set manually in the retry policy specific to this package.\n  // https://learn.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations\n  if (pipelineOptions.retryOptions === undefined) {\n    pipelineOptions.retryOptions = {\n      tryTimeoutInMs: 30 * 1000,\n    };\n  } else if (pipelineOptions.retryOptions.tryTimeoutInMs === undefined) {\n    (pipelineOptions.retryOptions as any).tryTimeoutInMs = 30 * 1000;\n  }\n  return new StorageContextClient(url, getCoreClientOptions(pipeline));\n}\n"]}