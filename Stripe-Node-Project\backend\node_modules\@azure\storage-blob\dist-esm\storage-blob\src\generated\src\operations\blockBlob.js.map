{"version": 3, "file": "blockBlob.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/blockBlob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AAEjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAmBnD,6CAA6C;AAC7C,MAAM,OAAO,aAAa;IAGxB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;OAQG;IACH,MAAM,CACJ,aAAqB,EACrB,IAAsC,EACtC,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,EAChC,mBAAmB,CACpB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,cAAc,CACZ,aAAqB,EACrB,UAAkB,EAClB,OAA+C;QAE/C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,EACtC,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,UAAU,CACR,OAAe,EACf,aAAqB,EACrB,IAAsC,EACtC,OAA2C;QAE3C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,EACzC,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,iBAAiB,CACf,OAAe,EACf,aAAqB,EACrB,SAAiB,EACjB,OAAkD;QAElD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,EAC9C,8BAA8B,CAC/B,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,eAAe,CACb,MAAuB,EACvB,OAAgD;QAEhD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,MAAM,EAAE,OAAO,EAAE,EACnB,4BAA4B,CAC7B,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,YAAY,CACV,QAAuB,EACvB,OAA6C;QAE7C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,QAAQ,EAAE,OAAO,EAAE,EACrB,yBAAyB,CAC1B,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,WAAW,EAAE,UAAU,CAAC,KAAK;IAC7B,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,yBAAyB;QACpC,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,QAAQ;IACnB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,2BAA2B,GAA6B;IAC5D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,uCAAuC;SAC/D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,qBAAqB;QAChC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,wBAAwB;KACpC;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,uBAAuB,GAA6B;IACxD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,0BAA0B;SAClD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,KAAK;IAC7B,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;KACnB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,yBAAyB;QACpC,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,QAAQ;IACnB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,8BAA8B,GAA6B;IAC/D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,iCAAiC;SACzD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,0CAA0C;SAClE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;KACnB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,qBAAqB;QAChC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,kBAAkB;QAC7B,UAAU,CAAC,YAAY;KACxB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,4BAA4B,GAA6B;IAC7D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,wCAAwC;SAChE;KACF;IACD,WAAW,EAAE,UAAU,CAAC,MAAM;IAC9B,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,yBAAyB;KACrC;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,yBAAyB,GAA6B;IAC1D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { BlockBlob } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  BlockBlobUploadOptionalParams,\n  BlockBlobUploadResponse,\n  BlockBlobPutBlobFromUrlOptionalParams,\n  BlockBlobPutBlobFromUrlResponse,\n  BlockBlobStageBlockOptionalParams,\n  BlockBlobStageBlockResponse,\n  BlockBlobStageBlockFromURLOptionalParams,\n  BlockBlobStageBlockFromURLResponse,\n  BlockLookupList,\n  BlockBlobCommitBlockListOptionalParams,\n  BlockBlobCommitBlockListResponse,\n  BlockListType,\n  BlockBlobGetBlockListOptionalParams,\n  BlockBlobGetBlockListResponse,\n} from \"../models\";\n\n/** Class containing BlockBlob operations. */\nexport class BlockBlobImpl implements BlockBlob {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class BlockBlob class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * The Upload Block Blob operation updates the content of an existing block blob. Updating an existing\n   * block blob overwrites any existing metadata on the blob. Partial updates are not supported with Put\n   * Blob; the content of the existing blob is overwritten with the content of the new blob. To perform a\n   * partial update of the content of a block blob, use the Put Block List operation.\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  upload(\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: BlockBlobUploadOptionalParams,\n  ): Promise<BlockBlobUploadResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, body, options },\n      uploadOperationSpec,\n    );\n  }\n\n  /**\n   * The Put Blob from URL operation creates a new Block Blob where the contents of the blob are read\n   * from a given URL.  This API is supported beginning with the 2020-04-08 version. Partial updates are\n   * not supported with Put Blob from URL; the content of an existing blob is overwritten with the\n   * content of the new blob.  To perform partial updates to a block blob’s contents using a source URL,\n   * use the Put Block from URL API in conjunction with Put Block List.\n   * @param contentLength The length of the request.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  putBlobFromUrl(\n    contentLength: number,\n    copySource: string,\n    options?: BlockBlobPutBlobFromUrlOptionalParams,\n  ): Promise<BlockBlobPutBlobFromUrlResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, copySource, options },\n      putBlobFromUrlOperationSpec,\n    );\n  }\n\n  /**\n   * The Stage Block operation creates a new block to be committed as part of a blob\n   * @param blockId A valid Base64 string value that identifies the block. Prior to encoding, the string\n   *                must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified\n   *                for the blockid parameter must be the same size for each block.\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  stageBlock(\n    blockId: string,\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: BlockBlobStageBlockOptionalParams,\n  ): Promise<BlockBlobStageBlockResponse> {\n    return this.client.sendOperationRequest(\n      { blockId, contentLength, body, options },\n      stageBlockOperationSpec,\n    );\n  }\n\n  /**\n   * The Stage Block operation creates a new block to be committed as part of a blob where the contents\n   * are read from a URL.\n   * @param blockId A valid Base64 string value that identifies the block. Prior to encoding, the string\n   *                must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified\n   *                for the blockid parameter must be the same size for each block.\n   * @param contentLength The length of the request.\n   * @param sourceUrl Specify a URL to the copy source.\n   * @param options The options parameters.\n   */\n  stageBlockFromURL(\n    blockId: string,\n    contentLength: number,\n    sourceUrl: string,\n    options?: BlockBlobStageBlockFromURLOptionalParams,\n  ): Promise<BlockBlobStageBlockFromURLResponse> {\n    return this.client.sendOperationRequest(\n      { blockId, contentLength, sourceUrl, options },\n      stageBlockFromURLOperationSpec,\n    );\n  }\n\n  /**\n   * The Commit Block List operation writes a blob by specifying the list of block IDs that make up the\n   * blob. In order to be written as part of a blob, a block must have been successfully written to the\n   * server in a prior Put Block operation. You can call Put Block List to update a blob by uploading\n   * only those blocks that have changed, then committing the new and existing blocks together. You can\n   * do this by specifying whether to commit a block from the committed block list or from the\n   * uncommitted block list, or to commit the most recently uploaded version of the block, whichever list\n   * it may belong to.\n   * @param blocks Blob Blocks.\n   * @param options The options parameters.\n   */\n  commitBlockList(\n    blocks: BlockLookupList,\n    options?: BlockBlobCommitBlockListOptionalParams,\n  ): Promise<BlockBlobCommitBlockListResponse> {\n    return this.client.sendOperationRequest(\n      { blocks, options },\n      commitBlockListOperationSpec,\n    );\n  }\n\n  /**\n   * The Get Block List operation retrieves the list of blocks that have been uploaded as part of a block\n   * blob\n   * @param listType Specifies whether to return the list of committed blocks, the list of uncommitted\n   *                 blocks, or both lists together.\n   * @param options The options parameters.\n   */\n  getBlockList(\n    listType: BlockListType,\n    options?: BlockBlobGetBlockListOptionalParams,\n  ): Promise<BlockBlobGetBlockListResponse> {\n    return this.client.sendOperationRequest(\n      { listType, options },\n      getBlockListOperationSpec,\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst uploadOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlockBlobUploadHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlockBlobUploadExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.body1,\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.contentLength,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobCacheControl,\n    Parameters.blobContentType,\n    Parameters.blobContentMD5,\n    Parameters.blobContentEncoding,\n    Parameters.blobContentLanguage,\n    Parameters.blobContentDisposition,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n    Parameters.encryptionScope,\n    Parameters.tier,\n    Parameters.blobTagsString,\n    Parameters.legalHold1,\n    Parameters.transactionalContentMD5,\n    Parameters.transactionalContentCrc64,\n    Parameters.contentType1,\n    Parameters.accept2,\n    Parameters.blobType2,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"binary\",\n  serializer: xmlSerializer,\n};\nconst putBlobFromUrlOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlockBlobPutBlobFromUrlHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlockBlobPutBlobFromUrlExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobCacheControl,\n    Parameters.blobContentType,\n    Parameters.blobContentMD5,\n    Parameters.blobContentEncoding,\n    Parameters.blobContentLanguage,\n    Parameters.blobContentDisposition,\n    Parameters.encryptionScope,\n    Parameters.tier,\n    Parameters.sourceIfModifiedSince,\n    Parameters.sourceIfUnmodifiedSince,\n    Parameters.sourceIfMatch,\n    Parameters.sourceIfNoneMatch,\n    Parameters.sourceIfTags,\n    Parameters.copySource,\n    Parameters.blobTagsString,\n    Parameters.sourceContentMD5,\n    Parameters.copySourceAuthorization,\n    Parameters.copySourceTags,\n    Parameters.transactionalContentMD5,\n    Parameters.blobType2,\n    Parameters.copySourceBlobProperties,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst stageBlockOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlockBlobStageBlockHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlockBlobStageBlockExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.body1,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp24,\n    Parameters.blockId,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.encryptionScope,\n    Parameters.transactionalContentMD5,\n    Parameters.transactionalContentCrc64,\n    Parameters.contentType1,\n    Parameters.accept2,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"binary\",\n  serializer: xmlSerializer,\n};\nconst stageBlockFromURLOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlockBlobStageBlockFromURLHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlockBlobStageBlockFromURLExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp24,\n    Parameters.blockId,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.encryptionScope,\n    Parameters.sourceIfModifiedSince,\n    Parameters.sourceIfUnmodifiedSince,\n    Parameters.sourceIfMatch,\n    Parameters.sourceIfNoneMatch,\n    Parameters.sourceContentMD5,\n    Parameters.copySourceAuthorization,\n    Parameters.sourceUrl,\n    Parameters.sourceContentCrc64,\n    Parameters.sourceRange1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst commitBlockListOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.BlockBlobCommitBlockListHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlockBlobCommitBlockListExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.blocks,\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp25],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobCacheControl,\n    Parameters.blobContentType,\n    Parameters.blobContentMD5,\n    Parameters.blobContentEncoding,\n    Parameters.blobContentLanguage,\n    Parameters.blobContentDisposition,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n    Parameters.encryptionScope,\n    Parameters.tier,\n    Parameters.blobTagsString,\n    Parameters.legalHold1,\n    Parameters.transactionalContentMD5,\n    Parameters.transactionalContentCrc64,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst getBlockListOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.BlockList,\n      headersMapper: Mappers.BlockBlobGetBlockListHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.BlockBlobGetBlockListExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.snapshot,\n    Parameters.comp25,\n    Parameters.listType,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\n"]}