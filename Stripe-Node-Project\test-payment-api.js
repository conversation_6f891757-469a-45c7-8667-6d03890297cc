// Test payment API directly
const https = require('http');

const paymentData = {
    amount: 10.00,
    description: 'Test Payment',
    customer_email: '<EMAIL>',
    customer_name: '<PERSON>',
    niche: 'other',
    order_id: 'TEST123',
    card: {
        number: '****************',
        exp_month: 12,
        exp_year: 2026,
        cvc: '123'
    },
    vendor_id: '4b65134b-88f1-40a8-951b-694876338cd8'
};

const postData = JSON.stringify(paymentData);

const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/api/payments/process',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-API-Key': '1433bf7619d3ceb6f3f3fe579a36bc72af6bd3d678b81cae48471dc8ca92d23a',
        'Content-Length': Buffer.byteLength(postData)
    }
};

console.log('🧪 Testing Payment API...');
console.log('📤 Sending payment data:', paymentData);

const req = https.request(options, (res) => {
    console.log(`📊 Status Code: ${res.statusCode}`);
    console.log(`📋 Headers:`, res.headers);

    let data = '';
    res.on('data', (chunk) => {
        data += chunk;
    });

    res.on('end', () => {
        console.log('📥 Response:');
        try {
            const response = JSON.parse(data);
            console.log(JSON.stringify(response, null, 2));
        } catch (e) {
            console.log('Raw response:', data);
        }
    });
});

req.on('error', (e) => {
    console.error('❌ Request error:', e.message);
});

req.write(postData);
req.end();
