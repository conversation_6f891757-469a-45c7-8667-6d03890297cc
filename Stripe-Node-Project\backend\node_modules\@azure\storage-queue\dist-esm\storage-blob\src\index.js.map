{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AAGtD,cAAc,qBAAqB,CAAC;AACpC,cAAc,WAAW,CAAC;AAC1B,cAAc,mBAAmB,CAAC;AAClC,cAAc,mBAAmB,CAAC;AAClC,cAAc,6BAA6B,CAAC;AAC5C,cAAc,+BAA+B,CAAC;AAC9C,cAAc,0BAA0B,CAAC;AACzC,OAAO,EAEL,iCAAiC,GAClC,MAAM,iCAAiC,CAAC;AACzC,cAAc,aAAa,CAAC;AAC5B,cAAc,mBAAmB,CAAC;AAClC,cAAc,iBAAiB,CAAC;AAChC,cAAc,0BAA0B,CAAC;AACzC,OAAO,EAEL,8BAA8B,GAC/B,MAAM,8BAA8B,CAAC;AACtC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,+BAA+B,CAAC;AAC9C,cAAc,mCAAmC,CAAC;AAClD,cAAc,0BAA0B,CAAC;AACzC,cAAc,0CAA0C,CAAC;AAGzD,OAAO,EACL,aAAa,EACb,mBAAmB,EAUnB,mBAAmB,EAEnB,6BAA6B,GAC9B,MAAM,UAAU,CAAC;AAClB,OAAO,EACL,QAAQ,EAGR,cAAc,EACd,WAAW,EAUX,kBAAkB,GAEnB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,cAAc,sCAAsC,CAAC;AACrD,cAAc,6BAA6B,CAAC;AAC5C,cAAc,6BAA6B,CAAC;AAC5C,cAAc,6CAA6C,CAAC;AAC5D,cAAc,0BAA0B,CAAC;AAEzC,cAAc,mBAAmB,CAAC;AAYlC,OAAO,EAAE,SAAS,EAAE,CAAC;AAMrB,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { RestError } from \"@azure/core-rest-pipeline\";\n\nexport { PollOperationState, PollerLike } from \"@azure/core-lro\";\nexport * from \"./BlobServiceClient\";\nexport * from \"./Clients\";\nexport * from \"./ContainerClient\";\nexport * from \"./BlobLeaseClient\";\nexport * from \"./sas/AccountSASPermissions\";\nexport * from \"./sas/AccountSASResourceTypes\";\nexport * from \"./sas/AccountSASServices\";\nexport {\n  AccountSASSignatureValues,\n  generateAccountSASQueryParameters,\n} from \"./sas/AccountSASSignatureValues\";\nexport * from \"./BlobBatch\";\nexport * from \"./BlobBatchClient\";\nexport * from \"./BatchResponse\";\nexport * from \"./sas/BlobSASPermissions\";\nexport {\n  BlobSASSignatureValues,\n  generateBlobSASQueryParameters,\n} from \"./sas/BlobSASSignatureValues\";\nexport * from \"./StorageBrowserPolicyFactory\";\nexport * from \"./sas/ContainerSASPermissions\";\nexport * from \"./credentials/AnonymousCredential\";\nexport * from \"./credentials/Credential\";\nexport * from \"./credentials/StorageSharedKeyCredential\";\nexport { SasIPRange } from \"./sas/SasIPRange\";\nexport { Range } from \"./Range\";\nexport {\n  BlockBlobTier,\n  PremiumPageBlobTier,\n  Tags,\n  BlobDownloadResponseParsed,\n  BlobImmutabilityPolicy,\n  ObjectReplicationPolicy,\n  ObjectReplicationRule,\n  ObjectReplicationStatus,\n  BlobQueryArrowField,\n  BlobQueryArrowFieldType,\n  HttpAuthorization,\n  StorageBlobAudience,\n  PollerLikeWithCancellation,\n  getBlobServiceAccountAudience,\n} from \"./models\";\nexport {\n  Pipeline,\n  PipelineLike,\n  PipelineOptions,\n  isPipelineLike,\n  newPipeline,\n  StoragePipelineOptions,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions,\n  WebResource,\n  HttpOperationResponse,\n  HttpHeaders,\n  HttpRequestBody,\n  IHttpClient,\n  StorageOAuthScopes,\n  ServiceClientOptions,\n} from \"./Pipeline\";\nexport { BaseRequestPolicy } from \"./policies/RequestPolicy\";\nexport * from \"./policies/AnonymousCredentialPolicy\";\nexport * from \"./policies/CredentialPolicy\";\nexport * from \"./StorageRetryPolicyFactory\";\nexport * from \"./policies/StorageSharedKeyCredentialPolicy\";\nexport * from \"./sas/SASQueryParameters\";\nexport { CommonOptions } from \"./StorageClient\";\nexport * from \"./generatedModels\";\nexport {\n  AppendBlobRequestConditions,\n  BlobRequestConditions,\n  Metadata,\n  PageBlobRequestConditions,\n  TagConditions,\n  ContainerRequestConditions,\n  ModificationConditions,\n  MatchConditions,\n  ModifiedAccessConditions,\n} from \"./models\";\nexport { RestError };\nexport {\n  PageBlobGetPageRangesDiffResponse,\n  PageBlobGetPageRangesResponse,\n  PageList,\n} from \"./PageBlobRangeResponse\";\nexport { logger } from \"./log\";\nexport {\n  BlobBeginCopyFromUrlPollState,\n  CopyPollerBlobClient,\n} from \"./pollers/BlobStartCopyFromUrlPoller\";\n"]}