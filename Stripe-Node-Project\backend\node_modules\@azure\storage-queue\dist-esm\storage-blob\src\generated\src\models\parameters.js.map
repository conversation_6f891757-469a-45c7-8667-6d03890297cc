{"version": 3, "file": "parameters.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/models/parameters.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAOH,OAAO,EACL,qBAAqB,IAAI,2BAA2B,EACpD,OAAO,IAAI,aAAa,EACxB,YAAY,IAAI,kBAAkB,EAClC,QAAQ,IAAI,cAAc,EAC1B,eAAe,IAAI,qBAAqB,GACzC,MAAM,mBAAmB,CAAC;AAE3B,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAuB;IACvD,aAAa,EAAE,uBAAuB;IACtC,MAAM,EAAE,2BAA2B;CACpC,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,QAAQ;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,GAAG,GAA0B;IACxC,aAAa,EAAE,KAAK;IACpB,MAAM,EAAE;QACN,cAAc,EAAE,KAAK;QACrB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,KAAK;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;IACD,YAAY,EAAE,IAAI;CACnB,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA4B;IAC9C,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAA4B;IAC3C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA4B;IACvD,aAAa,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;IAC9C,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,SAAS;QACzB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;IACvC,MAAM,EAAE;QACN,cAAc,EAAE,wBAAwB;QACxC,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,QAAQ;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACpC,MAAM,EAAE;QACN,cAAc,EAAE,QAAQ;QACxB,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACpC,MAAM,EAAE;QACN,cAAc,EAAE,QAAQ;QACxB,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA4B;IAClD,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,YAAY;QAC5B,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA4B;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IACrC,MAAM,EAAE;QACN,cAAc,EAAE,SAAS;QACzB,OAAO,EAAE,SAAS;QAClB,cAAc,EAAE,2BAA2B;QAC3C,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,CAAC;iBACjD;aACF;SACF;KACF;IACD,gBAAgB,EAAE,KAAK;CACxB,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE,aAAa;CACtB,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,mBAAmB;QACjC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA4B;IAC/C,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAuB;IACtC,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,cAAc,EAAE,MAAM;QACtB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,MAAM;QACf,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,eAAe;IAC9B,MAAM,EAAE;QACN,cAAc,EAAE,gBAAgB;QAChC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,gBAAgB;QACzB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAuB;IACtD,aAAa,EAAE,sBAAsB;IACrC,MAAM,EAAE;QACN,cAAc,EAAE,cAAc;QAC9B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,cAAc;QACvB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;IACnC,MAAM,EAAE;QACN,cAAc,EAAE,OAAO;QACvB,OAAO,EAAE,OAAO;QAChB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA4B;IAC/C,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,YAAY,EAAE,WAAW;QACzB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,MAAM,EAAE;QACN,cAAc,EAAE,WAAW;QAC3B,OAAO,EAAE,WAAW;QACpB,sBAAsB,EAAE,YAAY;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;SACpC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACpC,MAAM,EAAE;QACN,cAAc,EAAE,yBAAyB;QACzC,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;SACrC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAuB;IACxD,aAAa,EAAE;QACb,SAAS;QACT,0BAA0B;QAC1B,wBAAwB;KACzB;IACD,MAAM,EAAE;QACN,cAAc,EAAE,+BAA+B;QAC/C,OAAO,EAAE,+BAA+B;QACxC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAAuB;IAChE,aAAa,EAAE;QACb,SAAS;QACT,0BAA0B;QAC1B,gCAAgC;KACjC;IACD,MAAM,EAAE;QACN,cAAc,EAAE,qCAAqC;QACrD,OAAO,EAAE,qCAAqC;QAC9C,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,CAAC,SAAS,EAAE,uBAAuB,EAAE,SAAS,CAAC;IAC9D,MAAM,EAAE;QACN,cAAc,EAAE,eAAe;QAC/B,OAAO,EAAE,eAAe;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,EAAE,iBAAiB,CAAC;IACzE,MAAM,EAAE;QACN,cAAc,EAAE,mBAAmB;QACnC,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE;YACJ,IAAI,EAAE,iBAAiB;SACxB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAuB;IACnD,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,EAAE,mBAAmB,CAAC;IAC3E,MAAM,EAAE;QACN,cAAc,EAAE,qBAAqB;QACrC,OAAO,EAAE,qBAAqB;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,iBAAiB;SACxB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;IAC1C,MAAM,EAAE;QACN,cAAc,EAAE,cAAc;QAC9B,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,IAAI;QAClB,cAAc,EAAE,kBAAkB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,kBAAkB;iBAC9B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAuB;IACtD,aAAa,EAAE,CAAC,SAAS,EAAE,sBAAsB,CAAC;IAClD,MAAM,EAAE;QACN,cAAc,EAAE,6BAA6B;QAC7C,OAAO,EAAE,6BAA6B;QACtC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAuB;IACzD,aAAa,EAAE,CAAC,SAAS,EAAE,yBAAyB,CAAC;IACrD,MAAM,EAAE;QACN,cAAc,EAAE,gCAAgC;QAChD,OAAO,EAAE,gCAAgC;QACzC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,QAAQ;QACtB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAuB;IACrD,aAAa,EAAE,qBAAqB;IACpC,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;IAC3C,MAAM,EAAE;QACN,cAAc,EAAE,sBAAsB;QACtC,OAAO,EAAE,sBAAsB;QAC/B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,mBAAmB;QACnC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,MAAM,EAAE;QACN,cAAc,EAAE,qBAAqB;QACrC,OAAO,EAAE,qBAAqB;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;IAC7C,MAAM,EAAE;QACN,cAAc,EAAE,wBAAwB;QACxC,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,mBAAmB;QACnC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,cAAc,EAAE,eAAe;QAC/B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,eAAe;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,mBAAmB;QACnC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,mBAAmB;QACnC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,cAAc,EAAE,yBAAyB;QACzC,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,QAAQ;QACtB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,mBAAmB;QACnC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAuB;IAClD,aAAa,EAAE,iBAAiB;IAChC,MAAM,EAAE;QACN,cAAc,EAAE,wBAAwB;QACxC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA4B;IAC/C,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IACrC,MAAM,EAAE;QACN,cAAc,EAAE,SAAS;QACzB,OAAO,EAAE,SAAS;QAClB,cAAc,EAAE,sBAAsB;QACtC,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,MAAM;wBACN,SAAS;wBACT,UAAU;wBACV,WAAW;wBACX,kBAAkB;wBAClB,UAAU;wBACV,MAAM;wBACN,oBAAoB;wBACpB,WAAW;wBACX,qBAAqB;qBACtB;iBACF;aACF;SACF;KACF;IACD,gBAAgB,EAAE,KAAK;CACxB,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAA4B;IAChD,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,cAAc,EAAE,WAAW;QAC3B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA4B;IAC/C,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,MAAM,EAAE;QACN,cAAc,EAAE,UAAU;QAC1B,OAAO,EAAE,UAAU;QACnB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAA4B;IAChD,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;IACvC,MAAM,EAAE;QACN,cAAc,EAAE,WAAW;QAC3B,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAuB;IACvC,aAAa,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC;IACnC,MAAM,EAAE;QACN,cAAc,EAAE,YAAY;QAC5B,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAuB;IACpD,aAAa,EAAE,CAAC,SAAS,EAAE,oBAAoB,CAAC;IAChD,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAuB;IACtD,aAAa,EAAE,CAAC,SAAS,EAAE,sBAAsB,CAAC;IAClD,MAAM,EAAE;QACN,cAAc,EAAE,8BAA8B;QAC9C,OAAO,EAAE,8BAA8B;QACvC,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC;IACtD,MAAM,EAAE;QACN,cAAc,EAAE,qBAAqB;QACrC,OAAO,EAAE,qBAAqB;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAuB;IACrD,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,qBAAqB,CAAC;IAC5D,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAuB;IACrD,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,qBAAqB,CAAC;IAC5D,MAAM,EAAE;QACN,cAAc,EAAE,2BAA2B;QAC3C,OAAO,EAAE,2BAA2B;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,EAAE,SAAS,CAAC;IACjE,MAAM,EAAE;QACN,cAAc,EAAE,UAAU;QAC1B,OAAO,EAAE,UAAU;QACnB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,EAAE,aAAa,CAAC;IACrE,MAAM,EAAE;QACN,cAAc,EAAE,eAAe;QAC/B,OAAO,EAAE,eAAe;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,EAAE,QAAQ,CAAC;IAChE,MAAM,EAAE;QACN,cAAc,EAAE,cAAc;QAC9B,OAAO,EAAE,cAAc;QACvB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;IAC7C,MAAM,EAAE;QACN,cAAc,EAAE,uBAAuB;QACvC,OAAO,EAAE,uBAAuB;QAChC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SACnC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAA4B;IACrD,aAAa,EAAE,CAAC,SAAS,EAAE,gBAAgB,CAAC;IAC5C,MAAM,EAAE;QACN,cAAc,EAAE,YAAY;QAC5B,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,QAAQ;QACtB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,eAAe;IAC9B,MAAM,EAAE;QACN,cAAc,EAAE,oBAAoB;QACpC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,oBAAoB;QAC7B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;IACvC,MAAM,EAAE;QACN,cAAc,EAAE,kBAAkB;QAClC,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAuB;IAClD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IACjE,MAAM,EAAE;QACN,cAAc,EAAE,yBAAyB;QACzC,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,CAAC;IAChE,MAAM,EAAE;QACN,cAAc,EAAE,wBAAwB;QACxC,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAuB;IAChD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,gBAAgB,CAAC;IAC/D,MAAM,EAAE;QACN,cAAc,EAAE,uBAAuB;QACvC,OAAO,EAAE,uBAAuB;QAChC,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;SAClB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAuB;IACrD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;IACpE,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAAuB;IACrD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,qBAAqB,CAAC;IACpE,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAuB;IACxD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,EAAE,wBAAwB,CAAC;IACvE,MAAM,EAAE;QACN,cAAc,EAAE,+BAA+B;QAC/C,OAAO,EAAE,+BAA+B;QACxC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,sBAAsB;QACpC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAAuB;IAC1D,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,CAAC;IACtD,MAAM,EAAE;QACN,cAAc,EAAE,qCAAqC;QACrD,OAAO,EAAE,qCAAqC;QAC9C,IAAI,EAAE;YACJ,IAAI,EAAE,iBAAiB;SACxB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAAuB;IACxD,aAAa,EAAE,CAAC,SAAS,EAAE,wBAAwB,CAAC;IACpD,MAAM,EAAE;QACN,cAAc,EAAE,+BAA+B;QAC/C,OAAO,EAAE,+BAA+B;QACxC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;SACjD;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,WAAW;QACzB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,cAAc,EAAE,iBAAiB;QACjC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,iBAAiB;QAC1B,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;IAC7C,MAAM,EAAE;QACN,cAAc,EAAE,uBAAuB;QACvC,OAAO,EAAE,uBAAuB;QAChC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAuB;IACtC,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;IAClC,MAAM,EAAE;QACN,cAAc,EAAE,kBAAkB;QAClC,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE;gBACb,IAAI;gBACJ,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;aACP;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAuB;IACnD,aAAa,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC;IAC/C,MAAM,EAAE;QACN,cAAc,EAAE,yBAAyB;QACzC,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;SACpC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAAuB;IACvD,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,uBAAuB;KACxB;IACD,MAAM,EAAE;QACN,cAAc,EAAE,+BAA+B;QAC/C,OAAO,EAAE,+BAA+B;QACxC,IAAI,EAAE;YACJ,IAAI,EAAE,iBAAiB;SACxB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAuB;IACzD,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,yBAAyB;KAC1B;IACD,MAAM,EAAE;QACN,cAAc,EAAE,iCAAiC;QACjD,OAAO,EAAE,iCAAiC;QAC1C,IAAI,EAAE;YACJ,IAAI,EAAE,iBAAiB;SACxB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,CAAC,SAAS,EAAE,gCAAgC,EAAE,eAAe,CAAC;IAC7E,MAAM,EAAE;QACN,cAAc,EAAE,sBAAsB;QACtC,OAAO,EAAE,sBAAsB;QAC/B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAuB;IACnD,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,mBAAmB;KACpB;IACD,MAAM,EAAE;QACN,cAAc,EAAE,2BAA2B;QAC3C,OAAO,EAAE,2BAA2B;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,gCAAgC,EAAE,cAAc,CAAC;IAC5E,MAAM,EAAE;QACN,cAAc,EAAE,qBAAqB;QACrC,OAAO,EAAE,qBAAqB;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAuB;IAC5C,aAAa,EAAE,YAAY;IAC3B,MAAM,EAAE;QACN,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAuB;IAChD,aAAa,EAAE,CAAC,SAAS,EAAE,gBAAgB,CAAC;IAC5C,MAAM,EAAE;QACN,cAAc,EAAE,WAAW;QAC3B,OAAO,EAAE,WAAW;QACpB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,MAAM,EAAE;QACN,cAAc,EAAE,gBAAgB;QAChC,OAAO,EAAE,gBAAgB;QACzB,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAuB;IAC5C,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;IACvC,MAAM,EAAE;QACN,cAAc,EAAE,iBAAiB;QACjC,OAAO,EAAE,iBAAiB;QAC1B,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,iBAAiB;IAChC,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,oBAAoB;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAuB;IAClD,aAAa,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;IAC9C,MAAM,EAAE;QACN,cAAc,EAAE,yBAAyB;QACzC,OAAO,EAAE,yBAAyB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;SAClB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAuB;IACzD,aAAa,EAAE,CAAC,SAAS,EAAE,yBAAyB,CAAC;IACrD,MAAM,EAAE;QACN,cAAc,EAAE,gCAAgC;QAChD,OAAO,EAAE,gCAAgC;QACzC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAuB;IAChD,aAAa,EAAE,CAAC,SAAS,EAAE,gBAAgB,CAAC;IAC5C,MAAM,EAAE;QACN,cAAc,EAAE,6BAA6B;QAC7C,OAAO,EAAE,6BAA6B;QACtC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;SACnC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAuB;IACzD,aAAa,EAAE,yBAAyB;IACxC,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,kBAAkB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,cAAc,EAAE,QAAQ;QACxB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAuB;IACvC,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE;gBACb,IAAI;gBACJ,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,MAAM;gBACN,SAAS;gBACT,MAAM;aACP;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;IAC1C,MAAM,EAAE,kBAAkB;CAC3B,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAAuB;IACtC,aAAa,EAAE,CAAC,SAAS,EAAE,MAAM,CAAC;IAClC,MAAM,EAAE,cAAc;CACvB,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAuB;IACzD,aAAa,EAAE,CAAC,SAAS,EAAE,yBAAyB,CAAC;IACrD,MAAM,EAAE;QACN,cAAc,EAAE,aAAa;QAC7B,OAAO,EAAE,aAAa;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;SAClB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAAuB;IAC3D,aAAa,EAAE,CAAC,SAAS,EAAE,2BAA2B,CAAC;IACvD,MAAM,EAAE;QACN,cAAc,EAAE,oBAAoB;QACpC,OAAO,EAAE,oBAAoB;QAC7B,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;SAClB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,UAAU;IACzB,MAAM,EAAE;QACN,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,gBAAgB;QAChC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAuB;IACnD,aAAa,EAAE,mBAAmB;IAClC,MAAM,EAAE;QACN,cAAc,EAAE,0BAA0B;QAC1C,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,0BAA0B;QACnC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAuB;IACpD,aAAa,EAAE,CAAC,SAAS,EAAE,oBAAoB,CAAC;IAChD,MAAM,EAAE;QACN,YAAY,EAAE,CAAC;QACf,cAAc,EAAE,2BAA2B;QAC3C,OAAO,EAAE,2BAA2B;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,YAAY,EAAE,0BAA0B;QACxC,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAAuB;IACvC,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,cAAc,EAAE,MAAM;QACtB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,MAAM;QACf,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,QAAQ;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,YAAY,EAAE,QAAQ;QACtB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,iBAAiB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iCAAiC,GAAuB;IACnE,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,mCAAmC;KACpC;IACD,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAAuB;IAC1D,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,0BAA0B;KAC3B;IACD,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAuB;IACzD,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,yBAAyB;KAC1B;IACD,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAuB;IAC5C,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,iBAAiB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,WAAW;IAC1B,MAAM,EAAE;QACN,cAAc,EAAE,kBAAkB;QAClC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,kBAAkB;QAC3B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,aAAa;IAC5B,MAAM,EAAE;QACN,cAAc,EAAE,mBAAmB;QACnC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAuB;IACpD,aAAa,EAAE,CAAC,SAAS,EAAE,oBAAoB,CAAC;IAChD,MAAM,EAAE;QACN,cAAc,EAAE,2BAA2B;QAC3C,OAAO,EAAE,2BAA2B;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;SAClB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,OAAO;IACtB,MAAM,EAAE;QACN,cAAc,EAAE,YAAY;QAC5B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA4B;IACnD,aAAa,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;IAC1C,MAAM,EAAE;QACN,cAAc,EAAE,cAAc;QAC9B,OAAO,EAAE,cAAc;QACvB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAuB;IACjD,aAAa,EAAE,CAAC,SAAS,EAAE,iBAAiB,CAAC;IAC7C,MAAM,EAAE;QACN,cAAc,EAAE,4BAA4B;QAC5C,OAAO,EAAE,4BAA4B;QACrC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAAuB;IACtD,aAAa,EAAE,sBAAsB;IACrC,MAAM,EAAE;QACN,cAAc,EAAE,6BAA6B;QAC7C,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,6BAA6B;QACtC,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC;SAC9C;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,UAAU;IACzB,MAAM,EAAE;QACN,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,gBAAgB;QAChC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,aAAa;QAC3B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,CAAC,SAAS,EAAE,gCAAgC,EAAE,SAAS,CAAC;IACvE,MAAM,EAAE;QACN,cAAc,EAAE,6BAA6B;QAC7C,OAAO,EAAE,6BAA6B;QACtC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAAuB;IAChD,aAAa,EAAE;QACb,SAAS;QACT,gCAAgC;QAChC,gBAAgB;KACjB;IACD,MAAM,EAAE;QACN,cAAc,EAAE,+BAA+B;QAC/C,OAAO,EAAE,+BAA+B;QACxC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,cAAc,EAAE,mBAAmB;QACnC,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,UAAU;IACzB,MAAM,EAAE;QACN,YAAY,EAAE,WAAW;QACzB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,gBAAgB;QAChC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAAuB;IAC1D,aAAa,EAAE,CAAC,SAAS,EAAE,0BAA0B,CAAC;IACtD,MAAM,EAAE;QACN,cAAc,EAAE,kCAAkC;QAClD,OAAO,EAAE,kCAAkC;QAC3C,IAAI,EAAE;YACJ,IAAI,EAAE,SAAS;SAChB;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA4B;IAC9C,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,cAAc,EAAE,SAAS;QACzB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE,qBAAqB;CAC9B,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,WAAW;QACzB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA4B;IAC/C,aAAa,EAAE,UAAU;IACzB,MAAM,EAAE;QACN,YAAY,EAAE,WAAW;QACzB,cAAc,EAAE,eAAe;QAC/B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,eAAe;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,MAAM;YACZ,aAAa,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,KAAK,CAAC;SACnD;KACF;CACF,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  OperationParameter,\n  OperationURLParameter,\n  OperationQueryParameter,\n} from \"@azure/core-client\";\nimport {\n  BlobServiceProperties as BlobServicePropertiesMapper,\n  KeyInfo as KeyInfoMapper,\n  QueryRequest as QueryRequestMapper,\n  BlobTags as BlobTagsMapper,\n  BlockLookupList as BlockLookupListMapper,\n} from \"../models/mappers\";\n\nexport const contentType: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobServiceProperties: OperationParameter = {\n  parameterPath: \"blobServiceProperties\",\n  mapper: BlobServicePropertiesMapper,\n};\n\nexport const accept: OperationParameter = {\n  parameterPath: \"accept\",\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Accept\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const url: OperationURLParameter = {\n  parameterPath: \"url\",\n  mapper: {\n    serializedName: \"url\",\n    required: true,\n    xmlName: \"url\",\n    type: {\n      name: \"String\",\n    },\n  },\n  skipEncoding: true,\n};\n\nexport const restype: OperationQueryParameter = {\n  parameterPath: \"restype\",\n  mapper: {\n    defaultValue: \"service\",\n    isConstant: true,\n    serializedName: \"restype\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"properties\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const timeoutInSeconds: OperationQueryParameter = {\n  parameterPath: [\"options\", \"timeoutInSeconds\"],\n  mapper: {\n    constraints: {\n      InclusiveMinimum: 0,\n    },\n    serializedName: \"timeout\",\n    xmlName: \"timeout\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const version: OperationParameter = {\n  parameterPath: \"version\",\n  mapper: {\n    defaultValue: \"2025-05-05\",\n    isConstant: true,\n    serializedName: \"x-ms-version\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const requestId: OperationParameter = {\n  parameterPath: [\"options\", \"requestId\"],\n  mapper: {\n    serializedName: \"x-ms-client-request-id\",\n    xmlName: \"x-ms-client-request-id\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const accept1: OperationParameter = {\n  parameterPath: \"accept\",\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Accept\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp1: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"stats\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp2: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"list\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const prefix: OperationQueryParameter = {\n  parameterPath: [\"options\", \"prefix\"],\n  mapper: {\n    serializedName: \"prefix\",\n    xmlName: \"prefix\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const marker: OperationQueryParameter = {\n  parameterPath: [\"options\", \"marker\"],\n  mapper: {\n    serializedName: \"marker\",\n    xmlName: \"marker\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const maxPageSize: OperationQueryParameter = {\n  parameterPath: [\"options\", \"maxPageSize\"],\n  mapper: {\n    constraints: {\n      InclusiveMinimum: 1,\n    },\n    serializedName: \"maxresults\",\n    xmlName: \"maxresults\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const include: OperationQueryParameter = {\n  parameterPath: [\"options\", \"include\"],\n  mapper: {\n    serializedName: \"include\",\n    xmlName: \"include\",\n    xmlElementName: \"ListContainersIncludeType\",\n    type: {\n      name: \"Sequence\",\n      element: {\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"metadata\", \"deleted\", \"system\"],\n        },\n      },\n    },\n  },\n  collectionFormat: \"CSV\",\n};\n\nexport const keyInfo: OperationParameter = {\n  parameterPath: \"keyInfo\",\n  mapper: KeyInfoMapper,\n};\n\nexport const comp3: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"userdelegationkey\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const restype1: OperationQueryParameter = {\n  parameterPath: \"restype\",\n  mapper: {\n    defaultValue: \"account\",\n    isConstant: true,\n    serializedName: \"restype\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const body: OperationParameter = {\n  parameterPath: \"body\",\n  mapper: {\n    serializedName: \"body\",\n    required: true,\n    xmlName: \"body\",\n    type: {\n      name: \"Stream\",\n    },\n  },\n};\n\nexport const comp4: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"batch\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const contentLength: OperationParameter = {\n  parameterPath: \"contentLength\",\n  mapper: {\n    serializedName: \"Content-Length\",\n    required: true,\n    xmlName: \"Content-Length\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const multipartContentType: OperationParameter = {\n  parameterPath: \"multipartContentType\",\n  mapper: {\n    serializedName: \"Content-Type\",\n    required: true,\n    xmlName: \"Content-Type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp5: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"blobs\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const where: OperationQueryParameter = {\n  parameterPath: [\"options\", \"where\"],\n  mapper: {\n    serializedName: \"where\",\n    xmlName: \"where\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const restype2: OperationQueryParameter = {\n  parameterPath: \"restype\",\n  mapper: {\n    defaultValue: \"container\",\n    isConstant: true,\n    serializedName: \"restype\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const metadata: OperationParameter = {\n  parameterPath: [\"options\", \"metadata\"],\n  mapper: {\n    serializedName: \"x-ms-meta\",\n    xmlName: \"x-ms-meta\",\n    headerCollectionPrefix: \"x-ms-meta-\",\n    type: {\n      name: \"Dictionary\",\n      value: { type: { name: \"String\" } },\n    },\n  },\n};\n\nexport const access: OperationParameter = {\n  parameterPath: [\"options\", \"access\"],\n  mapper: {\n    serializedName: \"x-ms-blob-public-access\",\n    xmlName: \"x-ms-blob-public-access\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"container\", \"blob\"],\n    },\n  },\n};\n\nexport const defaultEncryptionScope: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"containerEncryptionScope\",\n    \"defaultEncryptionScope\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-default-encryption-scope\",\n    xmlName: \"x-ms-default-encryption-scope\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const preventEncryptionScopeOverride: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"containerEncryptionScope\",\n    \"preventEncryptionScopeOverride\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-deny-encryption-scope-override\",\n    xmlName: \"x-ms-deny-encryption-scope-override\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const leaseId: OperationParameter = {\n  parameterPath: [\"options\", \"leaseAccessConditions\", \"leaseId\"],\n  mapper: {\n    serializedName: \"x-ms-lease-id\",\n    xmlName: \"x-ms-lease-id\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const ifModifiedSince: OperationParameter = {\n  parameterPath: [\"options\", \"modifiedAccessConditions\", \"ifModifiedSince\"],\n  mapper: {\n    serializedName: \"If-Modified-Since\",\n    xmlName: \"If-Modified-Since\",\n    type: {\n      name: \"DateTimeRfc1123\",\n    },\n  },\n};\n\nexport const ifUnmodifiedSince: OperationParameter = {\n  parameterPath: [\"options\", \"modifiedAccessConditions\", \"ifUnmodifiedSince\"],\n  mapper: {\n    serializedName: \"If-Unmodified-Since\",\n    xmlName: \"If-Unmodified-Since\",\n    type: {\n      name: \"DateTimeRfc1123\",\n    },\n  },\n};\n\nexport const comp6: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"metadata\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp7: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"acl\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const containerAcl: OperationParameter = {\n  parameterPath: [\"options\", \"containerAcl\"],\n  mapper: {\n    serializedName: \"containerAcl\",\n    xmlName: \"SignedIdentifiers\",\n    xmlIsWrapped: true,\n    xmlElementName: \"SignedIdentifier\",\n    type: {\n      name: \"Sequence\",\n      element: {\n        type: {\n          name: \"Composite\",\n          className: \"SignedIdentifier\",\n        },\n      },\n    },\n  },\n};\n\nexport const comp8: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"undelete\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const deletedContainerName: OperationParameter = {\n  parameterPath: [\"options\", \"deletedContainerName\"],\n  mapper: {\n    serializedName: \"x-ms-deleted-container-name\",\n    xmlName: \"x-ms-deleted-container-name\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const deletedContainerVersion: OperationParameter = {\n  parameterPath: [\"options\", \"deletedContainerVersion\"],\n  mapper: {\n    serializedName: \"x-ms-deleted-container-version\",\n    xmlName: \"x-ms-deleted-container-version\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp9: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"rename\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceContainerName: OperationParameter = {\n  parameterPath: \"sourceContainerName\",\n  mapper: {\n    serializedName: \"x-ms-source-container-name\",\n    required: true,\n    xmlName: \"x-ms-source-container-name\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceLeaseId: OperationParameter = {\n  parameterPath: [\"options\", \"sourceLeaseId\"],\n  mapper: {\n    serializedName: \"x-ms-source-lease-id\",\n    xmlName: \"x-ms-source-lease-id\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp10: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"lease\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const action: OperationParameter = {\n  parameterPath: \"action\",\n  mapper: {\n    defaultValue: \"acquire\",\n    isConstant: true,\n    serializedName: \"x-ms-lease-action\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const duration: OperationParameter = {\n  parameterPath: [\"options\", \"duration\"],\n  mapper: {\n    serializedName: \"x-ms-lease-duration\",\n    xmlName: \"x-ms-lease-duration\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const proposedLeaseId: OperationParameter = {\n  parameterPath: [\"options\", \"proposedLeaseId\"],\n  mapper: {\n    serializedName: \"x-ms-proposed-lease-id\",\n    xmlName: \"x-ms-proposed-lease-id\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const action1: OperationParameter = {\n  parameterPath: \"action\",\n  mapper: {\n    defaultValue: \"release\",\n    isConstant: true,\n    serializedName: \"x-ms-lease-action\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const leaseId1: OperationParameter = {\n  parameterPath: \"leaseId\",\n  mapper: {\n    serializedName: \"x-ms-lease-id\",\n    required: true,\n    xmlName: \"x-ms-lease-id\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const action2: OperationParameter = {\n  parameterPath: \"action\",\n  mapper: {\n    defaultValue: \"renew\",\n    isConstant: true,\n    serializedName: \"x-ms-lease-action\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const action3: OperationParameter = {\n  parameterPath: \"action\",\n  mapper: {\n    defaultValue: \"break\",\n    isConstant: true,\n    serializedName: \"x-ms-lease-action\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const breakPeriod: OperationParameter = {\n  parameterPath: [\"options\", \"breakPeriod\"],\n  mapper: {\n    serializedName: \"x-ms-lease-break-period\",\n    xmlName: \"x-ms-lease-break-period\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const action4: OperationParameter = {\n  parameterPath: \"action\",\n  mapper: {\n    defaultValue: \"change\",\n    isConstant: true,\n    serializedName: \"x-ms-lease-action\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const proposedLeaseId1: OperationParameter = {\n  parameterPath: \"proposedLeaseId\",\n  mapper: {\n    serializedName: \"x-ms-proposed-lease-id\",\n    required: true,\n    xmlName: \"x-ms-proposed-lease-id\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const include1: OperationQueryParameter = {\n  parameterPath: [\"options\", \"include\"],\n  mapper: {\n    serializedName: \"include\",\n    xmlName: \"include\",\n    xmlElementName: \"ListBlobsIncludeItem\",\n    type: {\n      name: \"Sequence\",\n      element: {\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"copy\",\n            \"deleted\",\n            \"metadata\",\n            \"snapshots\",\n            \"uncommittedblobs\",\n            \"versions\",\n            \"tags\",\n            \"immutabilitypolicy\",\n            \"legalhold\",\n            \"deletedwithversions\",\n          ],\n        },\n      },\n    },\n  },\n  collectionFormat: \"CSV\",\n};\n\nexport const delimiter: OperationQueryParameter = {\n  parameterPath: \"delimiter\",\n  mapper: {\n    serializedName: \"delimiter\",\n    required: true,\n    xmlName: \"delimiter\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const snapshot: OperationQueryParameter = {\n  parameterPath: [\"options\", \"snapshot\"],\n  mapper: {\n    serializedName: \"snapshot\",\n    xmlName: \"snapshot\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const versionId: OperationQueryParameter = {\n  parameterPath: [\"options\", \"versionId\"],\n  mapper: {\n    serializedName: \"versionid\",\n    xmlName: \"versionid\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const range: OperationParameter = {\n  parameterPath: [\"options\", \"range\"],\n  mapper: {\n    serializedName: \"x-ms-range\",\n    xmlName: \"x-ms-range\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const rangeGetContentMD5: OperationParameter = {\n  parameterPath: [\"options\", \"rangeGetContentMD5\"],\n  mapper: {\n    serializedName: \"x-ms-range-get-content-md5\",\n    xmlName: \"x-ms-range-get-content-md5\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const rangeGetContentCRC64: OperationParameter = {\n  parameterPath: [\"options\", \"rangeGetContentCRC64\"],\n  mapper: {\n    serializedName: \"x-ms-range-get-content-crc64\",\n    xmlName: \"x-ms-range-get-content-crc64\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const encryptionKey: OperationParameter = {\n  parameterPath: [\"options\", \"cpkInfo\", \"encryptionKey\"],\n  mapper: {\n    serializedName: \"x-ms-encryption-key\",\n    xmlName: \"x-ms-encryption-key\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const encryptionKeySha256: OperationParameter = {\n  parameterPath: [\"options\", \"cpkInfo\", \"encryptionKeySha256\"],\n  mapper: {\n    serializedName: \"x-ms-encryption-key-sha256\",\n    xmlName: \"x-ms-encryption-key-sha256\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const encryptionAlgorithm: OperationParameter = {\n  parameterPath: [\"options\", \"cpkInfo\", \"encryptionAlgorithm\"],\n  mapper: {\n    serializedName: \"x-ms-encryption-algorithm\",\n    xmlName: \"x-ms-encryption-algorithm\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const ifMatch: OperationParameter = {\n  parameterPath: [\"options\", \"modifiedAccessConditions\", \"ifMatch\"],\n  mapper: {\n    serializedName: \"If-Match\",\n    xmlName: \"If-Match\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const ifNoneMatch: OperationParameter = {\n  parameterPath: [\"options\", \"modifiedAccessConditions\", \"ifNoneMatch\"],\n  mapper: {\n    serializedName: \"If-None-Match\",\n    xmlName: \"If-None-Match\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const ifTags: OperationParameter = {\n  parameterPath: [\"options\", \"modifiedAccessConditions\", \"ifTags\"],\n  mapper: {\n    serializedName: \"x-ms-if-tags\",\n    xmlName: \"x-ms-if-tags\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const deleteSnapshots: OperationParameter = {\n  parameterPath: [\"options\", \"deleteSnapshots\"],\n  mapper: {\n    serializedName: \"x-ms-delete-snapshots\",\n    xmlName: \"x-ms-delete-snapshots\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"include\", \"only\"],\n    },\n  },\n};\n\nexport const blobDeleteType: OperationQueryParameter = {\n  parameterPath: [\"options\", \"blobDeleteType\"],\n  mapper: {\n    serializedName: \"deletetype\",\n    xmlName: \"deletetype\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp11: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"expiry\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const expiryOptions: OperationParameter = {\n  parameterPath: \"expiryOptions\",\n  mapper: {\n    serializedName: \"x-ms-expiry-option\",\n    required: true,\n    xmlName: \"x-ms-expiry-option\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const expiresOn: OperationParameter = {\n  parameterPath: [\"options\", \"expiresOn\"],\n  mapper: {\n    serializedName: \"x-ms-expiry-time\",\n    xmlName: \"x-ms-expiry-time\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobCacheControl: OperationParameter = {\n  parameterPath: [\"options\", \"blobHttpHeaders\", \"blobCacheControl\"],\n  mapper: {\n    serializedName: \"x-ms-blob-cache-control\",\n    xmlName: \"x-ms-blob-cache-control\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobContentType: OperationParameter = {\n  parameterPath: [\"options\", \"blobHttpHeaders\", \"blobContentType\"],\n  mapper: {\n    serializedName: \"x-ms-blob-content-type\",\n    xmlName: \"x-ms-blob-content-type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobContentMD5: OperationParameter = {\n  parameterPath: [\"options\", \"blobHttpHeaders\", \"blobContentMD5\"],\n  mapper: {\n    serializedName: \"x-ms-blob-content-md5\",\n    xmlName: \"x-ms-blob-content-md5\",\n    type: {\n      name: \"ByteArray\",\n    },\n  },\n};\n\nexport const blobContentEncoding: OperationParameter = {\n  parameterPath: [\"options\", \"blobHttpHeaders\", \"blobContentEncoding\"],\n  mapper: {\n    serializedName: \"x-ms-blob-content-encoding\",\n    xmlName: \"x-ms-blob-content-encoding\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobContentLanguage: OperationParameter = {\n  parameterPath: [\"options\", \"blobHttpHeaders\", \"blobContentLanguage\"],\n  mapper: {\n    serializedName: \"x-ms-blob-content-language\",\n    xmlName: \"x-ms-blob-content-language\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobContentDisposition: OperationParameter = {\n  parameterPath: [\"options\", \"blobHttpHeaders\", \"blobContentDisposition\"],\n  mapper: {\n    serializedName: \"x-ms-blob-content-disposition\",\n    xmlName: \"x-ms-blob-content-disposition\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp12: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"immutabilityPolicies\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const immutabilityPolicyExpiry: OperationParameter = {\n  parameterPath: [\"options\", \"immutabilityPolicyExpiry\"],\n  mapper: {\n    serializedName: \"x-ms-immutability-policy-until-date\",\n    xmlName: \"x-ms-immutability-policy-until-date\",\n    type: {\n      name: \"DateTimeRfc1123\",\n    },\n  },\n};\n\nexport const immutabilityPolicyMode: OperationParameter = {\n  parameterPath: [\"options\", \"immutabilityPolicyMode\"],\n  mapper: {\n    serializedName: \"x-ms-immutability-policy-mode\",\n    xmlName: \"x-ms-immutability-policy-mode\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"Mutable\", \"Unlocked\", \"Locked\"],\n    },\n  },\n};\n\nexport const comp13: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"legalhold\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const legalHold: OperationParameter = {\n  parameterPath: \"legalHold\",\n  mapper: {\n    serializedName: \"x-ms-legal-hold\",\n    required: true,\n    xmlName: \"x-ms-legal-hold\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const encryptionScope: OperationParameter = {\n  parameterPath: [\"options\", \"encryptionScope\"],\n  mapper: {\n    serializedName: \"x-ms-encryption-scope\",\n    xmlName: \"x-ms-encryption-scope\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp14: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"snapshot\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const tier: OperationParameter = {\n  parameterPath: [\"options\", \"tier\"],\n  mapper: {\n    serializedName: \"x-ms-access-tier\",\n    xmlName: \"x-ms-access-tier\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\n        \"P4\",\n        \"P6\",\n        \"P10\",\n        \"P15\",\n        \"P20\",\n        \"P30\",\n        \"P40\",\n        \"P50\",\n        \"P60\",\n        \"P70\",\n        \"P80\",\n        \"Hot\",\n        \"Cool\",\n        \"Archive\",\n        \"Cold\",\n      ],\n    },\n  },\n};\n\nexport const rehydratePriority: OperationParameter = {\n  parameterPath: [\"options\", \"rehydratePriority\"],\n  mapper: {\n    serializedName: \"x-ms-rehydrate-priority\",\n    xmlName: \"x-ms-rehydrate-priority\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"High\", \"Standard\"],\n    },\n  },\n};\n\nexport const sourceIfModifiedSince: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"sourceModifiedAccessConditions\",\n    \"sourceIfModifiedSince\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-source-if-modified-since\",\n    xmlName: \"x-ms-source-if-modified-since\",\n    type: {\n      name: \"DateTimeRfc1123\",\n    },\n  },\n};\n\nexport const sourceIfUnmodifiedSince: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"sourceModifiedAccessConditions\",\n    \"sourceIfUnmodifiedSince\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-source-if-unmodified-since\",\n    xmlName: \"x-ms-source-if-unmodified-since\",\n    type: {\n      name: \"DateTimeRfc1123\",\n    },\n  },\n};\n\nexport const sourceIfMatch: OperationParameter = {\n  parameterPath: [\"options\", \"sourceModifiedAccessConditions\", \"sourceIfMatch\"],\n  mapper: {\n    serializedName: \"x-ms-source-if-match\",\n    xmlName: \"x-ms-source-if-match\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceIfNoneMatch: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"sourceModifiedAccessConditions\",\n    \"sourceIfNoneMatch\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-source-if-none-match\",\n    xmlName: \"x-ms-source-if-none-match\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceIfTags: OperationParameter = {\n  parameterPath: [\"options\", \"sourceModifiedAccessConditions\", \"sourceIfTags\"],\n  mapper: {\n    serializedName: \"x-ms-source-if-tags\",\n    xmlName: \"x-ms-source-if-tags\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const copySource: OperationParameter = {\n  parameterPath: \"copySource\",\n  mapper: {\n    serializedName: \"x-ms-copy-source\",\n    required: true,\n    xmlName: \"x-ms-copy-source\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobTagsString: OperationParameter = {\n  parameterPath: [\"options\", \"blobTagsString\"],\n  mapper: {\n    serializedName: \"x-ms-tags\",\n    xmlName: \"x-ms-tags\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sealBlob: OperationParameter = {\n  parameterPath: [\"options\", \"sealBlob\"],\n  mapper: {\n    serializedName: \"x-ms-seal-blob\",\n    xmlName: \"x-ms-seal-blob\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const legalHold1: OperationParameter = {\n  parameterPath: [\"options\", \"legalHold\"],\n  mapper: {\n    serializedName: \"x-ms-legal-hold\",\n    xmlName: \"x-ms-legal-hold\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const xMsRequiresSync: OperationParameter = {\n  parameterPath: \"xMsRequiresSync\",\n  mapper: {\n    defaultValue: \"true\",\n    isConstant: true,\n    serializedName: \"x-ms-requires-sync\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceContentMD5: OperationParameter = {\n  parameterPath: [\"options\", \"sourceContentMD5\"],\n  mapper: {\n    serializedName: \"x-ms-source-content-md5\",\n    xmlName: \"x-ms-source-content-md5\",\n    type: {\n      name: \"ByteArray\",\n    },\n  },\n};\n\nexport const copySourceAuthorization: OperationParameter = {\n  parameterPath: [\"options\", \"copySourceAuthorization\"],\n  mapper: {\n    serializedName: \"x-ms-copy-source-authorization\",\n    xmlName: \"x-ms-copy-source-authorization\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const copySourceTags: OperationParameter = {\n  parameterPath: [\"options\", \"copySourceTags\"],\n  mapper: {\n    serializedName: \"x-ms-copy-source-tag-option\",\n    xmlName: \"x-ms-copy-source-tag-option\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"REPLACE\", \"COPY\"],\n    },\n  },\n};\n\nexport const comp15: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"copy\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const copyActionAbortConstant: OperationParameter = {\n  parameterPath: \"copyActionAbortConstant\",\n  mapper: {\n    defaultValue: \"abort\",\n    isConstant: true,\n    serializedName: \"x-ms-copy-action\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const copyId: OperationQueryParameter = {\n  parameterPath: \"copyId\",\n  mapper: {\n    serializedName: \"copyid\",\n    required: true,\n    xmlName: \"copyid\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp16: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"tier\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const tier1: OperationParameter = {\n  parameterPath: \"tier\",\n  mapper: {\n    serializedName: \"x-ms-access-tier\",\n    required: true,\n    xmlName: \"x-ms-access-tier\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\n        \"P4\",\n        \"P6\",\n        \"P10\",\n        \"P15\",\n        \"P20\",\n        \"P30\",\n        \"P40\",\n        \"P50\",\n        \"P60\",\n        \"P70\",\n        \"P80\",\n        \"Hot\",\n        \"Cool\",\n        \"Archive\",\n        \"Cold\",\n      ],\n    },\n  },\n};\n\nexport const queryRequest: OperationParameter = {\n  parameterPath: [\"options\", \"queryRequest\"],\n  mapper: QueryRequestMapper,\n};\n\nexport const comp17: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"query\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp18: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"tags\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const tags: OperationParameter = {\n  parameterPath: [\"options\", \"tags\"],\n  mapper: BlobTagsMapper,\n};\n\nexport const transactionalContentMD5: OperationParameter = {\n  parameterPath: [\"options\", \"transactionalContentMD5\"],\n  mapper: {\n    serializedName: \"Content-MD5\",\n    xmlName: \"Content-MD5\",\n    type: {\n      name: \"ByteArray\",\n    },\n  },\n};\n\nexport const transactionalContentCrc64: OperationParameter = {\n  parameterPath: [\"options\", \"transactionalContentCrc64\"],\n  mapper: {\n    serializedName: \"x-ms-content-crc64\",\n    xmlName: \"x-ms-content-crc64\",\n    type: {\n      name: \"ByteArray\",\n    },\n  },\n};\n\nexport const blobType: OperationParameter = {\n  parameterPath: \"blobType\",\n  mapper: {\n    defaultValue: \"PageBlob\",\n    isConstant: true,\n    serializedName: \"x-ms-blob-type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobContentLength: OperationParameter = {\n  parameterPath: \"blobContentLength\",\n  mapper: {\n    serializedName: \"x-ms-blob-content-length\",\n    required: true,\n    xmlName: \"x-ms-blob-content-length\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const blobSequenceNumber: OperationParameter = {\n  parameterPath: [\"options\", \"blobSequenceNumber\"],\n  mapper: {\n    defaultValue: 0,\n    serializedName: \"x-ms-blob-sequence-number\",\n    xmlName: \"x-ms-blob-sequence-number\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const contentType1: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/octet-stream\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const body1: OperationParameter = {\n  parameterPath: \"body\",\n  mapper: {\n    serializedName: \"body\",\n    required: true,\n    xmlName: \"body\",\n    type: {\n      name: \"Stream\",\n    },\n  },\n};\n\nexport const accept2: OperationParameter = {\n  parameterPath: \"accept\",\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Accept\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp19: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"page\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const pageWrite: OperationParameter = {\n  parameterPath: \"pageWrite\",\n  mapper: {\n    defaultValue: \"update\",\n    isConstant: true,\n    serializedName: \"x-ms-page-write\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const ifSequenceNumberLessThanOrEqualTo: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"sequenceNumberAccessConditions\",\n    \"ifSequenceNumberLessThanOrEqualTo\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-if-sequence-number-le\",\n    xmlName: \"x-ms-if-sequence-number-le\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const ifSequenceNumberLessThan: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"sequenceNumberAccessConditions\",\n    \"ifSequenceNumberLessThan\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-if-sequence-number-lt\",\n    xmlName: \"x-ms-if-sequence-number-lt\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const ifSequenceNumberEqualTo: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"sequenceNumberAccessConditions\",\n    \"ifSequenceNumberEqualTo\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-if-sequence-number-eq\",\n    xmlName: \"x-ms-if-sequence-number-eq\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const pageWrite1: OperationParameter = {\n  parameterPath: \"pageWrite\",\n  mapper: {\n    defaultValue: \"clear\",\n    isConstant: true,\n    serializedName: \"x-ms-page-write\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceUrl: OperationParameter = {\n  parameterPath: \"sourceUrl\",\n  mapper: {\n    serializedName: \"x-ms-copy-source\",\n    required: true,\n    xmlName: \"x-ms-copy-source\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceRange: OperationParameter = {\n  parameterPath: \"sourceRange\",\n  mapper: {\n    serializedName: \"x-ms-source-range\",\n    required: true,\n    xmlName: \"x-ms-source-range\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sourceContentCrc64: OperationParameter = {\n  parameterPath: [\"options\", \"sourceContentCrc64\"],\n  mapper: {\n    serializedName: \"x-ms-source-content-crc64\",\n    xmlName: \"x-ms-source-content-crc64\",\n    type: {\n      name: \"ByteArray\",\n    },\n  },\n};\n\nexport const range1: OperationParameter = {\n  parameterPath: \"range\",\n  mapper: {\n    serializedName: \"x-ms-range\",\n    required: true,\n    xmlName: \"x-ms-range\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp20: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"pagelist\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const prevsnapshot: OperationQueryParameter = {\n  parameterPath: [\"options\", \"prevsnapshot\"],\n  mapper: {\n    serializedName: \"prevsnapshot\",\n    xmlName: \"prevsnapshot\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const prevSnapshotUrl: OperationParameter = {\n  parameterPath: [\"options\", \"prevSnapshotUrl\"],\n  mapper: {\n    serializedName: \"x-ms-previous-snapshot-url\",\n    xmlName: \"x-ms-previous-snapshot-url\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const sequenceNumberAction: OperationParameter = {\n  parameterPath: \"sequenceNumberAction\",\n  mapper: {\n    serializedName: \"x-ms-sequence-number-action\",\n    required: true,\n    xmlName: \"x-ms-sequence-number-action\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"max\", \"update\", \"increment\"],\n    },\n  },\n};\n\nexport const comp21: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"incrementalcopy\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobType1: OperationParameter = {\n  parameterPath: \"blobType\",\n  mapper: {\n    defaultValue: \"AppendBlob\",\n    isConstant: true,\n    serializedName: \"x-ms-blob-type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp22: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"appendblock\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const maxSize: OperationParameter = {\n  parameterPath: [\"options\", \"appendPositionAccessConditions\", \"maxSize\"],\n  mapper: {\n    serializedName: \"x-ms-blob-condition-maxsize\",\n    xmlName: \"x-ms-blob-condition-maxsize\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const appendPosition: OperationParameter = {\n  parameterPath: [\n    \"options\",\n    \"appendPositionAccessConditions\",\n    \"appendPosition\",\n  ],\n  mapper: {\n    serializedName: \"x-ms-blob-condition-appendpos\",\n    xmlName: \"x-ms-blob-condition-appendpos\",\n    type: {\n      name: \"Number\",\n    },\n  },\n};\n\nexport const sourceRange1: OperationParameter = {\n  parameterPath: [\"options\", \"sourceRange\"],\n  mapper: {\n    serializedName: \"x-ms-source-range\",\n    xmlName: \"x-ms-source-range\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const comp23: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"seal\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blobType2: OperationParameter = {\n  parameterPath: \"blobType\",\n  mapper: {\n    defaultValue: \"BlockBlob\",\n    isConstant: true,\n    serializedName: \"x-ms-blob-type\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const copySourceBlobProperties: OperationParameter = {\n  parameterPath: [\"options\", \"copySourceBlobProperties\"],\n  mapper: {\n    serializedName: \"x-ms-copy-source-blob-properties\",\n    xmlName: \"x-ms-copy-source-blob-properties\",\n    type: {\n      name: \"Boolean\",\n    },\n  },\n};\n\nexport const comp24: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"block\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blockId: OperationQueryParameter = {\n  parameterPath: \"blockId\",\n  mapper: {\n    serializedName: \"blockid\",\n    required: true,\n    xmlName: \"blockid\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const blocks: OperationParameter = {\n  parameterPath: \"blocks\",\n  mapper: BlockLookupListMapper,\n};\n\nexport const comp25: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"blocklist\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\",\n    },\n  },\n};\n\nexport const listType: OperationQueryParameter = {\n  parameterPath: \"listType\",\n  mapper: {\n    defaultValue: \"committed\",\n    serializedName: \"blocklisttype\",\n    required: true,\n    xmlName: \"blocklisttype\",\n    type: {\n      name: \"Enum\",\n      allowedValues: [\"committed\", \"uncommitted\", \"all\"],\n    },\n  },\n};\n"]}