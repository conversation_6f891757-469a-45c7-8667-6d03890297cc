{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/utils/constants.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,CAAC,MAAM,WAAW,GAAW,SAAS,CAAC;AAC7C,MAAM,CAAC,MAAM,eAAe,GAAW,YAAY,CAAC;AAEpD,MAAM,CAAC,MAAM,gCAAgC,GAAW,GAAG,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,QAAQ;AACnF,MAAM,CAAC,MAAM,gCAAgC,GAAW,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,SAAS;AACrF,MAAM,CAAC,MAAM,qBAAqB,GAAW,KAAK,CAAC;AACnD,MAAM,CAAC,MAAM,+BAA+B,GAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM;AAC9E,MAAM,CAAC,MAAM,iCAAiC,GAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,MAAM;AAChF,MAAM,CAAC,MAAM,mCAAmC,GAAW,CAAC,CAAC;AAE7D,MAAM,CAAC,MAAM,eAAe,GAAW,GAAG,GAAG,IAAI,CAAC,CAAC,QAAQ;AAC3D;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAsB,oCAAoC,CAAC;AAE1F,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,UAAU,EAAE;QACV,sBAAsB,EAAE,GAAG;QAC3B,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,UAAU;QACpB,SAAS,EAAE,WAAW;QACtB,OAAO,EAAE,SAAS;KACnB;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC/B,aAAa,EAAE,GAAG;IAClB,aAAa,EAAE,GAAG;IAClB,cAAc,EAAE,GAAG;IACnB,kBAAkB,EAAE,GAAG;IACvB,0BAA0B,EAAE,GAAG;CAChC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG;IAC7B,aAAa,EAAE,eAAe;IAC9B,oBAAoB,EAAE,QAAQ;IAC9B,gBAAgB,EAAE,kBAAkB;IACpC,UAAU,EAAE,YAAY;IACxB,gBAAgB,EAAE,kBAAkB;IACpC,cAAc,EAAE,gBAAgB;IAChC,WAAW,EAAE,aAAa;IAC1B,yBAAyB,EAAE,2BAA2B;IACtD,YAAY,EAAE,cAAc;IAC5B,MAAM,EAAE,QAAQ;IAChB,IAAI,EAAE,MAAM;IACZ,QAAQ,EAAE,UAAU;IACpB,iBAAiB,EAAE,mBAAmB;IACtC,aAAa,EAAE,eAAe;IAC9B,mBAAmB,EAAE,qBAAqB;IAC1C,kBAAkB,EAAE,OAAO;IAC3B,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,YAAY;IACxB,sBAAsB,EAAE,wBAAwB;IAChD,gBAAgB,EAAE,kBAAkB;IACpC,SAAS,EAAE,WAAW;IACtB,eAAe,EAAE,iBAAiB;IAClC,YAAY,EAAE,cAAc;IAC5B,wBAAwB,EAAE,6BAA6B;CACxD,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC3B,MAAM,CAAC,MAAM,OAAO,GAAG,GAAG,CAAC;AAE3B,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AACzC,MAAM,CAAC,MAAM,iBAAiB,GAAG,GAAG,CAAC;AACrC,MAAM,CAAC,MAAM,0BAA0B,GAAG,CAAC,GAAG,SAAS,CAAC;AACxD,MAAM,CAAC,MAAM,gBAAgB,GAAG,MAAM,CAAC;AACvC,MAAM,CAAC,MAAM,gBAAgB,GAAG,UAAU,CAAC;AAE3C,MAAM,CAAC,MAAM,wBAAwB,GAAG,QAAQ,CAAC;AAEjD,MAAM,CAAC,MAAM,2BAA2B,GAAG,sNAAsN,CAAC;AAElQ,MAAM,CAAC,MAAM,oCAAoC,GAAG;IAClD,6BAA6B;IAC7B,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,YAAY;IACZ,wBAAwB;IACxB,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,+BAA+B;IAC/B,cAAc;IACd,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,kBAAkB;IAClB,aAAa;IACb,eAAe;IACf,MAAM;IACN,eAAe;IACf,QAAQ;IACR,MAAM;IACN,oBAAoB;IACpB,kBAAkB;IAClB,2BAA2B;IAC3B,cAAc;IACd,oBAAoB;IACpB,kBAAkB;IAClB,8BAA8B;IAC9B,qBAAqB;IACrB,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,+BAA+B;IAC/B,uBAAuB;IACvB,eAAe;IACf,mBAAmB;IACnB,UAAU;IACV,mBAAmB;IACnB,eAAe;IACf,qBAAqB;IACrB,kBAAkB;IAClB,8BAA8B;IAC9B,2BAA2B;IAC3B,mBAAmB;IACnB,qBAAqB;IACrB,yBAAyB;IACzB,yBAAyB;IACzB,iCAAiC;IACjC,+BAA+B;IAC/B,6BAA6B;IAC7B,+BAA+B;IAC/B,4BAA4B;IAC5B,4BAA4B;IAC5B,0BAA0B;IAC1B,uBAAuB;IACvB,wBAAwB;IACxB,yBAAyB;IACzB,2BAA2B;IAC3B,gBAAgB;IAChB,gCAAgC;IAChC,oBAAoB;IACpB,+BAA+B;IAC/B,uBAAuB;IACvB,4BAA4B;IAC5B,qCAAqC;IACrC,2BAA2B;IAC3B,4BAA4B;IAC5B,4BAA4B;IAC5B,4BAA4B;IAC5B,uBAAuB;IACvB,mBAAmB;IACnB,yBAAyB;IACzB,qBAAqB;IACrB,eAAe;IACf,iBAAiB;IACjB,iBAAiB;IACjB,wBAAwB;IACxB,4BAA4B;IAC5B,yBAAyB;IACzB,6BAA6B;IAC7B,eAAe;IACf,yBAAyB;IACzB,sBAAsB;IACtB,+BAA+B;IAC/B,2BAA2B;IAC3B,iCAAiC;IACjC,gBAAgB;IAChB,4BAA4B;IAC5B,6BAA6B;IAC7B,8BAA8B;IAC9B,cAAc;IACd,qBAAqB;CACtB,CAAC;AAEF,MAAM,CAAC,MAAM,wCAAwC,GAAG;IACtD,MAAM;IACN,YAAY;IACZ,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,SAAS;IACT,eAAe;IACf,WAAW;IACX,cAAc;IACd,KAAK;IACL,OAAO;IACP,KAAK;IACL,KAAK;IACL,OAAO;IACP,KAAK;IACL,UAAU;CACX,CAAC;AAEF,MAAM,CAAC,MAAM,sCAAsC,GAAG,qCAAqC,CAAC;AAC5F,MAAM,CAAC,MAAM,yCAAyC,GACpD,2CAA2C,CAAC;AAE9C,iDAAiD;AACjD,wGAAwG;AACxG,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;CACR,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport const SDK_VERSION: string = \"12.27.0\";\nexport const SERVICE_VERSION: string = \"2025-05-05\";\n\nexport const BLOCK_BLOB_MAX_UPLOAD_BLOB_BYTES: number = 256 * 1024 * 1024; // 256MB\nexport const BLOCK_BLOB_MAX_STAGE_BLOCK_BYTES: number = 4000 * 1024 * 1024; // 4000MB\nexport const BLOCK_BLOB_MAX_BLOCKS: number = 50000;\nexport const DEFAULT_BLOCK_BUFFER_SIZE_BYTES: number = 8 * 1024 * 1024; // 8MB\nexport const DEFAULT_BLOB_DOWNLOAD_BLOCK_BYTES: number = 4 * 1024 * 1024; // 4MB\nexport const DEFAULT_MAX_DOWNLOAD_RETRY_REQUESTS: number = 5;\n\nexport const REQUEST_TIMEOUT: number = 100 * 1000; // In ms\n/**\n * The OAuth scope to use with Azure Storage.\n */\nexport const StorageOAuthScopes: string | string[] = \"https://storage.azure.com/.default\";\n\nexport const URLConstants = {\n  Parameters: {\n    FORCE_BROWSER_NO_CACHE: \"_\",\n    SIGNATURE: \"sig\",\n    SNAPSHOT: \"snapshot\",\n    VERSIONID: \"versionid\",\n    TIMEOUT: \"timeout\",\n  },\n};\n\nexport const HTTPURLConnection = {\n  HTTP_ACCEPTED: 202,\n  HTTP_CONFLICT: 409,\n  HTTP_NOT_FOUND: 404,\n  HTTP_PRECON_FAILED: 412,\n  HTTP_RANGE_NOT_SATISFIABLE: 416,\n};\n\nexport const HeaderConstants = {\n  AUTHORIZATION: \"Authorization\",\n  AUTHORIZATION_SCHEME: \"Bearer\",\n  CONTENT_ENCODING: \"Content-Encoding\",\n  CONTENT_ID: \"Content-ID\",\n  CONTENT_LANGUAGE: \"Content-Language\",\n  CONTENT_LENGTH: \"Content-Length\",\n  CONTENT_MD5: \"Content-Md5\",\n  CONTENT_TRANSFER_ENCODING: \"Content-Transfer-Encoding\",\n  CONTENT_TYPE: \"Content-Type\",\n  COOKIE: \"Cookie\",\n  DATE: \"date\",\n  IF_MATCH: \"if-match\",\n  IF_MODIFIED_SINCE: \"if-modified-since\",\n  IF_NONE_MATCH: \"if-none-match\",\n  IF_UNMODIFIED_SINCE: \"if-unmodified-since\",\n  PREFIX_FOR_STORAGE: \"x-ms-\",\n  RANGE: \"Range\",\n  USER_AGENT: \"User-Agent\",\n  X_MS_CLIENT_REQUEST_ID: \"x-ms-client-request-id\",\n  X_MS_COPY_SOURCE: \"x-ms-copy-source\",\n  X_MS_DATE: \"x-ms-date\",\n  X_MS_ERROR_CODE: \"x-ms-error-code\",\n  X_MS_VERSION: \"x-ms-version\",\n  X_MS_CopySourceErrorCode: \"x-ms-copy-source-error-code\",\n};\n\nexport const ETagNone = \"\";\nexport const ETagAny = \"*\";\n\nexport const SIZE_1_MB = 1 * 1024 * 1024;\nexport const BATCH_MAX_REQUEST = 256;\nexport const BATCH_MAX_PAYLOAD_IN_BYTES = 4 * SIZE_1_MB;\nexport const HTTP_LINE_ENDING = \"\\r\\n\";\nexport const HTTP_VERSION_1_1 = \"HTTP/1.1\";\n\nexport const EncryptionAlgorithmAES25 = \"AES256\";\n\nexport const DevelopmentConnectionString = `DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;BlobEndpoint=http://127.0.0.1:10000/devstoreaccount1;`;\n\nexport const StorageBlobLoggingAllowedHeaderNames = [\n  \"Access-Control-Allow-Origin\",\n  \"Cache-Control\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"Request-Id\",\n  \"traceparent\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"x-ms-client-request-id\",\n  \"x-ms-date\",\n  \"x-ms-error-code\",\n  \"x-ms-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-version\",\n  \"Accept-Ranges\",\n  \"Content-Disposition\",\n  \"Content-Encoding\",\n  \"Content-Language\",\n  \"Content-MD5\",\n  \"Content-Range\",\n  \"ETag\",\n  \"Last-Modified\",\n  \"Server\",\n  \"Vary\",\n  \"x-ms-content-crc64\",\n  \"x-ms-copy-action\",\n  \"x-ms-copy-completion-time\",\n  \"x-ms-copy-id\",\n  \"x-ms-copy-progress\",\n  \"x-ms-copy-status\",\n  \"x-ms-has-immutability-policy\",\n  \"x-ms-has-legal-hold\",\n  \"x-ms-lease-state\",\n  \"x-ms-lease-status\",\n  \"x-ms-range\",\n  \"x-ms-request-server-encrypted\",\n  \"x-ms-server-encrypted\",\n  \"x-ms-snapshot\",\n  \"x-ms-source-range\",\n  \"If-Match\",\n  \"If-Modified-Since\",\n  \"If-None-Match\",\n  \"If-Unmodified-Since\",\n  \"x-ms-access-tier\",\n  \"x-ms-access-tier-change-time\",\n  \"x-ms-access-tier-inferred\",\n  \"x-ms-account-kind\",\n  \"x-ms-archive-status\",\n  \"x-ms-blob-append-offset\",\n  \"x-ms-blob-cache-control\",\n  \"x-ms-blob-committed-block-count\",\n  \"x-ms-blob-condition-appendpos\",\n  \"x-ms-blob-condition-maxsize\",\n  \"x-ms-blob-content-disposition\",\n  \"x-ms-blob-content-encoding\",\n  \"x-ms-blob-content-language\",\n  \"x-ms-blob-content-length\",\n  \"x-ms-blob-content-md5\",\n  \"x-ms-blob-content-type\",\n  \"x-ms-blob-public-access\",\n  \"x-ms-blob-sequence-number\",\n  \"x-ms-blob-type\",\n  \"x-ms-copy-destination-snapshot\",\n  \"x-ms-creation-time\",\n  \"x-ms-default-encryption-scope\",\n  \"x-ms-delete-snapshots\",\n  \"x-ms-delete-type-permanent\",\n  \"x-ms-deny-encryption-scope-override\",\n  \"x-ms-encryption-algorithm\",\n  \"x-ms-if-sequence-number-eq\",\n  \"x-ms-if-sequence-number-le\",\n  \"x-ms-if-sequence-number-lt\",\n  \"x-ms-incremental-copy\",\n  \"x-ms-lease-action\",\n  \"x-ms-lease-break-period\",\n  \"x-ms-lease-duration\",\n  \"x-ms-lease-id\",\n  \"x-ms-lease-time\",\n  \"x-ms-page-write\",\n  \"x-ms-proposed-lease-id\",\n  \"x-ms-range-get-content-md5\",\n  \"x-ms-rehydrate-priority\",\n  \"x-ms-sequence-number-action\",\n  \"x-ms-sku-name\",\n  \"x-ms-source-content-md5\",\n  \"x-ms-source-if-match\",\n  \"x-ms-source-if-modified-since\",\n  \"x-ms-source-if-none-match\",\n  \"x-ms-source-if-unmodified-since\",\n  \"x-ms-tag-count\",\n  \"x-ms-encryption-key-sha256\",\n  \"x-ms-copy-source-error-code\",\n  \"x-ms-copy-source-status-code\",\n  \"x-ms-if-tags\",\n  \"x-ms-source-if-tags\",\n];\n\nexport const StorageBlobLoggingAllowedQueryParameters = [\n  \"comp\",\n  \"maxresults\",\n  \"rscc\",\n  \"rscd\",\n  \"rsce\",\n  \"rscl\",\n  \"rsct\",\n  \"se\",\n  \"si\",\n  \"sip\",\n  \"sp\",\n  \"spr\",\n  \"sr\",\n  \"srt\",\n  \"ss\",\n  \"st\",\n  \"sv\",\n  \"include\",\n  \"marker\",\n  \"prefix\",\n  \"copyid\",\n  \"restype\",\n  \"blockid\",\n  \"blocklisttype\",\n  \"delimiter\",\n  \"prevsnapshot\",\n  \"ske\",\n  \"skoid\",\n  \"sks\",\n  \"skt\",\n  \"sktid\",\n  \"skv\",\n  \"snapshot\",\n];\n\nexport const BlobUsesCustomerSpecifiedEncryptionMsg = \"BlobUsesCustomerSpecifiedEncryption\";\nexport const BlobDoesNotUseCustomerSpecifiedEncryption =\n  \"BlobDoesNotUseCustomerSpecifiedEncryption\";\n\n/// List of ports used for path style addressing.\n/// Path style addressing means that storage account is put in URI's Path segment in instead of in host.\nexport const PathStylePorts = [\n  \"10000\",\n  \"10001\",\n  \"10002\",\n  \"10003\",\n  \"10004\",\n  \"10100\",\n  \"10101\",\n  \"10102\",\n  \"10103\",\n  \"10104\",\n  \"11000\",\n  \"11001\",\n  \"11002\",\n  \"11003\",\n  \"11004\",\n  \"11100\",\n  \"11101\",\n  \"11102\",\n  \"11103\",\n  \"11104\",\n];\n"]}