{"version": 3, "file": "agentPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/agentPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,eAAe,IAAI,kBAAkB,EACrC,WAAW,IAAI,cAAc,GAC9B,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,kBAAkB,CAAC;AAElD;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAa;IACvC,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { Agent } from \"../interfaces.js\";\nimport {\n  agentPolicyName as tspAgentPolicyName,\n  agentPolicy as tspAgentPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * Name of the Agent Policy\n */\nexport const agentPolicyName = tspAgentPolicyName;\n\n/**\n * Gets a pipeline policy that sets http.agent\n */\nexport function agentPolicy(agent?: Agent): PipelinePolicy {\n  return tspAgentPolicy(agent);\n}\n"]}