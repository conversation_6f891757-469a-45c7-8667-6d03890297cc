{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../src/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAYlC;;GAEG;AACH,MAAM,CAAN,IAAY,oBAKX;AALD,WAAY,oBAAoB;IAC9B;;OAEG;IACH,iFAAyD,CAAA;AAC3D,CAAC,EALW,oBAAoB,KAApB,oBAAoB,QAK/B;AAED;;GAEG;AACH,MAAM,UAAU,8BAA8B,CAAC,kBAA0B;IACvE,OAAO,WAAW,kBAAkB,kCAAkC,CAAC;AACzE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * A collection of key-value string pairs.\n */\nexport interface Metadata {\n  /**\n   * A key-value string pair.\n   */\n  [propertyName: string]: string;\n}\n\n/**\n * Defines the known cloud audiences for Storage.\n */\nexport enum StorageQueueAudience {\n  /**\n   * The OAuth scope to use to retrieve an AAD token for Azure Storage.\n   */\n  StorageOAuthScopes = \"https://storage.azure.com/.default\",\n}\n\n/**\n * To get OAuth audience for a storage account for queue service.\n */\nexport function getQueueServiceAccountAudience(storageAccountName: string): string {\n  return `https://${storageAccountName}.queue.core.windows.net/.default`;\n}\n"]}