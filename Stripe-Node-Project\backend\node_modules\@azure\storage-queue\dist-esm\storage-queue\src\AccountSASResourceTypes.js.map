{"version": 3, "file": "AccountSASResourceTypes.js", "sourceRoot": "", "sources": ["../../../src/AccountSASResourceTypes.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;GAQG;AACH,MAAM,OAAO,uBAAuB;IAApC;QA6BE;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;QAEhC;;WAEG;QACI,cAAS,GAAY,KAAK,CAAC;QAElC;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;IAqBjC,CAAC;IA9DC;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,aAAqB;QACvC,MAAM,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAE9D,KAAK,MAAM,CAAC,IAAI,aAAa,EAAE,CAAC;YAC9B,QAAQ,CAAC,EAAE,CAAC;gBACV,KAAK,GAAG;oBACN,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,SAAS,GAAG,IAAI,CAAC;oBACzC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACtC,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,0BAA0B,CAAC,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAiBD;;;;;OAKG;IACI,QAAQ;QACb,MAAM,aAAa,GAAa,EAAE,CAAC;QACnC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC;QACD,OAAO,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAChC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * This is a helper class to construct a string representing the resources accessible by an AccountSAS. Setting a value\n * to true means that any SAS which uses these permissions will grant access to that resource type. Once all the\n * values are set, this should be serialized with toString and set as the resources field on an\n * {@link AccountSASSignatureValues} object. It is possible to construct the resources string without this class, but\n * the order of the resources is particular and this class guarantees correctness.\n */\nexport class AccountSASResourceTypes {\n  /**\n   * Creates an {@link AccountSASResourceTypes} from the specified resource types string. This method will throw an\n   * Error if it encounters a character that does not correspond to a valid resource type.\n   *\n   * @param resourceTypes -\n   */\n  public static parse(resourceTypes: string): AccountSASResourceTypes {\n    const accountSASResourceTypes = new AccountSASResourceTypes();\n\n    for (const c of resourceTypes) {\n      switch (c) {\n        case \"s\":\n          accountSASResourceTypes.service = true;\n          break;\n        case \"c\":\n          accountSASResourceTypes.container = true;\n          break;\n        case \"o\":\n          accountSASResourceTypes.object = true;\n          break;\n        default:\n          throw new RangeError(`Invalid resource type: ${c}`);\n      }\n    }\n\n    return accountSASResourceTypes;\n  }\n\n  /**\n   * Permission to access service level APIs granted.\n   */\n  public service: boolean = false;\n\n  /**\n   * Permission to access container level APIs (Blob Containers, Tables, Queues, File Shares) granted.\n   */\n  public container: boolean = false;\n\n  /**\n   * Permission to access object level APIs (Blobs, Table Entities, Queue Messages, Files) granted.\n   */\n  public object: boolean = false;\n\n  /**\n   * Converts the given resource types to a string.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n   *\n   */\n  public toString(): string {\n    const resourceTypes: string[] = [];\n    if (this.service) {\n      resourceTypes.push(\"s\");\n    }\n    if (this.container) {\n      resourceTypes.push(\"c\");\n    }\n    if (this.object) {\n      resourceTypes.push(\"o\");\n    }\n    return resourceTypes.join(\"\");\n  }\n}\n"]}