{"version": 3, "file": "error.js", "sourceRoot": "", "sources": ["../../../src/util/error.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAQlC,0BAOC;AAbD,2CAAuC;AAEvC;;;GAGG;AACH,SAAgB,OAAO,CAAC,CAAU;IAChC,IAAI,IAAA,oBAAQ,EAAC,CAAC,CAAC,EAAE,CAAC;QAChB,MAAM,OAAO,GAAG,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;QAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,CAAC,OAAO,KAAK,QAAQ,CAAC;QACjD,OAAO,OAAO,IAAI,UAAU,CAAC;IAC/B,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { isObject } from \"./object.js\";\n\n/**\n * Typeguard for an error object shape (has name and message)\n * @param e - Something caught by a catch clause.\n */\nexport function isError(e: unknown): e is Error {\n  if (isObject(e)) {\n    const hasName = typeof e.name === \"string\";\n    const hasMessage = typeof e.message === \"string\";\n    return hasName && hasMessage;\n  }\n  return false;\n}\n"]}