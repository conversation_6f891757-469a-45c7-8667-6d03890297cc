{"version": 3, "file": "StorageContextClient.js", "sourceRoot": "", "sources": ["../../../src/StorageContextClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,aAAa;IACrD,KAAK,CAAC,oBAAoB,CACxB,kBAAsC,EACtC,aAA4B;QAE5B,MAAM,mBAAmB,qBAAQ,aAAa,CAAE,CAAC;QAEjD,IACE,mBAAmB,CAAC,IAAI,KAAK,cAAc;YAC3C,mBAAmB,CAAC,IAAI,KAAK,uBAAuB;YACpD,mBAAmB,CAAC,IAAI,KAAK,mCAAmC,EAChE,CAAC;YACD,mBAAmB,CAAC,IAAI,GAAG,EAAE,CAAC;QAChC,CAAC;QACD,OAAO,KAAK,CAAC,oBAAoB,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;IAC7E,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OperationArguments, OperationSpec } from \"@azure/core-client\";\nimport { StorageClient } from \"./generated/src\";\n\n/**\n * @internal\n */\nexport class StorageContextClient extends StorageClient {\n  async sendOperationRequest<T>(\n    operationArguments: OperationArguments,\n    operationSpec: OperationSpec,\n  ): Promise<T> {\n    const operationSpecToSend = { ...operationSpec };\n\n    if (\n      operationSpecToSend.path === \"/{queueName}\" ||\n      operationSpecToSend.path === \"/{queueName}/messages\" ||\n      operationSpecToSend.path === \"/{queueName}/messages/{messageid}\"\n    ) {\n      operationSpecToSend.path = \"\";\n    }\n    return super.sendOperationRequest(operationArguments, operationSpecToSend);\n  }\n}\n"]}