{"version": 3, "file": "Pipeline.js", "sourceRoot": "", "sources": ["../../../src/Pipeline.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAOlC,OAAO,EAQL,iBAAiB,EACjB,gCAAgC,GACjC,MAAM,yBAAyB,CAAC;AAQjC,OAAO,EAEL,+BAA+B,EAC/B,4BAA4B,GAC7B,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,iCAAiC,EAAE,oBAAoB,EAAE,MAAM,oBAAoB,CAAC;AAC7F,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAEzD,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAErD,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AACxE,OAAO,EAAE,0BAA0B,EAAE,MAAM,0CAA0C,CAAC;AACtF,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,EACL,kBAAkB,EAClB,oCAAoC,EACpC,wCAAwC,EACxC,WAAW,GACZ,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,0BAA0B,EAAE,MAAM,eAAe,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,mCAAmC,CAAC;AACzE,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AACrE,OAAO,EAAE,gCAAgC,EAAE,MAAM,+CAA+C,CAAC;AACjG,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAC5E,OAAO,EAAE,iCAAiC,EAAE,MAAM,8CAA8C,CAAC;AAEjG,kFAAkF;AAClF,kCAAkC;AAClC,OAAO,EACL,kBAAkB,GASnB,CAAC;AAsDF;;;;GAIG;AACH,MAAM,UAAU,cAAc,CAAC,QAAiB;IAC9C,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAC9C,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,YAAY,GAAG,QAAwB,CAAC;IAE9C,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC;QACrC,OAAO,YAAY,CAAC,OAAO,KAAK,QAAQ;QACxC,OAAO,YAAY,CAAC,sBAAsB,KAAK,UAAU,CAC1D,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,QAAQ;IAUnB;;;;;OAKG;IACH,YAAY,SAAiC,EAAE,UAA2B,EAAE;QAC1E,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;;;;OAKG;IACI,sBAAsB;QAC3B,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;YACnC,sBAAsB,EAAE,IAAI,CAAC,SAAS;SACvC,CAAC;IACJ,CAAC;CACF;AAiCD;;;;;;GAMG;AACH,MAAM,UAAU,WAAW,CACzB,UAA+E,EAC/E,kBAA0C,EAAE;IAE5C,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,UAAU,GAAG,IAAI,mBAAmB,EAAE,CAAC;IACzC,CAAC;IACD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,EAAE,EAAE,eAAe,CAAC,CAAC;IAClD,QAAgB,CAAC,WAAW,GAAG,UAAU,CAAC;IAC3C,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,wBAAwB,CAC/B,QAAsB;IAEtB,MAAM,qBAAqB,GAAG;QAC5B,qBAAqB;QACrB,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,2BAA2B;QAC3B,+BAA+B;QAC/B,uBAAuB;KACxB,CAAC;IACF,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9B,MAAM,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3D,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;QACH,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM,WAAW,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC;YACvF,mEAAmE;YACnE,OAAO;gBACL,eAAe,EAAE,gCAAgC,CAAC,cAAc,CAAC;gBACjE,UAAU,EAAE,WAAW;aACxB,CAAC;QACJ,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,QAAsB;;IACzD,MAAM,KAA2C,QAAQ,CAAC,OAAiC,EAArF,EAAE,UAAU,EAAE,QAAQ,OAA+D,EAA1D,WAAW,cAAtC,cAAwC,CAA6C,CAAC;IAE5F,IAAI,UAAU,GAAgB,QAAgB,CAAC,eAAe,CAAC;IAC/D,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,0BAA0B,EAAE,CAAC;QAClF,QAAgB,CAAC,eAAe,GAAG,UAAU,CAAC;IACjD,CAAC;IAED,IAAI,YAAY,GAAkB,QAAgB,CAAC,aAAa,CAAC;IACjE,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,MAAM,cAAc,GAAG,+BAA+B,WAAW,EAAE,CAAC;QACpE,MAAM,eAAe,GACnB,WAAW,CAAC,gBAAgB,IAAI,WAAW,CAAC,gBAAgB,CAAC,eAAe;YAC1E,CAAC,CAAC,GAAG,WAAW,CAAC,gBAAgB,CAAC,eAAe,IAAI,cAAc,EAAE;YACrE,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC;QAC1B,YAAY,GAAG,oBAAoB,iCAC9B,WAAW,KACd,cAAc,EAAE;gBACd,4BAA4B,EAAE,oCAAoC;gBAClE,gCAAgC,EAAE,wCAAwC;gBAC1E,MAAM,EAAE,MAAM,CAAC,IAAI;aACpB,EACD,gBAAgB,EAAE;gBAChB,eAAe;aAChB,EACD,oBAAoB,EAAE;gBACpB,YAAY;gBACZ,iBAAiB,EAAE;oBACjB,GAAG,EAAE;wBACH,oEAAoE;wBACpE,eAAe;wBACf,UAAU,EAAE,GAAG;qBAChB;iBACF;aACF,EACD,sBAAsB,EAAE;gBACtB,QAAQ;gBACR,iBAAiB,EAAE;oBACjB,GAAG,EAAE;wBACH,oEAAoE;wBACpE,eAAe;wBACf,UAAU,EAAE,GAAG;qBAChB;iBACF;aACF,IACD,CAAC;QACH,YAAY,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;QAC9C,YAAY,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,4BAA4B,EAAE,CAAC,CAAC;QAClE,YAAY,CAAC,SAAS,CAAC,iCAAiC,EAAE,CAAC,CAAC;QAC5D,YAAY,CAAC,SAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;QACzF,YAAY,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAC/C,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,gBAAgB,EAAE,CAAC;YACrB,YAAY,CAAC,SAAS,CACpB,gBAAgB,CAAC,eAAe,EAChC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS,CAClE,CAAC;QACJ,CAAC;QACD,MAAM,UAAU,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,YAAY,CAAC,SAAS,CACpB,+BAA+B,CAAC;gBAC9B,UAAU;gBACV,MAAM,EAAE,MAAA,WAAW,CAAC,QAAQ,mCAAI,kBAAkB;gBAClD,kBAAkB,EAAE,EAAE,2BAA2B,EAAE,iCAAiC,EAAE;aACvF,CAAC,EACF,EAAE,KAAK,EAAE,MAAM,EAAE,CAClB,CAAC;QACJ,CAAC;aAAM,IAAI,UAAU,YAAY,0BAA0B,EAAE,CAAC;YAC5D,YAAY,CAAC,SAAS,CACpB,gCAAgC,CAAC;gBAC/B,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,UAAU,EAAG,UAAkB,CAAC,UAAU;aAC3C,CAAC,EACF,EAAE,KAAK,EAAE,MAAM,EAAE,CAClB,CAAC;QACJ,CAAC;QACA,QAAgB,CAAC,aAAa,GAAG,YAAY,CAAC;IACjD,CAAC;IACD,uCACK,WAAW,KACd,uBAAuB,EAAE,IAAI,EAC7B,UAAU,EACV,QAAQ,EAAE,YAAY,IACtB;AACJ,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,QAAsB;IAEtB,mDAAmD;IACnD,IAAK,QAAgB,CAAC,WAAW,EAAE,CAAC;QAClC,OAAQ,QAAgB,CAAC,WAAW,CAAC;IACvC,CAAC;IACD,wFAAwF;IACxF,IAAI,UAAU,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAC3C,KAAK,MAAM,OAAO,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC;QACzC,IAAI,iBAAiB,CAAE,OAAe,CAAC,UAAU,CAAC,EAAE,CAAC;YACnD,uEAAuE;YACvE,0DAA0D;YAC1D,UAAU,GAAI,OAAe,CAAC,UAAU,CAAC;QAC3C,CAAC;aAAM,IAAI,4BAA4B,CAAC,OAAO,CAAC,EAAE,CAAC;YACjD,OAAO,OAAO,CAAC;QACjB,CAAC;IACH,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,4BAA4B,CACnC,OAA6B;IAE7B,IAAI,OAAO,YAAY,0BAA0B,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,4BAA4B,CAAC;AACnE,CAAC;AAED,SAAS,qBAAqB,CAAC,OAA6B;IAC1D,IAAI,OAAO,YAAY,mBAAmB,EAAE,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAqB,CAAC;AAC5D,CAAC;AAED,SAAS,4BAA4B,CAAC,OAA6B;IACjE,OAAO,iBAAiB,CAAE,OAAe,CAAC,UAAU,CAAC,CAAC;AACxD,CAAC;AAED,SAAS,6BAA6B,CACpC,OAA6B;IAE7B,IAAI,OAAO,YAAY,2BAA2B,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,6BAA6B,CAAC;AACpE,CAAC;AAED,SAAS,2BAA2B,CAClC,OAA6B;IAE7B,IAAI,OAAO,YAAY,yBAAyB,EAAE,CAAC;QACjD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,2BAA2B,CAAC;AAClE,CAAC;AAED,SAAS,+BAA+B,CAAC,OAA6B;IACpE,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,wBAAwB,CAAC;AAC/D,CAAC;AAED,SAAS,uBAAuB,CAAC,OAA6B;IAC5D,OAAO,OAAO,CAAC,WAAW,CAAC,IAAI,KAAK,uBAAuB,CAAC;AAC9D,CAAC;AAED,SAAS,uBAAuB,CAAC,OAA6B;IAC5D,MAAM,aAAa,GAAG;QACpB,+BAA+B;QAC/B,eAAe;QACf,WAAW;QACX,aAAa;QACb,oCAAoC;QACpC,iBAAiB;QACjB,uBAAuB;KACxB,CAAC;IAEF,MAAM,cAAc,GAAgB;QAClC,WAAW,EAAE,KAAK,EAAE,OAAoB,EAAE,EAAE;YAC1C,OAAO;gBACL,OAAO;gBACP,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE;gBAChC,MAAM,EAAE,GAAG;aACZ,CAAC;QACJ,CAAC;KACF,CAAC;IACF,MAAM,wBAAwB,GAAyB;QACrD,GAAG,CAAC,SAA+B,EAAE,QAAgB;YACnD,gBAAgB;QAClB,CAAC;QACD,SAAS,CAAC,SAA+B;YACvC,OAAO,KAAK,CAAC;QACf,CAAC;KACF,CAAC;IACF,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,wBAAwB,CAAC,CAAC;IAChF,MAAM,UAAU,GAAG,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC;IACnD,6EAA6E;IAC7E,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,EAAE;QAC5C,OAAO,UAAU,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;IAChD,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  KeepAliveOptions,\n  ExtendedServiceClientOptions,\n  HttpPipelineLogLevel,\n} from \"@azure/core-http-compat\";\nimport {\n  CompatResponse as HttpOperationResponse,\n  RequestPolicy as IHttpClient,\n  HttpHeadersLike as HttpHeaders,\n  RequestPolicy,\n  RequestPolicyFactory,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n  WebResourceLike as WebResource,\n  convertHttpClient,\n  createRequestPolicyFactoryPolicy,\n} from \"@azure/core-http-compat\";\nimport type {\n  ProxySettings as ProxyOptions,\n  UserAgentPolicyOptions as UserAgentOptions,\n  Pipeline as CorePipeline,\n  PipelinePolicy,\n  HttpClient,\n} from \"@azure/core-rest-pipeline\";\nimport {\n  RequestBodyType as HttpRequestBody,\n  bearerTokenAuthenticationPolicy,\n  decompressResponsePolicyName,\n} from \"@azure/core-rest-pipeline\";\nimport { authorizeRequestOnTenantChallenge, createClientPipeline } from \"@azure/core-client\";\nimport { parseXML, stringifyXML } from \"@azure/core-xml\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\n\nimport { logger } from \"./log\";\nimport type { StorageRetryOptions } from \"./StorageRetryPolicyFactory\";\nimport { StorageRetryPolicyFactory } from \"./StorageRetryPolicyFactory\";\nimport { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport {\n  StorageOAuthScopes,\n  StorageBlobLoggingAllowedHeaderNames,\n  StorageBlobLoggingAllowedQueryParameters,\n  SDK_VERSION,\n} from \"./utils/constants\";\nimport { getCachedDefaultHttpClient } from \"./utils/cache\";\nimport { storageBrowserPolicy } from \"./policies/StorageBrowserPolicyV2\";\nimport { storageRetryPolicy } from \"./policies/StorageRetryPolicyV2\";\nimport { storageSharedKeyCredentialPolicy } from \"./policies/StorageSharedKeyCredentialPolicyV2\";\nimport { StorageBrowserPolicyFactory } from \"./StorageBrowserPolicyFactory\";\nimport { storageCorrectContentLengthPolicy } from \"./policies/StorageCorrectContentLengthPolicy\";\n\n// Export following interfaces and types for customers who want to implement their\n// own RequestPolicy or HTTPClient\nexport {\n  StorageOAuthScopes,\n  IHttpClient,\n  HttpHeaders,\n  HttpRequestBody,\n  HttpOperationResponse,\n  WebResource,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions,\n};\n\n/**\n * A subset of `@azure/core-http` ServiceClientOptions\n */\nexport interface ServiceClientOptions {\n  /**\n   * Optional. Configures the HTTP client to send requests and receive responses.\n   */\n  httpClient?: IHttpClient;\n  /**\n   * Optional. Overrides the default policy factories.\n   */\n  requestPolicyFactories?:\n    | RequestPolicyFactory[]\n    | ((defaultRequestPolicyFactories: RequestPolicyFactory[]) => void | RequestPolicyFactory[]);\n}\n\n/**\n * Option interface for Pipeline constructor.\n */\nexport interface PipelineOptions {\n  /**\n   * Optional. Configures the HTTP client to send requests and receive responses.\n   */\n  httpClient?: IHttpClient;\n}\n\n/**\n * An interface for the {@link Pipeline} class containing HTTP request policies.\n * You can create a default Pipeline by calling {@link newPipeline}.\n * Or you can create a Pipeline with your own policies by the constructor of Pipeline.\n *\n * Refer to {@link newPipeline} and provided policies before implementing your\n * customized Pipeline.\n */\nexport interface PipelineLike {\n  /**\n   * A list of chained request policy factories.\n   */\n  readonly factories: RequestPolicyFactory[];\n  /**\n   * Configures pipeline logger and HTTP client.\n   */\n  readonly options: PipelineOptions;\n  /**\n   * Transfer Pipeline object to ServiceClientOptions object which is required by\n   * ServiceClient constructor.\n   *\n   * @returns The ServiceClientOptions object from this Pipeline.\n   */\n  toServiceClientOptions(): ServiceClientOptions;\n}\n\n/**\n * A helper to decide if a given argument satisfies the Pipeline contract\n * @param pipeline - An argument that may be a Pipeline\n * @returns true when the argument satisfies the Pipeline contract\n */\nexport function isPipelineLike(pipeline: unknown): pipeline is PipelineLike {\n  if (!pipeline || typeof pipeline !== \"object\") {\n    return false;\n  }\n\n  const castPipeline = pipeline as PipelineLike;\n\n  return (\n    Array.isArray(castPipeline.factories) &&\n    typeof castPipeline.options === \"object\" &&\n    typeof castPipeline.toServiceClientOptions === \"function\"\n  );\n}\n\n/**\n * A Pipeline class containing HTTP request policies.\n * You can create a default Pipeline by calling {@link newPipeline}.\n * Or you can create a Pipeline with your own policies by the constructor of Pipeline.\n *\n * Refer to {@link newPipeline} and provided policies before implementing your\n * customized Pipeline.\n */\nexport class Pipeline implements PipelineLike {\n  /**\n   * A list of chained request policy factories.\n   */\n  public readonly factories: RequestPolicyFactory[];\n  /**\n   * Configures pipeline logger and HTTP client.\n   */\n  public readonly options: PipelineOptions;\n\n  /**\n   * Creates an instance of Pipeline. Customize HTTPClient by implementing IHttpClient interface.\n   *\n   * @param factories -\n   * @param options -\n   */\n  constructor(factories: RequestPolicyFactory[], options: PipelineOptions = {}) {\n    this.factories = factories;\n    this.options = options;\n  }\n\n  /**\n   * Transfer Pipeline object to ServiceClientOptions object which is required by\n   * ServiceClient constructor.\n   *\n   * @returns The ServiceClientOptions object from this Pipeline.\n   */\n  public toServiceClientOptions(): ServiceClientOptions {\n    return {\n      httpClient: this.options.httpClient,\n      requestPolicyFactories: this.factories,\n    };\n  }\n}\n\n/**\n * Options interface for the {@link newPipeline} function.\n */\nexport interface StoragePipelineOptions {\n  /**\n   * Options to configure a proxy for outgoing requests.\n   */\n  proxyOptions?: ProxyOptions;\n  /**\n   * Options for adding user agent details to outgoing requests.\n   */\n  userAgentOptions?: UserAgentOptions;\n  /**\n   * Configures the built-in retry policy behavior.\n   */\n  retryOptions?: StorageRetryOptions;\n  /**\n   * Keep alive configurations. Default keep-alive is enabled.\n   */\n  keepAliveOptions?: KeepAliveOptions;\n  /**\n   * Configures the HTTP client to send requests and receive responses.\n   */\n  httpClient?: IHttpClient;\n  /**\n   * The audience used to retrieve an AAD token.\n   * By default, audience 'https://storage.azure.com/.default' will be used.\n   */\n  audience?: string | string[];\n}\n\n/**\n * Creates a new Pipeline object with Credential provided.\n *\n * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n * @param pipelineOptions - Optional. Options.\n * @returns A new Pipeline object.\n */\nexport function newPipeline(\n  credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n  pipelineOptions: StoragePipelineOptions = {},\n): Pipeline {\n  if (!credential) {\n    credential = new AnonymousCredential();\n  }\n  const pipeline = new Pipeline([], pipelineOptions);\n  (pipeline as any)._credential = credential;\n  return pipeline;\n}\n\nfunction processDownlevelPipeline(\n  pipeline: PipelineLike,\n): { wrappedPolicies: PipelinePolicy; afterRetry: boolean } | undefined {\n  const knownFactoryFunctions = [\n    isAnonymousCredential,\n    isStorageSharedKeyCredential,\n    isCoreHttpBearerTokenFactory,\n    isStorageBrowserPolicyFactory,\n    isStorageRetryPolicyFactory,\n    isStorageTelemetryPolicyFactory,\n    isCoreHttpPolicyFactory,\n  ];\n  if (pipeline.factories.length) {\n    const novelFactories = pipeline.factories.filter((factory) => {\n      return !knownFactoryFunctions.some((knownFactory) => knownFactory(factory));\n    });\n    if (novelFactories.length) {\n      const hasInjector = novelFactories.some((factory) => isInjectorPolicyFactory(factory));\n      // if there are any left over, wrap in a requestPolicyFactoryPolicy\n      return {\n        wrappedPolicies: createRequestPolicyFactoryPolicy(novelFactories),\n        afterRetry: hasInjector,\n      };\n    }\n  }\n  return undefined;\n}\n\nexport function getCoreClientOptions(pipeline: PipelineLike): ExtendedServiceClientOptions {\n  const { httpClient: v1Client, ...restOptions } = pipeline.options as StoragePipelineOptions;\n\n  let httpClient: HttpClient = (pipeline as any)._coreHttpClient;\n  if (!httpClient) {\n    httpClient = v1Client ? convertHttpClient(v1Client) : getCachedDefaultHttpClient();\n    (pipeline as any)._coreHttpClient = httpClient;\n  }\n\n  let corePipeline: CorePipeline = (pipeline as any)._corePipeline;\n  if (!corePipeline) {\n    const packageDetails = `azsdk-js-azure-storage-blob/${SDK_VERSION}`;\n    const userAgentPrefix =\n      restOptions.userAgentOptions && restOptions.userAgentOptions.userAgentPrefix\n        ? `${restOptions.userAgentOptions.userAgentPrefix} ${packageDetails}`\n        : `${packageDetails}`;\n    corePipeline = createClientPipeline({\n      ...restOptions,\n      loggingOptions: {\n        additionalAllowedHeaderNames: StorageBlobLoggingAllowedHeaderNames,\n        additionalAllowedQueryParameters: StorageBlobLoggingAllowedQueryParameters,\n        logger: logger.info,\n      },\n      userAgentOptions: {\n        userAgentPrefix,\n      },\n      serializationOptions: {\n        stringifyXML,\n        serializerOptions: {\n          xml: {\n            // Use customized XML char key of \"#\" so we can deserialize metadata\n            // with \"_\" key\n            xmlCharKey: \"#\",\n          },\n        },\n      },\n      deserializationOptions: {\n        parseXML,\n        serializerOptions: {\n          xml: {\n            // Use customized XML char key of \"#\" so we can deserialize metadata\n            // with \"_\" key\n            xmlCharKey: \"#\",\n          },\n        },\n      },\n    });\n    corePipeline.removePolicy({ phase: \"Retry\" });\n    corePipeline.removePolicy({ name: decompressResponsePolicyName });\n    corePipeline.addPolicy(storageCorrectContentLengthPolicy());\n    corePipeline.addPolicy(storageRetryPolicy(restOptions.retryOptions), { phase: \"Retry\" });\n    corePipeline.addPolicy(storageBrowserPolicy());\n    const downlevelResults = processDownlevelPipeline(pipeline);\n    if (downlevelResults) {\n      corePipeline.addPolicy(\n        downlevelResults.wrappedPolicies,\n        downlevelResults.afterRetry ? { afterPhase: \"Retry\" } : undefined,\n      );\n    }\n    const credential = getCredentialFromPipeline(pipeline);\n    if (isTokenCredential(credential)) {\n      corePipeline.addPolicy(\n        bearerTokenAuthenticationPolicy({\n          credential,\n          scopes: restOptions.audience ?? StorageOAuthScopes,\n          challengeCallbacks: { authorizeRequestOnChallenge: authorizeRequestOnTenantChallenge },\n        }),\n        { phase: \"Sign\" },\n      );\n    } else if (credential instanceof StorageSharedKeyCredential) {\n      corePipeline.addPolicy(\n        storageSharedKeyCredentialPolicy({\n          accountName: credential.accountName,\n          accountKey: (credential as any).accountKey,\n        }),\n        { phase: \"Sign\" },\n      );\n    }\n    (pipeline as any)._corePipeline = corePipeline;\n  }\n  return {\n    ...restOptions,\n    allowInsecureConnection: true,\n    httpClient,\n    pipeline: corePipeline,\n  };\n}\n\nexport function getCredentialFromPipeline(\n  pipeline: PipelineLike,\n): StorageSharedKeyCredential | AnonymousCredential | TokenCredential {\n  // see if we squirreled one away on the type itself\n  if ((pipeline as any)._credential) {\n    return (pipeline as any)._credential;\n  }\n  // if it came from another package, loop over the factories and look for one like before\n  let credential = new AnonymousCredential();\n  for (const factory of pipeline.factories) {\n    if (isTokenCredential((factory as any).credential)) {\n      // Only works if the factory has been attached a \"credential\" property.\n      // We do that in newPipeline() when using TokenCredential.\n      credential = (factory as any).credential;\n    } else if (isStorageSharedKeyCredential(factory)) {\n      return factory;\n    }\n  }\n  return credential;\n}\n\nfunction isStorageSharedKeyCredential(\n  factory: RequestPolicyFactory,\n): factory is StorageSharedKeyCredential {\n  if (factory instanceof StorageSharedKeyCredential) {\n    return true;\n  }\n  return factory.constructor.name === \"StorageSharedKeyCredential\";\n}\n\nfunction isAnonymousCredential(factory: RequestPolicyFactory): factory is AnonymousCredential {\n  if (factory instanceof AnonymousCredential) {\n    return true;\n  }\n  return factory.constructor.name === \"AnonymousCredential\";\n}\n\nfunction isCoreHttpBearerTokenFactory(factory: RequestPolicyFactory): boolean {\n  return isTokenCredential((factory as any).credential);\n}\n\nfunction isStorageBrowserPolicyFactory(\n  factory: RequestPolicyFactory,\n): factory is StorageBrowserPolicyFactory {\n  if (factory instanceof StorageBrowserPolicyFactory) {\n    return true;\n  }\n  return factory.constructor.name === \"StorageBrowserPolicyFactory\";\n}\n\nfunction isStorageRetryPolicyFactory(\n  factory: RequestPolicyFactory,\n): factory is StorageRetryPolicyFactory {\n  if (factory instanceof StorageRetryPolicyFactory) {\n    return true;\n  }\n  return factory.constructor.name === \"StorageRetryPolicyFactory\";\n}\n\nfunction isStorageTelemetryPolicyFactory(factory: RequestPolicyFactory): boolean {\n  return factory.constructor.name === \"TelemetryPolicyFactory\";\n}\n\nfunction isInjectorPolicyFactory(factory: RequestPolicyFactory): boolean {\n  return factory.constructor.name === \"InjectorPolicyFactory\";\n}\n\nfunction isCoreHttpPolicyFactory(factory: RequestPolicyFactory): boolean {\n  const knownPolicies = [\n    \"GenerateClientRequestIdPolicy\",\n    \"TracingPolicy\",\n    \"LogPolicy\",\n    \"ProxyPolicy\",\n    \"DisableResponseDecompressionPolicy\",\n    \"KeepAlivePolicy\",\n    \"DeserializationPolicy\",\n  ];\n\n  const mockHttpClient: IHttpClient = {\n    sendRequest: async (request: WebResource) => {\n      return {\n        request,\n        headers: request.headers.clone(),\n        status: 500,\n      };\n    },\n  };\n  const mockRequestPolicyOptions: RequestPolicyOptions = {\n    log(_logLevel: HttpPipelineLogLevel, _message: string): void {\n      /* do nothing */\n    },\n    shouldLog(_logLevel: HttpPipelineLogLevel): boolean {\n      return false;\n    },\n  };\n  const policyInstance = factory.create(mockHttpClient, mockRequestPolicyOptions);\n  const policyName = policyInstance.constructor.name;\n  // bundlers sometimes add a custom suffix to the class name to make it unique\n  return knownPolicies.some((knownPolicyName) => {\n    return policyName.startsWith(knownPolicyName);\n  });\n}\n"]}