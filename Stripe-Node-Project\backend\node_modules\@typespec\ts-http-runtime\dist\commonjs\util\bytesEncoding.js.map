{"version": 3, "file": "bytesEncoding.js", "sourceRoot": "", "sources": ["../../../src/util/bytesEncoding.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;AAWlC,gDAEC;AAQD,gDAEC;AAlBD;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,KAAiB,EAAE,MAAoB;IACxE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,KAAa,EAAE,MAAoB;IACpE,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACpC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/** The supported character encoding type */\nexport type EncodingType = \"utf-8\" | \"base64\" | \"base64url\" | \"hex\";\n\n/**\n * The helper that transforms bytes with specific character encoding into string\n * @param bytes - the uint8array bytes\n * @param format - the format we use to encode the byte\n * @returns a string of the encoded string\n */\nexport function uint8ArrayToString(bytes: Uint8Array, format: EncodingType): string {\n  return Buffer.from(bytes).toString(format);\n}\n\n/**\n * The helper that transforms string to specific character encoded bytes array.\n * @param value - the string to be converted\n * @param format - the format we use to decode the value\n * @returns a uint8array\n */\nexport function stringToUint8Array(value: string, format: EncodingType): Uint8Array {\n  return Buffer.from(value, format);\n}\n"]}