{"version": 3, "file": "BlobServiceClient.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/BlobServiceClient.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AA2B1C,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAEzD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,4BAA4B,EAC5B,MAAM,GACP,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,0BAA0B,EAAE,MAAM,0CAA0C,CAAC;AACtF,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAExE,OAAO,EAAE,oBAAoB,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAC5E,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,qBAAqB,EAAE,MAAM,6BAA6B,CAAC;AAGpE,OAAO,EACL,iCAAiC,EACjC,yCAAyC,GAC1C,MAAM,iCAAiC,CAAC;AACzC,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAqR9D;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,aAAa;IAMlD;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,oBAAoB,CAChC,gBAAwB;IACxB,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,MAAM,cAAc,GAAG,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;QACtE,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAChD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;oBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC1E,CAAC;gBAED,MAAM,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBAC3D,OAAO,IAAI,iBAAiB,CAAC,cAAc,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YACjE,OAAO,IAAI,iBAAiB,CAAC,cAAc,CAAC,GAAG,GAAG,GAAG,GAAG,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC/F,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;QACJ,CAAC;IACH,CAAC;IAqDD,YACE,GAAW,EACX,oBAIgB;IAChB,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,IAAI,QAAsB,CAAC;QAC3B,IAAI,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzC,QAAQ,GAAG,oBAAoB,CAAC;QAClC,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,oBAAoB,YAAY,0BAA0B,CAAC;YACtE,oBAAoB,YAAY,mBAAmB;YACnD,iBAAiB,CAAC,oBAAoB,CAAC,EACvC,CAAC;YACD,QAAQ,GAAG,WAAW,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,8DAA8D;YAC9D,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;OAWG;IACI,kBAAkB,CAAC,aAAqB;QAC7C,OAAO,IAAI,eAAe,CACxB,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,EAC5D,IAAI,CAAC,QAAQ,CACd,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,eAAe,CAC1B,aAAqB,EACrB,UAAkC,EAAE;QAKpC,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAC/D,MAAM,uBAAuB,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;YAC7E,OAAO;gBACL,eAAe;gBACf,uBAAuB;aACxB,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,eAAe,CAC1B,aAAqB,EACrB,UAAwC,EAAE;QAE1C,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAC/D,OAAO,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAChD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,iBAAiB,CAC5B,oBAA4B,EAC5B,uBAA+B,EAC/B,UAA2C,EAAE;QAK7C,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAC7C,OAAO,CAAC,wBAAwB,IAAI,oBAAoB,CACzD,CAAC;YACF,qCAAqC;YACrC,MAAM,gBAAgB,GAAG,eAAe,CAAC,sBAAsB,CAAC,CAAC,SAAS,CAAC;YAC3E,MAAM,yBAAyB,GAAG,cAAc,CAI9C,MAAM,gBAAgB,CAAC,OAAO,CAAC;gBAC7B,oBAAoB;gBACpB,uBAAuB;gBACvB,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,OAAO,EAAE,eAAe,EAAE,yBAAyB,EAAE,CAAC;QACxD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,gEAAgE;IAChE,8HAA8H;IACtH,KAAK,CAAC,eAAe,CAC3B,mBAA2B,EAC3B,wBAAgC,EAChC,UAAyC,EAAE;QAK3C,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;YAC1E,qCAAqC;YACrC,MAAM,gBAAgB,GAAG,eAAe,CAAC,sBAAsB,CAAC,CAAC,SAAS,CAAC;YAC3E,MAAM,uBAAuB,GAAG,cAAc,CAI5C,MAAM,gBAAgB,CAAC,MAAM,CAAC,mBAAmB,kCAC5C,cAAc,KACjB,aAAa,EAAE,MAAA,OAAO,CAAC,eAAe,0CAAE,OAAO,IAC/C,CACH,CAAC;YACF,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,CAAC;QACtD,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,aAAa,CACxB,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CACxB,UAAiC,EACjC,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,UAAU,EAAE;gBAClD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CACxB,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,cAAc,CACzB,UAAwC,EAAE;QAE1C,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC;gBACvC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,KAAK,CAAC,qBAAqB,CACjC,MAAe,EACf,UAA+C,EAAE;QAEjD,OAAO,aAAa,CAAC,QAAQ,CAC3B,yCAAyC,EACzC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CAInB,MAAM,IAAI,CAAC,cAAc,CAAC,qBAAqB,+BAC7C,WAAW,EAAE,OAAO,CAAC,WAAW,EAChC,MAAM,IACH,OAAO,KACV,OAAO,EAAE,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAClF,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACK,KAAK,CAAC,sBAAsB,CAClC,sBAA8B,EAC9B,MAAe,EACf,UAAgD,EAAE;QAElD,OAAO,aAAa,CAAC,QAAQ,CAC3B,0CAA0C,EAC1C,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,KAAK,EAAE,sBAAsB;gBAC7B,MAAM;gBACN,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,MAAM,eAAe,mCAChB,QAAQ,KACX,SAAS,EAAE,QAAQ,CAAC,SAAS,EAC7B,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;;oBACjC,IAAI,QAAQ,GAAG,EAAE,CAAC;oBAClB,IAAI,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,UAAU,CAAC,MAAM,MAAK,CAAC,EAAE,CAAC;wBACvC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC3C,CAAC;oBACD,uCAAY,IAAI,KAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAG;gBACxD,CAAC,CAAC,GACH,CAAC;YACF,OAAO,eAAe,CAAC;QACzB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACY,uBAAuB;qFACpC,sBAA8B,EAC9B,MAAe,EACf,UAAgD,EAAE;YAElD,IAAI,QAAQ,CAAC;YACb,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,QAAQ,GAAG,cAAM,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA,CAAC;oBACtF,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtC,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC;oBACpC,oBAAM,QAAQ,CAAA,CAAC;gBACjB,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;;;;;OAQG;IACY,oBAAoB;kFACjC,sBAA8B,EAC9B,UAAgD,EAAE;;YAElD,IAAI,MAA0B,CAAC;;gBAC/B,KAA4B,eAAA,KAAA,cAAA,IAAI,CAAC,uBAAuB,CACtD,sBAAsB,EACtB,MAAM,EACN,OAAO,CACR,CAAA,IAAA,+DAAE,CAAC;oBAJwB,cAI3B;oBAJ2B,WAI3B;oBAJU,MAAM,OAAO,KAAA,CAAA;oBAKtB,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,OAAO,CAAC,KAAK,CAAA,CAAA,CAAA,CAAC;gBACvB,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8EG;IACI,eAAe,CACpB,sBAA8B,EAC9B,UAAwC,EAAE;QAE1C,8CAA8C;QAC9C,MAAM,kBAAkB,qBACnB,OAAO,CACX,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;QACnF,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,iBAAiB,kBACpF,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,kBAAkB,EACrB,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACY,YAAY;0EACzB,MAAe,EACf,UAA+C,EAAE;YAEjD,IAAI,6BAA6B,CAAC;YAClC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,6BAA6B,GAAG,cAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,CAAC;oBAClF,6BAA6B,CAAC,cAAc;wBAC1C,6BAA6B,CAAC,cAAc,IAAI,EAAE,CAAC;oBACrD,MAAM,GAAG,6BAA6B,CAAC,iBAAiB,CAAC;oBACzD,oBAAM,cAAM,6BAA6B,CAAA,CAAA,CAAC;gBAC5C,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;OAIG;IACY,SAAS;uEACtB,UAA+C,EAAE;;YAEjD,IAAI,MAA0B,CAAC;;gBAC/B,KAA4B,eAAA,KAAA,cAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,IAAA,+DAAE,CAAC;oBAArC,cAAkC;oBAAlC,WAAkC;oBAAnD,MAAM,OAAO,KAAA,CAAA;oBACtB,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,OAAO,CAAC,cAAc,CAAA,CAAA,CAAA,CAAC;gBAChC,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAyEG;IACI,cAAc,CACnB,UAAwC,EAAE;QAE1C,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAM,OAAO,GAAgC,EAAE,CAAC;QAChD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YAC1B,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QAED,mDAAmD;QACnD,MAAM,kBAAkB,mCACnB,OAAO,GACP,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAC3C,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QAChD,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,kBACjD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,kBAAkB,EACrB,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,oBAAoB,CAC/B,QAAc,EACd,SAAe,EACf,UAA8C,EAAE;QAEhD,OAAO,aAAa,CAAC,QAAQ,CAC3B,wCAAwC,EACxC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAC5C;gBACE,QAAQ,EAAE,oBAAoB,CAAC,QAAQ,EAAE,KAAK,CAAC;gBAC/C,SAAS,EAAE,oBAAoB,CAAC,SAAS,EAAE,KAAK,CAAC;aAClD,EACD;gBACE,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CACF,CACF,CAAC;YAEF,MAAM,iBAAiB,GAAG;gBACxB,cAAc,EAAE,QAAQ,CAAC,cAAc;gBACvC,cAAc,EAAE,QAAQ,CAAC,cAAc;gBACvC,cAAc,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;gBACjD,eAAe,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC;gBACnD,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,KAAK,EAAE,QAAQ,CAAC,KAAK;aACtB,CAAC;YAEF,MAAM,GAAG,mBACP,SAAS,EAAE,QAAQ,CAAC,SAAS,EAC7B,SAAS,EAAE,QAAQ,CAAC,SAAS,EAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe,EACzC,OAAO,EAAE,QAAQ,CAAC,OAAO,EACzB,IAAI,EAAE,QAAQ,CAAC,IAAI,EACnB,SAAS,EAAE,QAAQ,CAAC,SAAS,IAC1B,iBAAiB,CACrB,CAAC;YAEF,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB;QACvB,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,qBAAqB,CAC1B,SAAgB,EAChB,cAAqC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,EACrE,gBAAwB,KAAK,EAC7B,UAA+C,EAAE;QAEjD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,CACd,+FAA+F,CAChG,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,GAAG,GAAG,iCAAiC,iBAEzC,WAAW;YACX,SAAS;YACT,aAAa,EACb,QAAQ,EAAE,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAC/C,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,QAAQ,EAAE,CAAC;QAEb,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,uBAAuB,CAC5B,SAAgB,EAChB,cAAqC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,EACrE,gBAAwB,KAAK,EAC7B,UAA+C,EAAE;QAEjD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,CACd,+FAA+F,CAChG,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,yCAAyC,iBAE5C,WAAW;YACX,SAAS;YACT,aAAa,EACb,QAAQ,EAAE,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAC/C,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,YAAY,CAAC;IACjB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\nimport { getDefaultProxySettings } from \"@azure/core-rest-pipeline\";\nimport { isNode } from \"@azure/core-util\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type {\n  ServiceGetUserDelegationKeyHeaders,\n  ContainerCreateResponse,\n  ContainerDeleteResponse,\n  ServiceGetPropertiesResponse,\n  BlobServiceProperties,\n  ServiceSetPropertiesResponse,\n  ServiceGetStatisticsResponse,\n  ServiceGetAccountInfoResponse,\n  ServiceListContainersSegmentResponse,\n  ContainerItem,\n  UserDelegationKeyModel,\n  ContainerUndeleteResponse,\n  FilterBlobSegmentModel,\n  ServiceFilterBlobsHeaders,\n  ContainerRenameResponse,\n  LeaseAccessConditions,\n  FilterBlobSegment,\n  FilterBlobItem,\n  ServiceGetPropertiesResponseInternal,\n  ServiceGetStatisticsResponseInternal,\n  ServiceListContainersSegmentResponseInternal,\n} from \"./generatedModels\";\nimport type { Service } from \"./generated/src/operationsInterfaces\";\nimport type { StoragePipelineOptions, PipelineLike } from \"./Pipeline\";\nimport { newPipeline, isPipelineLike } from \"./Pipeline\";\nimport type { ContainerCreateOptions, ContainerDeleteMethodOptions } from \"./ContainerClient\";\nimport { ContainerClient } from \"./ContainerClient\";\nimport type { WithResponse } from \"./utils/utils.common\";\nimport {\n  appendToURLPath,\n  appendToURLQuery,\n  extractConnectionStringParts,\n  toTags,\n} from \"./utils/utils.common\";\nimport { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport type { PageSettings, PagedAsyncIterableIterator } from \"@azure/core-paging\";\nimport { truncatedISO8061Date, assertResponse } from \"./utils/utils.common\";\nimport { tracingClient } from \"./utils/tracing\";\nimport { BlobBatchClient } from \"./BlobBatchClient\";\nimport type { CommonOptions } from \"./StorageClient\";\nimport { StorageClient } from \"./StorageClient\";\nimport { AccountSASPermissions } from \"./sas/AccountSASPermissions\";\nimport type { SASProtocol } from \"./sas/SASQueryParameters\";\nimport type { SasIPRange } from \"./sas/SasIPRange\";\nimport {\n  generateAccountSASQueryParameters,\n  generateAccountSASQueryParametersInternal,\n} from \"./sas/AccountSASSignatureValues\";\nimport { AccountSASServices } from \"./sas/AccountSASServices\";\nimport type {\n  ContainerRenameHeaders,\n  ContainerRestoreHeaders,\n  ListContainersIncludeType,\n  ServiceFilterBlobsResponse,\n  ServiceGetAccountInfoHeaders,\n  ServiceGetPropertiesHeaders,\n  ServiceGetStatisticsHeaders,\n  ServiceGetUserDelegationKeyResponse as ServiceGetUserDelegationKeyResponseModel,\n  ServiceListContainersSegmentHeaders,\n  ServiceSetPropertiesHeaders,\n} from \"./generated/src\";\n\n/**\n * Options to configure the {@link BlobServiceClient.getProperties} operation.\n */\nexport interface ServiceGetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.setProperties} operation.\n */\nexport interface ServiceSetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.getAccountInfo} operation.\n */\nexport interface ServiceGetAccountInfoOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.getStatistics} operation.\n */\nexport interface ServiceGetStatisticsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure the Service - Get User Delegation Key.\n */\nexport interface ServiceGetUserDelegationKeyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.listContainerSegment} operation.\n */\ninterface ServiceListContainersSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Filters the results to return only containers\n   * whose name begins with the specified prefix.\n   */\n  prefix?: string;\n  /**\n   * Specifies the maximum number of containers\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n  /**\n   * Include this parameter to\n   * specify that the container's metadata be returned as part of the response\n   * body. Possible values include: 'metadata'\n   */\n  include?: ListContainersIncludeType | ListContainersIncludeType[];\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.listContainers} operation.\n */\nexport interface ServiceListContainersOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Filters the results to return only containers\n   * whose name begins with the specified prefix.\n   */\n  prefix?: string;\n  /**\n   * Specifies whether the container's metadata\n   *                                   should be returned as part of the response body.\n   */\n  includeMetadata?: boolean;\n\n  /**\n   * Specifies whether soft deleted containers should be included in the response.\n   */\n  includeDeleted?: boolean;\n  /**\n   * Specifies whether system containers should be included in the response.\n   */\n  includeSystem?: boolean;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.findBlobsByTagsSegment} operation.\n */\ninterface ServiceFindBlobsByTagsSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Specifies the maximum number of blobs\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.findBlobsByTags} operation.\n */\nexport interface ServiceFindBlobByTagsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * The response of {@link BlobServiceClient.findBlobsByTags} operation.\n */\nexport type ServiceFindBlobsByTagsSegmentResponse = WithResponse<\n  FilterBlobSegment & ServiceFilterBlobsHeaders,\n  ServiceFilterBlobsHeaders,\n  FilterBlobSegmentModel\n>;\n\n/**\n * A user delegation key.\n */\nexport interface UserDelegationKey {\n  /**\n   * The Azure Active Directory object ID in GUID format.\n   */\n  signedObjectId: string;\n  /**\n   * The Azure Active Directory tenant ID in GUID format.\n   */\n  signedTenantId: string;\n  /**\n   * The date-time the key is active.\n   */\n  signedStartsOn: Date;\n  /**\n   * The date-time the key expires.\n   */\n  signedExpiresOn: Date;\n  /**\n   * Abbreviation of the Azure Storage service that accepts the key.\n   */\n  signedService: string;\n  /**\n   * The service version that created the key.\n   */\n  signedVersion: string;\n  /**\n   * The key as a base64 string.\n   */\n  value: string;\n}\n\n/**\n * Contains response data for the {@link getUserDelegationKey} operation.\n */\nexport declare type ServiceGetUserDelegationKeyResponse = WithResponse<\n  UserDelegationKey & ServiceGetUserDelegationKeyHeaders,\n  ServiceGetUserDelegationKeyHeaders,\n  UserDelegationKeyModel\n>;\n\n/**\n * Options to configure {@link BlobServiceClient.undeleteContainer} operation.\n */\nexport interface ServiceUndeleteContainerOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Optional. Specifies the new name of the restored container.\n   * Will use its original name if this is not specified.\n   * @deprecated Restore container to a different name is not supported by service anymore.\n   */\n  destinationContainerName?: string;\n}\n\n/**\n * Options to configure {@link BlobServiceClient.renameContainer} operation.\n */\nexport interface ServiceRenameContainerOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Condition to meet for the source container.\n   */\n  sourceCondition?: LeaseAccessConditions;\n}\n\n/**\n * Options to configure {@link BlobServiceClient.generateAccountSasUrl} operation.\n */\nexport interface ServiceGenerateAccountSasUrlOptions {\n  /**\n   * The version of the service this SAS will target. If not specified, it will default to the version targeted by the\n   * library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols allowed.\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n  /**\n   * Optional. IP range allowed.\n   */\n  ipRange?: SasIPRange;\n  /**\n   * Optional. Encryption scope to use when sending requests authorized with this SAS URI.\n   */\n  encryptionScope?: string;\n}\n\n/**\n * A BlobServiceClient represents a Client to the Azure Storage Blob service allowing you\n * to manipulate blob containers.\n */\nexport class BlobServiceClient extends StorageClient {\n  /**\n   * serviceContext provided by protocol layer.\n   */\n  private serviceContext: Service;\n\n  /**\n   *\n   * Creates an instance of BlobServiceClient from connection string.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  public static fromConnectionString(\n    connectionString: string,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ): BlobServiceClient {\n    options = options || {};\n    const extractedCreds = extractConnectionStringParts(connectionString);\n    if (extractedCreds.kind === \"AccountConnString\") {\n      if (isNode) {\n        const sharedKeyCredential = new StorageSharedKeyCredential(\n          extractedCreds.accountName!,\n          extractedCreds.accountKey,\n        );\n\n        if (!options.proxyOptions) {\n          options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n        }\n\n        const pipeline = newPipeline(sharedKeyCredential, options);\n        return new BlobServiceClient(extractedCreds.url, pipeline);\n      } else {\n        throw new Error(\"Account connection string is only supported in Node.js environment\");\n      }\n    } else if (extractedCreds.kind === \"SASConnString\") {\n      const pipeline = newPipeline(new AnonymousCredential(), options);\n      return new BlobServiceClient(extractedCreds.url + \"?\" + extractedCreds.accountSas, pipeline);\n    } else {\n      throw new Error(\n        \"Connection string must be either an Account connection string or a SAS connection string\",\n      );\n    }\n  }\n\n  /**\n   * Creates an instance of BlobServiceClient.\n   *\n   * @param url - A Client string pointing to Azure Storage blob service, such as\n   *                     \"https://myaccount.blob.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.blob.core.windows.net?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   *\n   * Example using DefaultAzureCredential from `@azure/identity`:\n   *\n   * ```js\n   * const account = \"<storage account name>\";\n   *\n   * const defaultAzureCredential = new DefaultAzureCredential();\n   *\n   * const blobServiceClient = new BlobServiceClient(\n   *   `https://${account}.blob.core.windows.net`,\n   *   defaultAzureCredential\n   * );\n   * ```\n   *\n   * Example using an account name/key:\n   *\n   * ```js\n   * const account = \"<storage account name>\"\n   * const sharedKeyCredential = new StorageSharedKeyCredential(account, \"<account key>\");\n   *\n   * const blobServiceClient = new BlobServiceClient(\n   *   `https://${account}.blob.core.windows.net`,\n   *   sharedKeyCredential\n   * );\n   * ```\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of BlobServiceClient.\n   *\n   * @param url - A Client string pointing to Azure Storage blob service, such as\n   *                     \"https://myaccount.blob.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.blob.core.windows.net?sasString\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    url: string,\n    credentialOrPipeline?:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    let pipeline: PipelineLike;\n    if (isPipelineLike(credentialOrPipeline)) {\n      pipeline = credentialOrPipeline;\n    } else if (\n      (isNode && credentialOrPipeline instanceof StorageSharedKeyCredential) ||\n      credentialOrPipeline instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipeline)\n    ) {\n      pipeline = newPipeline(credentialOrPipeline, options);\n    } else {\n      // The second parameter is undefined. Use anonymous credential\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    }\n    super(url, pipeline);\n    this.serviceContext = this.storageClientContext.service;\n  }\n\n  /**\n   * Creates a {@link ContainerClient} object\n   *\n   * @param containerName - A container name\n   * @returns A new ContainerClient object for the given container name.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const containerClient = blobServiceClient.getContainerClient(\"<container name>\");\n   * ```\n   */\n  public getContainerClient(containerName: string): ContainerClient {\n    return new ContainerClient(\n      appendToURLPath(this.url, encodeURIComponent(containerName)),\n      this.pipeline,\n    );\n  }\n\n  /**\n   * Create a Blob container. @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-container\n   *\n   * @param containerName - Name of the container to create.\n   * @param options - Options to configure Container Create operation.\n   * @returns Container creation response and the corresponding container client.\n   */\n  public async createContainer(\n    containerName: string,\n    options: ContainerCreateOptions = {},\n  ): Promise<{\n    containerClient: ContainerClient;\n    containerCreateResponse: ContainerCreateResponse;\n  }> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-createContainer\",\n      options,\n      async (updatedOptions) => {\n        const containerClient = this.getContainerClient(containerName);\n        const containerCreateResponse = await containerClient.create(updatedOptions);\n        return {\n          containerClient,\n          containerCreateResponse,\n        };\n      },\n    );\n  }\n\n  /**\n   * Deletes a Blob container.\n   *\n   * @param containerName - Name of the container to delete.\n   * @param options - Options to configure Container Delete operation.\n   * @returns Container deletion response.\n   */\n  public async deleteContainer(\n    containerName: string,\n    options: ContainerDeleteMethodOptions = {},\n  ): Promise<ContainerDeleteResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-deleteContainer\",\n      options,\n      async (updatedOptions) => {\n        const containerClient = this.getContainerClient(containerName);\n        return containerClient.delete(updatedOptions);\n      },\n    );\n  }\n\n  /**\n   * Restore a previously deleted Blob container.\n   * This API is only functional if Container Soft Delete is enabled for the storage account associated with the container.\n   *\n   * @param deletedContainerName - Name of the previously deleted container.\n   * @param deletedContainerVersion - Version of the previously deleted container, used to uniquely identify the deleted container.\n   * @param options - Options to configure Container Restore operation.\n   * @returns Container deletion response.\n   */\n  public async undeleteContainer(\n    deletedContainerName: string,\n    deletedContainerVersion: string,\n    options: ServiceUndeleteContainerOptions = {},\n  ): Promise<{\n    containerClient: ContainerClient;\n    containerUndeleteResponse: ContainerUndeleteResponse;\n  }> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-undeleteContainer\",\n      options,\n      async (updatedOptions) => {\n        const containerClient = this.getContainerClient(\n          options.destinationContainerName || deletedContainerName,\n        );\n        // Hack to access a protected member.\n        const containerContext = containerClient[\"storageClientContext\"].container;\n        const containerUndeleteResponse = assertResponse<\n          ContainerRestoreHeaders,\n          ContainerRestoreHeaders\n        >(\n          await containerContext.restore({\n            deletedContainerName,\n            deletedContainerVersion,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n        return { containerClient, containerUndeleteResponse };\n      },\n    );\n  }\n\n  /**\n   * Rename an existing Blob Container.\n   *\n   * @param sourceContainerName - The name of the source container.\n   * @param destinationContainerName - The new name of the container.\n   * @param options - Options to configure Container Rename operation.\n   */\n  /* eslint-disable-next-line @typescript-eslint/ban-ts-comment */\n  // @ts-ignore Need to hide this interface for now. Make it public and turn on the live tests for it when the service is ready.\n  private async renameContainer(\n    sourceContainerName: string,\n    destinationContainerName: string,\n    options: ServiceRenameContainerOptions = {},\n  ): Promise<{\n    containerClient: ContainerClient;\n    containerRenameResponse: ContainerRenameResponse;\n  }> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-renameContainer\",\n      options,\n      async (updatedOptions) => {\n        const containerClient = this.getContainerClient(destinationContainerName);\n        // Hack to access a protected member.\n        const containerContext = containerClient[\"storageClientContext\"].container;\n        const containerRenameResponse = assertResponse<\n          ContainerRenameHeaders,\n          ContainerRenameHeaders\n        >(\n          await containerContext.rename(sourceContainerName, {\n            ...updatedOptions,\n            sourceLeaseId: options.sourceCondition?.leaseId,\n          }),\n        );\n        return { containerClient, containerRenameResponse };\n      },\n    );\n  }\n\n  /**\n   * Gets the properties of a storage account’s Blob service, including properties\n   * for Storage Analytics and CORS (Cross-Origin Resource Sharing) rules.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-blob-service-properties\n   *\n   * @param options - Options to the Service Get Properties operation.\n   * @returns Response data for the Service Get Properties operation.\n   */\n  public async getProperties(\n    options: ServiceGetPropertiesOptions = {},\n  ): Promise<ServiceGetPropertiesResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-getProperties\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ServiceGetPropertiesResponseInternal, ServiceGetPropertiesHeaders>(\n          await this.serviceContext.getProperties({\n            abortSignal: options.abortSignal,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Sets properties for a storage account’s Blob service endpoint, including properties\n   * for Storage Analytics, CORS (Cross-Origin Resource Sharing) rules and soft delete settings.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-blob-service-properties\n   *\n   * @param properties -\n   * @param options - Options to the Service Set Properties operation.\n   * @returns Response data for the Service Set Properties operation.\n   */\n  public async setProperties(\n    properties: BlobServiceProperties,\n    options: ServiceSetPropertiesOptions = {},\n  ): Promise<ServiceSetPropertiesResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-setProperties\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ServiceSetPropertiesHeaders, ServiceSetPropertiesHeaders>(\n          await this.serviceContext.setProperties(properties, {\n            abortSignal: options.abortSignal,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Retrieves statistics related to replication for the Blob service. It is only\n   * available on the secondary location endpoint when read-access geo-redundant\n   * replication is enabled for the storage account.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-blob-service-stats\n   *\n   * @param options - Options to the Service Get Statistics operation.\n   * @returns Response data for the Service Get Statistics operation.\n   */\n  public async getStatistics(\n    options: ServiceGetStatisticsOptions = {},\n  ): Promise<ServiceGetStatisticsResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-getStatistics\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ServiceGetStatisticsResponseInternal, ServiceGetStatisticsHeaders>(\n          await this.serviceContext.getStatistics({\n            abortSignal: options.abortSignal,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * The Get Account Information operation returns the sku name and account kind\n   * for the specified account.\n   * The Get Account Information operation is available on service versions beginning\n   * with version 2018-03-28.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-account-information\n   *\n   * @param options - Options to the Service Get Account Info operation.\n   * @returns Response data for the Service Get Account Info operation.\n   */\n  public async getAccountInfo(\n    options: ServiceGetAccountInfoOptions = {},\n  ): Promise<ServiceGetAccountInfoResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-getAccountInfo\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ServiceGetAccountInfoHeaders, ServiceGetAccountInfoHeaders>(\n          await this.serviceContext.getAccountInfo({\n            abortSignal: options.abortSignal,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Returns a list of the containers under the specified account.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/list-containers2\n   *\n   * @param marker - A string value that identifies the portion of\n   *                        the list of containers to be returned with the next listing operation. The\n   *                        operation returns the continuationToken value within the response body if the\n   *                        listing operation did not return all containers remaining to be listed\n   *                        with the current page. The continuationToken value can be used as the value for\n   *                        the marker parameter in a subsequent call to request the next page of list\n   *                        items. The marker value is opaque to the client.\n   * @param options - Options to the Service List Container Segment operation.\n   * @returns Response data for the Service List Container Segment operation.\n   */\n  private async listContainersSegment(\n    marker?: string,\n    options: ServiceListContainersSegmentOptions = {},\n  ): Promise<ServiceListContainersSegmentResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-listContainersSegment\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          ServiceListContainersSegmentResponseInternal,\n          ServiceListContainersSegmentHeaders\n        >(\n          await this.serviceContext.listContainersSegment({\n            abortSignal: options.abortSignal,\n            marker,\n            ...options,\n            include: typeof options.include === \"string\" ? [options.include] : options.include,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * The Filter Blobs operation enables callers to list blobs across all containers whose tags\n   * match a given search expression. Filter blobs searches across all containers within a\n   * storage account but can be scoped within the expression to a single container.\n   *\n   * @param tagFilterSqlExpression - The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                        The given expression must evaluate to true for a blob to be returned in the results.\n   *                                        The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                        however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param marker - A string value that identifies the portion of\n   *                          the list of blobs to be returned with the next listing operation. The\n   *                          operation returns the continuationToken value within the response body if the\n   *                          listing operation did not return all blobs remaining to be listed\n   *                          with the current page. The continuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of list\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to find blobs by tags.\n   */\n  private async findBlobsByTagsSegment(\n    tagFilterSqlExpression: string,\n    marker?: string,\n    options: ServiceFindBlobsByTagsSegmentOptions = {},\n  ): Promise<ServiceFindBlobsByTagsSegmentResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-findBlobsByTagsSegment\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          ServiceFilterBlobsResponse,\n          ServiceFilterBlobsHeaders,\n          FilterBlobSegmentModel\n        >(\n          await this.serviceContext.filterBlobs({\n            abortSignal: options.abortSignal,\n            where: tagFilterSqlExpression,\n            marker,\n            maxPageSize: options.maxPageSize,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        const wrappedResponse: ServiceFindBlobsByTagsSegmentResponse = {\n          ...response,\n          _response: response._response, // _response is made non-enumerable\n          blobs: response.blobs.map((blob) => {\n            let tagValue = \"\";\n            if (blob.tags?.blobTagSet.length === 1) {\n              tagValue = blob.tags.blobTagSet[0].value;\n            }\n            return { ...blob, tags: toTags(blob.tags), tagValue };\n          }),\n        };\n        return wrappedResponse;\n      },\n    );\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for ServiceFindBlobsByTagsSegmentResponse.\n   *\n   * @param tagFilterSqlExpression -  The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                         The given expression must evaluate to true for a blob to be returned in the results.\n   *                                         The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                         however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param marker - A string value that identifies the portion of\n   *                          the list of blobs to be returned with the next listing operation. The\n   *                          operation returns the continuationToken value within the response body if the\n   *                          listing operation did not return all blobs remaining to be listed\n   *                          with the current page. The continuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of list\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to find blobs by tags.\n   */\n  private async *findBlobsByTagsSegments(\n    tagFilterSqlExpression: string,\n    marker?: string,\n    options: ServiceFindBlobsByTagsSegmentOptions = {},\n  ): AsyncIterableIterator<ServiceFindBlobsByTagsSegmentResponse> {\n    let response;\n    if (!!marker || marker === undefined) {\n      do {\n        response = await this.findBlobsByTagsSegment(tagFilterSqlExpression, marker, options);\n        response.blobs = response.blobs || [];\n        marker = response.continuationToken;\n        yield response;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for blobs.\n   *\n   * @param tagFilterSqlExpression -  The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                         The given expression must evaluate to true for a blob to be returned in the results.\n   *                                         The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                         however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param options - Options to findBlobsByTagsItems.\n   */\n  private async *findBlobsByTagsItems(\n    tagFilterSqlExpression: string,\n    options: ServiceFindBlobsByTagsSegmentOptions = {},\n  ): AsyncIterableIterator<FilterBlobItem> {\n    let marker: string | undefined;\n    for await (const segment of this.findBlobsByTagsSegments(\n      tagFilterSqlExpression,\n      marker,\n      options,\n    )) {\n      yield* segment.blobs;\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to find all blobs with specified tag\n   * under the specified account.\n   *\n   * .byPage() returns an async iterable iterator to list the blobs in pages.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-blob-service-properties\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * let i = 1;\n   * for await (const blob of blobServiceClient.findBlobsByTags(\"tagkey='tagvalue'\")) {\n   *   console.log(`Blob ${i++}: ${container.name}`);\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * const iter = blobServiceClient.findBlobsByTags(\"tagkey='tagvalue'\");\n   * let blobItem = await iter.next();\n   * while (!blobItem.done) {\n   *   console.log(`Blob ${i++}: ${blobItem.value.name}`);\n   *   blobItem = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const response of blobServiceClient.findBlobsByTags(\"tagkey='tagvalue'\").byPage({ maxPageSize: 20 })) {\n   *   if (response.blobs) {\n   *     for (const blob of response.blobs) {\n   *       console.log(`Blob ${i++}: ${blob.name}`);\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = blobServiceClient.findBlobsByTags(\"tagkey='tagvalue'\").byPage({ maxPageSize: 2 });\n   * let response = (await iterator.next()).value;\n   *\n   * // Prints 2 blob names\n   * if (response.blobs) {\n   *   for (const blob of response.blobs) {\n   *     console.log(`Blob ${i++}: ${blob.name}`);\n   *   }\n   * }\n   *\n   * // Gets next marker\n   * let marker = response.continuationToken;\n   * // Passing next marker as continuationToken\n   * iterator = blobServiceClient\n   *   .findBlobsByTags(\"tagkey='tagvalue'\")\n   *   .byPage({ continuationToken: marker, maxPageSize: 10 });\n   * response = (await iterator.next()).value;\n   *\n   * // Prints blob names\n   * if (response.blobs) {\n   *   for (const blob of response.blobs) {\n   *      console.log(`Blob ${i++}: ${blob.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * @param tagFilterSqlExpression -  The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                         The given expression must evaluate to true for a blob to be returned in the results.\n   *                                         The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                         however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param options - Options to find blobs by tags.\n   */\n  public findBlobsByTags(\n    tagFilterSqlExpression: string,\n    options: ServiceFindBlobByTagsOptions = {},\n  ): PagedAsyncIterableIterator<FilterBlobItem, ServiceFindBlobsByTagsSegmentResponse> {\n    // AsyncIterableIterator to iterate over blobs\n    const listSegmentOptions: ServiceFindBlobsByTagsSegmentOptions = {\n      ...options,\n    };\n\n    const iter = this.findBlobsByTagsItems(tagFilterSqlExpression, listSegmentOptions);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.findBlobsByTagsSegments(tagFilterSqlExpression, settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...listSegmentOptions,\n        });\n      },\n    };\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for ServiceListContainersSegmentResponses\n   *\n   * @param marker - A string value that identifies the portion of\n   *                        the list of containers to be returned with the next listing operation. The\n   *                        operation returns the continuationToken value within the response body if the\n   *                        listing operation did not return all containers remaining to be listed\n   *                        with the current page. The continuationToken value can be used as the value for\n   *                        the marker parameter in a subsequent call to request the next page of list\n   *                        items. The marker value is opaque to the client.\n   * @param options - Options to list containers operation.\n   */\n  private async *listSegments(\n    marker?: string,\n    options: ServiceListContainersSegmentOptions = {},\n  ): AsyncIterableIterator<ServiceListContainersSegmentResponse> {\n    let listContainersSegmentResponse;\n    if (!!marker || marker === undefined) {\n      do {\n        listContainersSegmentResponse = await this.listContainersSegment(marker, options);\n        listContainersSegmentResponse.containerItems =\n          listContainersSegmentResponse.containerItems || [];\n        marker = listContainersSegmentResponse.continuationToken;\n        yield await listContainersSegmentResponse;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for Container Items\n   *\n   * @param options - Options to list containers operation.\n   */\n  private async *listItems(\n    options: ServiceListContainersSegmentOptions = {},\n  ): AsyncIterableIterator<ContainerItem> {\n    let marker: string | undefined;\n    for await (const segment of this.listSegments(marker, options)) {\n      yield* segment.containerItems;\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to list all the containers\n   * under the specified account.\n   *\n   * .byPage() returns an async iterable iterator to list the containers in pages.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * let i = 1;\n   * for await (const container of blobServiceClient.listContainers()) {\n   *   console.log(`Container ${i++}: ${container.name}`);\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * const iter = blobServiceClient.listContainers();\n   * let containerItem = await iter.next();\n   * while (!containerItem.done) {\n   *   console.log(`Container ${i++}: ${containerItem.value.name}`);\n   *   containerItem = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const response of blobServiceClient.listContainers().byPage({ maxPageSize: 20 })) {\n   *   if (response.containerItems) {\n   *     for (const container of response.containerItems) {\n   *       console.log(`Container ${i++}: ${container.name}`);\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = blobServiceClient.listContainers().byPage({ maxPageSize: 2 });\n   * let response = (await iterator.next()).value;\n   *\n   * // Prints 2 container names\n   * if (response.containerItems) {\n   *   for (const container of response.containerItems) {\n   *     console.log(`Container ${i++}: ${container.name}`);\n   *   }\n   * }\n   *\n   * // Gets next marker\n   * let marker = response.continuationToken;\n   * // Passing next marker as continuationToken\n   * iterator = blobServiceClient\n   *   .listContainers()\n   *   .byPage({ continuationToken: marker, maxPageSize: 10 });\n   * response = (await iterator.next()).value;\n   *\n   * // Prints 10 container names\n   * if (response.containerItems) {\n   *   for (const container of response.containerItems) {\n   *      console.log(`Container ${i++}: ${container.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * @param options - Options to list containers.\n   * @returns An asyncIterableIterator that supports paging.\n   */\n  public listContainers(\n    options: ServiceListContainersOptions = {},\n  ): PagedAsyncIterableIterator<ContainerItem, ServiceListContainersSegmentResponse> {\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    const include: ListContainersIncludeType[] = [];\n    if (options.includeDeleted) {\n      include.push(\"deleted\");\n    }\n    if (options.includeMetadata) {\n      include.push(\"metadata\");\n    }\n    if (options.includeSystem) {\n      include.push(\"system\");\n    }\n\n    // AsyncIterableIterator to iterate over containers\n    const listSegmentOptions: ServiceListContainersSegmentOptions = {\n      ...options,\n      ...(include.length > 0 ? { include } : {}),\n    };\n\n    const iter = this.listItems(listSegmentOptions);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.listSegments(settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...listSegmentOptions,\n        });\n      },\n    };\n  }\n\n  /**\n   * ONLY AVAILABLE WHEN USING BEARER TOKEN AUTHENTICATION (TokenCredential).\n   *\n   * Retrieves a user delegation key for the Blob service. This is only a valid operation when using\n   * bearer token authentication.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-user-delegation-key\n   *\n   * @param startsOn -      The start time for the user delegation SAS. Must be within 7 days of the current time\n   * @param expiresOn -     The end time for the user delegation SAS. Must be within 7 days of the current time\n   */\n  public async getUserDelegationKey(\n    startsOn: Date,\n    expiresOn: Date,\n    options: ServiceGetUserDelegationKeyOptions = {},\n  ): Promise<ServiceGetUserDelegationKeyResponse> {\n    return tracingClient.withSpan(\n      \"BlobServiceClient-getUserDelegationKey\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          ServiceGetUserDelegationKeyResponseModel,\n          ServiceGetUserDelegationKeyHeaders,\n          UserDelegationKeyModel\n        >(\n          await this.serviceContext.getUserDelegationKey(\n            {\n              startsOn: truncatedISO8061Date(startsOn, false),\n              expiresOn: truncatedISO8061Date(expiresOn, false),\n            },\n            {\n              abortSignal: options.abortSignal,\n              tracingOptions: updatedOptions.tracingOptions,\n            },\n          ),\n        );\n\n        const userDelegationKey = {\n          signedObjectId: response.signedObjectId,\n          signedTenantId: response.signedTenantId,\n          signedStartsOn: new Date(response.signedStartsOn),\n          signedExpiresOn: new Date(response.signedExpiresOn),\n          signedService: response.signedService,\n          signedVersion: response.signedVersion,\n          value: response.value,\n        };\n\n        const res: ServiceGetUserDelegationKeyResponse = {\n          _response: response._response,\n          requestId: response.requestId,\n          clientRequestId: response.clientRequestId,\n          version: response.version,\n          date: response.date,\n          errorCode: response.errorCode,\n          ...userDelegationKey,\n        };\n\n        return res;\n      },\n    );\n  }\n\n  /**\n   * Creates a BlobBatchClient object to conduct batch operations.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch\n   *\n   * @returns A new BlobBatchClient object for this service.\n   */\n  public getBlobBatchClient(): BlobBatchClient {\n    return new BlobBatchClient(this.url, this.pipeline);\n  }\n\n  /**\n   * Only available for BlobServiceClient constructed with a shared key credential.\n   *\n   * Generates a Blob account Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-account-sas\n   *\n   * @param expiresOn - Optional. The time at which the shared access signature becomes invalid. Default to an hour later if not provided.\n   * @param permissions - Specifies the list of permissions to be associated with the SAS.\n   * @param resourceTypes - Specifies the resource types associated with the shared access signature.\n   * @param options - Optional parameters.\n   * @returns An account SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateAccountSasUrl(\n    expiresOn?: Date,\n    permissions: AccountSASPermissions = AccountSASPermissions.parse(\"r\"),\n    resourceTypes: string = \"sco\",\n    options: ServiceGenerateAccountSasUrlOptions = {},\n  ): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw RangeError(\n        \"Can only generate the account SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    if (expiresOn === undefined) {\n      const now = new Date();\n      expiresOn = new Date(now.getTime() + 3600 * 1000);\n    }\n\n    const sas = generateAccountSASQueryParameters(\n      {\n        permissions,\n        expiresOn,\n        resourceTypes,\n        services: AccountSASServices.parse(\"b\").toString(),\n        ...options,\n      },\n      this.credential,\n    ).toString();\n\n    return appendToURLQuery(this.url, sas);\n  }\n\n  /**\n   * Only available for BlobServiceClient constructed with a shared key credential.\n   *\n   * Generates string to sign for a Blob account Shared Access Signature (SAS) URI based on\n   * the client properties and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-account-sas\n   *\n   * @param expiresOn - Optional. The time at which the shared access signature becomes invalid. Default to an hour later if not provided.\n   * @param permissions - Specifies the list of permissions to be associated with the SAS.\n   * @param resourceTypes - Specifies the resource types associated with the shared access signature.\n   * @param options - Optional parameters.\n   * @returns An account SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateSasStringToSign(\n    expiresOn?: Date,\n    permissions: AccountSASPermissions = AccountSASPermissions.parse(\"r\"),\n    resourceTypes: string = \"sco\",\n    options: ServiceGenerateAccountSasUrlOptions = {},\n  ): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw RangeError(\n        \"Can only generate the account SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    if (expiresOn === undefined) {\n      const now = new Date();\n      expiresOn = new Date(now.getTime() + 3600 * 1000);\n    }\n\n    return generateAccountSASQueryParametersInternal(\n      {\n        permissions,\n        expiresOn,\n        resourceTypes,\n        services: AccountSASServices.parse(\"b\").toString(),\n        ...options,\n      },\n      this.credential,\n    ).stringToSign;\n  }\n}\n"]}