const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { supabase, supabaseAdmin } = require('../config/database');

/**
 * Authentication middleware for protecting API routes
 * Verifies JWT tokens and WordPress API keys
 */
async function authMiddleware(req, res, next) {
  try {
    const authHeader = req.headers.authorization;
    const apiKey = req.headers['x-api-key'];
    
    // Check for JWT token
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        return next();
      } catch (jwtError) {
        console.error('JWT verification failed:', jwtError.message);
        return res.status(401).json({
          error: 'Invalid token',
          message: 'JWT token verification failed'
        });
      }
    }
    
    // Check for API key (WordPress integration)
    if (apiKey) {
      try {
        // Hash the incoming API key to compare with stored hash
        const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

        // Verify API key against database using admin client to bypass RLS
        const { data: apiKeyData, error } = await supabaseAdmin
          .from('api_keys')
          .select('*')
          .eq('key_hash', keyHash)
          .eq('is_active', true)
          .single();
        
        if (error || !apiKeyData) {
          return res.status(401).json({
            error: 'Invalid API key',
            message: 'API key not found or inactive'
          });
        }
        
        // Update last used timestamp
        await supabaseAdmin
          .from('api_keys')
          .update({ last_used_at: new Date().toISOString() })
          .eq('id', apiKeyData.id);
        
        // Set user context from API key
        req.user = {
          id: apiKeyData.user_id,
          type: 'api_key',
          permissions: apiKeyData.permissions || [],
          wordpress_site: apiKeyData.wordpress_site
        };
        
        return next();
      } catch (apiError) {
        console.error('API key verification failed:', apiError.message);
        return res.status(401).json({
          error: 'API key verification failed',
          message: 'Unable to verify API key'
        });
      }
    }
    
    // No valid authentication found
    return res.status(401).json({
      error: 'Authentication required',
      message: 'Please provide a valid JWT token or API key'
    });
    
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return res.status(500).json({
      error: 'Authentication error',
      message: 'Internal server error during authentication'
    });
  }
}

/**
 * Generate JWT token for user
 * @param {Object} user - User object
 * @returns {string} - JWT token
 */
function generateToken(user) {
  const payload = {
    id: user.id,
    email: user.email,
    type: 'user'
  };
  
  return jwt.sign(payload, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  });
}

/**
 * Generate API key for WordPress integration
 * @param {Object} userData - User and site data
 * @returns {Promise<string>} - Generated API key
 */
async function generateApiKey(userData) {
  try {
    // Generate a random API key
    const apiKey = require('crypto').randomBytes(32).toString('hex');
    
    // Store API key in database
    const { data, error } = await supabase
      .from('api_keys')
      .insert({
        key_hash: apiKey,
        user_id: userData.user_id,
        wordpress_site: userData.wordpress_site,
        permissions: userData.permissions || ['payments', 'commissions'],
        is_active: true,
        created_at: new Date().toISOString()
      })
      .select()
      .single();
    
    if (error) {
      throw error;
    }
    
    return apiKey;
  } catch (error) {
    console.error('Error generating API key:', error);
    throw error;
  }
}

/**
 * Middleware to check specific permissions
 * @param {Array} requiredPermissions - Array of required permissions
 * @returns {Function} - Middleware function
 */
function requirePermissions(requiredPermissions) {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'User not authenticated'
      });
    }
    
    // Admin users have all permissions
    if (req.user.type === 'admin') {
      return next();
    }
    
    // Check if user has required permissions
    const userPermissions = req.user.permissions || [];
    const hasPermission = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        error: 'Insufficient permissions',
        message: `Required permissions: ${requiredPermissions.join(', ')}`
      });
    }
    
    next();
  };
}

module.exports = {
  authMiddleware,
  generateToken,
  generateApiKey,
  requirePermissions
};
