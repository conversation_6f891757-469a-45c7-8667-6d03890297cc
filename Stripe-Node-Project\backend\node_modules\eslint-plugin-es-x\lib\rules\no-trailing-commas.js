/**
 * <AUTHOR> <https://github.com/mysticatea>
 * See LICENSE file in root directory for full license.
 */
"use strict"

const { getSourceCode } = require("eslint-compat-utils")
const { isCommaToken } = require("../utils")

module.exports = {
    meta: {
        docs: {
            description: "disallow trailing commas in array/object literals.",
            category: "ES5",
            recommended: false,
            url: "http://eslint-community.github.io/eslint-plugin-es-x/rules/no-trailing-commas.html",
        },
        fixable: null,
        messages: {
            forbidden:
                "ES5 trailing commas in array/object literals are forbidden.",
        },
        schema: [],
        type: "problem",
    },
    create(context) {
        const sourceCode = getSourceCode(context)
        return {
            "ArrayExpression, ArrayPattern, ObjectExpression, ObjectPattern"(
                node,
            ) {
                const token = sourceCode.getLastToken(node, 1)
                if (isCommaToken(token)) {
                    context.report({ node, messageId: "forbidden" })
                }
            },
        }
    },
}
