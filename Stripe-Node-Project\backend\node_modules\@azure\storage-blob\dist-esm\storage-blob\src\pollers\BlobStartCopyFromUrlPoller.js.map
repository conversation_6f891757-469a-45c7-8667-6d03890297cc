{"version": 3, "file": "BlobStartCopyFromUrlPoller.js", "sourceRoot": "", "sources": ["../../../../src/pollers/BlobStartCopyFromUrlPoller.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEzC,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AA0EzC;;;;;GAKG;AACH,MAAM,OAAO,0BAA2B,SAAQ,MAG/C;IAGC,YAAY,OAA0C;QACpD,MAAM,EACJ,UAAU,EACV,UAAU,EACV,YAAY,GAAG,KAAK,EACpB,UAAU,EACV,UAAU,EACV,uBAAuB,GACxB,GAAG,OAAO,CAAC;QAEZ,IAAI,KAAgD,CAAC;QAErD,IAAI,UAAU,EAAE,CAAC;YACf,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;QACvC,CAAC;QAED,MAAM,SAAS,GAAG,qCAAqC,iCAClD,KAAK,KACR,UAAU;YACV,UAAU;YACV,uBAAuB,IACvB,CAAC;QAEH,KAAK,CAAC,SAAS,CAAC,CAAC;QAEjB,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAEM,KAAK;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClC,CAAC;CACF;AAED;;;;;GAKG;AACH,MAAM,MAAM,GAAgD,KAAK,UAAU,MAAM,CAE/E,OAAO,GAAG,EAAE;IAEZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,MAAM,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;IACzB,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;QACtB,OAAO,qCAAqC,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QACzB,OAAO,qCAAqC,CAAC,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,sFAAsF;IACtF,MAAM,KAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,EAAE;QAC9C,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,CAAC,CAAC;IACH,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;IAEzB,OAAO,qCAAqC,CAAC,KAAK,CAAC,CAAC;AACtD,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,MAAM,GAAgD,KAAK,UAAU,MAAM,CAE/E,OAAO,GAAG,EAAE;IAEZ,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACzB,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,uBAAuB,EAAE,GAAG,KAAK,CAAC;IAElE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;QACrB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,uBAAuB,CAAC,CAAC;QAEtF,4BAA4B;QAC5B,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAC7B,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACpC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;YACtB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;SAAM,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QAC9B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;YAC1F,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;YAC5C,MAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC;YAC5C,IAAI,YAAY,EAAE,CAAC;gBACjB,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;YACpC,CAAC;YACD,IACE,UAAU,KAAK,SAAS;gBACxB,YAAY,KAAK,gBAAgB;gBACjC,OAAO,OAAO,CAAC,YAAY,KAAK,UAAU,EAC1C,CAAC;gBACD,2CAA2C;gBAC3C,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC9B,CAAC;iBAAM,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;gBACpC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;gBACtB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,CAAC;iBAAM,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACnC,KAAK,CAAC,KAAK,GAAG,IAAI,KAAK,CACrB,kCAAkC,MAAM,CAAC,qBAAqB,IAAI,SAAS,GAAG,CAC/E,CAAC;gBACF,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;YAC3B,CAAC;QACH,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;YAClB,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,OAAO,qCAAqC,CAAC,KAAK,CAAC,CAAC;AACtD,CAAC,CAAC;AAEF;;;;;GAKG;AACH,MAAM,QAAQ,GAAkD,SAAS,QAAQ;IAG/E,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;QAC1D,2FAA2F;QAC3F,IAAI,GAAG,KAAK,YAAY,EAAE,CAAC;YACzB,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;GAGG;AACH,SAAS,qCAAqC,CAC5C,KAAoC;IAEpC,OAAO;QACL,KAAK,oBAAO,KAAK,CAAE;QACnB,MAAM;QACN,QAAQ;QACR,MAAM;KACP,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { delay } from \"@azure/core-util\";\nimport type { PollOperation, PollOperationState } from \"@azure/core-lro\";\nimport { Poller } from \"@azure/core-lro\";\nimport type {\n  BlobClient,\n  BlobStartCopyFromURLOptions,\n  BlobBeginCopyFromURLResponse,\n} from \"../Clients\";\n\n/**\n * Defines the operations from a {@link BlobClient} that are needed for the poller\n * returned by {@link BlobClient.beginCopyFromURL} to work.\n */\nexport type CopyPollerBlobClient = Pick<BlobClient, \"abortCopyFromURL\" | \"getProperties\"> & {\n  startCopyFromURL(\n    copySource: string,\n    options?: BlobStartCopyFromURLOptions,\n  ): Promise<BlobBeginCopyFromURLResponse>;\n};\n\n/**\n * The state used by the poller returned from {@link BlobClient.beginCopyFromURL}.\n *\n * This state is passed into the user-specified `onProgress` callback\n * whenever copy progress is detected.\n */\nexport interface BlobBeginCopyFromUrlPollState\n  extends PollOperationState<BlobBeginCopyFromURLResponse> {\n  /**\n   * The instance of {@link BlobClient} that was used when calling {@link BlobClient.beginCopyFromURL}.\n   */\n  readonly blobClient: CopyPollerBlobClient;\n  /**\n   * The copyId that identifies the in-progress blob copy.\n   */\n  copyId?: string;\n  /**\n   * the progress of the blob copy as reported by the service.\n   */\n  copyProgress?: string;\n  /**\n   * The source URL provided in {@link BlobClient.beginCopyFromURL}.\n   */\n  copySource: string;\n  /**\n   * The options that were passed to the initial {@link BlobClient.beginCopyFromURL} call.\n   * This is exposed for the poller and should not be modified directly.\n   */\n  readonly startCopyFromURLOptions?: BlobStartCopyFromURLOptions;\n}\n\n/**\n * The PollOperation responsible for:\n *  - performing the initial startCopyFromURL\n *  - checking the copy status via getProperties\n *  - cancellation via abortCopyFromURL\n * @hidden\n */\nexport interface BlobBeginCopyFromURLPollOperation\n  extends PollOperation<BlobBeginCopyFromUrlPollState, BlobBeginCopyFromURLResponse> {}\n\n/**\n * The set of options used to configure the poller.\n * This is an internal interface populated by {@link BlobClient.beginCopyFromURL}.\n *\n * @hidden\n */\nexport interface BlobBeginCopyFromUrlPollerOptions {\n  blobClient: CopyPollerBlobClient;\n  copySource: string;\n  intervalInMs?: number;\n  onProgress?: (state: BlobBeginCopyFromUrlPollState) => void;\n  resumeFrom?: string;\n  startCopyFromURLOptions?: BlobStartCopyFromURLOptions;\n}\n\n/**\n * This is the poller returned by {@link BlobClient.beginCopyFromURL}.\n * This can not be instantiated directly outside of this package.\n *\n * @hidden\n */\nexport class BlobBeginCopyFromUrlPoller extends Poller<\n  BlobBeginCopyFromUrlPollState,\n  BlobBeginCopyFromURLResponse\n> {\n  public intervalInMs: number;\n\n  constructor(options: BlobBeginCopyFromUrlPollerOptions) {\n    const {\n      blobClient,\n      copySource,\n      intervalInMs = 15000,\n      onProgress,\n      resumeFrom,\n      startCopyFromURLOptions,\n    } = options;\n\n    let state: BlobBeginCopyFromUrlPollState | undefined;\n\n    if (resumeFrom) {\n      state = JSON.parse(resumeFrom).state;\n    }\n\n    const operation = makeBlobBeginCopyFromURLPollOperation({\n      ...state,\n      blobClient,\n      copySource,\n      startCopyFromURLOptions,\n    });\n\n    super(operation);\n\n    if (typeof onProgress === \"function\") {\n      this.onProgress(onProgress);\n    }\n\n    this.intervalInMs = intervalInMs;\n  }\n\n  public delay(): Promise<void> {\n    return delay(this.intervalInMs);\n  }\n}\n\n/**\n * Note: Intentionally using function expression over arrow function expression\n * so that the function can be invoked with a different context.\n * This affects what `this` refers to.\n * @hidden\n */\nconst cancel: BlobBeginCopyFromURLPollOperation[\"cancel\"] = async function cancel(\n  this: BlobBeginCopyFromURLPollOperation,\n  options = {},\n) {\n  const state = this.state;\n  const { copyId } = state;\n  if (state.isCompleted) {\n    return makeBlobBeginCopyFromURLPollOperation(state);\n  }\n\n  if (!copyId) {\n    state.isCancelled = true;\n    return makeBlobBeginCopyFromURLPollOperation(state);\n  }\n\n  // if abortCopyFromURL throws, it will bubble up to user's poller.cancelOperation call\n  await state.blobClient.abortCopyFromURL(copyId, {\n    abortSignal: options.abortSignal,\n  });\n  state.isCancelled = true;\n\n  return makeBlobBeginCopyFromURLPollOperation(state);\n};\n\n/**\n * Note: Intentionally using function expression over arrow function expression\n * so that the function can be invoked with a different context.\n * This affects what `this` refers to.\n * @hidden\n */\nconst update: BlobBeginCopyFromURLPollOperation[\"update\"] = async function update(\n  this: BlobBeginCopyFromURLPollOperation,\n  options = {},\n): Promise<BlobBeginCopyFromURLPollOperation> {\n  const state = this.state;\n  const { blobClient, copySource, startCopyFromURLOptions } = state;\n\n  if (!state.isStarted) {\n    state.isStarted = true;\n    const result = await blobClient.startCopyFromURL(copySource, startCopyFromURLOptions);\n\n    // copyId is needed to abort\n    state.copyId = result.copyId;\n    if (result.copyStatus === \"success\") {\n      state.result = result;\n      state.isCompleted = true;\n    }\n  } else if (!state.isCompleted) {\n    try {\n      const result = await state.blobClient.getProperties({ abortSignal: options.abortSignal });\n      const { copyStatus, copyProgress } = result;\n      const prevCopyProgress = state.copyProgress;\n      if (copyProgress) {\n        state.copyProgress = copyProgress;\n      }\n      if (\n        copyStatus === \"pending\" &&\n        copyProgress !== prevCopyProgress &&\n        typeof options.fireProgress === \"function\"\n      ) {\n        // trigger in setTimeout, or swallow error?\n        options.fireProgress(state);\n      } else if (copyStatus === \"success\") {\n        state.result = result;\n        state.isCompleted = true;\n      } else if (copyStatus === \"failed\") {\n        state.error = new Error(\n          `Blob copy failed with reason: \"${result.copyStatusDescription || \"unknown\"}\"`,\n        );\n        state.isCompleted = true;\n      }\n    } catch (err: any) {\n      state.error = err;\n      state.isCompleted = true;\n    }\n  }\n\n  return makeBlobBeginCopyFromURLPollOperation(state);\n};\n\n/**\n * Note: Intentionally using function expression over arrow function expression\n * so that the function can be invoked with a different context.\n * This affects what `this` refers to.\n * @hidden\n */\nconst toString: BlobBeginCopyFromURLPollOperation[\"toString\"] = function toString(\n  this: BlobBeginCopyFromURLPollOperation,\n) {\n  return JSON.stringify({ state: this.state }, (key, value) => {\n    // remove blobClient from serialized state since a client can't be hydrated from this info.\n    if (key === \"blobClient\") {\n      return undefined;\n    }\n    return value;\n  });\n};\n\n/**\n * Creates a poll operation given the provided state.\n * @hidden\n */\nfunction makeBlobBeginCopyFromURLPollOperation(\n  state: BlobBeginCopyFromUrlPollState,\n): BlobBeginCopyFromURLPollOperation {\n  return {\n    state: { ...state },\n    cancel,\n    toString,\n    update,\n  };\n}\n"]}