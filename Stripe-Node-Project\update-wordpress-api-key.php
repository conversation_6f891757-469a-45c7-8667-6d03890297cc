<?php
/**
 * Update WordPress API Key Script
 * Run this from the WordPress directory to update the API key
 */

// Define WordPress path
define('WP_USE_THEMES', false);
require_once('wp-config.php');
require_once('wp-load.php');

// New API key from the backend script
$new_api_key = 'sk_api_0cf81e725444e5aa7f2d3a5e77768affab627c57bc61efab710c002939dcdd0f';

echo "🔧 Updating WordPress API Key...\n";

// Update the API key in WordPress options
$updated = update_option('stripe_node_api_key', $new_api_key);

if ($updated) {
    echo "✅ API key updated successfully!\n";
} else {
    echo "ℹ️  API key was already set to this value or update failed.\n";
}

// Verify the update
$stored_key = get_option('stripe_node_api_key');
echo "📋 Current stored API key: " . substr($stored_key, 0, 20) . "...\n";

// Test if the keys match
if ($stored_key === $new_api_key) {
    echo "✅ API key verification successful!\n";
} else {
    echo "❌ API key verification failed!\n";
    echo "Expected: " . substr($new_api_key, 0, 20) . "...\n";
    echo "Got: " . substr($stored_key, 0, 20) . "...\n";
}

echo "\n🔄 Please refresh the test payment page to use the new API key.\n";
?>
