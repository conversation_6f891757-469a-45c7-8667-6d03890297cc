{"version": 3, "file": "parameters.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/models/parameters.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAOH,OAAO,EACL,sBAAsB,IAAI,4BAA4B,EACtD,YAAY,IAAI,kBAAkB,EACnC,MAAM,mBAAmB,CAAC;AAE3B,MAAM,CAAC,MAAM,WAAW,GAAuB;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAAuB;IAC5C,aAAa,EAAE,YAAY;IAC3B,MAAM,EAAE,4BAA4B;CACrC,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAAuB;IACxC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,QAAQ;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,GAAG,GAA0B;IACxC,aAAa,EAAE,KAAK;IACpB,MAAM,EAAE;QACN,cAAc,EAAE,KAAK;QACrB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,KAAK;QACd,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;IACD,YAAY,EAAE,IAAI;CACnB,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA4B;IAC9C,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,YAAY,EAAE,SAAS;QACvB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,SAAS;QACzB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,IAAI,GAA4B;IAC3C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA4B;IACvD,aAAa,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;IAC9C,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,SAAS;QACzB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,SAAS;IACxB,MAAM,EAAE;QACN,YAAY,EAAE,YAAY;QAC1B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,cAAc;QAC9B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAAuB;IAC3C,aAAa,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;IACvC,MAAM,EAAE;QACN,cAAc,EAAE,wBAAwB;QACxC,OAAO,EAAE,wBAAwB;QACjC,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAAuB;IACzC,aAAa,EAAE,QAAQ;IACvB,MAAM,EAAE;QACN,YAAY,EAAE,iBAAiB;QAC/B,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,QAAQ;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,OAAO;QACrB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACpC,MAAM,EAAE;QACN,cAAc,EAAE,QAAQ;QACxB,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,MAAM,GAA4B;IAC7C,aAAa,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC;IACpC,MAAM,EAAE;QACN,cAAc,EAAE,QAAQ;QACxB,OAAO,EAAE,QAAQ;QACjB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA4B;IAClD,aAAa,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;IACzC,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,YAAY;QAC5B,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA4B;IAC9C,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IACrC,MAAM,EAAE;QACN,cAAc,EAAE,SAAS;QACzB,OAAO,EAAE,SAAS;QAClB,cAAc,EAAE,uBAAuB;QACvC,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,YAAY,EAAE,UAAU;gBACxB,UAAU,EAAE,IAAI;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;IACD,gBAAgB,EAAE,KAAK;CACxB,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,MAAM,EAAE;QACN,cAAc,EAAE,WAAW;QAC3B,OAAO,EAAE,WAAW;QACpB,sBAAsB,EAAE,YAAY;QACpC,IAAI,EAAE;YACJ,IAAI,EAAE,YAAY;YAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;SACpC;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,UAAU;QACxB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA4B;IAC5C,aAAa,EAAE,MAAM;IACrB,MAAM,EAAE;QACN,YAAY,EAAE,KAAK;QACnB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,MAAM;QACtB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAuB;IAC1C,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,MAAM,EAAE;QACN,cAAc,EAAE,UAAU;QAC1B,OAAO,EAAE,mBAAmB;QAC5B,YAAY,EAAE,IAAI;QAClB,cAAc,EAAE,kBAAkB;QAClC,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,kBAAkB;iBAC9B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA4B;IACvD,aAAa,EAAE,CAAC,SAAS,EAAE,kBAAkB,CAAC;IAC9C,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,eAAe;QAC/B,OAAO,EAAE,eAAe;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA4B;IACxD,aAAa,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC;IAC/C,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,MAAM;YACxB,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,mBAAmB;QACnC,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAuB;IAC9C,aAAa,EAAE,cAAc;IAC7B,MAAM,EAAE,kBAAkB;CAC3B,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA4B;IACxD,aAAa,EAAE,CAAC,SAAS,EAAE,mBAAmB,CAAC;IAC/C,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,CAAC,CAAC;SACrB;QACD,cAAc,EAAE,YAAY;QAC5B,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA4B;IAC/C,aAAa,EAAE,UAAU;IACzB,MAAM,EAAE;QACN,YAAY,EAAE,MAAM;QACpB,UAAU,EAAE,IAAI;QAChB,cAAc,EAAE,UAAU;QAC1B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAuB;IAC/C,aAAa,EAAE,CAAC,SAAS,EAAE,cAAc,CAAC;IAC1C,MAAM,EAAE,kBAAkB;CAC3B,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA4B;IACjD,aAAa,EAAE,YAAY;IAC3B,MAAM,EAAE;QACN,cAAc,EAAE,YAAY;QAC5B,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,YAAY;QACrB,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA4B;IACzD,aAAa,EAAE,mBAAmB;IAClC,MAAM,EAAE;QACN,WAAW,EAAE;YACX,gBAAgB,EAAE,MAAM;YACxB,gBAAgB,EAAE,CAAC;SACpB;QACD,cAAc,EAAE,mBAAmB;QACnC,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  OperationParameter,\n  OperationURLParameter,\n  OperationQueryParameter\n} from \"@azure/core-client\";\nimport {\n  QueueServiceProperties as QueueServicePropertiesMapper,\n  QueueMessage as QueueMessageMapper\n} from \"../models/mappers\";\n\nexport const contentType: OperationParameter = {\n  parameterPath: [\"options\", \"contentType\"],\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Content-Type\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const properties: OperationParameter = {\n  parameterPath: \"properties\",\n  mapper: QueueServicePropertiesMapper\n};\n\nexport const accept: OperationParameter = {\n  parameterPath: \"accept\",\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Accept\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const url: OperationURLParameter = {\n  parameterPath: \"url\",\n  mapper: {\n    serializedName: \"url\",\n    required: true,\n    xmlName: \"url\",\n    type: {\n      name: \"String\"\n    }\n  },\n  skipEncoding: true\n};\n\nexport const restype: OperationQueryParameter = {\n  parameterPath: \"restype\",\n  mapper: {\n    defaultValue: \"service\",\n    isConstant: true,\n    serializedName: \"restype\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const comp: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"properties\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const timeoutInSeconds: OperationQueryParameter = {\n  parameterPath: [\"options\", \"timeoutInSeconds\"],\n  mapper: {\n    constraints: {\n      InclusiveMinimum: 0\n    },\n    serializedName: \"timeout\",\n    xmlName: \"timeout\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const version: OperationParameter = {\n  parameterPath: \"version\",\n  mapper: {\n    defaultValue: \"2025-05-05\",\n    isConstant: true,\n    serializedName: \"x-ms-version\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const requestId: OperationParameter = {\n  parameterPath: [\"options\", \"requestId\"],\n  mapper: {\n    serializedName: \"x-ms-client-request-id\",\n    xmlName: \"x-ms-client-request-id\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const accept1: OperationParameter = {\n  parameterPath: \"accept\",\n  mapper: {\n    defaultValue: \"application/xml\",\n    isConstant: true,\n    serializedName: \"Accept\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const comp1: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"stats\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const comp2: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"list\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const prefix: OperationQueryParameter = {\n  parameterPath: [\"options\", \"prefix\"],\n  mapper: {\n    serializedName: \"prefix\",\n    xmlName: \"prefix\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const marker: OperationQueryParameter = {\n  parameterPath: [\"options\", \"marker\"],\n  mapper: {\n    serializedName: \"marker\",\n    xmlName: \"marker\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const maxPageSize: OperationQueryParameter = {\n  parameterPath: [\"options\", \"maxPageSize\"],\n  mapper: {\n    constraints: {\n      InclusiveMinimum: 1\n    },\n    serializedName: \"maxresults\",\n    xmlName: \"maxresults\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const include: OperationQueryParameter = {\n  parameterPath: [\"options\", \"include\"],\n  mapper: {\n    serializedName: \"include\",\n    xmlName: \"include\",\n    xmlElementName: \"ListQueuesIncludeType\",\n    type: {\n      name: \"Sequence\",\n      element: {\n        defaultValue: \"metadata\",\n        isConstant: true,\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  },\n  collectionFormat: \"CSV\"\n};\n\nexport const metadata: OperationParameter = {\n  parameterPath: [\"options\", \"metadata\"],\n  mapper: {\n    serializedName: \"x-ms-meta\",\n    xmlName: \"x-ms-meta\",\n    headerCollectionPrefix: \"x-ms-meta-\",\n    type: {\n      name: \"Dictionary\",\n      value: { type: { name: \"String\" } }\n    }\n  }\n};\n\nexport const comp3: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"metadata\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const comp4: OperationQueryParameter = {\n  parameterPath: \"comp\",\n  mapper: {\n    defaultValue: \"acl\",\n    isConstant: true,\n    serializedName: \"comp\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const queueAcl: OperationParameter = {\n  parameterPath: [\"options\", \"queueAcl\"],\n  mapper: {\n    serializedName: \"queueAcl\",\n    xmlName: \"SignedIdentifiers\",\n    xmlIsWrapped: true,\n    xmlElementName: \"SignedIdentifier\",\n    type: {\n      name: \"Sequence\",\n      element: {\n        type: {\n          name: \"Composite\",\n          className: \"SignedIdentifier\"\n        }\n      }\n    }\n  }\n};\n\nexport const numberOfMessages: OperationQueryParameter = {\n  parameterPath: [\"options\", \"numberOfMessages\"],\n  mapper: {\n    constraints: {\n      InclusiveMinimum: 1\n    },\n    serializedName: \"numofmessages\",\n    xmlName: \"numofmessages\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const visibilityTimeout: OperationQueryParameter = {\n  parameterPath: [\"options\", \"visibilityTimeout\"],\n  mapper: {\n    constraints: {\n      InclusiveMaximum: 604800,\n      InclusiveMinimum: 0\n    },\n    serializedName: \"visibilitytimeout\",\n    xmlName: \"visibilitytimeout\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const queueMessage: OperationParameter = {\n  parameterPath: \"queueMessage\",\n  mapper: QueueMessageMapper\n};\n\nexport const messageTimeToLive: OperationQueryParameter = {\n  parameterPath: [\"options\", \"messageTimeToLive\"],\n  mapper: {\n    constraints: {\n      InclusiveMinimum: -1\n    },\n    serializedName: \"messagettl\",\n    xmlName: \"messagettl\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n\nexport const peekonly: OperationQueryParameter = {\n  parameterPath: \"peekonly\",\n  mapper: {\n    defaultValue: \"true\",\n    isConstant: true,\n    serializedName: \"peekonly\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const queueMessage1: OperationParameter = {\n  parameterPath: [\"options\", \"queueMessage\"],\n  mapper: QueueMessageMapper\n};\n\nexport const popReceipt: OperationQueryParameter = {\n  parameterPath: \"popReceipt\",\n  mapper: {\n    serializedName: \"popreceipt\",\n    required: true,\n    xmlName: \"popreceipt\",\n    type: {\n      name: \"String\"\n    }\n  }\n};\n\nexport const visibilityTimeout1: OperationQueryParameter = {\n  parameterPath: \"visibilityTimeout\",\n  mapper: {\n    constraints: {\n      InclusiveMaximum: 604800,\n      InclusiveMinimum: 0\n    },\n    serializedName: \"visibilitytimeout\",\n    required: true,\n    xmlName: \"visibilitytimeout\",\n    type: {\n      name: \"Number\"\n    }\n  }\n};\n"]}