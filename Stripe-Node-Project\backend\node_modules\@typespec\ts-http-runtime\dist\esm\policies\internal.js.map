{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/policies/internal.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAChE,OAAO,EACL,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,+BAA+B,CAAC;AACvC,OAAO,EACL,kBAAkB,EAClB,sBAAsB,GAEvB,MAAM,yBAAyB,CAAC;AACjC,OAAO,EACL,sBAAsB,EACtB,0BAA0B,GAE3B,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,WAAW,EAAsB,MAAM,kBAAkB,CAAC;AAMnE,OAAO,EAAE,sBAAsB,EAAE,0BAA0B,EAAE,MAAM,6BAA6B,CAAC;AACjG,OAAO,EAAE,qBAAqB,EAAE,yBAAyB,EAAE,MAAM,4BAA4B,CAAC;AAC9F,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AACzE,OAAO,EAAE,SAAS,EAAE,aAAa,EAAoB,MAAM,gBAAgB,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,sBAAsB,CAAC;AAC5E,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,kBAAkB,CAAC;AACzF,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAyB,MAAM,qBAAqB,CAAC;AAChG,OAAO,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAA0B,MAAM,sBAAsB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { agentPolicy, agentPolicyName } from \"./agentPolicy.js\";\nexport {\n  decompressResponsePolicy,\n  decompressResponsePolicyName,\n} from \"./decompressResponsePolicy.js\";\nexport {\n  defaultRetryPolicy,\n  defaultRetryPolicyName,\n  DefaultRetryPolicyOptions,\n} from \"./defaultRetryPolicy.js\";\nexport {\n  exponentialRetryPolicy,\n  exponentialRetryPolicyName,\n  ExponentialRetryPolicyOptions,\n} from \"./exponentialRetryPolicy.js\";\nexport { retryPolicy, RetryPolicyOptions } from \"./retryPolicy.js\";\nexport {\n  RetryInformation,\n  RetryModifiers,\n  RetryStrategy,\n} from \"../retryStrategies/retryStrategy.js\";\nexport { systemErrorRetryPolicy, systemErrorRetryPolicyName } from \"./systemErrorRetryPolicy.js\";\nexport { throttlingRetryPolicy, throttlingRetryPolicyName } from \"./throttlingRetryPolicy.js\";\nexport { formDataPolicy, formDataPolicyName } from \"./formDataPolicy.js\";\nexport { logPolicy, logPolicyName, LogPolicyOptions } from \"./logPolicy.js\";\nexport { multipartPolicy, multipartPolicyName } from \"./multipartPolicy.js\";\nexport { proxyPolicy, proxyPolicyName, getDefaultProxySettings } from \"./proxyPolicy.js\";\nexport { redirectPolicy, redirectPolicyName, RedirectPolicyOptions } from \"./redirectPolicy.js\";\nexport { tlsPolicy, tlsPolicyName } from \"./tlsPolicy.js\";\nexport { userAgentPolicy, userAgentPolicyName, UserAgentPolicyOptions } from \"./userAgentPolicy.js\";\n"]}