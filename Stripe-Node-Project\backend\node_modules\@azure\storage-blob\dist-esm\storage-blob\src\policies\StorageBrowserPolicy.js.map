{"version": 3, "file": "StorageBrowserPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageBrowserPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AACpD,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAExD;;;;;;;;;;GAUG;AACH,MAAM,OAAO,oBAAqB,SAAQ,iBAAiB;IACzD;;;;OAIG;IACH,wGAAwG;IACxG,uEAAuE;IACvE,YAAY,UAAyB,EAAE,OAA6B;QAClE,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,WAAW,CAAC,OAAoB;QAC3C,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE,CAAC;YACtF,OAAO,CAAC,GAAG,GAAG,eAAe,CAC3B,OAAO,CAAC,GAAG,EACX,YAAY,CAAC,UAAU,CAAC,sBAAsB,EAC9C,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAChC,CAAC;QACJ,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAE/C,oFAAoF;QACpF,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;QAEvD,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n  WebResourceLike as WebResource,\n  CompatResponse as HttpOperationResponse,\n} from \"@azure/core-http-compat\";\nimport { BaseRequestPolicy } from \"./RequestPolicy\";\nimport { isNode } from \"@azure/core-util\";\n\nimport { HeaderConstants, URLConstants } from \"../utils/constants\";\nimport { setURLParameter } from \"../utils/utils.common\";\n\n/**\n * StorageBrowserPolicy will handle differences between Node.js and browser runtime, including:\n *\n * 1. Browsers cache GET/HEAD requests by adding conditional headers such as 'IF_MODIFIED_SINCE'.\n * StorageBrowserPolicy is a policy used to add a timestamp query to GET/HEAD request URL\n * thus avoid the browser cache.\n *\n * 2. Remove cookie header for security\n *\n * 3. Remove content-length header to avoid browsers warning\n */\nexport class StorageBrowserPolicy extends BaseRequestPolicy {\n  /**\n   * Creates an instance of StorageBrowserPolicy.\n   * @param nextPolicy -\n   * @param options -\n   */\n  // The base class has a protected constructor. Adding a public one to enable constructing of this class.\n  /* eslint-disable-next-line @typescript-eslint/no-useless-constructor*/\n  constructor(nextPolicy: RequestPolicy, options: RequestPolicyOptions) {\n    super(nextPolicy, options);\n  }\n\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   */\n  public async sendRequest(request: WebResource): Promise<HttpOperationResponse> {\n    if (isNode) {\n      return this._nextPolicy.sendRequest(request);\n    }\n\n    if (request.method.toUpperCase() === \"GET\" || request.method.toUpperCase() === \"HEAD\") {\n      request.url = setURLParameter(\n        request.url,\n        URLConstants.Parameters.FORCE_BROWSER_NO_CACHE,\n        new Date().getTime().toString(),\n      );\n    }\n\n    request.headers.remove(HeaderConstants.COOKIE);\n\n    // According to XHR standards, content-length should be fully controlled by browsers\n    request.headers.remove(HeaderConstants.CONTENT_LENGTH);\n\n    return this._nextPolicy.sendRequest(request);\n  }\n}\n"]}