{"version": 3, "file": "service.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/service.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAcnD,2CAA2C;AAC3C,MAAM,OAAO,WAAW;IAGtB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,aAAa,CACX,UAAkC,EAClC,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,UAAU,EAAE,OAAO,EAAE,EACvB,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,aAAa,CACX,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,iBAAiB,CACf,OAAgD;QAEhD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,8BAA8B,CAC/B,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,UAAU;IAClC,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;KAC5B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,sBAAsB;YAC1C,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;KAC5B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,sBAAsB;YAC1C,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,8BAA8B,GAA6B;IAC/D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,yBAAyB;YAC7C,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,wCAAwC;SAChE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,OAAO;KACnB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { Service } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  QueueServiceProperties,\n  ServiceSetPropertiesOptionalParams,\n  ServiceSetPropertiesResponse,\n  ServiceGetPropertiesOptionalParams,\n  ServiceGetPropertiesResponse,\n  ServiceGetStatisticsOptionalParams,\n  ServiceGetStatisticsResponse,\n  ServiceListQueuesSegmentOptionalParams,\n  ServiceListQueuesSegmentResponse\n} from \"../models\";\n\n/** Class containing Service operations. */\nexport class ServiceImpl implements Service {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class Service class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * Sets properties for a storage account's Queue service endpoint, including properties for Storage\n   * Analytics and CORS (Cross-Origin Resource Sharing) rules\n   * @param properties The StorageService properties.\n   * @param options The options parameters.\n   */\n  setProperties(\n    properties: QueueServiceProperties,\n    options?: ServiceSetPropertiesOptionalParams\n  ): Promise<ServiceSetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { properties, options },\n      setPropertiesOperationSpec\n    );\n  }\n\n  /**\n   * gets the properties of a storage account's Queue service, including properties for Storage Analytics\n   * and CORS (Cross-Origin Resource Sharing) rules.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: ServiceGetPropertiesOptionalParams\n  ): Promise<ServiceGetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPropertiesOperationSpec\n    );\n  }\n\n  /**\n   * Retrieves statistics related to replication for the Queue service. It is only available on the\n   * secondary location endpoint when read-access geo-redundant replication is enabled for the storage\n   * account.\n   * @param options The options parameters.\n   */\n  getStatistics(\n    options?: ServiceGetStatisticsOptionalParams\n  ): Promise<ServiceGetStatisticsResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getStatisticsOperationSpec\n    );\n  }\n\n  /**\n   * The List Queues Segment operation returns a list of the queues under the specified account\n   * @param options The options parameters.\n   */\n  listQueuesSegment(\n    options?: ServiceListQueuesSegmentOptionalParams\n  ): Promise<ServiceListQueuesSegmentResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      listQueuesSegmentOperationSpec\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst setPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.ServiceSetPropertiesHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceSetPropertiesExceptionHeaders\n    }\n  },\n  requestBody: Parameters.properties,\n  queryParameters: [\n    Parameters.restype,\n    Parameters.comp,\n    Parameters.timeoutInSeconds\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer\n};\nconst getPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.QueueServiceProperties,\n      headersMapper: Mappers.ServiceGetPropertiesHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceGetPropertiesExceptionHeaders\n    }\n  },\n  queryParameters: [\n    Parameters.restype,\n    Parameters.comp,\n    Parameters.timeoutInSeconds\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst getStatisticsOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.QueueServiceStatistics,\n      headersMapper: Mappers.ServiceGetStatisticsHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceGetStatisticsExceptionHeaders\n    }\n  },\n  queryParameters: [\n    Parameters.restype,\n    Parameters.timeoutInSeconds,\n    Parameters.comp1\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst listQueuesSegmentOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.ListQueuesSegmentResponse,\n      headersMapper: Mappers.ServiceListQueuesSegmentHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceListQueuesSegmentExceptionHeaders\n    }\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp2,\n    Parameters.prefix,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.include\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\n"]}