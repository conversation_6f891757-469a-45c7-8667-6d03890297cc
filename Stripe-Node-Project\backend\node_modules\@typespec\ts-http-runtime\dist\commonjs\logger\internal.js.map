{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/logger/internal.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,yCAIqB;AAHnB,gHAAA,mBAAmB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport {\n  createLoggerContext,\n  type CreateLoggerContextOptions,\n  type LoggerContext,\n} from \"./logger.js\";\n"]}