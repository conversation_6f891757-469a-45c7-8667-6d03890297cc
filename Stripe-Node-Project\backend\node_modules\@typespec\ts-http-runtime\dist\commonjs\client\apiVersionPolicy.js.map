{"version": 3, "file": "apiVersionPolicy.js", "sourceRoot": "", "sources": ["../../../src/client/apiVersionPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAYlC,4CAgBC;AAvBY,QAAA,oBAAoB,GAAG,kBAAkB,CAAC;AAEvD;;;;GAIG;AACH,SAAgB,gBAAgB,CAAC,OAAsB;IACrD,OAAO;QACL,IAAI,EAAE,4BAAoB;QAC1B,WAAW,EAAE,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACzB,oDAAoD;YACpD,wEAAwE;YACxE,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC7B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC/D,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAClB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GACzD,eAAe,OAAO,CAAC,UAAU,EAAE,CAAC;YACtC,CAAC;YAED,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { ClientOptions } from \"./common.js\";\n\nexport const apiVersionPolicyName = \"ApiVersionPolicy\";\n\n/**\n * Creates a policy that sets the apiVersion as a query parameter on every request\n * @param options - Client options\n * @returns Pipeline policy that sets the apiVersion as a query parameter on every request\n */\nexport function apiVersionPolicy(options: ClientOptions): PipelinePolicy {\n  return {\n    name: apiVersionPolicyName,\n    sendRequest: (req, next) => {\n      // Use the apiVesion defined in request url directly\n      // Append one if there is no apiVesion and we have one at client options\n      const url = new URL(req.url);\n      if (!url.searchParams.get(\"api-version\") && options.apiVersion) {\n        req.url = `${req.url}${\n          Array.from(url.searchParams.keys()).length > 0 ? \"&\" : \"?\"\n        }api-version=${options.apiVersion}`;\n      }\n\n      return next(req);\n    },\n  };\n}\n"]}