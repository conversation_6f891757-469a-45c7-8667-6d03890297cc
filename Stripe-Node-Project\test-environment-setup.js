#!/usr/bin/env node

/**
 * Testing Environment Setup and Validation
 * This script sets up and validates the complete testing environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Setting Up Testing Environment');
console.log('==================================\n');

// Environment validation
function validateEnvironment() {
    console.log('📋 Validating Environment...');
    
    const checks = [
        {
            name: 'Node.js version',
            check: () => {
                const version = process.version;
                const major = parseInt(version.slice(1).split('.')[0]);
                return major >= 16;
            },
            fix: 'Install Node.js 16+ from https://nodejs.org'
        },
        {
            name: 'Backend directory',
            check: () => fs.existsSync(path.join(__dirname, 'backend')),
            fix: 'Ensure you are in the project root directory'
        },
        {
            name: 'WordPress plugin directory',
            check: () => fs.existsSync(path.join(__dirname, 'wordpress-plugin')),
            fix: 'Ensure WordPress plugin directory exists'
        },
        {
            name: 'Package.json',
            check: () => fs.existsSync(path.join(__dirname, 'backend', 'package.json')),
            fix: 'Run npm init in backend directory'
        }
    ];

    let allPassed = true;
    checks.forEach(check => {
        const passed = check.check();
        console.log(`   ${passed ? '✅' : '❌'} ${check.name}`);
        if (!passed) {
            console.log(`      Fix: ${check.fix}`);
            allPassed = false;
        }
    });

    return allPassed;
}

// Install dependencies
function installDependencies() {
    console.log('\n📦 Installing Dependencies...');
    
    try {
        process.chdir(path.join(__dirname, 'backend'));
        console.log('   Installing backend dependencies...');
        execSync('npm install', { stdio: 'pipe' });
        console.log('   ✅ Backend dependencies installed');
        
        // Install testing dependencies
        const testDeps = ['jest', 'supertest', 'artillery'];
        testDeps.forEach(dep => {
            try {
                execSync(`npm list ${dep}`, { stdio: 'pipe' });
                console.log(`   ✅ ${dep} already installed`);
            } catch {
                console.log(`   📦 Installing ${dep}...`);
                execSync(`npm install --save-dev ${dep}`, { stdio: 'pipe' });
                console.log(`   ✅ ${dep} installed`);
            }
        });
        
        process.chdir(__dirname);
        return true;
    } catch (error) {
        console.log(`   ❌ Error installing dependencies: ${error.message}`);
        return false;
    }
}

// Setup environment file
function setupEnvironment() {
    console.log('\n⚙️ Setting Up Environment...');
    
    const envPath = path.join(__dirname, 'backend', '.env');
    
    if (fs.existsSync(envPath)) {
        console.log('   ✅ .env file already exists');
        
        // Validate required variables
        const envContent = fs.readFileSync(envPath, 'utf8');
        const requiredVars = [
            'SUPABASE_URL',
            'SUPABASE_SERVICE_ROLE_KEY', 
            'STRIPE_SECRET_KEY',
            'JWT_SECRET'
        ];
        
        const missingVars = requiredVars.filter(varName => 
            !envContent.includes(`${varName}=`) || envContent.includes(`${varName}=your_`)
        );
        
        if (missingVars.length > 0) {
            console.log('   ⚠️  Missing configuration for:');
            missingVars.forEach(varName => {
                console.log(`      - ${varName}`);
            });
            console.log('   Please update .env file with your actual credentials');
        } else {
            console.log('   ✅ All required environment variables configured');
        }
    } else {
        console.log('   📝 Creating .env template...');
        
        const envTemplate = `# Environment
NODE_ENV=development
PORT=3000

# Database (Replace with your Supabase credentials)
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe (Replace with your Stripe test keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Security (Generate secure random strings)
JWT_SECRET=${generateRandomString()}
API_SECRET_KEY=${generateRandomString()}
WEBHOOK_SECRET=${generateRandomString()}

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Optional Security
ENABLE_IP_WHITELIST=false
ENABLE_SIGNATURE_VALIDATION=true
SECURITY_WEBHOOK_URL=
`;
        
        fs.writeFileSync(envPath, envTemplate);
        console.log('   ✅ .env template created');
        console.log('   ⚠️  Please edit .env file with your actual credentials');
    }
}

// Generate random string for secrets
function generateRandomString() {
    return require('crypto').randomBytes(32).toString('base64');
}

// Create test data
function createTestData() {
    console.log('\n🎯 Creating Test Data...');
    
    const testDataPath = path.join(__dirname, 'backend', 'tests', 'test-data.json');
    const testData = {
        vendors: [
            {
                id: 'test-vendor-restaurant',
                name: 'Test Restaurant',
                email: '<EMAIL>',
                niche: 'restaurant',
                commission_rate: 0.03
            },
            {
                id: 'test-vendor-grocery',
                name: 'Test Grocery Store',
                email: '<EMAIL>',
                niche: 'grocery',
                commission_rate: 0.025
            }
        ],
        payments: [
            {
                amount: 2500,
                currency: 'usd',
                vendor_id: 'test-vendor-restaurant',
                order_id: 'test-order-001',
                customer_email: '<EMAIL>'
            },
            {
                amount: 5000,
                currency: 'usd',
                vendor_id: 'test-vendor-grocery',
                order_id: 'test-order-002',
                customer_email: '<EMAIL>'
            }
        ],
        stripe_test_cards: {
            success: '****************',
            declined: '****************',
            insufficient_funds: '****************',
            expired: '****************'
        }
    };
    
    // Ensure tests directory exists
    const testsDir = path.dirname(testDataPath);
    if (!fs.existsSync(testsDir)) {
        fs.mkdirSync(testsDir, { recursive: true });
    }
    
    fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2));
    console.log('   ✅ Test data created');
}

// Validate database connection
function validateDatabase() {
    console.log('\n🗄️ Validating Database Connection...');
    
    const envPath = path.join(__dirname, 'backend', '.env');
    if (!fs.existsSync(envPath)) {
        console.log('   ❌ .env file not found');
        return false;
    }
    
    const envContent = fs.readFileSync(envPath, 'utf8');
    const hasSupabaseUrl = envContent.includes('SUPABASE_URL=') && !envContent.includes('SUPABASE_URL=your_');
    const hasSupabaseKey = envContent.includes('SUPABASE_SERVICE_ROLE_KEY=') && !envContent.includes('SUPABASE_SERVICE_ROLE_KEY=your_');
    
    if (hasSupabaseUrl && hasSupabaseKey) {
        console.log('   ✅ Database credentials configured');
        console.log('   ℹ️  Run the server to test actual connection');
    } else {
        console.log('   ⚠️  Database credentials need configuration');
        console.log('   Please update SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in .env');
    }
    
    return hasSupabaseUrl && hasSupabaseKey;
}

// Main setup function
async function setupTestingEnvironment() {
    console.log('Starting testing environment setup...\n');
    
    // Step 1: Validate environment
    if (!validateEnvironment()) {
        console.log('\n❌ Environment validation failed. Please fix the issues above.');
        process.exit(1);
    }
    
    // Step 2: Install dependencies
    if (!installDependencies()) {
        console.log('\n❌ Failed to install dependencies.');
        process.exit(1);
    }
    
    // Step 3: Setup environment
    setupEnvironment();
    
    // Step 4: Create test data
    createTestData();
    
    // Step 5: Validate database
    validateDatabase();
    
    console.log('\n🎉 Testing Environment Setup Complete!');
    console.log('\nNext Steps:');
    console.log('1. Edit backend/.env with your actual credentials');
    console.log('2. Run: cd backend && npm start');
    console.log('3. Test: curl http://localhost:3000/health');
    console.log('4. Run tests: npm test');
    console.log('\nSee TESTING_GUIDE.md for detailed testing instructions.');
}

// Run setup
setupTestingEnvironment().catch(console.error);
