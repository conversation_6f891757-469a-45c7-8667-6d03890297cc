{"version": 3, "file": "ContainerSASPermissions.js", "sourceRoot": "", "sources": ["../../../../src/sas/ContainerSASPermissions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;GAMG;AACH,MAAM,OAAO,uBAAuB;IAApC;QA6GE;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,UAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,kBAAa,GAAY,KAAK,CAAC;QAEtC;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;QAEhC;;WAEG;QACI,0BAAqB,GAAY,KAAK,CAAC;QAE9C;;WAEG;QACI,oBAAe,GAAY,KAAK,CAAC;QAExC;;WAEG;QACI,iBAAY,GAAY,KAAK,CAAC;IAqDvC,CAAC;IAhOC;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,WAAmB;QACrC,MAAM,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAE9D,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,GAAG;oBACN,uBAAuB,CAAC,IAAI,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,GAAG,GAAG,IAAI,CAAC;oBACnC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACtC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC;oBACrC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACtC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,IAAI,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,GAAG,GAAG,IAAI,CAAC;oBACnC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC7C,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,IAAI,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvC,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,qBAAqB,GAAG,IAAI,CAAC;oBACrD,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC/C,MAAM;gBACR,KAAK,GAAG;oBACN,uBAAuB,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC5C,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,sBAAsB,IAAI,EAAE,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAED,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAI,CAAC,cAA2C;QAC5D,MAAM,uBAAuB,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAC9D,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,uBAAuB,CAAC,IAAI,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC;YACvB,uBAAuB,CAAC,GAAG,GAAG,IAAI,CAAC;QACrC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC;QACxC,CAAC;QACD,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;YACzB,uBAAuB,CAAC,KAAK,GAAG,IAAI,CAAC;QACvC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC;QACxC,CAAC;QACD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,uBAAuB,CAAC,IAAI,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YACjC,uBAAuB,CAAC,aAAa,GAAG,IAAI,CAAC;QAC/C,CAAC;QACD,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC;YACvB,uBAAuB,CAAC,GAAG,GAAG,IAAI,CAAC;QACrC,CAAC;QACD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,uBAAuB,CAAC,IAAI,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;QACzC,CAAC;QACD,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACzC,uBAAuB,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACvD,CAAC;QACD,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;YACnC,uBAAuB,CAAC,eAAe,GAAG,IAAI,CAAC;QACjD,CAAC;QACD,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;YAChC,uBAAuB,CAAC,YAAY,GAAG,IAAI,CAAC;QAC9C,CAAC;QACD,OAAO,uBAAuB,CAAC;IACjC,CAAC;IAmED;;;;;;;OAOG;IACI,QAAQ;QACb,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * This is a helper class to construct a string representing the permissions granted by a ServiceSAS to a container.\n * Setting a value to true means that any SAS which uses these permissions will grant permissions for that operation.\n * Once all the values are set, this should be serialized with toString and set as the permissions field on a\n * {@link BlobSASSignatureValues} object. It is possible to construct the permissions string without this class, but\n * the order of the permissions is particular and this class guarantees correctness.\n */\nexport class ContainerSASPermissions {\n  /**\n   * Creates an {@link ContainerSASPermissions} from the specified permissions string. This method will throw an\n   * Error if it encounters a character that does not correspond to a valid permission.\n   *\n   * @param permissions -\n   */\n  public static parse(permissions: string): ContainerSASPermissions {\n    const containerSASPermissions = new ContainerSASPermissions();\n\n    for (const char of permissions) {\n      switch (char) {\n        case \"r\":\n          containerSASPermissions.read = true;\n          break;\n        case \"a\":\n          containerSASPermissions.add = true;\n          break;\n        case \"c\":\n          containerSASPermissions.create = true;\n          break;\n        case \"w\":\n          containerSASPermissions.write = true;\n          break;\n        case \"d\":\n          containerSASPermissions.delete = true;\n          break;\n        case \"l\":\n          containerSASPermissions.list = true;\n          break;\n        case \"t\":\n          containerSASPermissions.tag = true;\n          break;\n        case \"x\":\n          containerSASPermissions.deleteVersion = true;\n          break;\n        case \"m\":\n          containerSASPermissions.move = true;\n          break;\n        case \"e\":\n          containerSASPermissions.execute = true;\n          break;\n        case \"i\":\n          containerSASPermissions.setImmutabilityPolicy = true;\n          break;\n        case \"y\":\n          containerSASPermissions.permanentDelete = true;\n          break;\n        case \"f\":\n          containerSASPermissions.filterByTags = true;\n          break;\n        default:\n          throw new RangeError(`Invalid permission ${char}`);\n      }\n    }\n\n    return containerSASPermissions;\n  }\n\n  /**\n   * Creates a {@link ContainerSASPermissions} from a raw object which contains same keys as it\n   * and boolean values for them.\n   *\n   * @param permissionLike -\n   */\n  public static from(permissionLike: ContainerSASPermissionsLike): ContainerSASPermissions {\n    const containerSASPermissions = new ContainerSASPermissions();\n    if (permissionLike.read) {\n      containerSASPermissions.read = true;\n    }\n    if (permissionLike.add) {\n      containerSASPermissions.add = true;\n    }\n    if (permissionLike.create) {\n      containerSASPermissions.create = true;\n    }\n    if (permissionLike.write) {\n      containerSASPermissions.write = true;\n    }\n    if (permissionLike.delete) {\n      containerSASPermissions.delete = true;\n    }\n    if (permissionLike.list) {\n      containerSASPermissions.list = true;\n    }\n    if (permissionLike.deleteVersion) {\n      containerSASPermissions.deleteVersion = true;\n    }\n    if (permissionLike.tag) {\n      containerSASPermissions.tag = true;\n    }\n    if (permissionLike.move) {\n      containerSASPermissions.move = true;\n    }\n    if (permissionLike.execute) {\n      containerSASPermissions.execute = true;\n    }\n    if (permissionLike.setImmutabilityPolicy) {\n      containerSASPermissions.setImmutabilityPolicy = true;\n    }\n    if (permissionLike.permanentDelete) {\n      containerSASPermissions.permanentDelete = true;\n    }\n    if (permissionLike.filterByTags) {\n      containerSASPermissions.filterByTags = true;\n    }\n    return containerSASPermissions;\n  }\n\n  /**\n   * Specifies Read access granted.\n   */\n  public read: boolean = false;\n\n  /**\n   * Specifies Add access granted.\n   */\n  public add: boolean = false;\n\n  /**\n   * Specifies Create access granted.\n   */\n  public create: boolean = false;\n\n  /**\n   * Specifies Write access granted.\n   */\n  public write: boolean = false;\n\n  /**\n   * Specifies Delete access granted.\n   */\n  public delete: boolean = false;\n\n  /**\n   * Specifies Delete version access granted.\n   */\n  public deleteVersion: boolean = false;\n\n  /**\n   * Specifies List access granted.\n   */\n  public list: boolean = false;\n\n  /**\n   * Specfies Tag access granted.\n   */\n  public tag: boolean = false;\n\n  /**\n   * Specifies Move access granted.\n   */\n  public move: boolean = false;\n\n  /**\n   * Specifies Execute access granted.\n   */\n  public execute: boolean = false;\n\n  /**\n   * Specifies SetImmutabilityPolicy access granted.\n   */\n  public setImmutabilityPolicy: boolean = false;\n\n  /**\n   * Specifies that Permanent Delete is permitted.\n   */\n  public permanentDelete: boolean = false;\n\n  /**\n   * Specifies that Filter Blobs by Tags is permitted.\n   */\n  public filterByTags: boolean = false;\n\n  /**\n   * Converts the given permissions to a string. Using this method will guarantee the permissions are in an\n   * order accepted by the service.\n   *\n   * The order of the characters should be as specified here to ensure correctness.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   */\n  public toString(): string {\n    const permissions: string[] = [];\n    if (this.read) {\n      permissions.push(\"r\");\n    }\n    if (this.add) {\n      permissions.push(\"a\");\n    }\n    if (this.create) {\n      permissions.push(\"c\");\n    }\n    if (this.write) {\n      permissions.push(\"w\");\n    }\n    if (this.delete) {\n      permissions.push(\"d\");\n    }\n    if (this.deleteVersion) {\n      permissions.push(\"x\");\n    }\n    if (this.list) {\n      permissions.push(\"l\");\n    }\n    if (this.tag) {\n      permissions.push(\"t\");\n    }\n    if (this.move) {\n      permissions.push(\"m\");\n    }\n    if (this.execute) {\n      permissions.push(\"e\");\n    }\n    if (this.setImmutabilityPolicy) {\n      permissions.push(\"i\");\n    }\n    if (this.permanentDelete) {\n      permissions.push(\"y\");\n    }\n    if (this.filterByTags) {\n      permissions.push(\"f\");\n    }\n    return permissions.join(\"\");\n  }\n}\n\n/**\n * A type that looks like a Container SAS permission.\n * Used in {@link ContainerSASPermissions} to parse SAS permissions from raw objects.\n */\nexport interface ContainerSASPermissionsLike {\n  /**\n   * Specifies Read access granted.\n   */\n  read?: boolean;\n\n  /**\n   * Specifies Add access granted.\n   */\n  add?: boolean;\n\n  /**\n   * Specifies Create access granted.\n   */\n  create?: boolean;\n\n  /**\n   * Specifies Write access granted.\n   */\n  write?: boolean;\n\n  /**\n   * Specifies Delete access granted.\n   */\n  delete?: boolean;\n\n  /**\n   * Specifies Delete version access granted.\n   */\n  deleteVersion?: boolean;\n\n  /**\n   * Specifies List access granted.\n   */\n  list?: boolean;\n\n  /**\n   * Specfies Tag access granted.\n   */\n  tag?: boolean;\n\n  /**\n   * Specifies Move access granted.\n   */\n  move?: boolean;\n\n  /**\n   * Specifies Execute access granted.\n   */\n  execute?: boolean;\n\n  /**\n   * Specifies SetImmutabilityPolicy access granted.\n   */\n  setImmutabilityPolicy?: boolean;\n\n  /**\n   * Specifies that Permanent Delete is permitted.\n   */\n  permanentDelete?: boolean;\n\n  /**\n   * Specifies that Filter Blobs by Tags is permitted.\n   */\n  filterByTags?: boolean;\n}\n"]}