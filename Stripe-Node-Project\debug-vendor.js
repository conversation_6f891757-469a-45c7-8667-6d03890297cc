const { supabase, supabaseAdmin } = require('./src/config/database');

async function debugVendor() {
    console.log('🔍 Debugging vendor lookup...');
    
    const vendorId = '4b65134b-88f1-40a8-951b-694876338cd8';
    
    console.log('\n1. Testing with regular supabase client:');
    try {
        const { data: vendor1, error: error1 } = await supabase
            .from('vendors')
            .select('*')
            .eq('id', vendorId)
            .single();
        
        console.log('   Data:', vendor1);
        console.log('   Error:', error1);
    } catch (err) {
        console.log('   Exception:', err.message);
    }
    
    console.log('\n2. Testing with supabaseAdmin client:');
    try {
        const { data: vendor2, error: error2 } = await supabaseAdmin
            .from('vendors')
            .select('*')
            .eq('id', vendorId)
            .single();
        
        console.log('   Data:', vendor2);
        console.log('   Error:', error2);
    } catch (err) {
        console.log('   Exception:', err.message);
    }
    
    console.log('\n3. Testing with commission_rates join:');
    try {
        const { data: vendor3, error: error3 } = await supabaseAdmin
            .from('vendors')
            .select('*, commission_rates(*)')
            .eq('id', vendorId)
            .single();
        
        console.log('   Data:', vendor3);
        console.log('   Error:', error3);
    } catch (err) {
        console.log('   Exception:', err.message);
    }
    
    console.log('\n4. List all vendors:');
    try {
        const { data: allVendors, error: error4 } = await supabaseAdmin
            .from('vendors')
            .select('*');
        
        console.log('   Count:', allVendors?.length || 0);
        console.log('   Vendors:', allVendors);
        console.log('   Error:', error4);
    } catch (err) {
        console.log('   Exception:', err.message);
    }
}

debugVendor().catch(console.error);
