{"version": 3, "file": "AvroReadableFromStream.js", "sourceRoot": "", "sources": ["../../../../storage-internal-avro/src/AvroReadableFromStream.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAErD,MAAM,WAAW,GAAG,IAAI,UAAU,CAAC,2CAA2C,CAAC,CAAC;AAEhF,MAAM,OAAO,sBAAuB,SAAQ,YAAY;IAI9C,YAAY,CAAC,IAAqB;QACxC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,QAA+B;QACzC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;IACrB,CAAC;IACD,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IACM,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,UAAmC,EAAE;;QACnE,IAAI,MAAA,OAAO,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;YACjC,MAAM,WAAW,CAAC;QACpB,CAAC;QAED,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,OAAO,IAAI,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,uCAAuC;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,CAAC;YAC/B,gEAAgE;YAChE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,oDAAoD;YACpD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,4DAA4D;gBAC5D,MAAM,OAAO,GAAe,GAAG,EAAE;oBAC/B,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;oBAC5D,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;oBACvD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;oBACrD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;oBAEvD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;wBACxB,OAAO,CAAC,WAAY,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBAClE,CAAC;gBACH,CAAC,CAAC;gBAEF,MAAM,gBAAgB,GAAe,GAAG,EAAE;oBACxC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAChD,IAAI,aAAa,EAAE,CAAC;wBAClB,IAAI,CAAC,SAAS,IAAI,aAAa,CAAC,MAAM,CAAC;wBACvC,OAAO,EAAE,CAAC;wBACV,wEAAwE;wBACxE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC;oBAC5C,CAAC;gBACH,CAAC,CAAC;gBAEF,MAAM,cAAc,GAAe,GAAG,EAAE;oBACtC,OAAO,EAAE,CAAC;oBACV,MAAM,EAAE,CAAC;gBACX,CAAC,CAAC;gBAEF,MAAM,YAAY,GAAe,GAAG,EAAE;oBACpC,OAAO,EAAE,CAAC;oBACV,MAAM,CAAC,WAAW,CAAC,CAAC;gBACtB,CAAC,CAAC;gBAEF,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;gBAChD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;gBAC7C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;gBAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;gBAC7C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;oBACxB,OAAO,CAAC,WAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;gBAC/D,CAAC;gBACD,2DAA2D;YAC7D,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AvroReadableReadOptions } from \"./AvroReadable\";\nimport { AvroReadable } from \"./AvroReadable\";\nimport { AbortError } from \"@azure/abort-controller\";\n\nconst ABORT_ERROR = new AbortError(\"Reading from the avro stream was aborted.\");\n\nexport class AvroReadableFromStream extends AvroReadable {\n  private _position: number;\n  private _readable: NodeJS.ReadableStream;\n\n  private toUint8Array(data: string | Buffer): Uint8Array {\n    if (typeof data === \"string\") {\n      return Buffer.from(data);\n    }\n    return data;\n  }\n\n  constructor(readable: NodeJS.ReadableStream) {\n    super();\n    this._readable = readable;\n    this._position = 0;\n  }\n  public get position(): number {\n    return this._position;\n  }\n  public async read(size: number, options: AvroReadableReadOptions = {}): Promise<Uint8Array> {\n    if (options.abortSignal?.aborted) {\n      throw ABORT_ERROR;\n    }\n\n    if (size < 0) {\n      throw new Error(`size parameter should be positive: ${size}`);\n    }\n\n    if (size === 0) {\n      return new Uint8Array();\n    }\n\n    if (!this._readable.readable) {\n      throw new Error(\"Stream no longer readable.\");\n    }\n    // See if there is already enough data.\n    const chunk = this._readable.read(size);\n    if (chunk) {\n      this._position += chunk.length;\n      // chunk.length maybe less than desired size if the stream ends.\n      return this.toUint8Array(chunk);\n    } else {\n      // register callback to wait for enough data to read\n      return new Promise((resolve, reject) => {\n        /* eslint-disable @typescript-eslint/no-use-before-define */\n        const cleanUp: () => void = () => {\n          this._readable.removeListener(\"readable\", readableCallback);\n          this._readable.removeListener(\"error\", rejectCallback);\n          this._readable.removeListener(\"end\", rejectCallback);\n          this._readable.removeListener(\"close\", rejectCallback);\n\n          if (options.abortSignal) {\n            options.abortSignal!.removeEventListener(\"abort\", abortHandler);\n          }\n        };\n\n        const readableCallback: () => void = () => {\n          const callbackChunk = this._readable.read(size);\n          if (callbackChunk) {\n            this._position += callbackChunk.length;\n            cleanUp();\n            // callbackChunk.length maybe less than desired size if the stream ends.\n            resolve(this.toUint8Array(callbackChunk));\n          }\n        };\n\n        const rejectCallback: () => void = () => {\n          cleanUp();\n          reject();\n        };\n\n        const abortHandler: () => void = () => {\n          cleanUp();\n          reject(ABORT_ERROR);\n        };\n\n        this._readable.on(\"readable\", readableCallback);\n        this._readable.once(\"error\", rejectCallback);\n        this._readable.once(\"end\", rejectCallback);\n        this._readable.once(\"close\", rejectCallback);\n        if (options.abortSignal) {\n          options.abortSignal!.addEventListener(\"abort\", abortHandler);\n        }\n        /* eslint-enable @typescript-eslint/no-use-before-define */\n      });\n    }\n  }\n}\n"]}