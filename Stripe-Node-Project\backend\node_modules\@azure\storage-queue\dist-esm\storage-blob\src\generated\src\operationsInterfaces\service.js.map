{"version": 3, "file": "service.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/operationsInterfaces/service.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport {\n  BlobServiceProperties,\n  ServiceSetPropertiesOptionalParams,\n  ServiceSetPropertiesResponse,\n  ServiceGetPropertiesOptionalParams,\n  ServiceGetPropertiesResponse,\n  ServiceGetStatisticsOptionalParams,\n  ServiceGetStatisticsResponse,\n  ServiceListContainersSegmentOptionalParams,\n  ServiceListContainersSegmentResponse,\n  KeyInfo,\n  ServiceGetUserDelegationKeyOptionalParams,\n  ServiceGetUserDelegationKeyResponse,\n  ServiceGetAccountInfoOptionalParams,\n  ServiceGetAccountInfoResponse,\n  ServiceSubmitBatchOptionalParams,\n  ServiceSubmitBatchResponse,\n  ServiceFilterBlobsOptionalParams,\n  ServiceFilterBlobsResponse,\n} from \"../models\";\n\n/** Interface representing a Service. */\nexport interface Service {\n  /**\n   * Sets properties for a storage account's Blob service endpoint, including properties for Storage\n   * Analytics and CORS (Cross-Origin Resource Sharing) rules\n   * @param blobServiceProperties The StorageService properties.\n   * @param options The options parameters.\n   */\n  setProperties(\n    blobServiceProperties: BlobServiceProperties,\n    options?: ServiceSetPropertiesOptionalParams,\n  ): Promise<ServiceSetPropertiesResponse>;\n  /**\n   * gets the properties of a storage account's Blob service, including properties for Storage Analytics\n   * and CORS (Cross-Origin Resource Sharing) rules.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: ServiceGetPropertiesOptionalParams,\n  ): Promise<ServiceGetPropertiesResponse>;\n  /**\n   * Retrieves statistics related to replication for the Blob service. It is only available on the\n   * secondary location endpoint when read-access geo-redundant replication is enabled for the storage\n   * account.\n   * @param options The options parameters.\n   */\n  getStatistics(\n    options?: ServiceGetStatisticsOptionalParams,\n  ): Promise<ServiceGetStatisticsResponse>;\n  /**\n   * The List Containers Segment operation returns a list of the containers under the specified account\n   * @param options The options parameters.\n   */\n  listContainersSegment(\n    options?: ServiceListContainersSegmentOptionalParams,\n  ): Promise<ServiceListContainersSegmentResponse>;\n  /**\n   * Retrieves a user delegation key for the Blob service. This is only a valid operation when using\n   * bearer token authentication.\n   * @param keyInfo Key information\n   * @param options The options parameters.\n   */\n  getUserDelegationKey(\n    keyInfo: KeyInfo,\n    options?: ServiceGetUserDelegationKeyOptionalParams,\n  ): Promise<ServiceGetUserDelegationKeyResponse>;\n  /**\n   * Returns the sku name and account kind\n   * @param options The options parameters.\n   */\n  getAccountInfo(\n    options?: ServiceGetAccountInfoOptionalParams,\n  ): Promise<ServiceGetAccountInfoResponse>;\n  /**\n   * The Batch operation allows multiple API calls to be embedded into a single HTTP request.\n   * @param contentLength The length of the request.\n   * @param multipartContentType Required. The value of this header must be multipart/mixed with a batch\n   *                             boundary. Example header value: multipart/mixed; boundary=batch_<GUID>\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  submitBatch(\n    contentLength: number,\n    multipartContentType: string,\n    body: coreRestPipeline.RequestBodyType,\n    options?: ServiceSubmitBatchOptionalParams,\n  ): Promise<ServiceSubmitBatchResponse>;\n  /**\n   * The Filter Blobs operation enables callers to list blobs across all containers whose tags match a\n   * given search expression.  Filter blobs searches across all containers within a storage account but\n   * can be scoped within the expression to a single container.\n   * @param options The options parameters.\n   */\n  filterBlobs(\n    options?: ServiceFilterBlobsOptionalParams,\n  ): Promise<ServiceFilterBlobsResponse>;\n}\n"]}