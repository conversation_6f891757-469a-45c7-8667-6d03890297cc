{"version": 3, "file": "BlobSASSignatureValues.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/sas/BlobSASSignatureValues.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAE1D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,0BAA0B,EAAE,MAAM,2CAA2C,CAAC;AACvF,OAAO,EAAE,2BAA2B,EAAE,MAAM,4CAA4C,CAAC;AAEzF,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AA4O7D,MAAM,UAAU,8BAA8B,CAC5C,sBAA8C,EAC9C,sCAAsF,EACtF,WAAoB;IAEpB,OAAO,sCAAsC,CAC3C,sBAAsB,EACtB,sCAAsC,EACtC,WAAW,CACZ,CAAC,kBAAkB,CAAC;AACvB,CAAC;AAED,MAAM,UAAU,sCAAsC,CACpD,sBAA8C,EAC9C,sCAAsF,EACtF,WAAoB;IAEpB,MAAM,OAAO,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IAElG,MAAM,mBAAmB,GACvB,sCAAsC,YAAY,0BAA0B;QAC1E,CAAC,CAAC,sCAAsC;QACxC,CAAC,CAAC,SAAS,CAAC;IAChB,IAAI,2BAAoE,CAAC;IAEzE,IAAI,mBAAmB,KAAK,SAAS,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;QACnE,2BAA2B,GAAG,IAAI,2BAA2B,CAC3D,WAAW,EACX,sCAA2D,CAC5D,CAAC;IACJ,CAAC;IAED,IAAI,mBAAmB,KAAK,SAAS,IAAI,2BAA2B,KAAK,SAAS,EAAE,CAAC;QACnF,MAAM,SAAS,CAAC,gEAAgE,CAAC,CAAC;IACpF,CAAC;IAED,8DAA8D;IAC9D,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;QAC5B,IAAI,mBAAmB,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,sCAAsC,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,OAAO,yCAAyC,CAC9C,sBAAsB,EACtB,2BAA4B,CAC7B,CAAC;QACJ,CAAC;IACH,CAAC;IAED,gEAAgE;IAChE,gGAAgG;IAChG,0HAA0H;IAC1H,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;QAC5B,IAAI,mBAAmB,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,sCAAsC,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,8HAA8H;YAC9H,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;gBAC5B,OAAO,yCAAyC,CAC9C,sBAAsB,EACtB,2BAA4B,CAC7B,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,yCAAyC,CAC9C,sBAAsB,EACtB,2BAA4B,CAC7B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;QAC5B,IAAI,mBAAmB,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,sCAAsC,CAAC,sBAAsB,EAAE,mBAAmB,CAAC,CAAC;QAC7F,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,UAAU,CAClB,kGAAkG,CACnG,CAAC;QACJ,CAAC;IACH,CAAC;IAED,MAAM,IAAI,UAAU,CAAC,oCAAoC,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,SAAS,sCAAsC,CAC7C,sBAA8C,EAC9C,mBAA+C;IAE/C,sBAAsB,GAAG,wCAAwC,CAAC,sBAAsB,CAAC,CAAC;IAE1F,IACE,CAAC,sBAAsB,CAAC,UAAU;QAClC,CAAC,CAAC,sBAAsB,CAAC,WAAW,IAAI,sBAAsB,CAAC,SAAS,CAAC,EACzE,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,uGAAuG,CACxG,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,GAAW,GAAG,CAAC;IAC3B,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACpC,QAAQ,GAAG,GAAG,CAAC;IACjB,CAAC;IAED,8FAA8F;IAC9F,IAAI,mBAAuC,CAAC;IAC5C,IAAI,sBAAsB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACpC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAC5C,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACjD,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,sBAAsB,CAAC,QAAQ;YAC7B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC9D,CAAC,CAAC,EAAE;QACN,sBAAsB,CAAC,SAAS;YAC9B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,gBAAgB,CACd,mBAAmB,CAAC,WAAW,EAC/B,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,QAAQ,CAChC;QACD,sBAAsB,CAAC,UAAU;QACjC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACrF,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtE,sBAAsB,CAAC,OAAO;QAC9B,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QAC9E,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;QAC1F,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;QACpF,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;QACpF,sBAAsB,CAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;KAC7E,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAEtE,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,sBAAsB,CAAC,OAAQ,EAC/B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,SAAS,EAChC,sBAAsB,CAAC,OAAO,EAC9B,sBAAsB,CAAC,UAAU,EACjC,QAAQ,EACR,sBAAsB,CAAC,YAAY,EACnC,sBAAsB,CAAC,kBAAkB,EACzC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,WAAW,CACnC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,SAAS,sCAAsC,CAC7C,sBAA8C,EAC9C,mBAA+C;IAE/C,sBAAsB,GAAG,wCAAwC,CAAC,sBAAsB,CAAC,CAAC;IAE1F,IACE,CAAC,sBAAsB,CAAC,UAAU;QAClC,CAAC,CAAC,sBAAsB,CAAC,WAAW,IAAI,sBAAsB,CAAC,SAAS,CAAC,EACzE,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,uGAAuG,CACxG,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,GAAW,GAAG,CAAC;IAC3B,IAAI,SAAS,GAAG,sBAAsB,CAAC,YAAY,CAAC;IACpD,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACpC,QAAQ,GAAG,GAAG,CAAC;QACf,IAAI,sBAAsB,CAAC,YAAY,EAAE,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,sBAAsB,CAAC,SAAS,EAAE,CAAC;YAC5C,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,8FAA8F;IAC9F,IAAI,mBAAuC,CAAC;IAC5C,IAAI,sBAAsB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACpC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAC5C,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACjD,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,sBAAsB,CAAC,QAAQ;YAC7B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC9D,CAAC,CAAC,EAAE;QACN,sBAAsB,CAAC,SAAS;YAC9B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,gBAAgB,CACd,mBAAmB,CAAC,WAAW,EAC/B,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,QAAQ,CAChC;QACD,sBAAsB,CAAC,UAAU;QACjC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACrF,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtE,sBAAsB,CAAC,OAAO;QAC9B,QAAQ;QACR,SAAS;QACT,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QAC9E,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;QAC1F,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;QACpF,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;QACpF,sBAAsB,CAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;KAC7E,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAEtE,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,sBAAsB,CAAC,OAAQ,EAC/B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,SAAS,EAChC,sBAAsB,CAAC,OAAO,EAC9B,sBAAsB,CAAC,UAAU,EACjC,QAAQ,EACR,sBAAsB,CAAC,YAAY,EACnC,sBAAsB,CAAC,kBAAkB,EACzC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,WAAW,CACnC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;GAeG;AACH,SAAS,sCAAsC,CAC7C,sBAA8C,EAC9C,mBAA+C;IAE/C,sBAAsB,GAAG,wCAAwC,CAAC,sBAAsB,CAAC,CAAC;IAE1F,IACE,CAAC,sBAAsB,CAAC,UAAU;QAClC,CAAC,CAAC,sBAAsB,CAAC,WAAW,IAAI,sBAAsB,CAAC,SAAS,CAAC,EACzE,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,uGAAuG,CACxG,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,GAAW,GAAG,CAAC;IAC3B,IAAI,SAAS,GAAG,sBAAsB,CAAC,YAAY,CAAC;IACpD,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACpC,QAAQ,GAAG,GAAG,CAAC;QACf,IAAI,sBAAsB,CAAC,YAAY,EAAE,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,sBAAsB,CAAC,SAAS,EAAE,CAAC;YAC5C,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,8FAA8F;IAC9F,IAAI,mBAAuC,CAAC;IAC5C,IAAI,sBAAsB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACpC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAC5C,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACjD,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,sBAAsB,CAAC,QAAQ;YAC7B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC9D,CAAC,CAAC,EAAE;QACN,sBAAsB,CAAC,SAAS;YAC9B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,gBAAgB,CACd,mBAAmB,CAAC,WAAW,EAC/B,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,QAAQ,CAChC;QACD,sBAAsB,CAAC,UAAU;QACjC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACrF,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtE,sBAAsB,CAAC,OAAO;QAC9B,QAAQ;QACR,SAAS;QACT,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;QAC9E,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;QAC1F,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;QACpF,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE;QACpF,sBAAsB,CAAC,WAAW,CAAC,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;KAC7E,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAEtE,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,sBAAsB,CAAC,OAAQ,EAC/B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,SAAS,EAChC,sBAAsB,CAAC,OAAO,EAC9B,sBAAsB,CAAC,UAAU,EACjC,QAAQ,EACR,sBAAsB,CAAC,YAAY,EACnC,sBAAsB,CAAC,kBAAkB,EACzC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,WAAW,EAClC,SAAS,EACT,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,eAAe,CACvC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAS,yCAAyC,CAChD,sBAA8C,EAC9C,2BAAwD;IAExD,sBAAsB,GAAG,wCAAwC,CAAC,sBAAsB,CAAC,CAAC;IAE1F,sEAAsE;IACtE,IAAI,CAAC,sBAAsB,CAAC,WAAW,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAC;QAC7E,MAAM,IAAI,UAAU,CAClB,yGAAyG,CAC1G,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,GAAW,GAAG,CAAC;IAC3B,IAAI,SAAS,GAAG,sBAAsB,CAAC,YAAY,CAAC;IACpD,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACpC,QAAQ,GAAG,GAAG,CAAC;QACf,IAAI,sBAAsB,CAAC,YAAY,EAAE,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,sBAAsB,CAAC,SAAS,EAAE,CAAC;YAC5C,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,8FAA8F;IAC9F,IAAI,mBAAuC,CAAC;IAC5C,IAAI,sBAAsB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACpC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAC5C,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACjD,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,sBAAsB,CAAC,QAAQ;YAC7B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC9D,CAAC,CAAC,EAAE;QACN,sBAAsB,CAAC,SAAS;YAC9B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,gBAAgB,CACd,2BAA2B,CAAC,WAAW,EACvC,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,QAAQ,CAChC;QACD,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;QAC5D,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;QAC5D,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;YAC1D,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC;YAC3F,CAAC,CAAC,EAAE;QACN,2BAA2B,CAAC,iBAAiB,CAAC,eAAe;YAC3D,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,eAAe,EAAE,KAAK,CAAC;YAC5F,CAAC,CAAC,EAAE;QACN,2BAA2B,CAAC,iBAAiB,CAAC,aAAa;QAC3D,2BAA2B,CAAC,iBAAiB,CAAC,aAAa;QAC3D,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACrF,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtE,sBAAsB,CAAC,OAAO;QAC9B,QAAQ;QACR,SAAS;QACT,sBAAsB,CAAC,YAAY;QACnC,sBAAsB,CAAC,kBAAkB;QACzC,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,WAAW;KACnC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,2BAA2B,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC9E,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,sBAAsB,CAAC,OAAQ,EAC/B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,SAAS,EAChC,sBAAsB,CAAC,OAAO,EAC9B,sBAAsB,CAAC,UAAU,EACjC,QAAQ,EACR,sBAAsB,CAAC,YAAY,EACnC,sBAAsB,CAAC,kBAAkB,EACzC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,WAAW,EAClC,2BAA2B,CAAC,iBAAiB,CAC9C;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAS,yCAAyC,CAChD,sBAA8C,EAC9C,2BAAwD;IAExD,sBAAsB,GAAG,wCAAwC,CAAC,sBAAsB,CAAC,CAAC;IAE1F,sEAAsE;IACtE,IAAI,CAAC,sBAAsB,CAAC,WAAW,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAC;QAC7E,MAAM,IAAI,UAAU,CAClB,yGAAyG,CAC1G,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,GAAW,GAAG,CAAC;IAC3B,IAAI,SAAS,GAAG,sBAAsB,CAAC,YAAY,CAAC;IACpD,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACpC,QAAQ,GAAG,GAAG,CAAC;QACf,IAAI,sBAAsB,CAAC,YAAY,EAAE,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,sBAAsB,CAAC,SAAS,EAAE,CAAC;YAC5C,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,8FAA8F;IAC9F,IAAI,mBAAuC,CAAC;IAC5C,IAAI,sBAAsB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACpC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAC5C,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACjD,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,sBAAsB,CAAC,QAAQ;YAC7B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC9D,CAAC,CAAC,EAAE;QACN,sBAAsB,CAAC,SAAS;YAC9B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,gBAAgB,CACd,2BAA2B,CAAC,WAAW,EACvC,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,QAAQ,CAChC;QACD,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;QAC5D,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;QAC5D,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;YAC1D,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC;YAC3F,CAAC,CAAC,EAAE;QACN,2BAA2B,CAAC,iBAAiB,CAAC,eAAe;YAC3D,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,eAAe,EAAE,KAAK,CAAC;YAC5F,CAAC,CAAC,EAAE;QACN,2BAA2B,CAAC,iBAAiB,CAAC,aAAa;QAC3D,2BAA2B,CAAC,iBAAiB,CAAC,aAAa;QAC3D,sBAAsB,CAAC,0BAA0B;QACjD,SAAS,EAAE,gBAAgB;QAC3B,sBAAsB,CAAC,aAAa;QACpC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACrF,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtE,sBAAsB,CAAC,OAAO;QAC9B,QAAQ;QACR,SAAS;QACT,sBAAsB,CAAC,YAAY;QACnC,sBAAsB,CAAC,kBAAkB;QACzC,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,WAAW;KACnC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,2BAA2B,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC9E,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,sBAAsB,CAAC,OAAQ,EAC/B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,SAAS,EAChC,sBAAsB,CAAC,OAAO,EAC9B,sBAAsB,CAAC,UAAU,EACjC,QAAQ,EACR,sBAAsB,CAAC,YAAY,EACnC,sBAAsB,CAAC,kBAAkB,EACzC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,WAAW,EAClC,2BAA2B,CAAC,iBAAiB,EAC7C,sBAAsB,CAAC,0BAA0B,EACjD,sBAAsB,CAAC,aAAa,CACrC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAS,yCAAyC,CAChD,sBAA8C,EAC9C,2BAAwD;IAExD,sBAAsB,GAAG,wCAAwC,CAAC,sBAAsB,CAAC,CAAC;IAE1F,sEAAsE;IACtE,IAAI,CAAC,sBAAsB,CAAC,WAAW,IAAI,CAAC,sBAAsB,CAAC,SAAS,EAAE,CAAC;QAC7E,MAAM,IAAI,UAAU,CAClB,yGAAyG,CAC1G,CAAC;IACJ,CAAC;IAED,IAAI,QAAQ,GAAW,GAAG,CAAC;IAC3B,IAAI,SAAS,GAAG,sBAAsB,CAAC,YAAY,CAAC;IACpD,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;QACpC,QAAQ,GAAG,GAAG,CAAC;QACf,IAAI,sBAAsB,CAAC,YAAY,EAAE,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC;QAClB,CAAC;aAAM,IAAI,sBAAsB,CAAC,SAAS,EAAE,CAAC;YAC5C,QAAQ,GAAG,IAAI,CAAC;YAChB,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,8FAA8F;IAC9F,IAAI,mBAAuC,CAAC;IAC5C,IAAI,sBAAsB,CAAC,WAAW,EAAE,CAAC;QACvC,IAAI,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACpC,mBAAmB,GAAG,kBAAkB,CAAC,KAAK,CAC5C,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACjD,sBAAsB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC9C,CAAC,QAAQ,EAAE,CAAC;QACf,CAAC;IACH,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,sBAAsB,CAAC,QAAQ;YAC7B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC9D,CAAC,CAAC,EAAE;QACN,sBAAsB,CAAC,SAAS;YAC9B,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,SAAS,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,gBAAgB,CACd,2BAA2B,CAAC,WAAW,EACvC,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,QAAQ,CAChC;QACD,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;QAC5D,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;QAC5D,2BAA2B,CAAC,iBAAiB,CAAC,cAAc;YAC1D,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,cAAc,EAAE,KAAK,CAAC;YAC3F,CAAC,CAAC,EAAE;QACN,2BAA2B,CAAC,iBAAiB,CAAC,eAAe;YAC3D,CAAC,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,iBAAiB,CAAC,eAAe,EAAE,KAAK,CAAC;YAC5F,CAAC,CAAC,EAAE;QACN,2BAA2B,CAAC,iBAAiB,CAAC,aAAa;QAC3D,2BAA2B,CAAC,iBAAiB,CAAC,aAAa;QAC3D,sBAAsB,CAAC,0BAA0B;QACjD,SAAS,EAAE,gBAAgB;QAC3B,sBAAsB,CAAC,aAAa;QACpC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACrF,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACtE,sBAAsB,CAAC,OAAO;QAC9B,QAAQ;QACR,SAAS;QACT,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,YAAY;QACnC,sBAAsB,CAAC,kBAAkB;QACzC,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,eAAe;QACtC,sBAAsB,CAAC,WAAW;KACnC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,2BAA2B,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAC9E,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,sBAAsB,CAAC,OAAQ,EAC/B,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,QAAQ,EAC/B,sBAAsB,CAAC,SAAS,EAChC,sBAAsB,CAAC,OAAO,EAC9B,sBAAsB,CAAC,UAAU,EACjC,QAAQ,EACR,sBAAsB,CAAC,YAAY,EACnC,sBAAsB,CAAC,kBAAkB,EACzC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,eAAe,EACtC,sBAAsB,CAAC,WAAW,EAClC,2BAA2B,CAAC,iBAAiB,EAC7C,sBAAsB,CAAC,0BAA0B,EACjD,sBAAsB,CAAC,aAAa,EACpC,sBAAsB,CAAC,eAAe,CACvC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,WAAmB,EAAE,aAAqB,EAAE,QAAiB;IACrF,2CAA2C;IAC3C,oDAAoD;IACpD,MAAM,QAAQ,GAAa,CAAC,SAAS,WAAW,IAAI,aAAa,EAAE,CAAC,CAAC;IACrE,IAAI,QAAQ,EAAE,CAAC;QACb,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC;IAChC,CAAC;IACD,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,wCAAwC,CAC/C,sBAA8C;IAE9C,MAAM,OAAO,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IAClG,IAAI,sBAAsB,CAAC,YAAY,IAAI,OAAO,GAAG,YAAY,EAAE,CAAC;QAClE,MAAM,UAAU,CAAC,kEAAkE,CAAC,CAAC;IACvF,CAAC;IACD,IAAI,sBAAsB,CAAC,QAAQ,KAAK,SAAS,IAAI,sBAAsB,CAAC,YAAY,EAAE,CAAC;QACzF,MAAM,UAAU,CAAC,wDAAwD,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,sBAAsB,CAAC,SAAS,IAAI,OAAO,GAAG,YAAY,EAAE,CAAC;QAC/D,MAAM,UAAU,CAAC,+DAA+D,CAAC,CAAC;IACpF,CAAC;IACD,IAAI,sBAAsB,CAAC,QAAQ,KAAK,SAAS,IAAI,sBAAsB,CAAC,SAAS,EAAE,CAAC;QACtF,MAAM,UAAU,CAAC,qDAAqD,CAAC,CAAC;IAC1E,CAAC;IAED,IACE,sBAAsB,CAAC,WAAW;QAClC,sBAAsB,CAAC,WAAW,CAAC,qBAAqB;QACxD,OAAO,GAAG,YAAY,EACtB,CAAC;QACD,MAAM,UAAU,CAAC,iEAAiE,CAAC,CAAC;IACtF,CAAC;IAED,IACE,sBAAsB,CAAC,WAAW;QAClC,sBAAsB,CAAC,WAAW,CAAC,aAAa;QAChD,OAAO,GAAG,YAAY,EACtB,CAAC;QACD,MAAM,UAAU,CAAC,kEAAkE,CAAC,CAAC;IACvF,CAAC;IAED,IACE,sBAAsB,CAAC,WAAW;QAClC,sBAAsB,CAAC,WAAW,CAAC,eAAe;QAClD,OAAO,GAAG,YAAY,EACtB,CAAC;QACD,MAAM,UAAU,CAAC,kEAAkE,CAAC,CAAC;IACvF,CAAC;IAED,IACE,sBAAsB,CAAC,WAAW;QAClC,sBAAsB,CAAC,WAAW,CAAC,GAAG;QACtC,OAAO,GAAG,YAAY,EACtB,CAAC;QACD,MAAM,UAAU,CAAC,kEAAkE,CAAC,CAAC;IACvF,CAAC;IAED,IACE,OAAO,GAAG,YAAY;QACtB,sBAAsB,CAAC,WAAW;QAClC,CAAC,sBAAsB,CAAC,WAAW,CAAC,IAAI,IAAI,sBAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,EACvF,CAAC;QACD,MAAM,UAAU,CAAC,6EAA6E,CAAC,CAAC;IAClG,CAAC;IAED,IACE,OAAO,GAAG,YAAY;QACtB,sBAAsB,CAAC,WAAW;QACjC,sBAAsB,CAAC,WAAuC,CAAC,YAAY,EAC5E,CAAC;QACD,MAAM,UAAU,CAAC,sEAAsE,CAAC,CAAC;IAC3F,CAAC;IAED,IACE,OAAO,GAAG,YAAY;QACtB,CAAC,sBAAsB,CAAC,0BAA0B,IAAI,sBAAsB,CAAC,aAAa,CAAC,EAC3F,CAAC;QACD,MAAM,UAAU,CACd,mGAAmG,CACpG,CAAC;IACJ,CAAC;IAED,IAAI,sBAAsB,CAAC,eAAe,IAAI,OAAO,GAAG,YAAY,EAAE,CAAC;QACrE,MAAM,UAAU,CAAC,2EAA2E,CAAC,CAAC;IAChG,CAAC;IAED,sBAAsB,CAAC,OAAO,GAAG,OAAO,CAAC;IACzC,OAAO,sBAAsB,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nimport { BlobSASPermissions } from \"./BlobSASPermissions\";\nimport type { UserDelegationKey } from \"../BlobServiceClient\";\nimport { ContainerSASPermissions } from \"./ContainerSASPermissions\";\nimport { StorageSharedKeyCredential } from \"../credentials/StorageSharedKeyCredential\";\nimport { UserDelegationKeyCredential } from \"../credentials/UserDelegationKeyCredential\";\nimport type { SasIPRange } from \"./SasIPRange\";\nimport { ipRangeToString } from \"./SasIPRange\";\nimport type { SASProtocol } from \"./SASQueryParameters\";\nimport { SASQueryParameters } from \"./SASQueryParameters\";\nimport { SERVICE_VERSION } from \"../utils/constants\";\nimport { truncatedISO8061Date } from \"../utils/utils.common\";\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * BlobSASSignatureValues is used to help generating Blob service SAS tokens for containers or blobs.\n */\nexport interface BlobSASSignatureValues {\n  /**\n   * The version of the service this SAS will target. If not specified, it will default to the version targeted by the\n   * library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols, HTTPS only or HTTPSandHTTP\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n\n  /**\n   * Optional only when identifier is provided. The time after which the SAS will no longer work.\n   */\n  expiresOn?: Date;\n\n  /**\n   * Optional only when identifier is provided.\n   * Please refer to either {@link ContainerSASPermissions} or {@link BlobSASPermissions} depending on the resource\n   * being accessed for help constructing the permissions string.\n   */\n  permissions?: BlobSASPermissions | ContainerSASPermissions;\n\n  /**\n   * Optional. IP ranges allowed in this SAS.\n   */\n  ipRange?: SasIPRange;\n\n  /**\n   * The name of the container the SAS user may access.\n   */\n  containerName: string;\n\n  /**\n   * Optional. The blob name of the SAS user may access. Required if snapshotTime or versionId is provided.\n   */\n  blobName?: string;\n\n  /**\n   * Optional. Snapshot timestamp string the SAS user may access. Only supported from API version 2018-11-09.\n   */\n  snapshotTime?: string;\n\n  /**\n   * Optional. VersionId of the blob version the SAS user may access. Only supported from API version 2019-10-10.\n   */\n  versionId?: string;\n\n  /**\n   * Optional. The name of the access policy on the container this SAS references if any.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  identifier?: string;\n\n  /**\n   * Optional. Encryption scope to use when sending requests authorized with this SAS URI.\n   */\n  encryptionScope?: string;\n\n  /**\n   * Optional. The cache-control header for the SAS.\n   */\n  cacheControl?: string;\n\n  /**\n   * Optional. The content-disposition header for the SAS.\n   */\n  contentDisposition?: string;\n\n  /**\n   * Optional. The content-encoding header for the SAS.\n   */\n  contentEncoding?: string;\n\n  /**\n   * Optional. The content-language header for the SAS.\n   */\n  contentLanguage?: string;\n\n  /**\n   * Optional. The content-type header for the SAS.\n   */\n  contentType?: string;\n\n  /**\n   * Optional. Beginning in version 2020-02-10, specifies the Authorized AAD Object ID in GUID format. The AAD Object ID of a user\n   * authorized by the owner of the user delegation key to perform the action granted by the SAS. The Azure Storage service will\n   * ensure that the owner of the user delegation key has the required permissions before granting access but no additional permission\n   * check for the user specified in this value will be performed. This is only used for User Delegation SAS.\n   */\n  preauthorizedAgentObjectId?: string;\n\n  /**\n   * Optional. Beginning in version 2020-02-10, this is a GUID value that will be logged in the storage diagnostic logs and can be used to\n   * correlate SAS generation with storage resource access. This is only used for User Delegation SAS.\n   */\n  correlationId?: string;\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn and identifier.\n *\n * WARNING: When identifier is not provided, permissions and expiresOn are required.\n * You MUST assign value to identifier or expiresOn & permissions manually if you initial with\n * this constructor.\n *\n * Fill in the required details before running the following snippets.\n *\n * Example usage:\n *\n * ```js\n * // Generate service level SAS for a container\n * const containerSAS = generateBlobSASQueryParameters({\n *     containerName, // Required\n *     permissions: ContainerSASPermissions.parse(\"racwdl\"), // Required\n *     startsOn: new Date(), // Optional\n *     expiresOn: new Date(new Date().valueOf() + 86400 * 1000), // Required. Date type\n *     ipRange: { start: \"0.0.0.0\", end: \"***************\" }, // Optional\n *     protocol: SASProtocol.HttpsAndHttp, // Optional\n *     version: \"2016-05-31\" // Optional\n *   },\n *   sharedKeyCredential // StorageSharedKeyCredential - `new StorageSharedKeyCredential(account, accountKey)`\n * ).toString();\n * ```\n *\n * Example using an identifier:\n *\n * ```js\n * // Generate service level SAS for a container with identifier\n * // startsOn & permissions are optional when identifier is provided\n * const identifier = \"unique-id\";\n * await containerClient.setAccessPolicy(undefined, [\n *   {\n *     accessPolicy: {\n *       expiresOn: new Date(new Date().valueOf() + 86400 * 1000), // Date type\n *       permissions: ContainerSASPermissions.parse(\"racwdl\").toString(),\n *       startsOn: new Date() // Date type\n *     },\n *     id: identifier\n *   }\n * ]);\n *\n * const containerSAS = generateBlobSASQueryParameters(\n *   {\n *     containerName, // Required\n *     identifier // Required\n *   },\n *   sharedKeyCredential // StorageSharedKeyCredential - `new StorageSharedKeyCredential(account, accountKey)`\n * ).toString();\n * ```\n *\n * Example using a blob name:\n *\n * ```js\n * // Generate service level SAS for a blob\n * const blobSAS = generateBlobSASQueryParameters({\n *     containerName, // Required\n *     blobName, // Required\n *     permissions: BlobSASPermissions.parse(\"racwd\"), // Required\n *     startsOn: new Date(), // Optional\n *     expiresOn: new Date(new Date().valueOf() + 86400 * 1000), // Required. Date type\n *     cacheControl: \"cache-control-override\", // Optional\n *     contentDisposition: \"content-disposition-override\", // Optional\n *     contentEncoding: \"content-encoding-override\", // Optional\n *     contentLanguage: \"content-language-override\", // Optional\n *     contentType: \"content-type-override\", // Optional\n *     ipRange: { start: \"0.0.0.0\", end: \"***************\" }, // Optional\n *     protocol: SASProtocol.HttpsAndHttp, // Optional\n *     version: \"2016-05-31\" // Optional\n *   },\n *   sharedKeyCredential // StorageSharedKeyCredential - `new StorageSharedKeyCredential(account, accountKey)`\n * ).toString();\n * ```\n *\n * @param blobSASSignatureValues -\n * @param sharedKeyCredential -\n */\nexport function generateBlobSASQueryParameters(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): SASQueryParameters;\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * Creates an instance of SASQueryParameters.\n * WARNING: identifier will be ignored when generating user delegation SAS, permissions and expiresOn are required.\n *\n * Example usage:\n *\n * ```js\n * // Generate user delegation SAS for a container\n * const userDelegationKey = await blobServiceClient.getUserDelegationKey(startsOn, expiresOn);\n * const containerSAS = generateBlobSASQueryParameters({\n *     containerName, // Required\n *     permissions: ContainerSASPermissions.parse(\"racwdl\"), // Required\n *     startsOn, // Optional. Date type\n *     expiresOn, // Required. Date type\n *     ipRange: { start: \"0.0.0.0\", end: \"***************\" }, // Optional\n *     protocol: SASProtocol.HttpsAndHttp, // Optional\n *     version: \"2018-11-09\" // Must greater than or equal to 2018-11-09 to generate user delegation SAS\n *   },\n *   userDelegationKey, // UserDelegationKey\n *   accountName\n * ).toString();\n * ```\n *\n * @param blobSASSignatureValues -\n * @param userDelegationKey - Return value of `blobServiceClient.getUserDelegationKey()`\n * @param accountName -\n */\nexport function generateBlobSASQueryParameters(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  userDelegationKey: UserDelegationKey,\n  accountName: string,\n): SASQueryParameters;\n\nexport function generateBlobSASQueryParameters(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  sharedKeyCredentialOrUserDelegationKey: StorageSharedKeyCredential | UserDelegationKey,\n  accountName?: string,\n): SASQueryParameters {\n  return generateBlobSASQueryParametersInternal(\n    blobSASSignatureValues,\n    sharedKeyCredentialOrUserDelegationKey,\n    accountName,\n  ).sasQueryParameters;\n}\n\nexport function generateBlobSASQueryParametersInternal(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  sharedKeyCredentialOrUserDelegationKey: StorageSharedKeyCredential | UserDelegationKey,\n  accountName?: string,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  const version = blobSASSignatureValues.version ? blobSASSignatureValues.version : SERVICE_VERSION;\n\n  const sharedKeyCredential =\n    sharedKeyCredentialOrUserDelegationKey instanceof StorageSharedKeyCredential\n      ? sharedKeyCredentialOrUserDelegationKey\n      : undefined;\n  let userDelegationKeyCredential: UserDelegationKeyCredential | undefined;\n\n  if (sharedKeyCredential === undefined && accountName !== undefined) {\n    userDelegationKeyCredential = new UserDelegationKeyCredential(\n      accountName,\n      sharedKeyCredentialOrUserDelegationKey as UserDelegationKey,\n    );\n  }\n\n  if (sharedKeyCredential === undefined && userDelegationKeyCredential === undefined) {\n    throw TypeError(\"Invalid sharedKeyCredential, userDelegationKey or accountName.\");\n  }\n\n  // Version 2020-12-06 adds support for encryptionscope in SAS.\n  if (version >= \"2020-12-06\") {\n    if (sharedKeyCredential !== undefined) {\n      return generateBlobSASQueryParameters20201206(blobSASSignatureValues, sharedKeyCredential);\n    } else {\n      return generateBlobSASQueryParametersUDK20201206(\n        blobSASSignatureValues,\n        userDelegationKeyCredential!,\n      );\n    }\n  }\n\n  // Version 2019-12-12 adds support for the blob tags permission.\n  // Version 2018-11-09 adds support for the signed resource and signed blob snapshot time fields.\n  // https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas#constructing-the-signature-string\n  if (version >= \"2018-11-09\") {\n    if (sharedKeyCredential !== undefined) {\n      return generateBlobSASQueryParameters20181109(blobSASSignatureValues, sharedKeyCredential);\n    } else {\n      // Version 2020-02-10 delegation SAS signature construction includes preauthorizedAgentObjectId, agentObjectId, correlationId.\n      if (version >= \"2020-02-10\") {\n        return generateBlobSASQueryParametersUDK20200210(\n          blobSASSignatureValues,\n          userDelegationKeyCredential!,\n        );\n      } else {\n        return generateBlobSASQueryParametersUDK20181109(\n          blobSASSignatureValues,\n          userDelegationKeyCredential!,\n        );\n      }\n    }\n  }\n\n  if (version >= \"2015-04-05\") {\n    if (sharedKeyCredential !== undefined) {\n      return generateBlobSASQueryParameters20150405(blobSASSignatureValues, sharedKeyCredential);\n    } else {\n      throw new RangeError(\n        \"'version' must be >= '2018-11-09' when generating user delegation SAS using user delegation key.\",\n      );\n    }\n  }\n\n  throw new RangeError(\"'version' must be >= '2015-04-05'.\");\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n * IMPLEMENTATION FOR API VERSION FROM 2015-04-05 AND BEFORE 2018-11-09.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn and identifier.\n *\n * WARNING: When identifier is not provided, permissions and expiresOn are required.\n * You MUST assign value to identifier or expiresOn & permissions manually if you initial with\n * this constructor.\n *\n * @param blobSASSignatureValues -\n * @param sharedKeyCredential -\n */\nfunction generateBlobSASQueryParameters20150405(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  blobSASSignatureValues = SASSignatureValuesSanityCheckAndAutofill(blobSASSignatureValues);\n\n  if (\n    !blobSASSignatureValues.identifier &&\n    !(blobSASSignatureValues.permissions && blobSASSignatureValues.expiresOn)\n  ) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Blob SAS generation when 'identifier' is not provided.\",\n    );\n  }\n\n  let resource: string = \"c\";\n  if (blobSASSignatureValues.blobName) {\n    resource = \"b\";\n  }\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  let verifiedPermissions: string | undefined;\n  if (blobSASSignatureValues.permissions) {\n    if (blobSASSignatureValues.blobName) {\n      verifiedPermissions = BlobSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    } else {\n      verifiedPermissions = ContainerSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    }\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    blobSASSignatureValues.startsOn\n      ? truncatedISO8061Date(blobSASSignatureValues.startsOn, false)\n      : \"\",\n    blobSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(blobSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(\n      sharedKeyCredential.accountName,\n      blobSASSignatureValues.containerName,\n      blobSASSignatureValues.blobName,\n    ),\n    blobSASSignatureValues.identifier,\n    blobSASSignatureValues.ipRange ? ipRangeToString(blobSASSignatureValues.ipRange) : \"\",\n    blobSASSignatureValues.protocol ? blobSASSignatureValues.protocol : \"\",\n    blobSASSignatureValues.version,\n    blobSASSignatureValues.cacheControl ? blobSASSignatureValues.cacheControl : \"\",\n    blobSASSignatureValues.contentDisposition ? blobSASSignatureValues.contentDisposition : \"\",\n    blobSASSignatureValues.contentEncoding ? blobSASSignatureValues.contentEncoding : \"\",\n    blobSASSignatureValues.contentLanguage ? blobSASSignatureValues.contentLanguage : \"\",\n    blobSASSignatureValues.contentType ? blobSASSignatureValues.contentType : \"\",\n  ].join(\"\\n\");\n\n  const signature = sharedKeyCredential.computeHMACSHA256(stringToSign);\n\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      blobSASSignatureValues.version!,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      blobSASSignatureValues.protocol,\n      blobSASSignatureValues.startsOn,\n      blobSASSignatureValues.expiresOn,\n      blobSASSignatureValues.ipRange,\n      blobSASSignatureValues.identifier,\n      resource,\n      blobSASSignatureValues.cacheControl,\n      blobSASSignatureValues.contentDisposition,\n      blobSASSignatureValues.contentEncoding,\n      blobSASSignatureValues.contentLanguage,\n      blobSASSignatureValues.contentType,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n * IMPLEMENTATION FOR API VERSION FROM 2018-11-09.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn and identifier.\n *\n * WARNING: When identifier is not provided, permissions and expiresOn are required.\n * You MUST assign value to identifier or expiresOn & permissions manually if you initial with\n * this constructor.\n *\n * @param blobSASSignatureValues -\n * @param sharedKeyCredential -\n */\nfunction generateBlobSASQueryParameters20181109(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  blobSASSignatureValues = SASSignatureValuesSanityCheckAndAutofill(blobSASSignatureValues);\n\n  if (\n    !blobSASSignatureValues.identifier &&\n    !(blobSASSignatureValues.permissions && blobSASSignatureValues.expiresOn)\n  ) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Blob SAS generation when 'identifier' is not provided.\",\n    );\n  }\n\n  let resource: string = \"c\";\n  let timestamp = blobSASSignatureValues.snapshotTime;\n  if (blobSASSignatureValues.blobName) {\n    resource = \"b\";\n    if (blobSASSignatureValues.snapshotTime) {\n      resource = \"bs\";\n    } else if (blobSASSignatureValues.versionId) {\n      resource = \"bv\";\n      timestamp = blobSASSignatureValues.versionId;\n    }\n  }\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  let verifiedPermissions: string | undefined;\n  if (blobSASSignatureValues.permissions) {\n    if (blobSASSignatureValues.blobName) {\n      verifiedPermissions = BlobSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    } else {\n      verifiedPermissions = ContainerSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    }\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    blobSASSignatureValues.startsOn\n      ? truncatedISO8061Date(blobSASSignatureValues.startsOn, false)\n      : \"\",\n    blobSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(blobSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(\n      sharedKeyCredential.accountName,\n      blobSASSignatureValues.containerName,\n      blobSASSignatureValues.blobName,\n    ),\n    blobSASSignatureValues.identifier,\n    blobSASSignatureValues.ipRange ? ipRangeToString(blobSASSignatureValues.ipRange) : \"\",\n    blobSASSignatureValues.protocol ? blobSASSignatureValues.protocol : \"\",\n    blobSASSignatureValues.version,\n    resource,\n    timestamp,\n    blobSASSignatureValues.cacheControl ? blobSASSignatureValues.cacheControl : \"\",\n    blobSASSignatureValues.contentDisposition ? blobSASSignatureValues.contentDisposition : \"\",\n    blobSASSignatureValues.contentEncoding ? blobSASSignatureValues.contentEncoding : \"\",\n    blobSASSignatureValues.contentLanguage ? blobSASSignatureValues.contentLanguage : \"\",\n    blobSASSignatureValues.contentType ? blobSASSignatureValues.contentType : \"\",\n  ].join(\"\\n\");\n\n  const signature = sharedKeyCredential.computeHMACSHA256(stringToSign);\n\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      blobSASSignatureValues.version!,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      blobSASSignatureValues.protocol,\n      blobSASSignatureValues.startsOn,\n      blobSASSignatureValues.expiresOn,\n      blobSASSignatureValues.ipRange,\n      blobSASSignatureValues.identifier,\n      resource,\n      blobSASSignatureValues.cacheControl,\n      blobSASSignatureValues.contentDisposition,\n      blobSASSignatureValues.contentEncoding,\n      blobSASSignatureValues.contentLanguage,\n      blobSASSignatureValues.contentType,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n * IMPLEMENTATION FOR API VERSION FROM 2020-12-06.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn and identifier.\n *\n * WARNING: When identifier is not provided, permissions and expiresOn are required.\n * You MUST assign value to identifier or expiresOn & permissions manually if you initial with\n * this constructor.\n *\n * @param blobSASSignatureValues -\n * @param sharedKeyCredential -\n */\nfunction generateBlobSASQueryParameters20201206(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  blobSASSignatureValues = SASSignatureValuesSanityCheckAndAutofill(blobSASSignatureValues);\n\n  if (\n    !blobSASSignatureValues.identifier &&\n    !(blobSASSignatureValues.permissions && blobSASSignatureValues.expiresOn)\n  ) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Blob SAS generation when 'identifier' is not provided.\",\n    );\n  }\n\n  let resource: string = \"c\";\n  let timestamp = blobSASSignatureValues.snapshotTime;\n  if (blobSASSignatureValues.blobName) {\n    resource = \"b\";\n    if (blobSASSignatureValues.snapshotTime) {\n      resource = \"bs\";\n    } else if (blobSASSignatureValues.versionId) {\n      resource = \"bv\";\n      timestamp = blobSASSignatureValues.versionId;\n    }\n  }\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  let verifiedPermissions: string | undefined;\n  if (blobSASSignatureValues.permissions) {\n    if (blobSASSignatureValues.blobName) {\n      verifiedPermissions = BlobSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    } else {\n      verifiedPermissions = ContainerSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    }\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    blobSASSignatureValues.startsOn\n      ? truncatedISO8061Date(blobSASSignatureValues.startsOn, false)\n      : \"\",\n    blobSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(blobSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(\n      sharedKeyCredential.accountName,\n      blobSASSignatureValues.containerName,\n      blobSASSignatureValues.blobName,\n    ),\n    blobSASSignatureValues.identifier,\n    blobSASSignatureValues.ipRange ? ipRangeToString(blobSASSignatureValues.ipRange) : \"\",\n    blobSASSignatureValues.protocol ? blobSASSignatureValues.protocol : \"\",\n    blobSASSignatureValues.version,\n    resource,\n    timestamp,\n    blobSASSignatureValues.encryptionScope,\n    blobSASSignatureValues.cacheControl ? blobSASSignatureValues.cacheControl : \"\",\n    blobSASSignatureValues.contentDisposition ? blobSASSignatureValues.contentDisposition : \"\",\n    blobSASSignatureValues.contentEncoding ? blobSASSignatureValues.contentEncoding : \"\",\n    blobSASSignatureValues.contentLanguage ? blobSASSignatureValues.contentLanguage : \"\",\n    blobSASSignatureValues.contentType ? blobSASSignatureValues.contentType : \"\",\n  ].join(\"\\n\");\n\n  const signature = sharedKeyCredential.computeHMACSHA256(stringToSign);\n\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      blobSASSignatureValues.version!,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      blobSASSignatureValues.protocol,\n      blobSASSignatureValues.startsOn,\n      blobSASSignatureValues.expiresOn,\n      blobSASSignatureValues.ipRange,\n      blobSASSignatureValues.identifier,\n      resource,\n      blobSASSignatureValues.cacheControl,\n      blobSASSignatureValues.contentDisposition,\n      blobSASSignatureValues.contentEncoding,\n      blobSASSignatureValues.contentLanguage,\n      blobSASSignatureValues.contentType,\n      undefined,\n      undefined,\n      undefined,\n      blobSASSignatureValues.encryptionScope,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n * IMPLEMENTATION FOR API VERSION FROM 2018-11-09.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn.\n *\n * WARNING: identifier will be ignored, permissions and expiresOn are required.\n *\n * @param blobSASSignatureValues -\n * @param userDelegationKeyCredential -\n */\nfunction generateBlobSASQueryParametersUDK20181109(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  userDelegationKeyCredential: UserDelegationKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  blobSASSignatureValues = SASSignatureValuesSanityCheckAndAutofill(blobSASSignatureValues);\n\n  // Stored access policies are not supported for a user delegation SAS.\n  if (!blobSASSignatureValues.permissions || !blobSASSignatureValues.expiresOn) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Blob SAS generation when generating user delegation SAS.\",\n    );\n  }\n\n  let resource: string = \"c\";\n  let timestamp = blobSASSignatureValues.snapshotTime;\n  if (blobSASSignatureValues.blobName) {\n    resource = \"b\";\n    if (blobSASSignatureValues.snapshotTime) {\n      resource = \"bs\";\n    } else if (blobSASSignatureValues.versionId) {\n      resource = \"bv\";\n      timestamp = blobSASSignatureValues.versionId;\n    }\n  }\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  let verifiedPermissions: string | undefined;\n  if (blobSASSignatureValues.permissions) {\n    if (blobSASSignatureValues.blobName) {\n      verifiedPermissions = BlobSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    } else {\n      verifiedPermissions = ContainerSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    }\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    blobSASSignatureValues.startsOn\n      ? truncatedISO8061Date(blobSASSignatureValues.startsOn, false)\n      : \"\",\n    blobSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(blobSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(\n      userDelegationKeyCredential.accountName,\n      blobSASSignatureValues.containerName,\n      blobSASSignatureValues.blobName,\n    ),\n    userDelegationKeyCredential.userDelegationKey.signedObjectId,\n    userDelegationKeyCredential.userDelegationKey.signedTenantId,\n    userDelegationKeyCredential.userDelegationKey.signedStartsOn\n      ? truncatedISO8061Date(userDelegationKeyCredential.userDelegationKey.signedStartsOn, false)\n      : \"\",\n    userDelegationKeyCredential.userDelegationKey.signedExpiresOn\n      ? truncatedISO8061Date(userDelegationKeyCredential.userDelegationKey.signedExpiresOn, false)\n      : \"\",\n    userDelegationKeyCredential.userDelegationKey.signedService,\n    userDelegationKeyCredential.userDelegationKey.signedVersion,\n    blobSASSignatureValues.ipRange ? ipRangeToString(blobSASSignatureValues.ipRange) : \"\",\n    blobSASSignatureValues.protocol ? blobSASSignatureValues.protocol : \"\",\n    blobSASSignatureValues.version,\n    resource,\n    timestamp,\n    blobSASSignatureValues.cacheControl,\n    blobSASSignatureValues.contentDisposition,\n    blobSASSignatureValues.contentEncoding,\n    blobSASSignatureValues.contentLanguage,\n    blobSASSignatureValues.contentType,\n  ].join(\"\\n\");\n\n  const signature = userDelegationKeyCredential.computeHMACSHA256(stringToSign);\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      blobSASSignatureValues.version!,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      blobSASSignatureValues.protocol,\n      blobSASSignatureValues.startsOn,\n      blobSASSignatureValues.expiresOn,\n      blobSASSignatureValues.ipRange,\n      blobSASSignatureValues.identifier,\n      resource,\n      blobSASSignatureValues.cacheControl,\n      blobSASSignatureValues.contentDisposition,\n      blobSASSignatureValues.contentEncoding,\n      blobSASSignatureValues.contentLanguage,\n      blobSASSignatureValues.contentType,\n      userDelegationKeyCredential.userDelegationKey,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n * IMPLEMENTATION FOR API VERSION FROM 2020-02-10.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn.\n *\n * WARNING: identifier will be ignored, permissions and expiresOn are required.\n *\n * @param blobSASSignatureValues -\n * @param userDelegationKeyCredential -\n */\nfunction generateBlobSASQueryParametersUDK20200210(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  userDelegationKeyCredential: UserDelegationKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  blobSASSignatureValues = SASSignatureValuesSanityCheckAndAutofill(blobSASSignatureValues);\n\n  // Stored access policies are not supported for a user delegation SAS.\n  if (!blobSASSignatureValues.permissions || !blobSASSignatureValues.expiresOn) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Blob SAS generation when generating user delegation SAS.\",\n    );\n  }\n\n  let resource: string = \"c\";\n  let timestamp = blobSASSignatureValues.snapshotTime;\n  if (blobSASSignatureValues.blobName) {\n    resource = \"b\";\n    if (blobSASSignatureValues.snapshotTime) {\n      resource = \"bs\";\n    } else if (blobSASSignatureValues.versionId) {\n      resource = \"bv\";\n      timestamp = blobSASSignatureValues.versionId;\n    }\n  }\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  let verifiedPermissions: string | undefined;\n  if (blobSASSignatureValues.permissions) {\n    if (blobSASSignatureValues.blobName) {\n      verifiedPermissions = BlobSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    } else {\n      verifiedPermissions = ContainerSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    }\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    blobSASSignatureValues.startsOn\n      ? truncatedISO8061Date(blobSASSignatureValues.startsOn, false)\n      : \"\",\n    blobSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(blobSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(\n      userDelegationKeyCredential.accountName,\n      blobSASSignatureValues.containerName,\n      blobSASSignatureValues.blobName,\n    ),\n    userDelegationKeyCredential.userDelegationKey.signedObjectId,\n    userDelegationKeyCredential.userDelegationKey.signedTenantId,\n    userDelegationKeyCredential.userDelegationKey.signedStartsOn\n      ? truncatedISO8061Date(userDelegationKeyCredential.userDelegationKey.signedStartsOn, false)\n      : \"\",\n    userDelegationKeyCredential.userDelegationKey.signedExpiresOn\n      ? truncatedISO8061Date(userDelegationKeyCredential.userDelegationKey.signedExpiresOn, false)\n      : \"\",\n    userDelegationKeyCredential.userDelegationKey.signedService,\n    userDelegationKeyCredential.userDelegationKey.signedVersion,\n    blobSASSignatureValues.preauthorizedAgentObjectId,\n    undefined, // agentObjectId\n    blobSASSignatureValues.correlationId,\n    blobSASSignatureValues.ipRange ? ipRangeToString(blobSASSignatureValues.ipRange) : \"\",\n    blobSASSignatureValues.protocol ? blobSASSignatureValues.protocol : \"\",\n    blobSASSignatureValues.version,\n    resource,\n    timestamp,\n    blobSASSignatureValues.cacheControl,\n    blobSASSignatureValues.contentDisposition,\n    blobSASSignatureValues.contentEncoding,\n    blobSASSignatureValues.contentLanguage,\n    blobSASSignatureValues.contentType,\n  ].join(\"\\n\");\n\n  const signature = userDelegationKeyCredential.computeHMACSHA256(stringToSign);\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      blobSASSignatureValues.version!,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      blobSASSignatureValues.protocol,\n      blobSASSignatureValues.startsOn,\n      blobSASSignatureValues.expiresOn,\n      blobSASSignatureValues.ipRange,\n      blobSASSignatureValues.identifier,\n      resource,\n      blobSASSignatureValues.cacheControl,\n      blobSASSignatureValues.contentDisposition,\n      blobSASSignatureValues.contentEncoding,\n      blobSASSignatureValues.contentLanguage,\n      blobSASSignatureValues.contentType,\n      userDelegationKeyCredential.userDelegationKey,\n      blobSASSignatureValues.preauthorizedAgentObjectId,\n      blobSASSignatureValues.correlationId,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n * IMPLEMENTATION FOR API VERSION FROM 2020-12-06.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn.\n *\n * WARNING: identifier will be ignored, permissions and expiresOn are required.\n *\n * @param blobSASSignatureValues -\n * @param userDelegationKeyCredential -\n */\nfunction generateBlobSASQueryParametersUDK20201206(\n  blobSASSignatureValues: BlobSASSignatureValues,\n  userDelegationKeyCredential: UserDelegationKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  blobSASSignatureValues = SASSignatureValuesSanityCheckAndAutofill(blobSASSignatureValues);\n\n  // Stored access policies are not supported for a user delegation SAS.\n  if (!blobSASSignatureValues.permissions || !blobSASSignatureValues.expiresOn) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Blob SAS generation when generating user delegation SAS.\",\n    );\n  }\n\n  let resource: string = \"c\";\n  let timestamp = blobSASSignatureValues.snapshotTime;\n  if (blobSASSignatureValues.blobName) {\n    resource = \"b\";\n    if (blobSASSignatureValues.snapshotTime) {\n      resource = \"bs\";\n    } else if (blobSASSignatureValues.versionId) {\n      resource = \"bv\";\n      timestamp = blobSASSignatureValues.versionId;\n    }\n  }\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  let verifiedPermissions: string | undefined;\n  if (blobSASSignatureValues.permissions) {\n    if (blobSASSignatureValues.blobName) {\n      verifiedPermissions = BlobSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    } else {\n      verifiedPermissions = ContainerSASPermissions.parse(\n        blobSASSignatureValues.permissions.toString(),\n      ).toString();\n    }\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    blobSASSignatureValues.startsOn\n      ? truncatedISO8061Date(blobSASSignatureValues.startsOn, false)\n      : \"\",\n    blobSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(blobSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(\n      userDelegationKeyCredential.accountName,\n      blobSASSignatureValues.containerName,\n      blobSASSignatureValues.blobName,\n    ),\n    userDelegationKeyCredential.userDelegationKey.signedObjectId,\n    userDelegationKeyCredential.userDelegationKey.signedTenantId,\n    userDelegationKeyCredential.userDelegationKey.signedStartsOn\n      ? truncatedISO8061Date(userDelegationKeyCredential.userDelegationKey.signedStartsOn, false)\n      : \"\",\n    userDelegationKeyCredential.userDelegationKey.signedExpiresOn\n      ? truncatedISO8061Date(userDelegationKeyCredential.userDelegationKey.signedExpiresOn, false)\n      : \"\",\n    userDelegationKeyCredential.userDelegationKey.signedService,\n    userDelegationKeyCredential.userDelegationKey.signedVersion,\n    blobSASSignatureValues.preauthorizedAgentObjectId,\n    undefined, // agentObjectId\n    blobSASSignatureValues.correlationId,\n    blobSASSignatureValues.ipRange ? ipRangeToString(blobSASSignatureValues.ipRange) : \"\",\n    blobSASSignatureValues.protocol ? blobSASSignatureValues.protocol : \"\",\n    blobSASSignatureValues.version,\n    resource,\n    timestamp,\n    blobSASSignatureValues.encryptionScope,\n    blobSASSignatureValues.cacheControl,\n    blobSASSignatureValues.contentDisposition,\n    blobSASSignatureValues.contentEncoding,\n    blobSASSignatureValues.contentLanguage,\n    blobSASSignatureValues.contentType,\n  ].join(\"\\n\");\n\n  const signature = userDelegationKeyCredential.computeHMACSHA256(stringToSign);\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      blobSASSignatureValues.version!,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      blobSASSignatureValues.protocol,\n      blobSASSignatureValues.startsOn,\n      blobSASSignatureValues.expiresOn,\n      blobSASSignatureValues.ipRange,\n      blobSASSignatureValues.identifier,\n      resource,\n      blobSASSignatureValues.cacheControl,\n      blobSASSignatureValues.contentDisposition,\n      blobSASSignatureValues.contentEncoding,\n      blobSASSignatureValues.contentLanguage,\n      blobSASSignatureValues.contentType,\n      userDelegationKeyCredential.userDelegationKey,\n      blobSASSignatureValues.preauthorizedAgentObjectId,\n      blobSASSignatureValues.correlationId,\n      blobSASSignatureValues.encryptionScope,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\nfunction getCanonicalName(accountName: string, containerName: string, blobName?: string): string {\n  // Container: \"/blob/account/containerName\"\n  // Blob:      \"/blob/account/containerName/blobName\"\n  const elements: string[] = [`/blob/${accountName}/${containerName}`];\n  if (blobName) {\n    elements.push(`/${blobName}`);\n  }\n  return elements.join(\"\");\n}\n\nfunction SASSignatureValuesSanityCheckAndAutofill(\n  blobSASSignatureValues: BlobSASSignatureValues,\n): BlobSASSignatureValues {\n  const version = blobSASSignatureValues.version ? blobSASSignatureValues.version : SERVICE_VERSION;\n  if (blobSASSignatureValues.snapshotTime && version < \"2018-11-09\") {\n    throw RangeError(\"'version' must be >= '2018-11-09' when providing 'snapshotTime'.\");\n  }\n  if (blobSASSignatureValues.blobName === undefined && blobSASSignatureValues.snapshotTime) {\n    throw RangeError(\"Must provide 'blobName' when providing 'snapshotTime'.\");\n  }\n\n  if (blobSASSignatureValues.versionId && version < \"2019-10-10\") {\n    throw RangeError(\"'version' must be >= '2019-10-10' when providing 'versionId'.\");\n  }\n  if (blobSASSignatureValues.blobName === undefined && blobSASSignatureValues.versionId) {\n    throw RangeError(\"Must provide 'blobName' when providing 'versionId'.\");\n  }\n\n  if (\n    blobSASSignatureValues.permissions &&\n    blobSASSignatureValues.permissions.setImmutabilityPolicy &&\n    version < \"2020-08-04\"\n  ) {\n    throw RangeError(\"'version' must be >= '2020-08-04' when provided 'i' permission.\");\n  }\n\n  if (\n    blobSASSignatureValues.permissions &&\n    blobSASSignatureValues.permissions.deleteVersion &&\n    version < \"2019-10-10\"\n  ) {\n    throw RangeError(\"'version' must be >= '2019-10-10' when providing 'x' permission.\");\n  }\n\n  if (\n    blobSASSignatureValues.permissions &&\n    blobSASSignatureValues.permissions.permanentDelete &&\n    version < \"2019-10-10\"\n  ) {\n    throw RangeError(\"'version' must be >= '2019-10-10' when providing 'y' permission.\");\n  }\n\n  if (\n    blobSASSignatureValues.permissions &&\n    blobSASSignatureValues.permissions.tag &&\n    version < \"2019-12-12\"\n  ) {\n    throw RangeError(\"'version' must be >= '2019-12-12' when providing 't' permission.\");\n  }\n\n  if (\n    version < \"2020-02-10\" &&\n    blobSASSignatureValues.permissions &&\n    (blobSASSignatureValues.permissions.move || blobSASSignatureValues.permissions.execute)\n  ) {\n    throw RangeError(\"'version' must be >= '2020-02-10' when providing the 'm' or 'e' permission.\");\n  }\n\n  if (\n    version < \"2021-04-10\" &&\n    blobSASSignatureValues.permissions &&\n    (blobSASSignatureValues.permissions as ContainerSASPermissions).filterByTags\n  ) {\n    throw RangeError(\"'version' must be >= '2021-04-10' when providing the 'f' permission.\");\n  }\n\n  if (\n    version < \"2020-02-10\" &&\n    (blobSASSignatureValues.preauthorizedAgentObjectId || blobSASSignatureValues.correlationId)\n  ) {\n    throw RangeError(\n      \"'version' must be >= '2020-02-10' when providing 'preauthorizedAgentObjectId' or 'correlationId'.\",\n    );\n  }\n\n  if (blobSASSignatureValues.encryptionScope && version < \"2020-12-06\") {\n    throw RangeError(\"'version' must be >= '2020-12-06' when provided 'encryptionScope' in SAS.\");\n  }\n\n  blobSASSignatureValues.version = version;\n  return blobSASSignatureValues;\n}\n"]}