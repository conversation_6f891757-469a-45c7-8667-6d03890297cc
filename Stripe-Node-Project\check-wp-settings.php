<?php
/**
 * Check WordPress settings for Stripe Integration
 */

// Load WordPress
require_once('wordpress/wp-config.php');
require_once('wordpress/wp-includes/wp-db.php');
require_once('wordpress/wp-includes/functions.php');

// Connect to WordPress database
$wpdb = new wpdb(DB_USER, DB_PASSWORD, DB_NAME, DB_HOST);

echo "🔍 Checking WordPress Stripe Integration Settings...\n\n";

// Get the settings from WordPress options table
$settings = $wpdb->get_row(
    "SELECT option_value FROM {$wpdb->prefix}options WHERE option_name = 'stripe_integration_options'",
    ARRAY_A
);

if ($settings) {
    echo "✅ Settings found in database!\n";
    echo "Raw data: " . $settings['option_value'] . "\n\n";
    
    // Unserialize the data
    $options = maybe_unserialize($settings['option_value']);
    
    if (is_array($options)) {
        echo "📋 Parsed Settings:\n";
        echo "==================\n";
        foreach ($options as $key => $value) {
            if ($key === 'api_key' && !empty($value)) {
                echo "API Key: " . substr($value, 0, 10) . "... (hidden for security)\n";
            } else {
                echo ucfirst(str_replace('_', ' ', $key)) . ": " . $value . "\n";
            }
        }
    } else {
        echo "❌ Settings data is not in expected format\n";
        var_dump($options);
    }
} else {
    echo "❌ No settings found in database\n";
    
    // Check if the table exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}options'");
    if ($table_exists) {
        echo "✅ Options table exists\n";
        
        // Check for any stripe-related options
        $stripe_options = $wpdb->get_results(
            "SELECT option_name, option_value FROM {$wpdb->prefix}options WHERE option_name LIKE '%stripe%'",
            ARRAY_A
        );
        
        if ($stripe_options) {
            echo "\n🔍 Found other stripe-related options:\n";
            foreach ($stripe_options as $option) {
                echo "- " . $option['option_name'] . "\n";
            }
        } else {
            echo "❌ No stripe-related options found\n";
        }
    } else {
        echo "❌ Options table does not exist\n";
    }
}

echo "\n🎯 Expected values:\n";
echo "==================\n";
echo "API Key: sk_api_7036c6f38cab187f402a7c37e701554be2cd1a246c21bb384ad7ca594759c0ae\n";
echo "Vendor ID: 4b65134b-88f1-40a8-951b-694876338cd8\n";
echo "API Base URL: http://localhost:3000\n";
echo "Business Niche: other\n";
echo "Test Mode: 1 (true)\n";
?>
