{"version": 3, "file": "CredentialPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/CredentialPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAMpD;;;GAGG;AACH,MAAM,OAAgB,gBAAiB,SAAQ,iBAAiB;IAC9D;;;;OAIG;IACI,WAAW,CAAC,OAAoB;QACrC,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;;;;OAKG;IACO,WAAW,CAAC,OAAoB;QACxC,4EAA4E;QAC5E,qCAAqC;QACrC,OAAO,OAAO,CAAC;IACjB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { BaseRequestPolicy } from \"./RequestPolicy\";\nimport type {\n  WebResourceLike as WebResource,\n  CompatResponse as HttpOperationResponse,\n} from \"@azure/core-http-compat\";\n\n/**\n * Credential policy used to sign HTTP(S) requests before sending. This is an\n * abstract class.\n */\nexport abstract class CredentialPolicy extends BaseRequestPolicy {\n  /**\n   * Sends out request.\n   *\n   * @param request -\n   */\n  public sendRequest(request: WebResource): Promise<HttpOperationResponse> {\n    return this._nextPolicy.sendRequest(this.signRequest(request));\n  }\n\n  /**\n   * Child classes must implement this method with request signing. This method\n   * will be executed in {@link sendRequest}.\n   *\n   * @param request -\n   */\n  protected signRequest(request: WebResource): WebResource {\n    // Child classes must override this method with request signing. This method\n    // will be executed in sendRequest().\n    return request;\n  }\n}\n"]}