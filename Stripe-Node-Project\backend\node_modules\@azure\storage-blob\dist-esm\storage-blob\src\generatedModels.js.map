{"version": 3, "file": "generatedModels.js", "sourceRoot": "", "sources": ["../../../src/generatedModels.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAwgBlC,gFAAgF;AAChF,MAAM,CAAN,IAAY,4BAEX;AAFD,WAAY,4BAA4B;IACtC,iDAAiB,CAAA;AACnB,CAAC,EAFW,4BAA4B,KAA5B,4BAA4B,QAEvC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { Tags } from \".\";\nimport type { BlobPropertiesInternal as BlobProperties } from \"./generated/src/models\";\nimport {\n  AppendBlobAppendBlockFromUrlHeaders,\n  AppendBlobAppendBlockHeaders,\n  AppendBlobCreateHeaders,\n  BlobAbortCopyFromURLHeaders,\n  BlobCopyFromURLHeaders,\n  BlobCreateSnapshotHeaders,\n  BlobDeleteHeaders,\n  BlobDeleteImmutabilityPolicyHeaders,\n  BlobDownloadResponse as BlobDownloadResponseInternal,\n  BlobDownloadHeaders,\n  BlobGetPropertiesHeaders,\n  BlobGetTagsHeaders,\n  BlobTags,\n  BlobQueryResponse as BlobQueryResponseInternal,\n  BlobQueryHeaders,\n  BlobSetHttpHeadersHeaders,\n  BlobSetImmutabilityPolicyHeaders,\n  BlobSetLegalHoldHeaders,\n  BlobSetMetadataHeaders,\n  BlobSetTagsHeaders,\n  BlobSetTierHeaders,\n  BlobStartCopyFromURLHeaders,\n  BlobUndeleteHeaders,\n  BlockBlobCommitBlockListHeaders,\n  BlockBlobGetBlockListResponse as BlockBlobGetBlockListResponseInternal,\n  BlockBlobGetBlockListHeaders,\n  BlockBlobPutBlobFromUrlHeaders,\n  BlockBlobStageBlockFromURLHeaders,\n  BlockBlobStageBlockHeaders,\n  BlockBlobUploadHeaders,\n  ContainerCreateHeaders,\n  ContainerDeleteHeaders,\n  ContainerGetAccessPolicyHeaders,\n  ContainerGetPropertiesHeaders,\n  ContainerListBlobFlatSegmentHeaders,\n  ContainerListBlobHierarchySegmentHeaders,\n  ContainerRenameHeaders,\n  ContainerSetAccessPolicyHeaders,\n  ContainerSetMetadataHeaders,\n  ContainerRestoreHeaders as ContainerUndeleteHeaders,\n  PageBlobClearPagesHeaders,\n  PageBlobCopyIncrementalHeaders,\n  PageBlobCreateHeaders,\n  PageBlobGetPageRangesDiffResponse as PageBlobGetPageRangesDiffResponseInternal,\n  PageBlobGetPageRangesDiffHeaders,\n  PageBlobGetPageRangesResponse as PageBlobGetPageRangesResponseInternal,\n  PageBlobGetPageRangesHeaders,\n  PageBlobResizeHeaders,\n  PageBlobUpdateSequenceNumberHeaders,\n  PageBlobUploadPagesFromURLHeaders,\n  PageBlobUploadPagesHeaders,\n  PageList,\n  ServiceGetAccountInfoHeaders,\n  ServiceGetPropertiesResponse as ServiceGetPropertiesResponseInternal,\n  ServiceGetPropertiesHeaders,\n  ServiceGetStatisticsResponse as ServiceGetStatisticsResponseInternal,\n  ServiceGetStatisticsHeaders,\n  ServiceGetUserDelegationKeyHeaders,\n  ServiceListContainersSegmentResponse as ServiceListContainersSegmentResponseInternal,\n  ServiceListContainersSegmentHeaders,\n  ServiceSetPropertiesHeaders,\n  ServiceSubmitBatchResponse as ServiceSubmitBatchResponseInternal,\n  ServiceSubmitBatchHeaders,\n  SignedIdentifier as SignedIdentifierModel,\n  UserDelegationKey as UserDelegationKeyModel,\n  PageRange,\n  BlobGetAccountInfoHeaders,\n  ContainerGetAccountInfoHeaders,\n} from \"./generated/src/models\";\nimport {\n  WithResponse,\n  ResponseWithBody,\n  ResponseLike,\n  ResponseWithHeaders,\n  HttpResponse,\n} from \"./utils/utils.common\";\n\n/** Contains response data for the appendBlockFromUrl operation. */\nexport type AppendBlobAppendBlockFromUrlResponse = WithResponse<\n  AppendBlobAppendBlockFromUrlHeaders,\n  AppendBlobAppendBlockFromUrlHeaders\n>;\n/** Contains response data for the appendBlock operation. */\nexport type AppendBlobAppendBlockResponse = WithResponse<\n  AppendBlobAppendBlockHeaders,\n  AppendBlobAppendBlockHeaders\n>;\n/** Contains response data for the create operation. */\nexport type AppendBlobCreateResponse = WithResponse<\n  AppendBlobCreateHeaders,\n  AppendBlobCreateHeaders\n>;\n/** Contains response data for the abortCopyFromURL operation. */\nexport type BlobAbortCopyFromURLResponse = WithResponse<\n  BlobAbortCopyFromURLHeaders,\n  BlobAbortCopyFromURLHeaders\n>;\n/** Contains response data for the copyFromURL operation. */\nexport type BlobCopyFromURLResponse = WithResponse<BlobCopyFromURLHeaders, BlobCopyFromURLHeaders>;\n/** Contains response data for the createSnapshot operation. */\nexport type BlobCreateSnapshotResponse = WithResponse<\n  BlobCreateSnapshotHeaders,\n  BlobCreateSnapshotHeaders\n>;\n/** Contains response data for the delete operation. */\nexport type BlobDeleteResponse = WithResponse<BlobDeleteHeaders, BlobDeleteHeaders>;\n/** Contains response data for the deleteImmutabilityPolicy operation. */\nexport type BlobDeleteImmutabilityPolicyResponse = WithResponse<\n  BlobDeleteImmutabilityPolicyHeaders,\n  BlobDeleteImmutabilityPolicyHeaders\n>;\n/** Contains response data for the download operation. */\nexport type BlobDownloadResponseModel = WithResponse<\n  BlobDownloadResponseInternal,\n  BlobDownloadHeaders\n>;\n/** Contains response data for the getProperties operation. */\nexport type BlobGetPropertiesResponseModel = WithResponse<\n  BlobGetPropertiesHeaders,\n  BlobGetPropertiesHeaders\n>;\n/** Contains response data for the getAccountInfo operation. */\nexport type BlobGetAccountInfoResponse = WithResponse<\n  BlobGetAccountInfoHeaders,\n  BlobGetAccountInfoHeaders\n>;\n/** Contains response data for the query operation. */\nexport type BlobQueryResponseModel = WithResponse<BlobQueryResponseInternal, BlobQueryHeaders>;\n/** Contains response data for the setHttpHeaders operation. */\nexport type BlobSetHTTPHeadersResponse = WithResponse<\n  BlobSetHttpHeadersHeaders,\n  BlobSetHttpHeadersHeaders\n>;\n/** Contains response data for the setImmutabilityPolicy operation. */\nexport type BlobSetImmutabilityPolicyResponse = WithResponse<\n  BlobSetImmutabilityPolicyHeaders,\n  BlobSetImmutabilityPolicyHeaders\n>;\n/** Contains response data for the setLegalHold operation. */\nexport type BlobSetLegalHoldResponse = WithResponse<\n  BlobSetLegalHoldHeaders,\n  BlobSetLegalHoldHeaders\n>;\n/** Contains response data for the setMetadata operation. */\nexport type BlobSetMetadataResponse = WithResponse<BlobSetMetadataHeaders, BlobSetMetadataHeaders>;\n/** Contains response data for the setTags operation. */\nexport type BlobSetTagsResponse = WithResponse<BlobSetTagsHeaders, BlobSetTagsHeaders>;\n/** Contains response data for the setTier operation. */\nexport type BlobSetTierResponse = WithResponse<BlobSetTierHeaders, BlobSetTierHeaders>;\n/** Contains response data for the startCopyFromURL operation. */\nexport type BlobStartCopyFromURLResponse = WithResponse<\n  BlobStartCopyFromURLHeaders,\n  BlobStartCopyFromURLHeaders\n>;\n/** Contains response data for the undelete operation. */\nexport type BlobUndeleteResponse = WithResponse<BlobUndeleteHeaders, BlobUndeleteHeaders>;\n/** Contains response data for the commitBlockList operation. */\nexport type BlockBlobCommitBlockListResponse = WithResponse<\n  BlockBlobCommitBlockListHeaders,\n  BlockBlobCommitBlockListHeaders\n>;\n/** Contains response data for the getBlockList operation. */\nexport type BlockBlobGetBlockListResponse = WithResponse<\n  BlockBlobGetBlockListResponseInternal,\n  BlockBlobGetBlockListHeaders\n>;\n/** Contains response data for the putBlobFromUrl operation. */\nexport type BlockBlobPutBlobFromUrlResponse = WithResponse<\n  BlockBlobPutBlobFromUrlHeaders,\n  BlockBlobPutBlobFromUrlHeaders\n>;\n/** Contains response data for the stageBlockFromURL operation. */\nexport type BlockBlobStageBlockFromURLResponse = WithResponse<\n  BlockBlobStageBlockFromURLHeaders,\n  BlockBlobStageBlockFromURLHeaders\n>;\n/** Contains response data for the stageBlock operation. */\nexport type BlockBlobStageBlockResponse = WithResponse<\n  BlockBlobStageBlockHeaders,\n  BlockBlobStageBlockHeaders\n>;\n/** Contains response data for the upload operation. */\nexport type BlockBlobUploadResponse = WithResponse<BlockBlobUploadHeaders, BlockBlobUploadHeaders>;\n/** Contains response data for the create operation. */\nexport type ContainerCreateResponse = WithResponse<ContainerCreateHeaders, ContainerCreateHeaders>;\n/** Contains response data for the delete operation. */\nexport type ContainerDeleteResponse = WithResponse<ContainerDeleteHeaders, ContainerDeleteHeaders>;\n/** Contains response data for the getAccountInfo operation. */\nexport type ContainerGetAccountInfoResponse = WithResponse<\n  ContainerGetAccountInfoHeaders,\n  ContainerGetAccountInfoHeaders\n>;\n/** Contains response data for the getProperties operation. */\nexport type ContainerGetPropertiesResponse = WithResponse<\n  ContainerGetPropertiesHeaders,\n  ContainerGetPropertiesHeaders\n>;\n/** Contains response data for the rename operation. */\nexport type ContainerRenameResponse = WithResponse<ContainerRenameHeaders, ContainerRenameHeaders>;\n/** Contains response data for the setAccessPolicy operation. */\nexport type ContainerSetAccessPolicyResponse = WithResponse<\n  ContainerSetAccessPolicyHeaders,\n  ContainerSetAccessPolicyHeaders\n>;\n/** Contains response data for the setMetadata operation. */\nexport type ContainerSetMetadataResponse = WithResponse<\n  ContainerSetMetadataHeaders,\n  ContainerSetMetadataHeaders\n>;\n/** Contains response data for the restore operation. */\nexport type ContainerUndeleteResponse = WithResponse<\n  ContainerUndeleteHeaders,\n  ContainerUndeleteHeaders\n>;\n/** Contains response data for the clearPages operation. */\nexport type PageBlobClearPagesResponse = WithResponse<\n  PageBlobClearPagesHeaders,\n  PageBlobClearPagesHeaders\n>;\n/** Contains response data for the copyIncremental operation. */\nexport type PageBlobCopyIncrementalResponse = WithResponse<\n  PageBlobCopyIncrementalHeaders,\n  PageBlobCopyIncrementalHeaders\n>;\n/** Contains response data for the create operation. */\nexport type PageBlobCreateResponse = WithResponse<PageBlobCreateHeaders, PageBlobCreateHeaders>;\n/** Contains response data for the getPageRangesDiff operation. */\nexport type PageBlobGetPageRangesDiffResponseModel = WithResponse<\n  PageBlobGetPageRangesDiffResponseInternal,\n  PageBlobGetPageRangesDiffHeaders,\n  PageList\n>;\n/** Contains response data for the getPageRanges operation. */\nexport type PageBlobGetPageRangesResponseModel = WithResponse<\n  PageBlobGetPageRangesResponseInternal,\n  PageBlobGetPageRangesHeaders,\n  PageList\n>;\n/** Contains response data for the resize operation. */\nexport type PageBlobResizeResponse = WithResponse<PageBlobResizeHeaders, PageBlobResizeHeaders>;\n/** Contains response data for the updateSequenceNumber operation. */\nexport type PageBlobUpdateSequenceNumberResponse = WithResponse<\n  PageBlobUpdateSequenceNumberHeaders,\n  PageBlobUpdateSequenceNumberHeaders\n>;\n/** Contains response data for the uploadPagesFromURL operation. */\nexport type PageBlobUploadPagesFromURLResponse = WithResponse<\n  PageBlobUploadPagesFromURLHeaders,\n  PageBlobUploadPagesFromURLHeaders\n>;\n/** Contains response data for the uploadPages operation. */\nexport type PageBlobUploadPagesResponse = WithResponse<\n  PageBlobUploadPagesHeaders,\n  PageBlobUploadPagesHeaders\n>;\n/** Contains response data for the getAccountInfo operation. */\nexport type ServiceGetAccountInfoResponse = WithResponse<\n  ServiceGetAccountInfoHeaders,\n  ServiceGetAccountInfoHeaders\n>;\n/** Contains response data for the getProperties operation. */\nexport type ServiceGetPropertiesResponse = WithResponse<\n  ServiceGetPropertiesResponseInternal,\n  ServiceGetPropertiesHeaders\n>;\n/** Contains response data for the getStatistics operation. */\nexport type ServiceGetStatisticsResponse = WithResponse<\n  ServiceGetStatisticsResponseInternal,\n  ServiceGetStatisticsHeaders\n>;\n/** Contains response data for the listContainersSegment operation. */\nexport type ServiceListContainersSegmentResponse = WithResponse<\n  ServiceListContainersSegmentResponseInternal,\n  ServiceListContainersSegmentHeaders\n>;\n/** Contains response data for the setProperties operation. */\nexport type ServiceSetPropertiesResponse = WithResponse<\n  ServiceSetPropertiesHeaders,\n  ServiceSetPropertiesHeaders\n>;\n/** Contains response data for the submitBatch operation. */\nexport type ServiceSubmitBatchResponseModel = WithResponse<\n  ServiceSubmitBatchResponseInternal,\n  ServiceSubmitBatchHeaders\n>;\nexport {\n  AppendBlobAppendBlockFromUrlHeaders,\n  AppendBlobAppendBlockHeaders,\n  AppendBlobCreateHeaders,\n  BlobAbortCopyFromURLHeaders,\n  BlobCopyFromURLHeaders,\n  BlobCreateSnapshotHeaders,\n  BlobDeleteHeaders,\n  BlobDeleteImmutabilityPolicyHeaders,\n  BlobDownloadHeaders,\n  BlobDownloadResponseInternal,\n  BlobGetAccountInfoHeaders,\n  BlobGetPropertiesHeaders,\n  BlobGetTagsHeaders,\n  BlobTags,\n  BlobQueryHeaders,\n  BlobQueryResponseInternal,\n  BlobSetHttpHeadersHeaders as BlobSetHTTPHeadersHeaders,\n  BlobSetImmutabilityPolicyHeaders,\n  BlobSetLegalHoldHeaders,\n  BlobSetMetadataHeaders,\n  BlobSetTagsHeaders,\n  BlobSetTierHeaders,\n  BlobStartCopyFromURLHeaders,\n  BlobUndeleteHeaders,\n  BlockBlobCommitBlockListHeaders,\n  BlockBlobGetBlockListHeaders,\n  BlockBlobGetBlockListResponseInternal,\n  BlockBlobPutBlobFromUrlHeaders,\n  BlockBlobStageBlockFromURLHeaders,\n  BlockBlobStageBlockHeaders,\n  BlockBlobUploadHeaders,\n  ContainerCreateHeaders,\n  ContainerDeleteHeaders,\n  ContainerGetAccessPolicyHeaders,\n  ContainerGetAccountInfoHeaders,\n  ContainerGetPropertiesHeaders,\n  ContainerListBlobFlatSegmentHeaders,\n  ContainerListBlobHierarchySegmentHeaders,\n  ContainerRenameHeaders,\n  ContainerSetAccessPolicyHeaders,\n  ContainerSetMetadataHeaders,\n  ContainerUndeleteHeaders,\n  PageBlobClearPagesHeaders,\n  PageBlobCopyIncrementalHeaders,\n  PageBlobCreateHeaders,\n  PageBlobGetPageRangesDiffHeaders,\n  PageBlobGetPageRangesDiffResponseInternal,\n  PageBlobGetPageRangesHeaders,\n  PageBlobGetPageRangesResponseInternal,\n  PageBlobResizeHeaders,\n  PageBlobUpdateSequenceNumberHeaders,\n  PageBlobUploadPagesFromURLHeaders,\n  PageBlobUploadPagesHeaders,\n  PageList as PageListInternal,\n  ServiceGetAccountInfoHeaders,\n  ServiceGetPropertiesHeaders,\n  ServiceGetPropertiesResponseInternal,\n  ServiceGetStatisticsHeaders,\n  ServiceGetStatisticsResponseInternal,\n  ServiceGetUserDelegationKeyHeaders,\n  ServiceListContainersSegmentHeaders,\n  ServiceListContainersSegmentResponseInternal,\n  ServiceSetPropertiesHeaders,\n  ServiceSubmitBatchHeaders,\n  ServiceSubmitBatchResponseInternal,\n  SignedIdentifierModel,\n  UserDelegationKeyModel,\n  WithResponse,\n  ResponseWithBody,\n  ResponseLike,\n  HttpResponse,\n  ResponseWithHeaders,\n  PageRange,\n};\n\nexport {\n  AccessPolicy,\n  AccessTier,\n  AccountKind,\n  ArchiveStatus,\n  BlobImmutabilityPolicyMode,\n  BlobCopySourceTags,\n  BlobDownloadOptionalParams,\n  BlobPropertiesInternal as BlobProperties,\n  BlobHttpHeaders as BlobHTTPHeaders,\n  BlobType,\n  Block,\n  BlockList,\n  BlockListType,\n  BlobServiceProperties,\n  BlobServiceStatistics,\n  BlobTag,\n  ContainerFilterBlobsResponse,\n  ContainerFilterBlobsHeaders,\n  ContainerGetAccessPolicyResponse as ContainerGetAccessPolicyResponseModel,\n  ContainerBreakLeaseOptionalParams,\n  ContainerProperties,\n  CopyStatusType,\n  CorsRule,\n  ClearRange,\n  CpkInfo,\n  DeleteSnapshotsOptionType,\n  EncryptionAlgorithmType,\n  GeoReplication,\n  GeoReplicationStatusType,\n  LeaseAccessConditions,\n  LeaseDurationType,\n  LeaseStateType,\n  LeaseStatusType,\n  ListContainersSegmentResponse,\n  FilterBlobItem as FilterBlobItemModel,\n  FilterBlobSegment as FilterBlobSegmentModel,\n  ServiceFilterBlobsHeaders,\n  Logging,\n  Metrics,\n  ModifiedAccessConditions as ModifiedAccessConditionsModel,\n  PublicAccessType,\n  SequenceNumberActionType,\n  RehydratePriority,\n  RetentionPolicy,\n  AppendPositionAccessConditions,\n  SequenceNumberAccessConditions,\n  SkuName,\n  StaticWebsite,\n  ContainerItem,\n  ServiceSubmitBatchOptionalParams as ServiceSubmitBatchOptionalParamsModel,\n  ContainerEncryptionScope,\n  SyncCopyStatusType,\n} from \"./generated/src/models\";\n\n// Following definitions are to avoid breaking change.\nexport interface BlobPrefix {\n  name: string;\n}\n\n/** An enumeration of blobs */\nexport interface ListBlobsFlatSegmentResponseModel {\n  serviceEndpoint: string;\n  containerName: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  segment: BlobFlatListSegmentModel;\n  continuationToken?: string;\n}\n\nexport interface BlobFlatListSegmentModel {\n  blobItems: BlobItemInternal[];\n}\n\n/** An enumeration of blobs */\nexport interface ListBlobsHierarchySegmentResponseModel {\n  serviceEndpoint: string;\n  containerName: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  delimiter?: string;\n  segment: BlobHierarchyListSegmentModel;\n  continuationToken?: string;\n}\n\nexport interface BlobHierarchyListSegmentModel {\n  blobPrefixes?: BlobPrefix[];\n  blobItems: BlobItemInternal[];\n}\n\n/** An Azure Storage blob */\nexport interface BlobItemInternal {\n  name: string;\n  deleted: boolean;\n  snapshot: string;\n  versionId?: string;\n  isCurrentVersion?: boolean;\n  /** Properties of a blob */\n  properties: BlobProperties;\n  /** Dictionary of <string> */\n  metadata?: { [propertyName: string]: string };\n  /** Blob tags */\n  blobTags?: BlobTags;\n  /** Dictionary of <string> */\n  objectReplicationMetadata?: { [propertyName: string]: string };\n  /** Inactive root blobs which have any versions would have such tag with value true. */\n  hasVersionsOnly?: boolean;\n}\n\n/**\n * Blob info from a {@link BlobServiceClient.findBlobsByTags}\n */\nexport interface FilterBlobItem {\n  /**\n   * Blob Name.\n   */\n  name: string;\n\n  /**\n   * Container Name.\n   */\n  containerName: string;\n\n  /**\n   * Blob Tags.\n   */\n  tags?: Tags;\n\n  /**\n   * Tag value.\n   *\n   * @deprecated The service no longer returns this value. Use {@link tags} to fetch all matching Blob Tags.\n   */\n  tagValue: string;\n}\n\n/**\n * Segment response of {@link BlobServiceClient.findBlobsByTags} operation.\n */\nexport interface FilterBlobSegment {\n  serviceEndpoint: string;\n  where: string;\n  blobs: FilterBlobItem[];\n  continuationToken?: string;\n}\n\nexport interface PageRangeInfo {\n  start: number;\n  end: number;\n  isClear: boolean;\n}\n\n/** Known values of {@link EncryptionAlgorithmType} that the service accepts. */\nexport enum KnownEncryptionAlgorithmType {\n  AES256 = \"AES256\",\n}\n"]}