{"version": 3, "file": "RequestPolicy.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/policies/RequestPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAUlC;;GAEG;AACH,MAAM,OAAgB,iBAAiB;IACrC;;OAEG;IACH;IACE;;OAEG;IACM,WAA0B;IACnC;;OAEG;IACM,QAAkC;QAJlC,gBAAW,GAAX,WAAW,CAAe;QAI1B,aAAQ,GAAR,QAAQ,CAA0B;IAC1C,CAAC;IAQJ;;;;OAIG;IACI,SAAS,CAAC,QAA8B;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,QAA8B,EAAE,OAAe;QACxD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  HttpPipelineLogLevel,\n  RequestPolicy,\n  RequestPolicyOptionsLike,\n  WebResourceLike,\n  CompatResponse as HttpOperationResponse,\n} from \"@azure/core-http-compat\";\n\n/**\n * The base class from which all request policies derive.\n */\nexport abstract class BaseRequestPolicy implements RequestPolicy {\n  /**\n   * The main method to implement that manipulates a request/response.\n   */\n  protected constructor(\n    /**\n     * The next policy in the pipeline. Each policy is responsible for executing the next one if the request is to continue through the pipeline.\n     */\n    readonly _nextPolicy: RequestPolicy,\n    /**\n     * The options that can be passed to a given request policy.\n     */\n    readonly _options: RequestPolicyOptionsLike,\n  ) {}\n\n  /**\n   * Sends a network request based on the given web resource.\n   * @param webResource - A {@link WebResourceLike} that describes a HTTP request to be made.\n   */\n  public abstract sendRequest(webResource: WebResourceLike): Promise<HttpOperationResponse>;\n\n  /**\n   * Get whether or not a log with the provided log level should be logged.\n   * @param logLevel - The log level of the log that will be logged.\n   * @returns Whether or not a log with the provided log level should be logged.\n   */\n  public shouldLog(logLevel: HttpPipelineLogLevel): boolean {\n    return this._options.shouldLog(logLevel);\n  }\n\n  /**\n   * Attempt to log the provided message to the provided logger. If no logger was provided or if\n   * the log level does not meat the logger's threshold, then nothing will be logged.\n   * @param logLevel - The log level of this log.\n   * @param message - The message of this log.\n   */\n  public log(logLevel: HttpPipelineLogLevel, message: string): void {\n    this._options.log(logLevel, message);\n  }\n}\n"]}