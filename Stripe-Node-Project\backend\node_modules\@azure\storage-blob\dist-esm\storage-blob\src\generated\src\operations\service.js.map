{"version": 3, "file": "service.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/service.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AAEjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAuBnD,2CAA2C;AAC3C,MAAM,OAAO,WAAW;IAGtB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;OAKG;IACH,aAAa,CACX,qBAA4C,EAC5C,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,qBAAqB,EAAE,OAAO,EAAE,EAClC,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,aAAa,CACX,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,qBAAqB,CACnB,OAAoD;QAEpD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,kCAAkC,CACnC,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,oBAAoB,CAClB,OAAgB,EAChB,OAAmD;QAEnD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,OAAO,EAAE,EACpB,iCAAiC,CAClC,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,cAAc,CACZ,OAA6C;QAE7C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,2BAA2B,CAC5B,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,WAAW,CACT,aAAqB,EACrB,oBAA4B,EAC5B,IAAsC,EACtC,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,oBAAoB,EAAE,IAAI,EAAE,OAAO,EAAE,EACtD,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,WAAW,CACT,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,wBAAwB,CACzB,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,qBAAqB;IAC7C,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;KAC5B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,qBAAqB;YACzC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;KAC5B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,qBAAqB;YACzC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,kCAAkC,GAA6B;IACnE,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,6BAA6B;YACjD,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4CAA4C;SACpE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,OAAO;KACnB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,iCAAiC,GAA6B;IAClE,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,iBAAiB;YACrC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,2CAA2C;SACnE;KACF;IACD,WAAW,EAAE,UAAU,CAAC,OAAO;IAC/B,eAAe,EAAE;QACf,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,2BAA2B,GAA6B;IAC5D,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gBACxB,cAAc,EAAE,gBAAgB;aACjC;YACD,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,IAAI;IAC5B,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,oBAAoB;KAChC;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,GAAG;IACT,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,iBAAiB;YACrC,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,KAAK;KACjB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { Service } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  BlobServiceProperties,\n  ServiceSetPropertiesOptionalParams,\n  ServiceSetPropertiesResponse,\n  ServiceGetPropertiesOptionalParams,\n  ServiceGetPropertiesResponse,\n  ServiceGetStatisticsOptionalParams,\n  ServiceGetStatisticsResponse,\n  ServiceListContainersSegmentOptionalParams,\n  ServiceListContainersSegmentResponse,\n  KeyInfo,\n  ServiceGetUserDelegationKeyOptionalParams,\n  ServiceGetUserDelegationKeyResponse,\n  ServiceGetAccountInfoOptionalParams,\n  ServiceGetAccountInfoResponse,\n  ServiceSubmitBatchOptionalParams,\n  ServiceSubmitBatchResponse,\n  ServiceFilterBlobsOptionalParams,\n  ServiceFilterBlobsResponse,\n} from \"../models\";\n\n/** Class containing Service operations. */\nexport class ServiceImpl implements Service {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class Service class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * Sets properties for a storage account's Blob service endpoint, including properties for Storage\n   * Analytics and CORS (Cross-Origin Resource Sharing) rules\n   * @param blobServiceProperties The StorageService properties.\n   * @param options The options parameters.\n   */\n  setProperties(\n    blobServiceProperties: BlobServiceProperties,\n    options?: ServiceSetPropertiesOptionalParams,\n  ): Promise<ServiceSetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { blobServiceProperties, options },\n      setPropertiesOperationSpec,\n    );\n  }\n\n  /**\n   * gets the properties of a storage account's Blob service, including properties for Storage Analytics\n   * and CORS (Cross-Origin Resource Sharing) rules.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: ServiceGetPropertiesOptionalParams,\n  ): Promise<ServiceGetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPropertiesOperationSpec,\n    );\n  }\n\n  /**\n   * Retrieves statistics related to replication for the Blob service. It is only available on the\n   * secondary location endpoint when read-access geo-redundant replication is enabled for the storage\n   * account.\n   * @param options The options parameters.\n   */\n  getStatistics(\n    options?: ServiceGetStatisticsOptionalParams,\n  ): Promise<ServiceGetStatisticsResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getStatisticsOperationSpec,\n    );\n  }\n\n  /**\n   * The List Containers Segment operation returns a list of the containers under the specified account\n   * @param options The options parameters.\n   */\n  listContainersSegment(\n    options?: ServiceListContainersSegmentOptionalParams,\n  ): Promise<ServiceListContainersSegmentResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      listContainersSegmentOperationSpec,\n    );\n  }\n\n  /**\n   * Retrieves a user delegation key for the Blob service. This is only a valid operation when using\n   * bearer token authentication.\n   * @param keyInfo Key information\n   * @param options The options parameters.\n   */\n  getUserDelegationKey(\n    keyInfo: KeyInfo,\n    options?: ServiceGetUserDelegationKeyOptionalParams,\n  ): Promise<ServiceGetUserDelegationKeyResponse> {\n    return this.client.sendOperationRequest(\n      { keyInfo, options },\n      getUserDelegationKeyOperationSpec,\n    );\n  }\n\n  /**\n   * Returns the sku name and account kind\n   * @param options The options parameters.\n   */\n  getAccountInfo(\n    options?: ServiceGetAccountInfoOptionalParams,\n  ): Promise<ServiceGetAccountInfoResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getAccountInfoOperationSpec,\n    );\n  }\n\n  /**\n   * The Batch operation allows multiple API calls to be embedded into a single HTTP request.\n   * @param contentLength The length of the request.\n   * @param multipartContentType Required. The value of this header must be multipart/mixed with a batch\n   *                             boundary. Example header value: multipart/mixed; boundary=batch_<GUID>\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  submitBatch(\n    contentLength: number,\n    multipartContentType: string,\n    body: coreRestPipeline.RequestBodyType,\n    options?: ServiceSubmitBatchOptionalParams,\n  ): Promise<ServiceSubmitBatchResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, multipartContentType, body, options },\n      submitBatchOperationSpec,\n    );\n  }\n\n  /**\n   * The Filter Blobs operation enables callers to list blobs across all containers whose tags match a\n   * given search expression.  Filter blobs searches across all containers within a storage account but\n   * can be scoped within the expression to a single container.\n   * @param options The options parameters.\n   */\n  filterBlobs(\n    options?: ServiceFilterBlobsOptionalParams,\n  ): Promise<ServiceFilterBlobsResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      filterBlobsOperationSpec,\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst setPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.ServiceSetPropertiesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceSetPropertiesExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.blobServiceProperties,\n  queryParameters: [\n    Parameters.restype,\n    Parameters.comp,\n    Parameters.timeoutInSeconds,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst getPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.BlobServiceProperties,\n      headersMapper: Mappers.ServiceGetPropertiesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceGetPropertiesExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.restype,\n    Parameters.comp,\n    Parameters.timeoutInSeconds,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getStatisticsOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.BlobServiceStatistics,\n      headersMapper: Mappers.ServiceGetStatisticsHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceGetStatisticsExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.restype,\n    Parameters.timeoutInSeconds,\n    Parameters.comp1,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst listContainersSegmentOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.ListContainersSegmentResponse,\n      headersMapper: Mappers.ServiceListContainersSegmentHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceListContainersSegmentExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.comp2,\n    Parameters.prefix,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.include,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getUserDelegationKeyOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"POST\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.UserDelegationKey,\n      headersMapper: Mappers.ServiceGetUserDelegationKeyHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceGetUserDelegationKeyExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.keyInfo,\n  queryParameters: [\n    Parameters.restype,\n    Parameters.timeoutInSeconds,\n    Parameters.comp3,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst getAccountInfoOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      headersMapper: Mappers.ServiceGetAccountInfoHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceGetAccountInfoExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.comp,\n    Parameters.timeoutInSeconds,\n    Parameters.restype1,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst submitBatchOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"POST\",\n  responses: {\n    202: {\n      bodyMapper: {\n        type: { name: \"Stream\" },\n        serializedName: \"parsedResponse\",\n      },\n      headersMapper: Mappers.ServiceSubmitBatchHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceSubmitBatchExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.body,\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp4],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.contentLength,\n    Parameters.multipartContentType,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer,\n};\nconst filterBlobsOperationSpec: coreClient.OperationSpec = {\n  path: \"/\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.FilterBlobSegment,\n      headersMapper: Mappers.ServiceFilterBlobsHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.ServiceFilterBlobsExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.comp5,\n    Parameters.where,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\n"]}