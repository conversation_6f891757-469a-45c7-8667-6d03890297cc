const express = require('express');
const crypto = require('crypto');
const { stripe, stripeConfig } = require('../config/stripe');
const { validate, schemas } = require('../middleware/validation');
const { asyncHandler, ValidationError, NotFoundError } = require('../middleware/errorHandler');
const { supabase, supabaseAdmin } = require('../config/database');
const commissionService = require('../services/commissionService');

const router = express.Router();

/**
 * Create payment intent with commission calculation
 * POST /api/payments/create-intent
 */
router.post('/create-intent',
  validate(schemas.createPaymentIntent),
  asyncHandler(async (req, res) => {
    const {
      amount,
      currency = 'usd',
      customer_email,
      customer_name,
      niche,
      vendor_id,
      order_id,
      metadata = {}
    } = req.body;

    // Get vendor information
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('*')
      .eq('id', vendor_id)
      .eq('is_active', true)
      .single();

    if (vendorError || !vendor) {
      throw new NotFoundError('Vendor not found or inactive');
    }

    // Calculate commission
    const commissionData = await commissionService.calculateCommission({
      amount,
      niche,
      vendor_id
    });

    const finalAmount = amount - commissionData.commission_amount;

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      automatic_payment_methods: stripeConfig.paymentIntent.automaticPaymentMethods,
      capture_method: stripeConfig.paymentIntent.captureMethod,
      confirmation_method: stripeConfig.paymentIntent.confirmationMethod,
      metadata: {
        vendor_id,
        order_id,
        niche,
        commission_rate: commissionData.commission_rate.toString(),
        commission_amount: commissionData.commission_amount.toString(),
        final_amount: finalAmount.toString(),
        wordpress_site: req.user.wordpress_site || '',
        ...metadata
      },
      receipt_email: customer_email,
      description: `Payment for order ${order_id} - ${vendor.name}`,
      // For Stripe Connect, we'll handle transfers after successful payment
      transfer_data: vendor.stripe_account_id ? {
        destination: vendor.stripe_account_id,
        amount: Math.round(finalAmount * 100) // Amount after commission
      } : undefined
    });

    // Store transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        payment_intent_id: paymentIntent.id,
        vendor_id,
        order_id,
        amount,
        commission_rate: commissionData.commission_rate,
        commission_amount: commissionData.commission_amount,
        final_amount: finalAmount,
        currency,
        niche,
        customer_email,
        customer_name,
        status: 'pending',
        metadata,
        created_at: new Date().toISOString()
      });

    if (transactionError) {
      console.error('Failed to store transaction:', transactionError);
      // Don't fail the payment creation, but log the error
    }

    res.status(201).json({
      success: true,
      message: 'Payment intent created successfully',
      data: {
        client_secret: paymentIntent.client_secret,
        payment_intent_id: paymentIntent.id,
        amount: amount,
        commission_amount: commissionData.commission_amount,
        final_amount: finalAmount,
        commission_rate: commissionData.commission_rate,
        currency,
        vendor: {
          id: vendor.id,
          name: vendor.name,
          niche: vendor.niche
        }
      }
    });
  })
);

/**
 * Get payment intent details
 * GET /api/payments/intent/:payment_intent_id
 */
router.get('/intent/:payment_intent_id',
  asyncHandler(async (req, res) => {
    const { payment_intent_id } = req.params;

    // Get payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(payment_intent_id);

    // Get transaction record
    const { data: transaction, error } = await supabase
      .from('transactions')
      .select(`
        *,
        vendors (
          id,
          name,
          niche,
          stripe_account_id
        )
      `)
      .eq('payment_intent_id', payment_intent_id)
      .single();

    if (error) {
      throw new NotFoundError('Transaction not found');
    }

    res.json({
      success: true,
      data: {
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount / 100,
          currency: paymentIntent.currency,
          client_secret: paymentIntent.client_secret
        },
        transaction,
        vendor: transaction.vendors
      }
    });
  })
);

/**
 * Confirm payment intent
 * POST /api/payments/confirm/:payment_intent_id
 */
router.post('/confirm/:payment_intent_id',
  asyncHandler(async (req, res) => {
    const { payment_intent_id } = req.params;
    const { payment_method_id } = req.body;

    if (!payment_method_id) {
      throw new ValidationError('Payment method ID is required');
    }

    // Confirm payment intent
    const paymentIntent = await stripe.paymentIntents.confirm(payment_intent_id, {
      payment_method: payment_method_id,
      return_url: req.body.return_url || `${process.env.API_BASE_URL}/payments/success`
    });

    // Update transaction status
    await supabase
      .from('transactions')
      .update({
        status: paymentIntent.status,
        payment_method_id,
        updated_at: new Date().toISOString()
      })
      .eq('payment_intent_id', payment_intent_id);

    res.json({
      success: true,
      message: 'Payment intent confirmed',
      data: {
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          client_secret: paymentIntent.client_secret
        }
      }
    });
  })
);

/**
 * Cancel payment intent
 * POST /api/payments/cancel/:payment_intent_id
 */
router.post('/cancel/:payment_intent_id',
  asyncHandler(async (req, res) => {
    const { payment_intent_id } = req.params;
    const { reason = 'requested_by_customer' } = req.body;

    // Cancel payment intent
    const paymentIntent = await stripe.paymentIntents.cancel(payment_intent_id, {
      cancellation_reason: reason
    });

    // Update transaction status
    await supabase
      .from('transactions')
      .update({
        status: 'canceled',
        cancellation_reason: reason,
        updated_at: new Date().toISOString()
      })
      .eq('payment_intent_id', payment_intent_id);

    res.json({
      success: true,
      message: 'Payment intent canceled',
      data: {
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.status
        }
      }
    });
  })
);

/**
 * Get transaction history
 * GET /api/payments/transactions
 */
router.get('/transactions',
  asyncHandler(async (req, res) => {
    const {
      vendor_id,
      status,
      niche,
      limit = 50,
      offset = 0,
      start_date,
      end_date
    } = req.query;

    let query = supabase
      .from('transactions')
      .select(`
        *,
        vendors (
          id,
          name,
          niche
        )
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }
    if (status) {
      query = query.eq('status', status);
    }
    if (niche) {
      query = query.eq('niche', niche);
    }
    if (start_date) {
      query = query.gte('created_at', start_date);
    }
    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    const { data: transactions, error } = await query;

    if (error) {
      throw error;
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('transactions')
      .select('*', { count: 'exact', head: true });

    if (vendor_id) countQuery = countQuery.eq('vendor_id', vendor_id);
    if (status) countQuery = countQuery.eq('status', status);
    if (niche) countQuery = countQuery.eq('niche', niche);
    if (start_date) countQuery = countQuery.gte('created_at', start_date);
    if (end_date) countQuery = countQuery.lte('created_at', end_date);

    const { count, error: countError } = await countQuery;

    if (countError) {
      throw countError;
    }

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          total: count,
          limit: parseInt(limit),
          offset: parseInt(offset),
          has_more: count > offset + limit
        }
      }
    });
  })
);

/**
 * Process payment directly (for testing/simple integration)
 * POST /api/payments/process
 */
router.post('/process',
  validate(schemas.processPayment),
  asyncHandler(async (req, res) => {
    const {
      amount,
      currency = 'usd',
      description,
      customer_email,
      card,
      vendor_id
    } = req.body;

    try {
      // For testing, use a test payment method token instead of raw card data
      // In production, you would create the payment method on the frontend using Stripe.js
      let paymentMethod;

      if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
        // Use test payment method for development
        paymentMethod = { id: 'pm_card_visa' }; // Stripe test payment method
      } else {
        // Create payment method from card details (only in production with proper setup)
        paymentMethod = await stripe.paymentMethods.create({
          type: 'card',
          card: {
            number: card.number,
            exp_month: card.exp_month,
            exp_year: card.exp_year,
            cvc: card.cvc,
          },
        });
      }

      // Get vendor information for commission calculation
      const { data: vendor, error: vendorError } = await supabaseAdmin
        .from('vendors')
        .select('*')
        .eq('id', vendor_id)
        .single();

      if (vendorError || !vendor) {
        return res.status(400).json({
          error: 'Invalid vendor',
          message: 'Vendor not found or inactive'
        });
      }

      // Get commission rate based on vendor's niche
      const { data: commissionRateData, error: commissionError } = await supabaseAdmin
        .from('commission_rates')
        .select('rate')
        .eq('niche', vendor.niche)
        .eq('is_active', true)
        .single();

      // If no specific rate found, try default rate
      let commissionRate = 0.05; // Default 5%
      if (commissionRateData) {
        commissionRate = commissionRateData.rate;
      } else {
        const { data: defaultRate } = await supabaseAdmin
          .from('commission_rates')
          .select('rate')
          .eq('niche', 'default')
          .eq('is_active', true)
          .single();

        if (defaultRate) {
          commissionRate = defaultRate.rate;
        }
      }
      const commissionAmount = Math.round(amount * 100 * commissionRate);
      const netAmount = Math.round(amount * 100) - commissionAmount;

      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        payment_method: paymentMethod.id,
        confirmation_method: 'manual',
        confirm: true,
        return_url: `${process.env.API_BASE_URL || 'http://localhost:3000'}/payments/success`,
        description: `${description} (Vendor: ${vendor.name})`,
        metadata: {
          vendor_id,
          commission_amount: commissionAmount.toString(),
          net_amount: netAmount.toString(),
          customer_email
        }
      });

      // Record transaction in database
      const { data: transaction, error: transactionError } = await supabaseAdmin
        .from('transactions')
        .insert({
          id: crypto.randomUUID(),
          vendor_id,
          stripe_payment_intent_id: paymentIntent.id,
          amount: amount,
          commission_amount: commissionAmount / 100,
          net_amount: netAmount / 100,
          currency,
          status: paymentIntent.status,
          customer_email,
          description,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (transactionError) {
        console.error('Failed to record transaction:', transactionError);
      }

      res.status(200).json({
        success: true,
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency
        },
        transaction: transaction || null,
        commission: {
          rate: commissionRate,
          amount: commissionAmount / 100,
          net_amount: netAmount / 100
        }
      });

    } catch (error) {
      console.error('Payment processing error:', error);
      res.status(400).json({
        error: 'Payment failed',
        message: error.message
      });
    }
  })
);

module.exports = router;
