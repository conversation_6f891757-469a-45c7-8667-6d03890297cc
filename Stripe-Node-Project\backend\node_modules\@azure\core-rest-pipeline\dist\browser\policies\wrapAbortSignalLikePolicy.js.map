{"version": 3, "file": "wrapAbortSignalLikePolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/wrapAbortSignalLikePolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AAEjE,MAAM,CAAC,MAAM,6BAA6B,GAAG,2BAA2B,CAAC;AAEzE;;;;;GAKG;AACH,MAAM,UAAU,yBAAyB;IACvC,OAAO;QACL,IAAI,EAAE,6BAA6B;QACnC,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;YACnC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,mBAAmB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YAC1E,6CAA6C;YAC7C,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;YAClC,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;oBAAS,CAAC;gBACT,OAAO,aAAP,OAAO,uBAAP,OAAO,EAAI,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { type PipelinePolicy } from \"../pipeline.js\";\nimport { wrapAbortSignalLike } from \"../util/wrapAbortSignal.js\";\n\nexport const wrapAbortSignalLikePolicyName = \"wrapAbortSignalLikePolicy\";\n\n/**\n * Policy that ensure that any AbortSignalLike is wrapped in a native AbortSignal for processing by the pipeline.\n * Since the ts-http-runtime expects a native AbortSignal, this policy is used to ensure that any AbortSignalLike is wrapped in a native AbortSignal.\n *\n * @returns - created policy\n */\nexport function wrapAbortSignalLikePolicy(): PipelinePolicy {\n  return {\n    name: wrapAbortSignalLikePolicyName,\n    sendRequest: async (request, next) => {\n      if (!request.abortSignal) {\n        return next(request);\n      }\n\n      const { abortSignal, cleanup } = wrapAbortSignalLike(request.abortSignal);\n      // eslint-disable-next-line no-param-reassign\n      request.abortSignal = abortSignal;\n      try {\n        return await next(request);\n      } finally {\n        cleanup?.();\n      }\n    },\n  };\n}\n"]}