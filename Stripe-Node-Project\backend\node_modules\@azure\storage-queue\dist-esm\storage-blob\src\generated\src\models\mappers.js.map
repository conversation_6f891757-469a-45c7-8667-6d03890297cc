{"version": 3, "file": "mappers.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/models/mappers.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAIH,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,uBAAuB;IACvC,OAAO,EAAE,0BAA0B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,oBAAoB,EAAE;gBACpB,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,SAAS;iBACrB;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,SAAS;iBACrB;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,SAAS;iBACrB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,UAAU;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,UAAU;yBACtB;qBACF;iBACF;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,iBAAiB;iBAC7B;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,eAAe;iBAC3B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA+B;IACjD,cAAc,EAAE,SAAS;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,iBAAiB;iBAC7B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAA+B;IACzD,cAAc,EAAE,iBAAiB;IACjC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,IAAI,EAAE;gBACJ,WAAW,EAAE;oBACX,gBAAgB,EAAE,CAAC;iBACpB;gBACD,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA+B;IACjD,cAAc,EAAE,SAAS;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,iBAAiB;iBAC7B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA+B;IAClD,cAAc,EAAE,UAAU;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,UAAU;QACrB,eAAe,EAAE;YACf,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,WAAW,EAAE;oBACX,gBAAgB,EAAE,CAAC;iBACpB;gBACD,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAA+B;IACvD,cAAc,EAAE,eAAe;IAC/B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,eAAe;QAC1B,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,oBAAoB,EAAE;gBACpB,cAAc,EAAE,sBAAsB;gBACtC,OAAO,EAAE,sBAAsB;gBAC/B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,wBAAwB,EAAE;gBACxB,cAAc,EAAE,0BAA0B;gBAC1C,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,cAAc;QACzB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,yBAAyB,EAAE;gBACzB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,uBAAuB;IACvC,OAAO,EAAE,qBAAqB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,gBAAgB;iBAC5B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAA+B;IACxD,cAAc,EAAE,gBAAgB;IAChC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gBAAgB;QAC3B,eAAe,EAAE;YACf,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC;iBACpD;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,6BAA6B,GAA+B;IACvE,cAAc,EAAE,+BAA+B;IAC/C,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,+BAA+B;QAC1C,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,YAAY;gBACrB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,WAAW;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,eAAe;yBAC3B;qBACF;iBACF;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAA+B;IACvD,cAAc,EAAE,eAAe;IAC/B,OAAO,EAAE,WAAW;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,eAAe;QAC1B,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,qBAAqB;iBACjC;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAA+B;IAC7D,cAAc,EAAE,qBAAqB;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qBAAqB;QAChC,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtC;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,QAAQ;qBACT;iBACF;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;iBACrC;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;iBACrC;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,8BAA8B,EAAE;gBAC9B,cAAc,EAAE,6BAA6B;gBAC7C,OAAO,EAAE,6BAA6B;gBACtC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,uCAAuC,EAAE;gBACvC,cAAc,EAAE,uCAAuC;gBACvD,OAAO,EAAE,uCAAuC;gBAChD,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA+B;IACjD,cAAc,EAAE,SAAS;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE;YACf,QAAQ,EAAE;gBACR,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA+B;IAC3D,cAAc,EAAE,mBAAmB;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mBAAmB;QAC9B,eAAe,EAAE;YACf,cAAc,EAAE;gBACd,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,aAAa;gBAC7B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA+B;IAC3D,cAAc,EAAE,mBAAmB;IACnC,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mBAAmB;QAC9B,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,gBAAgB;yBAC5B;qBACF;iBACF;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAA+B;IACxD,cAAc,EAAE,gBAAgB;IAChC,OAAO,EAAE,MAAM;IACf,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gBAAgB;QAC3B,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,UAAU;iBACtB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA+B;IAClD,cAAc,EAAE,UAAU;IAC1B,OAAO,EAAE,MAAM;IACf,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,UAAU;QACrB,eAAe,EAAE;YACf,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,KAAK;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,SAAS;yBACrB;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA+B;IACjD,cAAc,EAAE,SAAS;IACzB,OAAO,EAAE,KAAK;IACd,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE;YACf,GAAG,EAAE;gBACH,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA+B;IAC1D,cAAc,EAAE,kBAAkB;IAClC,OAAO,EAAE,kBAAkB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kBAAkB;QAC7B,eAAe,EAAE;YACf,EAAE,EAAE;gBACF,cAAc,EAAE,IAAI;gBACpB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,cAAc;iBAC1B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,cAAc;QACzB,eAAe,EAAE;YACf,QAAQ,EAAE;gBACR,cAAc,EAAE,OAAO;gBACvB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,8BAA8B;IAC9C,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,qBAAqB;iBACjC;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAA+B;IAC7D,cAAc,EAAE,qBAAqB;IACrC,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qBAAqB;QAChC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,kBAAkB;yBAC9B;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA+B;IAC1D,cAAc,EAAE,kBAAkB;IAClC,OAAO,EAAE,MAAM;IACf,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kBAAkB;QAC7B,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,UAAU;iBACtB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,wBAAwB;iBACpC;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,UAAU;iBACtB;aACF;YACD,yBAAyB,EAAE;gBACzB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA+B;IAClD,cAAc,EAAE,UAAU;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,UAAU;QACrB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,WAAW,EAAE,IAAI;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,wBAAwB;IACxC,OAAO,EAAE,YAAY;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC;iBACvD;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtC;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,QAAQ;qBACT;iBACF;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;iBACrC;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;iBAC3D;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,IAAI;wBACJ,IAAI;wBACJ,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,SAAS;wBACT,MAAM;qBACP;iBACF;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,0BAA0B;wBAC1B,2BAA2B;wBAC3B,2BAA2B;qBAC5B;iBACF;aACF;YACD,yBAAyB,EAAE;gBACzB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,sBAAsB;gBACtC,OAAO,EAAE,sBAAsB;gBAC/B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;iBACpC;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,2BAA2B,EAAE;gBAC3B,cAAc,EAAE,6BAA6B;gBAC7C,OAAO,EAAE,6BAA6B;gBACtC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;iBACjD;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iCAAiC,GAA+B;IAC3E,cAAc,EAAE,mCAAmC;IACnD,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mCAAmC;QAC9C,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,0BAA0B;iBACtC;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAA+B;IAClE,cAAc,EAAE,0BAA0B;IAC1C,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0BAA0B;QACrC,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,cAAc,EAAE,YAAY;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,YAAY;yBACxB;qBACF;iBACF;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,cAAc,EAAE,MAAM;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,kBAAkB;yBAC9B;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA+B;IACpD,cAAc,EAAE,YAAY;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,YAAY;QACvB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,UAAU;iBACtB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAA+B;IACzD,cAAc,EAAE,iBAAiB;IACjC,OAAO,EAAE,WAAW;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,OAAO,EAAE,WAAW;gBACpB,cAAc,EAAE,WAAW;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,cAAc,EAAE,aAAa;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,cAAc,EAAE,QAAQ;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAA+B;IACnD,cAAc,EAAE,WAAW;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,WAAW;QACtB,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,OAAO;yBACnB;qBACF;iBACF;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,OAAO;yBACnB;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,KAAK,GAA+B;IAC/C,cAAc,EAAE,OAAO;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,OAAO;QAClB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA+B;IAClD,cAAc,EAAE,UAAU;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,UAAU;QACrB,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,OAAO,EAAE,WAAW;gBACpB,cAAc,EAAE,WAAW;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,WAAW;yBACvB;qBACF;iBACF;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,cAAc,EAAE,YAAY;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,YAAY;yBACxB;qBACF;iBACF;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAA+B;IACnD,cAAc,EAAE,WAAW;IAC3B,OAAO,EAAE,WAAW;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,WAAW;QACtB,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,GAAG,EAAE;gBACH,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA+B;IACpD,cAAc,EAAE,YAAY;IAC5B,OAAO,EAAE,YAAY;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,YAAY;QACvB,eAAe,EAAE;YACf,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,GAAG,EAAE;gBACH,cAAc,EAAE,KAAK;gBACrB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,cAAc,EAAE,cAAc;IAC9B,OAAO,EAAE,cAAc;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,cAAc;QACzB,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,oBAAoB;iBAChC;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,oBAAoB;iBAChC;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,oBAAoB;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,aAAa;iBACzB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,WAAW,GAA+B;IACrD,cAAc,EAAE,aAAa;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,aAAa;QACxB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC;iBACzD;aACF;YACD,0BAA0B,EAAE;gBAC1B,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,4BAA4B;iBACxC;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,uBAAuB;iBACnC;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,oBAAoB;iBAChC;aACF;YACD,wBAAwB,EAAE;gBACxB,cAAc,EAAE,0BAA0B;gBAC1C,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;iBACjC;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAA+B;IACpE,cAAc,EAAE,4BAA4B;IAC5C,OAAO,EAAE,4BAA4B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,uBAAuB;IACvC,OAAO,EAAE,uBAAuB;IAChC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,oBAAoB;IACpC,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,YAAY;yBACxB;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,UAAU,GAA+B;IACpD,cAAc,EAAE,YAAY;IAC5B,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,YAAY;QACvB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4CAA4C,GACvD;IACE,cAAc,EAAE,+CAA+C;IAC/D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8CAA8C;QACzD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2CAA2C,GACtD;IACE,cAAc,EAAE,8CAA8C;IAC9D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6CAA6C;QACxD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,cAAc;wBACd,cAAc;wBACd,gBAAgB;wBAChB,cAAc;wBACd,aAAa;qBACd;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,SAAS;wBACT,aAAa;wBACb,WAAW;wBACX,aAAa;wBACb,kBAAkB;qBACnB;iBACF;aACF;YACD,8BAA8B,EAAE;gBAC9B,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAChD;IACE,cAAc,EAAE,wCAAwC;IACxD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,6BAA6B,GAA+B;IACvE,cAAc,EAAE,gCAAgC;IAChD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,+BAA+B;QAC1C,eAAe,EAAE;YACf,QAAQ,EAAE;gBACR,cAAc,EAAE,WAAW;gBAC3B,sBAAsB,EAAE,YAAY;gBACpC,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;iBACrC;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,QAAQ;qBACT;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtC;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;iBACrC;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,8BAA8B;gBAC9C,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,2BAA2B,EAAE;gBAC3B,cAAc,EAAE,qCAAqC;gBACrD,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,uCAAuC,EAAE;gBACvC,cAAc,EAAE,gDAAgD;gBAChE,OAAO,EAAE,gDAAgD;gBACzD,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sCAAsC,GACjD;IACE,cAAc,EAAE,yCAAyC;IACzD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wCAAwC;QACnD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,gBAAgB,EAAE;gBAChB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,MAAM,CAAC;iBACrC;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wCAAwC,GACnD;IACE,cAAc,EAAE,2CAA2C;IAC3D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0CAA0C;QACrD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wCAAwC,GACnD;IACE,cAAc,EAAE,2CAA2C;IAC3D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0CAA0C;QACrD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,uBAAuB,GAA+B;IACjE,cAAc,EAAE,0BAA0B;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yBAAyB;QACpC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAChD;IACE,cAAc,EAAE,wCAAwC;IACxD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAChD;IACE,cAAc,EAAE,wCAAwC;IACxD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,0BAA0B,GAA+B;IACpE,cAAc,EAAE,6BAA6B;IAC7C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAA+B;IACpE,cAAc,EAAE,6BAA6B;IAC7C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4CAA4C,GACvD;IACE,cAAc,EAAE,+CAA+C;IAC/D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8CAA8C;QACzD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,wCAAwC,GACnD;IACE,cAAc,EAAE,2CAA2C;IAC3D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0CAA0C;QACrD,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,iDAAiD,GAC5D;IACE,cAAc,EAAE,oDAAoD;IACpE,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mDAAmD;QAC9D,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,cAAc;wBACd,cAAc;wBACd,gBAAgB;wBAChB,cAAc;wBACd,aAAa;qBACd;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,SAAS;wBACT,aAAa;wBACb,WAAW;wBACX,aAAa;wBACb,kBAAkB;qBACnB;iBACF;aACF;YACD,8BAA8B,EAAE;gBAC9B,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uCAAuC,GAClD;IACE,cAAc,EAAE,0CAA0C;IAC1D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yCAAyC;QACpD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,mBAAmB,GAA+B;IAC7D,cAAc,EAAE,sBAAsB;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qBAAqB;QAChC,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,WAAW;gBAC3B,sBAAsB,EAAE,YAAY;gBACpC,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,yBAAyB,EAAE;gBACzB,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,SAAS;gBACzB,sBAAsB,EAAE,UAAU;gBAClC,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC;iBACvD;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,8BAA8B;gBAC9C,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;iBAC3D;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;iBACrC;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,QAAQ;qBACT;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtC;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,uBAAuB,EAAE;gBACvB,cAAc,EAAE,iCAAiC;gBACjD,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,2BAA2B,EAAE;gBAC3B,cAAc,EAAE,qCAAqC;gBACrD,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;iBACjD;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wBAAwB,GAA+B;IAClE,cAAc,EAAE,2BAA2B;IAC3C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0BAA0B;QACrC,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,WAAW;gBAC3B,sBAAsB,EAAE,YAAY;gBACpC,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,yBAAyB,EAAE;gBACzB,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,SAAS;gBACzB,sBAAsB,EAAE,UAAU;gBAClC,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC;iBACvD;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,8BAA8B;gBAC9C,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;iBAC3D;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,gCAAgC;gBAChD,OAAO,EAAE,gCAAgC;gBACzC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;iBACrC;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,QAAQ;qBACT;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtC;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,uBAAuB,EAAE;gBACvB,cAAc,EAAE,iCAAiC;gBACjD,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,8BAA8B;gBAC9C,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;iBACpC;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,2BAA2B,EAAE;gBAC3B,cAAc,EAAE,qCAAqC;gBACrD,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;iBACjD;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iCAAiC,GAA+B;IAC3E,cAAc,EAAE,oCAAoC;IACpD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mCAAmC;QAC9C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA+B;IAC3D,cAAc,EAAE,oBAAoB;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mBAAmB;QAC9B,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAA+B;IACpE,cAAc,EAAE,6BAA6B;IAC7C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAA+B;IAC7D,cAAc,EAAE,sBAAsB;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qBAAqB;QAChC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAA+B;IAC9D,cAAc,EAAE,uBAAuB;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sBAAsB;QACjC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,6BAA6B,GAA+B;IACvE,cAAc,EAAE,gCAAgC;IAChD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,+BAA+B;QAC1C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,wBAAwB,EAAE;gBACxB,cAAc,EAAE,qCAAqC;gBACrD,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,sBAAsB,EAAE;gBACtB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;iBACjD;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yCAAyC,GACpD;IACE,cAAc,EAAE,4CAA4C;IAC5D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2CAA2C;QACtD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4CAA4C,GACvD;IACE,cAAc,EAAE,+CAA+C;IAC/D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8CAA8C;QACzD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,uBAAuB,GAA+B;IACjE,cAAc,EAAE,0BAA0B;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yBAAyB;QACpC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAA+B;IACjE,cAAc,EAAE,0BAA0B;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yBAAyB;QACpC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAA+B;IACjE,cAAc,EAAE,0BAA0B;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yBAAyB;QACpC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,wBAAwB;IACxC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,wBAAwB;IACxC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,QAAQ,EAAE;gBACR,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;iBAC3D;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,YAAY,EAAE,SAAS;gBACvB,UAAU,EAAE,IAAI;gBAChB,cAAc,EAAE,kBAAkB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAC/C;IACE,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,qBAAqB;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,cAAc;wBACd,cAAc;wBACd,gBAAgB;wBAChB,cAAc;wBACd,aAAa;qBACd;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,SAAS;wBACT,aAAa;wBACb,WAAW;wBACX,aAAa;wBACb,kBAAkB;qBACnB;iBACF;aACF;YACD,8BAA8B,EAAE;gBAC9B,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA+B;IAC1D,cAAc,EAAE,mBAAmB;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kBAAkB;QAC7B,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,WAAW;gBAC3B,sBAAsB,EAAE,YAAY;gBACpC,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,YAAY,CAAC;iBACvD;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,qBAAqB,EAAE;gBACrB,cAAc,EAAE,8BAA8B;gBAC9C,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;iBAC3D;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,qBAAqB;gBAC9B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC;iBACrC;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE;wBACb,WAAW;wBACX,QAAQ;wBACR,SAAS;wBACT,UAAU;wBACV,QAAQ;qBACT;iBACF;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC;iBACtC;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,uBAAuB,EAAE;gBACvB,cAAc,EAAE,iCAAiC;gBACjD,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,qBAAqB;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,qBAAqB;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,wBAAwB;IACxC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0BAA0B,GAA+B;IACpE,cAAc,EAAE,6BAA6B;IAC7C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iCAAiC,GAA+B;IAC3E,cAAc,EAAE,oCAAoC;IACpD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mCAAmC;QAC9C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0CAA0C,GACrD;IACE,cAAc,EAAE,6CAA6C;IAC7D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4CAA4C;QACvD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,0BAA0B;gBAC1C,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAChD;IACE,cAAc,EAAE,wCAAwC;IACxD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,0BAA0B;gBAC1C,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yCAAyC,GACpD;IACE,cAAc,EAAE,4CAA4C;IAC5D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2CAA2C;QACtD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,wBAAwB;IACxC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,kBAAkB,EAAE;gBAClB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4CAA4C,GACvD;IACE,cAAc,EAAE,+CAA+C;IAC/D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8CAA8C;QACzD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;iBAC3D;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uCAAuC,GAClD;IACE,cAAc,EAAE,0CAA0C;IAC1D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yCAAyC;QACpD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,uBAAuB,GAA+B;IACjE,cAAc,EAAE,0BAA0B;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yBAAyB;QACpC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,uBAAuB,EAAE;gBACvB,cAAc,EAAE,iCAAiC;gBACjD,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAChD;IACE,cAAc,EAAE,wCAAwC;IACxD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,gBAAgB,EAAE;gBAChB,cAAc,EAAE,yBAAyB;gBACzC,OAAO,EAAE,yBAAyB;gBAClC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,uBAAuB,EAAE;gBACvB,cAAc,EAAE,iCAAiC;gBACjD,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4CAA4C,GACvD;IACE,cAAc,EAAE,+CAA+C;IAC/D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8CAA8C;QACzD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,qBAAqB,GAA+B;IAC/D,cAAc,EAAE,wBAAwB;IACxC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uBAAuB;QAClC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,kBAAkB;gBAClC,OAAO,EAAE,kBAAkB;gBAC3B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,8BAA8B,GAA+B;IACxE,cAAc,EAAE,iCAAiC;IACjD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gCAAgC;QAC3C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uCAAuC,GAClD;IACE,cAAc,EAAE,0CAA0C;IAC1D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yCAAyC;QACpD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,0BAA0B,GAA+B;IACpE,cAAc,EAAE,6BAA6B;IAC7C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4BAA4B;QACvC,eAAe,EAAE;YACf,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mCAAmC,GAA+B;IAC7E,cAAc,EAAE,sCAAsC;IACtD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qCAAqC;QAChD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iCAAiC,GAA+B;IAC3E,cAAc,EAAE,oCAAoC;IACpD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mCAAmC;QAC9C,eAAe,EAAE;YACf,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,0CAA0C,GACrD;IACE,cAAc,EAAE,6CAA6C;IAC7D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,4CAA4C;QACvD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,oBAAoB;gBAC7B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;iBAClB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,+BAA+B;gBAC/C,OAAO,EAAE,+BAA+B;gBACxC,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,mBAAmB,EAAE;gBACnB,cAAc,EAAE,4BAA4B;gBAC5C,OAAO,EAAE,4BAA4B;gBACrC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,uBAAuB;gBACvC,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wCAAwC,GACnD;IACE,cAAc,EAAE,2CAA2C;IAC3D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0CAA0C;QACrD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEJ,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,YAAY,EAAE;gBACZ,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,0BAA0B;gBAC1C,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,qCAAqC,GAChD;IACE,cAAc,EAAE,wCAAwC;IACxD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,uCAAuC;QAClD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreClient from \"@azure/core-client\";\n\nexport const BlobServiceProperties: coreClient.CompositeMapper = {\n  serializedName: \"BlobServiceProperties\",\n  xmlName: \"StorageServiceProperties\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobServiceProperties\",\n    modelProperties: {\n      blobAnalyticsLogging: {\n        serializedName: \"Logging\",\n        xmlName: \"Logging\",\n        type: {\n          name: \"Composite\",\n          className: \"Logging\",\n        },\n      },\n      hourMetrics: {\n        serializedName: \"HourMetrics\",\n        xmlName: \"HourMetrics\",\n        type: {\n          name: \"Composite\",\n          className: \"Metrics\",\n        },\n      },\n      minuteMetrics: {\n        serializedName: \"MinuteMetrics\",\n        xmlName: \"MinuteMetrics\",\n        type: {\n          name: \"Composite\",\n          className: \"Metrics\",\n        },\n      },\n      cors: {\n        serializedName: \"Cors\",\n        xmlName: \"Cors\",\n        xmlIsWrapped: true,\n        xmlElementName: \"CorsRule\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"CorsRule\",\n            },\n          },\n        },\n      },\n      defaultServiceVersion: {\n        serializedName: \"DefaultServiceVersion\",\n        xmlName: \"DefaultServiceVersion\",\n        type: {\n          name: \"String\",\n        },\n      },\n      deleteRetentionPolicy: {\n        serializedName: \"DeleteRetentionPolicy\",\n        xmlName: \"DeleteRetentionPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"RetentionPolicy\",\n        },\n      },\n      staticWebsite: {\n        serializedName: \"StaticWebsite\",\n        xmlName: \"StaticWebsite\",\n        type: {\n          name: \"Composite\",\n          className: \"StaticWebsite\",\n        },\n      },\n    },\n  },\n};\n\nexport const Logging: coreClient.CompositeMapper = {\n  serializedName: \"Logging\",\n  type: {\n    name: \"Composite\",\n    className: \"Logging\",\n    modelProperties: {\n      version: {\n        serializedName: \"Version\",\n        required: true,\n        xmlName: \"Version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      deleteProperty: {\n        serializedName: \"Delete\",\n        required: true,\n        xmlName: \"Delete\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      read: {\n        serializedName: \"Read\",\n        required: true,\n        xmlName: \"Read\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      write: {\n        serializedName: \"Write\",\n        required: true,\n        xmlName: \"Write\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      retentionPolicy: {\n        serializedName: \"RetentionPolicy\",\n        xmlName: \"RetentionPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"RetentionPolicy\",\n        },\n      },\n    },\n  },\n};\n\nexport const RetentionPolicy: coreClient.CompositeMapper = {\n  serializedName: \"RetentionPolicy\",\n  type: {\n    name: \"Composite\",\n    className: \"RetentionPolicy\",\n    modelProperties: {\n      enabled: {\n        serializedName: \"Enabled\",\n        required: true,\n        xmlName: \"Enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      days: {\n        constraints: {\n          InclusiveMinimum: 1,\n        },\n        serializedName: \"Days\",\n        xmlName: \"Days\",\n        type: {\n          name: \"Number\",\n        },\n      },\n    },\n  },\n};\n\nexport const Metrics: coreClient.CompositeMapper = {\n  serializedName: \"Metrics\",\n  type: {\n    name: \"Composite\",\n    className: \"Metrics\",\n    modelProperties: {\n      version: {\n        serializedName: \"Version\",\n        xmlName: \"Version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      enabled: {\n        serializedName: \"Enabled\",\n        required: true,\n        xmlName: \"Enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      includeAPIs: {\n        serializedName: \"IncludeAPIs\",\n        xmlName: \"IncludeAPIs\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      retentionPolicy: {\n        serializedName: \"RetentionPolicy\",\n        xmlName: \"RetentionPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"RetentionPolicy\",\n        },\n      },\n    },\n  },\n};\n\nexport const CorsRule: coreClient.CompositeMapper = {\n  serializedName: \"CorsRule\",\n  type: {\n    name: \"Composite\",\n    className: \"CorsRule\",\n    modelProperties: {\n      allowedOrigins: {\n        serializedName: \"AllowedOrigins\",\n        required: true,\n        xmlName: \"AllowedOrigins\",\n        type: {\n          name: \"String\",\n        },\n      },\n      allowedMethods: {\n        serializedName: \"AllowedMethods\",\n        required: true,\n        xmlName: \"AllowedMethods\",\n        type: {\n          name: \"String\",\n        },\n      },\n      allowedHeaders: {\n        serializedName: \"AllowedHeaders\",\n        required: true,\n        xmlName: \"AllowedHeaders\",\n        type: {\n          name: \"String\",\n        },\n      },\n      exposedHeaders: {\n        serializedName: \"ExposedHeaders\",\n        required: true,\n        xmlName: \"ExposedHeaders\",\n        type: {\n          name: \"String\",\n        },\n      },\n      maxAgeInSeconds: {\n        constraints: {\n          InclusiveMinimum: 0,\n        },\n        serializedName: \"MaxAgeInSeconds\",\n        required: true,\n        xmlName: \"MaxAgeInSeconds\",\n        type: {\n          name: \"Number\",\n        },\n      },\n    },\n  },\n};\n\nexport const StaticWebsite: coreClient.CompositeMapper = {\n  serializedName: \"StaticWebsite\",\n  type: {\n    name: \"Composite\",\n    className: \"StaticWebsite\",\n    modelProperties: {\n      enabled: {\n        serializedName: \"Enabled\",\n        required: true,\n        xmlName: \"Enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      indexDocument: {\n        serializedName: \"IndexDocument\",\n        xmlName: \"IndexDocument\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorDocument404Path: {\n        serializedName: \"ErrorDocument404Path\",\n        xmlName: \"ErrorDocument404Path\",\n        type: {\n          name: \"String\",\n        },\n      },\n      defaultIndexDocumentPath: {\n        serializedName: \"DefaultIndexDocumentPath\",\n        xmlName: \"DefaultIndexDocumentPath\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const StorageError: coreClient.CompositeMapper = {\n  serializedName: \"StorageError\",\n  type: {\n    name: \"Composite\",\n    className: \"StorageError\",\n    modelProperties: {\n      message: {\n        serializedName: \"Message\",\n        xmlName: \"Message\",\n        type: {\n          name: \"String\",\n        },\n      },\n      code: {\n        serializedName: \"Code\",\n        xmlName: \"Code\",\n        type: {\n          name: \"String\",\n        },\n      },\n      authenticationErrorDetail: {\n        serializedName: \"AuthenticationErrorDetail\",\n        xmlName: \"AuthenticationErrorDetail\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobServiceStatistics: coreClient.CompositeMapper = {\n  serializedName: \"BlobServiceStatistics\",\n  xmlName: \"StorageServiceStats\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobServiceStatistics\",\n    modelProperties: {\n      geoReplication: {\n        serializedName: \"GeoReplication\",\n        xmlName: \"GeoReplication\",\n        type: {\n          name: \"Composite\",\n          className: \"GeoReplication\",\n        },\n      },\n    },\n  },\n};\n\nexport const GeoReplication: coreClient.CompositeMapper = {\n  serializedName: \"GeoReplication\",\n  type: {\n    name: \"Composite\",\n    className: \"GeoReplication\",\n    modelProperties: {\n      status: {\n        serializedName: \"Status\",\n        required: true,\n        xmlName: \"Status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"live\", \"bootstrap\", \"unavailable\"],\n        },\n      },\n      lastSyncOn: {\n        serializedName: \"LastSyncTime\",\n        required: true,\n        xmlName: \"LastSyncTime\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ListContainersSegmentResponse: coreClient.CompositeMapper = {\n  serializedName: \"ListContainersSegmentResponse\",\n  xmlName: \"EnumerationResults\",\n  type: {\n    name: \"Composite\",\n    className: \"ListContainersSegmentResponse\",\n    modelProperties: {\n      serviceEndpoint: {\n        serializedName: \"ServiceEndpoint\",\n        required: true,\n        xmlName: \"ServiceEndpoint\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\",\n        },\n      },\n      prefix: {\n        serializedName: \"Prefix\",\n        xmlName: \"Prefix\",\n        type: {\n          name: \"String\",\n        },\n      },\n      marker: {\n        serializedName: \"Marker\",\n        xmlName: \"Marker\",\n        type: {\n          name: \"String\",\n        },\n      },\n      maxPageSize: {\n        serializedName: \"MaxResults\",\n        xmlName: \"MaxResults\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      containerItems: {\n        serializedName: \"ContainerItems\",\n        required: true,\n        xmlName: \"Containers\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Container\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"ContainerItem\",\n            },\n          },\n        },\n      },\n      continuationToken: {\n        serializedName: \"NextMarker\",\n        xmlName: \"NextMarker\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerItem: coreClient.CompositeMapper = {\n  serializedName: \"ContainerItem\",\n  xmlName: \"Container\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerItem\",\n    modelProperties: {\n      name: {\n        serializedName: \"Name\",\n        required: true,\n        xmlName: \"Name\",\n        type: {\n          name: \"String\",\n        },\n      },\n      deleted: {\n        serializedName: \"Deleted\",\n        xmlName: \"Deleted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      version: {\n        serializedName: \"Version\",\n        xmlName: \"Version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      properties: {\n        serializedName: \"Properties\",\n        xmlName: \"Properties\",\n        type: {\n          name: \"Composite\",\n          className: \"ContainerProperties\",\n        },\n      },\n      metadata: {\n        serializedName: \"Metadata\",\n        xmlName: \"Metadata\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerProperties: coreClient.CompositeMapper = {\n  serializedName: \"ContainerProperties\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerProperties\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"Last-Modified\",\n        required: true,\n        xmlName: \"Last-Modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      etag: {\n        serializedName: \"Etag\",\n        required: true,\n        xmlName: \"Etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      leaseStatus: {\n        serializedName: \"LeaseStatus\",\n        xmlName: \"LeaseStatus\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"locked\", \"unlocked\"],\n        },\n      },\n      leaseState: {\n        serializedName: \"LeaseState\",\n        xmlName: \"LeaseState\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"available\",\n            \"leased\",\n            \"expired\",\n            \"breaking\",\n            \"broken\",\n          ],\n        },\n      },\n      leaseDuration: {\n        serializedName: \"LeaseDuration\",\n        xmlName: \"LeaseDuration\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"infinite\", \"fixed\"],\n        },\n      },\n      publicAccess: {\n        serializedName: \"PublicAccess\",\n        xmlName: \"PublicAccess\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"container\", \"blob\"],\n        },\n      },\n      hasImmutabilityPolicy: {\n        serializedName: \"HasImmutabilityPolicy\",\n        xmlName: \"HasImmutabilityPolicy\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      hasLegalHold: {\n        serializedName: \"HasLegalHold\",\n        xmlName: \"HasLegalHold\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      defaultEncryptionScope: {\n        serializedName: \"DefaultEncryptionScope\",\n        xmlName: \"DefaultEncryptionScope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      preventEncryptionScopeOverride: {\n        serializedName: \"DenyEncryptionScopeOverride\",\n        xmlName: \"DenyEncryptionScopeOverride\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      deletedOn: {\n        serializedName: \"DeletedTime\",\n        xmlName: \"DeletedTime\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      remainingRetentionDays: {\n        serializedName: \"RemainingRetentionDays\",\n        xmlName: \"RemainingRetentionDays\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      isImmutableStorageWithVersioningEnabled: {\n        serializedName: \"ImmutableStorageWithVersioningEnabled\",\n        xmlName: \"ImmutableStorageWithVersioningEnabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const KeyInfo: coreClient.CompositeMapper = {\n  serializedName: \"KeyInfo\",\n  type: {\n    name: \"Composite\",\n    className: \"KeyInfo\",\n    modelProperties: {\n      startsOn: {\n        serializedName: \"Start\",\n        required: true,\n        xmlName: \"Start\",\n        type: {\n          name: \"String\",\n        },\n      },\n      expiresOn: {\n        serializedName: \"Expiry\",\n        required: true,\n        xmlName: \"Expiry\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const UserDelegationKey: coreClient.CompositeMapper = {\n  serializedName: \"UserDelegationKey\",\n  type: {\n    name: \"Composite\",\n    className: \"UserDelegationKey\",\n    modelProperties: {\n      signedObjectId: {\n        serializedName: \"SignedOid\",\n        required: true,\n        xmlName: \"SignedOid\",\n        type: {\n          name: \"String\",\n        },\n      },\n      signedTenantId: {\n        serializedName: \"SignedTid\",\n        required: true,\n        xmlName: \"SignedTid\",\n        type: {\n          name: \"String\",\n        },\n      },\n      signedStartsOn: {\n        serializedName: \"SignedStart\",\n        required: true,\n        xmlName: \"SignedStart\",\n        type: {\n          name: \"String\",\n        },\n      },\n      signedExpiresOn: {\n        serializedName: \"SignedExpiry\",\n        required: true,\n        xmlName: \"SignedExpiry\",\n        type: {\n          name: \"String\",\n        },\n      },\n      signedService: {\n        serializedName: \"SignedService\",\n        required: true,\n        xmlName: \"SignedService\",\n        type: {\n          name: \"String\",\n        },\n      },\n      signedVersion: {\n        serializedName: \"SignedVersion\",\n        required: true,\n        xmlName: \"SignedVersion\",\n        type: {\n          name: \"String\",\n        },\n      },\n      value: {\n        serializedName: \"Value\",\n        required: true,\n        xmlName: \"Value\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const FilterBlobSegment: coreClient.CompositeMapper = {\n  serializedName: \"FilterBlobSegment\",\n  xmlName: \"EnumerationResults\",\n  type: {\n    name: \"Composite\",\n    className: \"FilterBlobSegment\",\n    modelProperties: {\n      serviceEndpoint: {\n        serializedName: \"ServiceEndpoint\",\n        required: true,\n        xmlName: \"ServiceEndpoint\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\",\n        },\n      },\n      where: {\n        serializedName: \"Where\",\n        required: true,\n        xmlName: \"Where\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobs: {\n        serializedName: \"Blobs\",\n        required: true,\n        xmlName: \"Blobs\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Blob\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"FilterBlobItem\",\n            },\n          },\n        },\n      },\n      continuationToken: {\n        serializedName: \"NextMarker\",\n        xmlName: \"NextMarker\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const FilterBlobItem: coreClient.CompositeMapper = {\n  serializedName: \"FilterBlobItem\",\n  xmlName: \"Blob\",\n  type: {\n    name: \"Composite\",\n    className: \"FilterBlobItem\",\n    modelProperties: {\n      name: {\n        serializedName: \"Name\",\n        required: true,\n        xmlName: \"Name\",\n        type: {\n          name: \"String\",\n        },\n      },\n      containerName: {\n        serializedName: \"ContainerName\",\n        required: true,\n        xmlName: \"ContainerName\",\n        type: {\n          name: \"String\",\n        },\n      },\n      tags: {\n        serializedName: \"Tags\",\n        xmlName: \"Tags\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobTags\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobTags: coreClient.CompositeMapper = {\n  serializedName: \"BlobTags\",\n  xmlName: \"Tags\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobTags\",\n    modelProperties: {\n      blobTagSet: {\n        serializedName: \"BlobTagSet\",\n        required: true,\n        xmlName: \"TagSet\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Tag\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"BlobTag\",\n            },\n          },\n        },\n      },\n    },\n  },\n};\n\nexport const BlobTag: coreClient.CompositeMapper = {\n  serializedName: \"BlobTag\",\n  xmlName: \"Tag\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobTag\",\n    modelProperties: {\n      key: {\n        serializedName: \"Key\",\n        required: true,\n        xmlName: \"Key\",\n        type: {\n          name: \"String\",\n        },\n      },\n      value: {\n        serializedName: \"Value\",\n        required: true,\n        xmlName: \"Value\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const SignedIdentifier: coreClient.CompositeMapper = {\n  serializedName: \"SignedIdentifier\",\n  xmlName: \"SignedIdentifier\",\n  type: {\n    name: \"Composite\",\n    className: \"SignedIdentifier\",\n    modelProperties: {\n      id: {\n        serializedName: \"Id\",\n        required: true,\n        xmlName: \"Id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      accessPolicy: {\n        serializedName: \"AccessPolicy\",\n        xmlName: \"AccessPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"AccessPolicy\",\n        },\n      },\n    },\n  },\n};\n\nexport const AccessPolicy: coreClient.CompositeMapper = {\n  serializedName: \"AccessPolicy\",\n  type: {\n    name: \"Composite\",\n    className: \"AccessPolicy\",\n    modelProperties: {\n      startsOn: {\n        serializedName: \"Start\",\n        xmlName: \"Start\",\n        type: {\n          name: \"String\",\n        },\n      },\n      expiresOn: {\n        serializedName: \"Expiry\",\n        xmlName: \"Expiry\",\n        type: {\n          name: \"String\",\n        },\n      },\n      permissions: {\n        serializedName: \"Permission\",\n        xmlName: \"Permission\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ListBlobsFlatSegmentResponse: coreClient.CompositeMapper = {\n  serializedName: \"ListBlobsFlatSegmentResponse\",\n  xmlName: \"EnumerationResults\",\n  type: {\n    name: \"Composite\",\n    className: \"ListBlobsFlatSegmentResponse\",\n    modelProperties: {\n      serviceEndpoint: {\n        serializedName: \"ServiceEndpoint\",\n        required: true,\n        xmlName: \"ServiceEndpoint\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\",\n        },\n      },\n      containerName: {\n        serializedName: \"ContainerName\",\n        required: true,\n        xmlName: \"ContainerName\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\",\n        },\n      },\n      prefix: {\n        serializedName: \"Prefix\",\n        xmlName: \"Prefix\",\n        type: {\n          name: \"String\",\n        },\n      },\n      marker: {\n        serializedName: \"Marker\",\n        xmlName: \"Marker\",\n        type: {\n          name: \"String\",\n        },\n      },\n      maxPageSize: {\n        serializedName: \"MaxResults\",\n        xmlName: \"MaxResults\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      segment: {\n        serializedName: \"Segment\",\n        xmlName: \"Blobs\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobFlatListSegment\",\n        },\n      },\n      continuationToken: {\n        serializedName: \"NextMarker\",\n        xmlName: \"NextMarker\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobFlatListSegment: coreClient.CompositeMapper = {\n  serializedName: \"BlobFlatListSegment\",\n  xmlName: \"Blobs\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobFlatListSegment\",\n    modelProperties: {\n      blobItems: {\n        serializedName: \"BlobItems\",\n        required: true,\n        xmlName: \"BlobItems\",\n        xmlElementName: \"Blob\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"BlobItemInternal\",\n            },\n          },\n        },\n      },\n    },\n  },\n};\n\nexport const BlobItemInternal: coreClient.CompositeMapper = {\n  serializedName: \"BlobItemInternal\",\n  xmlName: \"Blob\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobItemInternal\",\n    modelProperties: {\n      name: {\n        serializedName: \"Name\",\n        xmlName: \"Name\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobName\",\n        },\n      },\n      deleted: {\n        serializedName: \"Deleted\",\n        required: true,\n        xmlName: \"Deleted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      snapshot: {\n        serializedName: \"Snapshot\",\n        required: true,\n        xmlName: \"Snapshot\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"VersionId\",\n        xmlName: \"VersionId\",\n        type: {\n          name: \"String\",\n        },\n      },\n      isCurrentVersion: {\n        serializedName: \"IsCurrentVersion\",\n        xmlName: \"IsCurrentVersion\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      properties: {\n        serializedName: \"Properties\",\n        xmlName: \"Properties\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobPropertiesInternal\",\n        },\n      },\n      metadata: {\n        serializedName: \"Metadata\",\n        xmlName: \"Metadata\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      blobTags: {\n        serializedName: \"BlobTags\",\n        xmlName: \"Tags\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobTags\",\n        },\n      },\n      objectReplicationMetadata: {\n        serializedName: \"ObjectReplicationMetadata\",\n        xmlName: \"OrMetadata\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      hasVersionsOnly: {\n        serializedName: \"HasVersionsOnly\",\n        xmlName: \"HasVersionsOnly\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobName: coreClient.CompositeMapper = {\n  serializedName: \"BlobName\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobName\",\n    modelProperties: {\n      encoded: {\n        serializedName: \"Encoded\",\n        xmlName: \"Encoded\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      content: {\n        serializedName: \"content\",\n        xmlName: \"content\",\n        xmlIsMsText: true,\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobPropertiesInternal: coreClient.CompositeMapper = {\n  serializedName: \"BlobPropertiesInternal\",\n  xmlName: \"Properties\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobPropertiesInternal\",\n    modelProperties: {\n      createdOn: {\n        serializedName: \"Creation-Time\",\n        xmlName: \"Creation-Time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      lastModified: {\n        serializedName: \"Last-Modified\",\n        required: true,\n        xmlName: \"Last-Modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      etag: {\n        serializedName: \"Etag\",\n        required: true,\n        xmlName: \"Etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentLength: {\n        serializedName: \"Content-Length\",\n        xmlName: \"Content-Length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      contentType: {\n        serializedName: \"Content-Type\",\n        xmlName: \"Content-Type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentEncoding: {\n        serializedName: \"Content-Encoding\",\n        xmlName: \"Content-Encoding\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentLanguage: {\n        serializedName: \"Content-Language\",\n        xmlName: \"Content-Language\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"Content-MD5\",\n        xmlName: \"Content-MD5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      contentDisposition: {\n        serializedName: \"Content-Disposition\",\n        xmlName: \"Content-Disposition\",\n        type: {\n          name: \"String\",\n        },\n      },\n      cacheControl: {\n        serializedName: \"Cache-Control\",\n        xmlName: \"Cache-Control\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      blobType: {\n        serializedName: \"BlobType\",\n        xmlName: \"BlobType\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"BlockBlob\", \"PageBlob\", \"AppendBlob\"],\n        },\n      },\n      leaseStatus: {\n        serializedName: \"LeaseStatus\",\n        xmlName: \"LeaseStatus\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"locked\", \"unlocked\"],\n        },\n      },\n      leaseState: {\n        serializedName: \"LeaseState\",\n        xmlName: \"LeaseState\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"available\",\n            \"leased\",\n            \"expired\",\n            \"breaking\",\n            \"broken\",\n          ],\n        },\n      },\n      leaseDuration: {\n        serializedName: \"LeaseDuration\",\n        xmlName: \"LeaseDuration\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"infinite\", \"fixed\"],\n        },\n      },\n      copyId: {\n        serializedName: \"CopyId\",\n        xmlName: \"CopyId\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        serializedName: \"CopyStatus\",\n        xmlName: \"CopyStatus\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"pending\", \"success\", \"aborted\", \"failed\"],\n        },\n      },\n      copySource: {\n        serializedName: \"CopySource\",\n        xmlName: \"CopySource\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyProgress: {\n        serializedName: \"CopyProgress\",\n        xmlName: \"CopyProgress\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyCompletedOn: {\n        serializedName: \"CopyCompletionTime\",\n        xmlName: \"CopyCompletionTime\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyStatusDescription: {\n        serializedName: \"CopyStatusDescription\",\n        xmlName: \"CopyStatusDescription\",\n        type: {\n          name: \"String\",\n        },\n      },\n      serverEncrypted: {\n        serializedName: \"ServerEncrypted\",\n        xmlName: \"ServerEncrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      incrementalCopy: {\n        serializedName: \"IncrementalCopy\",\n        xmlName: \"IncrementalCopy\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      destinationSnapshot: {\n        serializedName: \"DestinationSnapshot\",\n        xmlName: \"DestinationSnapshot\",\n        type: {\n          name: \"String\",\n        },\n      },\n      deletedOn: {\n        serializedName: \"DeletedTime\",\n        xmlName: \"DeletedTime\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      remainingRetentionDays: {\n        serializedName: \"RemainingRetentionDays\",\n        xmlName: \"RemainingRetentionDays\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      accessTier: {\n        serializedName: \"AccessTier\",\n        xmlName: \"AccessTier\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"P4\",\n            \"P6\",\n            \"P10\",\n            \"P15\",\n            \"P20\",\n            \"P30\",\n            \"P40\",\n            \"P50\",\n            \"P60\",\n            \"P70\",\n            \"P80\",\n            \"Hot\",\n            \"Cool\",\n            \"Archive\",\n            \"Cold\",\n          ],\n        },\n      },\n      accessTierInferred: {\n        serializedName: \"AccessTierInferred\",\n        xmlName: \"AccessTierInferred\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      archiveStatus: {\n        serializedName: \"ArchiveStatus\",\n        xmlName: \"ArchiveStatus\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"rehydrate-pending-to-hot\",\n            \"rehydrate-pending-to-cool\",\n            \"rehydrate-pending-to-cold\",\n          ],\n        },\n      },\n      customerProvidedKeySha256: {\n        serializedName: \"CustomerProvidedKeySha256\",\n        xmlName: \"CustomerProvidedKeySha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"EncryptionScope\",\n        xmlName: \"EncryptionScope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      accessTierChangedOn: {\n        serializedName: \"AccessTierChangeTime\",\n        xmlName: \"AccessTierChangeTime\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      tagCount: {\n        serializedName: \"TagCount\",\n        xmlName: \"TagCount\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      expiresOn: {\n        serializedName: \"Expiry-Time\",\n        xmlName: \"Expiry-Time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isSealed: {\n        serializedName: \"Sealed\",\n        xmlName: \"Sealed\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      rehydratePriority: {\n        serializedName: \"RehydratePriority\",\n        xmlName: \"RehydratePriority\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"High\", \"Standard\"],\n        },\n      },\n      lastAccessedOn: {\n        serializedName: \"LastAccessTime\",\n        xmlName: \"LastAccessTime\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyExpiresOn: {\n        serializedName: \"ImmutabilityPolicyUntilDate\",\n        xmlName: \"ImmutabilityPolicyUntilDate\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyMode: {\n        serializedName: \"ImmutabilityPolicyMode\",\n        xmlName: \"ImmutabilityPolicyMode\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"Mutable\", \"Unlocked\", \"Locked\"],\n        },\n      },\n      legalHold: {\n        serializedName: \"LegalHold\",\n        xmlName: \"LegalHold\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const ListBlobsHierarchySegmentResponse: coreClient.CompositeMapper = {\n  serializedName: \"ListBlobsHierarchySegmentResponse\",\n  xmlName: \"EnumerationResults\",\n  type: {\n    name: \"Composite\",\n    className: \"ListBlobsHierarchySegmentResponse\",\n    modelProperties: {\n      serviceEndpoint: {\n        serializedName: \"ServiceEndpoint\",\n        required: true,\n        xmlName: \"ServiceEndpoint\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\",\n        },\n      },\n      containerName: {\n        serializedName: \"ContainerName\",\n        required: true,\n        xmlName: \"ContainerName\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\",\n        },\n      },\n      prefix: {\n        serializedName: \"Prefix\",\n        xmlName: \"Prefix\",\n        type: {\n          name: \"String\",\n        },\n      },\n      marker: {\n        serializedName: \"Marker\",\n        xmlName: \"Marker\",\n        type: {\n          name: \"String\",\n        },\n      },\n      maxPageSize: {\n        serializedName: \"MaxResults\",\n        xmlName: \"MaxResults\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      delimiter: {\n        serializedName: \"Delimiter\",\n        xmlName: \"Delimiter\",\n        type: {\n          name: \"String\",\n        },\n      },\n      segment: {\n        serializedName: \"Segment\",\n        xmlName: \"Blobs\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobHierarchyListSegment\",\n        },\n      },\n      continuationToken: {\n        serializedName: \"NextMarker\",\n        xmlName: \"NextMarker\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobHierarchyListSegment: coreClient.CompositeMapper = {\n  serializedName: \"BlobHierarchyListSegment\",\n  xmlName: \"Blobs\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobHierarchyListSegment\",\n    modelProperties: {\n      blobPrefixes: {\n        serializedName: \"BlobPrefixes\",\n        xmlName: \"BlobPrefixes\",\n        xmlElementName: \"BlobPrefix\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"BlobPrefix\",\n            },\n          },\n        },\n      },\n      blobItems: {\n        serializedName: \"BlobItems\",\n        required: true,\n        xmlName: \"BlobItems\",\n        xmlElementName: \"Blob\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"BlobItemInternal\",\n            },\n          },\n        },\n      },\n    },\n  },\n};\n\nexport const BlobPrefix: coreClient.CompositeMapper = {\n  serializedName: \"BlobPrefix\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobPrefix\",\n    modelProperties: {\n      name: {\n        serializedName: \"Name\",\n        xmlName: \"Name\",\n        type: {\n          name: \"Composite\",\n          className: \"BlobName\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockLookupList: coreClient.CompositeMapper = {\n  serializedName: \"BlockLookupList\",\n  xmlName: \"BlockList\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockLookupList\",\n    modelProperties: {\n      committed: {\n        serializedName: \"Committed\",\n        xmlName: \"Committed\",\n        xmlElementName: \"Committed\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"String\",\n            },\n          },\n        },\n      },\n      uncommitted: {\n        serializedName: \"Uncommitted\",\n        xmlName: \"Uncommitted\",\n        xmlElementName: \"Uncommitted\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"String\",\n            },\n          },\n        },\n      },\n      latest: {\n        serializedName: \"Latest\",\n        xmlName: \"Latest\",\n        xmlElementName: \"Latest\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"String\",\n            },\n          },\n        },\n      },\n    },\n  },\n};\n\nexport const BlockList: coreClient.CompositeMapper = {\n  serializedName: \"BlockList\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockList\",\n    modelProperties: {\n      committedBlocks: {\n        serializedName: \"CommittedBlocks\",\n        xmlName: \"CommittedBlocks\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Block\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"Block\",\n            },\n          },\n        },\n      },\n      uncommittedBlocks: {\n        serializedName: \"UncommittedBlocks\",\n        xmlName: \"UncommittedBlocks\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Block\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"Block\",\n            },\n          },\n        },\n      },\n    },\n  },\n};\n\nexport const Block: coreClient.CompositeMapper = {\n  serializedName: \"Block\",\n  type: {\n    name: \"Composite\",\n    className: \"Block\",\n    modelProperties: {\n      name: {\n        serializedName: \"Name\",\n        required: true,\n        xmlName: \"Name\",\n        type: {\n          name: \"String\",\n        },\n      },\n      size: {\n        serializedName: \"Size\",\n        required: true,\n        xmlName: \"Size\",\n        type: {\n          name: \"Number\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageList: coreClient.CompositeMapper = {\n  serializedName: \"PageList\",\n  type: {\n    name: \"Composite\",\n    className: \"PageList\",\n    modelProperties: {\n      pageRange: {\n        serializedName: \"PageRange\",\n        xmlName: \"PageRange\",\n        xmlElementName: \"PageRange\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"PageRange\",\n            },\n          },\n        },\n      },\n      clearRange: {\n        serializedName: \"ClearRange\",\n        xmlName: \"ClearRange\",\n        xmlElementName: \"ClearRange\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"ClearRange\",\n            },\n          },\n        },\n      },\n      continuationToken: {\n        serializedName: \"NextMarker\",\n        xmlName: \"NextMarker\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageRange: coreClient.CompositeMapper = {\n  serializedName: \"PageRange\",\n  xmlName: \"PageRange\",\n  type: {\n    name: \"Composite\",\n    className: \"PageRange\",\n    modelProperties: {\n      start: {\n        serializedName: \"Start\",\n        required: true,\n        xmlName: \"Start\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      end: {\n        serializedName: \"End\",\n        required: true,\n        xmlName: \"End\",\n        type: {\n          name: \"Number\",\n        },\n      },\n    },\n  },\n};\n\nexport const ClearRange: coreClient.CompositeMapper = {\n  serializedName: \"ClearRange\",\n  xmlName: \"ClearRange\",\n  type: {\n    name: \"Composite\",\n    className: \"ClearRange\",\n    modelProperties: {\n      start: {\n        serializedName: \"Start\",\n        required: true,\n        xmlName: \"Start\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      end: {\n        serializedName: \"End\",\n        required: true,\n        xmlName: \"End\",\n        type: {\n          name: \"Number\",\n        },\n      },\n    },\n  },\n};\n\nexport const QueryRequest: coreClient.CompositeMapper = {\n  serializedName: \"QueryRequest\",\n  xmlName: \"QueryRequest\",\n  type: {\n    name: \"Composite\",\n    className: \"QueryRequest\",\n    modelProperties: {\n      queryType: {\n        serializedName: \"QueryType\",\n        required: true,\n        xmlName: \"QueryType\",\n        type: {\n          name: \"String\",\n        },\n      },\n      expression: {\n        serializedName: \"Expression\",\n        required: true,\n        xmlName: \"Expression\",\n        type: {\n          name: \"String\",\n        },\n      },\n      inputSerialization: {\n        serializedName: \"InputSerialization\",\n        xmlName: \"InputSerialization\",\n        type: {\n          name: \"Composite\",\n          className: \"QuerySerialization\",\n        },\n      },\n      outputSerialization: {\n        serializedName: \"OutputSerialization\",\n        xmlName: \"OutputSerialization\",\n        type: {\n          name: \"Composite\",\n          className: \"QuerySerialization\",\n        },\n      },\n    },\n  },\n};\n\nexport const QuerySerialization: coreClient.CompositeMapper = {\n  serializedName: \"QuerySerialization\",\n  type: {\n    name: \"Composite\",\n    className: \"QuerySerialization\",\n    modelProperties: {\n      format: {\n        serializedName: \"Format\",\n        xmlName: \"Format\",\n        type: {\n          name: \"Composite\",\n          className: \"QueryFormat\",\n        },\n      },\n    },\n  },\n};\n\nexport const QueryFormat: coreClient.CompositeMapper = {\n  serializedName: \"QueryFormat\",\n  type: {\n    name: \"Composite\",\n    className: \"QueryFormat\",\n    modelProperties: {\n      type: {\n        serializedName: \"Type\",\n        required: true,\n        xmlName: \"Type\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"delimited\", \"json\", \"arrow\", \"parquet\"],\n        },\n      },\n      delimitedTextConfiguration: {\n        serializedName: \"DelimitedTextConfiguration\",\n        xmlName: \"DelimitedTextConfiguration\",\n        type: {\n          name: \"Composite\",\n          className: \"DelimitedTextConfiguration\",\n        },\n      },\n      jsonTextConfiguration: {\n        serializedName: \"JsonTextConfiguration\",\n        xmlName: \"JsonTextConfiguration\",\n        type: {\n          name: \"Composite\",\n          className: \"JsonTextConfiguration\",\n        },\n      },\n      arrowConfiguration: {\n        serializedName: \"ArrowConfiguration\",\n        xmlName: \"ArrowConfiguration\",\n        type: {\n          name: \"Composite\",\n          className: \"ArrowConfiguration\",\n        },\n      },\n      parquetTextConfiguration: {\n        serializedName: \"ParquetTextConfiguration\",\n        xmlName: \"ParquetTextConfiguration\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"any\" } },\n        },\n      },\n    },\n  },\n};\n\nexport const DelimitedTextConfiguration: coreClient.CompositeMapper = {\n  serializedName: \"DelimitedTextConfiguration\",\n  xmlName: \"DelimitedTextConfiguration\",\n  type: {\n    name: \"Composite\",\n    className: \"DelimitedTextConfiguration\",\n    modelProperties: {\n      columnSeparator: {\n        serializedName: \"ColumnSeparator\",\n        xmlName: \"ColumnSeparator\",\n        type: {\n          name: \"String\",\n        },\n      },\n      fieldQuote: {\n        serializedName: \"FieldQuote\",\n        xmlName: \"FieldQuote\",\n        type: {\n          name: \"String\",\n        },\n      },\n      recordSeparator: {\n        serializedName: \"RecordSeparator\",\n        xmlName: \"RecordSeparator\",\n        type: {\n          name: \"String\",\n        },\n      },\n      escapeChar: {\n        serializedName: \"EscapeChar\",\n        xmlName: \"EscapeChar\",\n        type: {\n          name: \"String\",\n        },\n      },\n      headersPresent: {\n        serializedName: \"HeadersPresent\",\n        xmlName: \"HasHeaders\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const JsonTextConfiguration: coreClient.CompositeMapper = {\n  serializedName: \"JsonTextConfiguration\",\n  xmlName: \"JsonTextConfiguration\",\n  type: {\n    name: \"Composite\",\n    className: \"JsonTextConfiguration\",\n    modelProperties: {\n      recordSeparator: {\n        serializedName: \"RecordSeparator\",\n        xmlName: \"RecordSeparator\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ArrowConfiguration: coreClient.CompositeMapper = {\n  serializedName: \"ArrowConfiguration\",\n  xmlName: \"ArrowConfiguration\",\n  type: {\n    name: \"Composite\",\n    className: \"ArrowConfiguration\",\n    modelProperties: {\n      schema: {\n        serializedName: \"Schema\",\n        required: true,\n        xmlName: \"Schema\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Field\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"ArrowField\",\n            },\n          },\n        },\n      },\n    },\n  },\n};\n\nexport const ArrowField: coreClient.CompositeMapper = {\n  serializedName: \"ArrowField\",\n  xmlName: \"Field\",\n  type: {\n    name: \"Composite\",\n    className: \"ArrowField\",\n    modelProperties: {\n      type: {\n        serializedName: \"Type\",\n        required: true,\n        xmlName: \"Type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      name: {\n        serializedName: \"Name\",\n        xmlName: \"Name\",\n        type: {\n          name: \"String\",\n        },\n      },\n      precision: {\n        serializedName: \"Precision\",\n        xmlName: \"Precision\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      scale: {\n        serializedName: \"Scale\",\n        xmlName: \"Scale\",\n        type: {\n          name: \"Number\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceSetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_setPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceSetPropertiesHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceSetPropertiesExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Service_setPropertiesExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ServiceSetPropertiesExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ServiceGetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetPropertiesHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceGetPropertiesExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Service_getPropertiesExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ServiceGetPropertiesExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ServiceGetStatisticsHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getStatisticsHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetStatisticsHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceGetStatisticsExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Service_getStatisticsExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ServiceGetStatisticsExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ServiceListContainersSegmentHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_listContainersSegmentHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceListContainersSegmentHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceListContainersSegmentExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Service_listContainersSegmentExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ServiceListContainersSegmentExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ServiceGetUserDelegationKeyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getUserDelegationKeyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetUserDelegationKeyHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceGetUserDelegationKeyExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Service_getUserDelegationKeyExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ServiceGetUserDelegationKeyExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ServiceGetAccountInfoHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getAccountInfoHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetAccountInfoHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      skuName: {\n        serializedName: \"x-ms-sku-name\",\n        xmlName: \"x-ms-sku-name\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"Standard_LRS\",\n            \"Standard_GRS\",\n            \"Standard_RAGRS\",\n            \"Standard_ZRS\",\n            \"Premium_LRS\",\n          ],\n        },\n      },\n      accountKind: {\n        serializedName: \"x-ms-account-kind\",\n        xmlName: \"x-ms-account-kind\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"Storage\",\n            \"BlobStorage\",\n            \"StorageV2\",\n            \"FileStorage\",\n            \"BlockBlobStorage\",\n          ],\n        },\n      },\n      isHierarchicalNamespaceEnabled: {\n        serializedName: \"x-ms-is-hns-enabled\",\n        xmlName: \"x-ms-is-hns-enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceGetAccountInfoExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Service_getAccountInfoExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ServiceGetAccountInfoExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ServiceSubmitBatchHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_submitBatchHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceSubmitBatchHeaders\",\n    modelProperties: {\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceSubmitBatchExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_submitBatchExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceSubmitBatchExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceFilterBlobsHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_filterBlobsHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceFilterBlobsHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ServiceFilterBlobsExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_filterBlobsExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceFilterBlobsExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerCreateHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_createHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerCreateHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerCreateExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_createExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerCreateExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerGetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_getPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerGetPropertiesHeaders\",\n    modelProperties: {\n      metadata: {\n        serializedName: \"x-ms-meta\",\n        headerCollectionPrefix: \"x-ms-meta-\",\n        xmlName: \"x-ms-meta\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseDuration: {\n        serializedName: \"x-ms-lease-duration\",\n        xmlName: \"x-ms-lease-duration\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"infinite\", \"fixed\"],\n        },\n      },\n      leaseState: {\n        serializedName: \"x-ms-lease-state\",\n        xmlName: \"x-ms-lease-state\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"available\",\n            \"leased\",\n            \"expired\",\n            \"breaking\",\n            \"broken\",\n          ],\n        },\n      },\n      leaseStatus: {\n        serializedName: \"x-ms-lease-status\",\n        xmlName: \"x-ms-lease-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"locked\", \"unlocked\"],\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobPublicAccess: {\n        serializedName: \"x-ms-blob-public-access\",\n        xmlName: \"x-ms-blob-public-access\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"container\", \"blob\"],\n        },\n      },\n      hasImmutabilityPolicy: {\n        serializedName: \"x-ms-has-immutability-policy\",\n        xmlName: \"x-ms-has-immutability-policy\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      hasLegalHold: {\n        serializedName: \"x-ms-has-legal-hold\",\n        xmlName: \"x-ms-has-legal-hold\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      defaultEncryptionScope: {\n        serializedName: \"x-ms-default-encryption-scope\",\n        xmlName: \"x-ms-default-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      denyEncryptionScopeOverride: {\n        serializedName: \"x-ms-deny-encryption-scope-override\",\n        xmlName: \"x-ms-deny-encryption-scope-override\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      isImmutableStorageWithVersioningEnabled: {\n        serializedName: \"x-ms-immutable-storage-with-versioning-enabled\",\n        xmlName: \"x-ms-immutable-storage-with-versioning-enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerGetPropertiesExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_getPropertiesExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerGetPropertiesExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerDeleteHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_deleteHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerDeleteHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerDeleteExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_deleteExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerDeleteExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerSetMetadataHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_setMetadataHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerSetMetadataHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerSetMetadataExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_setMetadataExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerSetMetadataExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerGetAccessPolicyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_getAccessPolicyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerGetAccessPolicyHeaders\",\n    modelProperties: {\n      blobPublicAccess: {\n        serializedName: \"x-ms-blob-public-access\",\n        xmlName: \"x-ms-blob-public-access\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"container\", \"blob\"],\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerGetAccessPolicyExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_getAccessPolicyExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerGetAccessPolicyExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerSetAccessPolicyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_setAccessPolicyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerSetAccessPolicyHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerSetAccessPolicyExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_setAccessPolicyExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerSetAccessPolicyExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerRestoreHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_restoreHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerRestoreHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerRestoreExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_restoreExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerRestoreExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerRenameHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_renameHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerRenameHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerRenameExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_renameExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerRenameExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerSubmitBatchHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_submitBatchHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerSubmitBatchHeaders\",\n    modelProperties: {\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerSubmitBatchExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_submitBatchExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerSubmitBatchExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerFilterBlobsHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_filterBlobsHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerFilterBlobsHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerFilterBlobsExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_filterBlobsExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerFilterBlobsExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerAcquireLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_acquireLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerAcquireLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseId: {\n        serializedName: \"x-ms-lease-id\",\n        xmlName: \"x-ms-lease-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerAcquireLeaseExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_acquireLeaseExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerAcquireLeaseExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerReleaseLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_releaseLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerReleaseLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerReleaseLeaseExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_releaseLeaseExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerReleaseLeaseExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerRenewLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_renewLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerRenewLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseId: {\n        serializedName: \"x-ms-lease-id\",\n        xmlName: \"x-ms-lease-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerRenewLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_renewLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerRenewLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerBreakLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_breakLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerBreakLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseTime: {\n        serializedName: \"x-ms-lease-time\",\n        xmlName: \"x-ms-lease-time\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerBreakLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_breakLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerBreakLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerChangeLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_changeLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerChangeLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseId: {\n        serializedName: \"x-ms-lease-id\",\n        xmlName: \"x-ms-lease-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerChangeLeaseExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_changeLeaseExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerChangeLeaseExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerListBlobFlatSegmentHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_listBlobFlatSegmentHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerListBlobFlatSegmentHeaders\",\n    modelProperties: {\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerListBlobFlatSegmentExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_listBlobFlatSegmentExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerListBlobFlatSegmentExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerListBlobHierarchySegmentHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_listBlobHierarchySegmentHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerListBlobHierarchySegmentHeaders\",\n      modelProperties: {\n        contentType: {\n          serializedName: \"content-type\",\n          xmlName: \"content-type\",\n          type: {\n            name: \"String\",\n          },\n        },\n        clientRequestId: {\n          serializedName: \"x-ms-client-request-id\",\n          xmlName: \"x-ms-client-request-id\",\n          type: {\n            name: \"String\",\n          },\n        },\n        requestId: {\n          serializedName: \"x-ms-request-id\",\n          xmlName: \"x-ms-request-id\",\n          type: {\n            name: \"String\",\n          },\n        },\n        version: {\n          serializedName: \"x-ms-version\",\n          xmlName: \"x-ms-version\",\n          type: {\n            name: \"String\",\n          },\n        },\n        date: {\n          serializedName: \"date\",\n          xmlName: \"date\",\n          type: {\n            name: \"DateTimeRfc1123\",\n          },\n        },\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerListBlobHierarchySegmentExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_listBlobHierarchySegmentExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerListBlobHierarchySegmentExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const ContainerGetAccountInfoHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Container_getAccountInfoHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ContainerGetAccountInfoHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      skuName: {\n        serializedName: \"x-ms-sku-name\",\n        xmlName: \"x-ms-sku-name\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"Standard_LRS\",\n            \"Standard_GRS\",\n            \"Standard_RAGRS\",\n            \"Standard_ZRS\",\n            \"Premium_LRS\",\n          ],\n        },\n      },\n      accountKind: {\n        serializedName: \"x-ms-account-kind\",\n        xmlName: \"x-ms-account-kind\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"Storage\",\n            \"BlobStorage\",\n            \"StorageV2\",\n            \"FileStorage\",\n            \"BlockBlobStorage\",\n          ],\n        },\n      },\n      isHierarchicalNamespaceEnabled: {\n        serializedName: \"x-ms-is-hns-enabled\",\n        xmlName: \"x-ms-is-hns-enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const ContainerGetAccountInfoExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Container_getAccountInfoExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"ContainerGetAccountInfoExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlobDownloadHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_downloadHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobDownloadHeaders\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      createdOn: {\n        serializedName: \"x-ms-creation-time\",\n        xmlName: \"x-ms-creation-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      metadata: {\n        serializedName: \"x-ms-meta\",\n        headerCollectionPrefix: \"x-ms-meta-\",\n        xmlName: \"x-ms-meta\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      objectReplicationPolicyId: {\n        serializedName: \"x-ms-or-policy-id\",\n        xmlName: \"x-ms-or-policy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      objectReplicationRules: {\n        serializedName: \"x-ms-or\",\n        headerCollectionPrefix: \"x-ms-or-\",\n        xmlName: \"x-ms-or\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      contentLength: {\n        serializedName: \"content-length\",\n        xmlName: \"content-length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentRange: {\n        serializedName: \"content-range\",\n        xmlName: \"content-range\",\n        type: {\n          name: \"String\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      contentEncoding: {\n        serializedName: \"content-encoding\",\n        xmlName: \"content-encoding\",\n        type: {\n          name: \"String\",\n        },\n      },\n      cacheControl: {\n        serializedName: \"cache-control\",\n        xmlName: \"cache-control\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentDisposition: {\n        serializedName: \"content-disposition\",\n        xmlName: \"content-disposition\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentLanguage: {\n        serializedName: \"content-language\",\n        xmlName: \"content-language\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      blobType: {\n        serializedName: \"x-ms-blob-type\",\n        xmlName: \"x-ms-blob-type\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"BlockBlob\", \"PageBlob\", \"AppendBlob\"],\n        },\n      },\n      copyCompletedOn: {\n        serializedName: \"x-ms-copy-completion-time\",\n        xmlName: \"x-ms-copy-completion-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyStatusDescription: {\n        serializedName: \"x-ms-copy-status-description\",\n        xmlName: \"x-ms-copy-status-description\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyId: {\n        serializedName: \"x-ms-copy-id\",\n        xmlName: \"x-ms-copy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyProgress: {\n        serializedName: \"x-ms-copy-progress\",\n        xmlName: \"x-ms-copy-progress\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copySource: {\n        serializedName: \"x-ms-copy-source\",\n        xmlName: \"x-ms-copy-source\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        serializedName: \"x-ms-copy-status\",\n        xmlName: \"x-ms-copy-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"pending\", \"success\", \"aborted\", \"failed\"],\n        },\n      },\n      leaseDuration: {\n        serializedName: \"x-ms-lease-duration\",\n        xmlName: \"x-ms-lease-duration\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"infinite\", \"fixed\"],\n        },\n      },\n      leaseState: {\n        serializedName: \"x-ms-lease-state\",\n        xmlName: \"x-ms-lease-state\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"available\",\n            \"leased\",\n            \"expired\",\n            \"breaking\",\n            \"broken\",\n          ],\n        },\n      },\n      leaseStatus: {\n        serializedName: \"x-ms-lease-status\",\n        xmlName: \"x-ms-lease-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"locked\", \"unlocked\"],\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      isCurrentVersion: {\n        serializedName: \"x-ms-is-current-version\",\n        xmlName: \"x-ms-is-current-version\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      acceptRanges: {\n        serializedName: \"accept-ranges\",\n        xmlName: \"accept-ranges\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobCommittedBlockCount: {\n        serializedName: \"x-ms-blob-committed-block-count\",\n        xmlName: \"x-ms-blob-committed-block-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-server-encrypted\",\n        xmlName: \"x-ms-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobContentMD5: {\n        serializedName: \"x-ms-blob-content-md5\",\n        xmlName: \"x-ms-blob-content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      tagCount: {\n        serializedName: \"x-ms-tag-count\",\n        xmlName: \"x-ms-tag-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      isSealed: {\n        serializedName: \"x-ms-blob-sealed\",\n        xmlName: \"x-ms-blob-sealed\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      lastAccessed: {\n        serializedName: \"x-ms-last-access-time\",\n        xmlName: \"x-ms-last-access-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyExpiresOn: {\n        serializedName: \"x-ms-immutability-policy-until-date\",\n        xmlName: \"x-ms-immutability-policy-until-date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyMode: {\n        serializedName: \"x-ms-immutability-policy-mode\",\n        xmlName: \"x-ms-immutability-policy-mode\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"Mutable\", \"Unlocked\", \"Locked\"],\n        },\n      },\n      legalHold: {\n        serializedName: \"x-ms-legal-hold\",\n        xmlName: \"x-ms-legal-hold\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobDownloadExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_downloadExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobDownloadExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobGetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_getPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobGetPropertiesHeaders\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      createdOn: {\n        serializedName: \"x-ms-creation-time\",\n        xmlName: \"x-ms-creation-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      metadata: {\n        serializedName: \"x-ms-meta\",\n        headerCollectionPrefix: \"x-ms-meta-\",\n        xmlName: \"x-ms-meta\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      objectReplicationPolicyId: {\n        serializedName: \"x-ms-or-policy-id\",\n        xmlName: \"x-ms-or-policy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      objectReplicationRules: {\n        serializedName: \"x-ms-or\",\n        headerCollectionPrefix: \"x-ms-or-\",\n        xmlName: \"x-ms-or\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      blobType: {\n        serializedName: \"x-ms-blob-type\",\n        xmlName: \"x-ms-blob-type\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"BlockBlob\", \"PageBlob\", \"AppendBlob\"],\n        },\n      },\n      copyCompletedOn: {\n        serializedName: \"x-ms-copy-completion-time\",\n        xmlName: \"x-ms-copy-completion-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyStatusDescription: {\n        serializedName: \"x-ms-copy-status-description\",\n        xmlName: \"x-ms-copy-status-description\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyId: {\n        serializedName: \"x-ms-copy-id\",\n        xmlName: \"x-ms-copy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyProgress: {\n        serializedName: \"x-ms-copy-progress\",\n        xmlName: \"x-ms-copy-progress\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copySource: {\n        serializedName: \"x-ms-copy-source\",\n        xmlName: \"x-ms-copy-source\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        serializedName: \"x-ms-copy-status\",\n        xmlName: \"x-ms-copy-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"pending\", \"success\", \"aborted\", \"failed\"],\n        },\n      },\n      isIncrementalCopy: {\n        serializedName: \"x-ms-incremental-copy\",\n        xmlName: \"x-ms-incremental-copy\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      destinationSnapshot: {\n        serializedName: \"x-ms-copy-destination-snapshot\",\n        xmlName: \"x-ms-copy-destination-snapshot\",\n        type: {\n          name: \"String\",\n        },\n      },\n      leaseDuration: {\n        serializedName: \"x-ms-lease-duration\",\n        xmlName: \"x-ms-lease-duration\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"infinite\", \"fixed\"],\n        },\n      },\n      leaseState: {\n        serializedName: \"x-ms-lease-state\",\n        xmlName: \"x-ms-lease-state\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"available\",\n            \"leased\",\n            \"expired\",\n            \"breaking\",\n            \"broken\",\n          ],\n        },\n      },\n      leaseStatus: {\n        serializedName: \"x-ms-lease-status\",\n        xmlName: \"x-ms-lease-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"locked\", \"unlocked\"],\n        },\n      },\n      contentLength: {\n        serializedName: \"content-length\",\n        xmlName: \"content-length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      contentEncoding: {\n        serializedName: \"content-encoding\",\n        xmlName: \"content-encoding\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentDisposition: {\n        serializedName: \"content-disposition\",\n        xmlName: \"content-disposition\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentLanguage: {\n        serializedName: \"content-language\",\n        xmlName: \"content-language\",\n        type: {\n          name: \"String\",\n        },\n      },\n      cacheControl: {\n        serializedName: \"cache-control\",\n        xmlName: \"cache-control\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      acceptRanges: {\n        serializedName: \"accept-ranges\",\n        xmlName: \"accept-ranges\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobCommittedBlockCount: {\n        serializedName: \"x-ms-blob-committed-block-count\",\n        xmlName: \"x-ms-blob-committed-block-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-server-encrypted\",\n        xmlName: \"x-ms-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      accessTier: {\n        serializedName: \"x-ms-access-tier\",\n        xmlName: \"x-ms-access-tier\",\n        type: {\n          name: \"String\",\n        },\n      },\n      accessTierInferred: {\n        serializedName: \"x-ms-access-tier-inferred\",\n        xmlName: \"x-ms-access-tier-inferred\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      archiveStatus: {\n        serializedName: \"x-ms-archive-status\",\n        xmlName: \"x-ms-archive-status\",\n        type: {\n          name: \"String\",\n        },\n      },\n      accessTierChangedOn: {\n        serializedName: \"x-ms-access-tier-change-time\",\n        xmlName: \"x-ms-access-tier-change-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      isCurrentVersion: {\n        serializedName: \"x-ms-is-current-version\",\n        xmlName: \"x-ms-is-current-version\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      tagCount: {\n        serializedName: \"x-ms-tag-count\",\n        xmlName: \"x-ms-tag-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      expiresOn: {\n        serializedName: \"x-ms-expiry-time\",\n        xmlName: \"x-ms-expiry-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isSealed: {\n        serializedName: \"x-ms-blob-sealed\",\n        xmlName: \"x-ms-blob-sealed\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      rehydratePriority: {\n        serializedName: \"x-ms-rehydrate-priority\",\n        xmlName: \"x-ms-rehydrate-priority\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"High\", \"Standard\"],\n        },\n      },\n      lastAccessed: {\n        serializedName: \"x-ms-last-access-time\",\n        xmlName: \"x-ms-last-access-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyExpiresOn: {\n        serializedName: \"x-ms-immutability-policy-until-date\",\n        xmlName: \"x-ms-immutability-policy-until-date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyMode: {\n        serializedName: \"x-ms-immutability-policy-mode\",\n        xmlName: \"x-ms-immutability-policy-mode\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"Mutable\", \"Unlocked\", \"Locked\"],\n        },\n      },\n      legalHold: {\n        serializedName: \"x-ms-legal-hold\",\n        xmlName: \"x-ms-legal-hold\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobGetPropertiesExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_getPropertiesExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobGetPropertiesExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobDeleteHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_deleteHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobDeleteHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobDeleteExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_deleteExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobDeleteExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobUndeleteHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_undeleteHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobUndeleteHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobUndeleteExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_undeleteExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobUndeleteExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetExpiryHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setExpiryHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetExpiryHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetExpiryExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setExpiryExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetExpiryExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetHttpHeadersHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setHttpHeadersHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetHttpHeadersHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetHttpHeadersExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setHttpHeadersExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetHttpHeadersExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetImmutabilityPolicyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setImmutabilityPolicyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetImmutabilityPolicyHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyExpiry: {\n        serializedName: \"x-ms-immutability-policy-until-date\",\n        xmlName: \"x-ms-immutability-policy-until-date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      immutabilityPolicyMode: {\n        serializedName: \"x-ms-immutability-policy-mode\",\n        xmlName: \"x-ms-immutability-policy-mode\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"Mutable\", \"Unlocked\", \"Locked\"],\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetImmutabilityPolicyExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Blob_setImmutabilityPolicyExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlobSetImmutabilityPolicyExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlobDeleteImmutabilityPolicyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_deleteImmutabilityPolicyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobDeleteImmutabilityPolicyHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobDeleteImmutabilityPolicyExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Blob_deleteImmutabilityPolicyExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlobDeleteImmutabilityPolicyExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlobSetLegalHoldHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setLegalHoldHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetLegalHoldHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      legalHold: {\n        serializedName: \"x-ms-legal-hold\",\n        xmlName: \"x-ms-legal-hold\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetLegalHoldExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setLegalHoldExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetLegalHoldExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetMetadataHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setMetadataHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetMetadataHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetMetadataExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setMetadataExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetMetadataExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobAcquireLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_acquireLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobAcquireLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseId: {\n        serializedName: \"x-ms-lease-id\",\n        xmlName: \"x-ms-lease-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobAcquireLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_acquireLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobAcquireLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobReleaseLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_releaseLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobReleaseLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobReleaseLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_releaseLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobReleaseLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobRenewLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_renewLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobRenewLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseId: {\n        serializedName: \"x-ms-lease-id\",\n        xmlName: \"x-ms-lease-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobRenewLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_renewLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobRenewLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobChangeLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_changeLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobChangeLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      leaseId: {\n        serializedName: \"x-ms-lease-id\",\n        xmlName: \"x-ms-lease-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobChangeLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_changeLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobChangeLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobBreakLeaseHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_breakLeaseHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobBreakLeaseHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      leaseTime: {\n        serializedName: \"x-ms-lease-time\",\n        xmlName: \"x-ms-lease-time\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobBreakLeaseExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_breakLeaseExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobBreakLeaseExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobCreateSnapshotHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_createSnapshotHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobCreateSnapshotHeaders\",\n    modelProperties: {\n      snapshot: {\n        serializedName: \"x-ms-snapshot\",\n        xmlName: \"x-ms-snapshot\",\n        type: {\n          name: \"String\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobCreateSnapshotExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_createSnapshotExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobCreateSnapshotExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobStartCopyFromURLHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_startCopyFromURLHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobStartCopyFromURLHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyId: {\n        serializedName: \"x-ms-copy-id\",\n        xmlName: \"x-ms-copy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        serializedName: \"x-ms-copy-status\",\n        xmlName: \"x-ms-copy-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"pending\", \"success\", \"aborted\", \"failed\"],\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobStartCopyFromURLExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Blob_startCopyFromURLExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlobStartCopyFromURLExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlobCopyFromURLHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_copyFromURLHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobCopyFromURLHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyId: {\n        serializedName: \"x-ms-copy-id\",\n        xmlName: \"x-ms-copy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        defaultValue: \"success\",\n        isConstant: true,\n        serializedName: \"x-ms-copy-status\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobCopyFromURLExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_copyFromURLExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobCopyFromURLExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobAbortCopyFromURLHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_abortCopyFromURLHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobAbortCopyFromURLHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobAbortCopyFromURLExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"Blob_abortCopyFromURLExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlobAbortCopyFromURLExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlobSetTierHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setTierHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetTierHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetTierExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setTierExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetTierExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobGetAccountInfoHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_getAccountInfoHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobGetAccountInfoHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      skuName: {\n        serializedName: \"x-ms-sku-name\",\n        xmlName: \"x-ms-sku-name\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"Standard_LRS\",\n            \"Standard_GRS\",\n            \"Standard_RAGRS\",\n            \"Standard_ZRS\",\n            \"Premium_LRS\",\n          ],\n        },\n      },\n      accountKind: {\n        serializedName: \"x-ms-account-kind\",\n        xmlName: \"x-ms-account-kind\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"Storage\",\n            \"BlobStorage\",\n            \"StorageV2\",\n            \"FileStorage\",\n            \"BlockBlobStorage\",\n          ],\n        },\n      },\n      isHierarchicalNamespaceEnabled: {\n        serializedName: \"x-ms-is-hns-enabled\",\n        xmlName: \"x-ms-is-hns-enabled\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobGetAccountInfoExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_getAccountInfoExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobGetAccountInfoExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobQueryHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_queryHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobQueryHeaders\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      metadata: {\n        serializedName: \"x-ms-meta\",\n        headerCollectionPrefix: \"x-ms-meta-\",\n        xmlName: \"x-ms-meta\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } },\n        },\n      },\n      contentLength: {\n        serializedName: \"content-length\",\n        xmlName: \"content-length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentRange: {\n        serializedName: \"content-range\",\n        xmlName: \"content-range\",\n        type: {\n          name: \"String\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      contentEncoding: {\n        serializedName: \"content-encoding\",\n        xmlName: \"content-encoding\",\n        type: {\n          name: \"String\",\n        },\n      },\n      cacheControl: {\n        serializedName: \"cache-control\",\n        xmlName: \"cache-control\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentDisposition: {\n        serializedName: \"content-disposition\",\n        xmlName: \"content-disposition\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentLanguage: {\n        serializedName: \"content-language\",\n        xmlName: \"content-language\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      blobType: {\n        serializedName: \"x-ms-blob-type\",\n        xmlName: \"x-ms-blob-type\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"BlockBlob\", \"PageBlob\", \"AppendBlob\"],\n        },\n      },\n      copyCompletionTime: {\n        serializedName: \"x-ms-copy-completion-time\",\n        xmlName: \"x-ms-copy-completion-time\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyStatusDescription: {\n        serializedName: \"x-ms-copy-status-description\",\n        xmlName: \"x-ms-copy-status-description\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyId: {\n        serializedName: \"x-ms-copy-id\",\n        xmlName: \"x-ms-copy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyProgress: {\n        serializedName: \"x-ms-copy-progress\",\n        xmlName: \"x-ms-copy-progress\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copySource: {\n        serializedName: \"x-ms-copy-source\",\n        xmlName: \"x-ms-copy-source\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        serializedName: \"x-ms-copy-status\",\n        xmlName: \"x-ms-copy-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"pending\", \"success\", \"aborted\", \"failed\"],\n        },\n      },\n      leaseDuration: {\n        serializedName: \"x-ms-lease-duration\",\n        xmlName: \"x-ms-lease-duration\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"infinite\", \"fixed\"],\n        },\n      },\n      leaseState: {\n        serializedName: \"x-ms-lease-state\",\n        xmlName: \"x-ms-lease-state\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\n            \"available\",\n            \"leased\",\n            \"expired\",\n            \"breaking\",\n            \"broken\",\n          ],\n        },\n      },\n      leaseStatus: {\n        serializedName: \"x-ms-lease-status\",\n        xmlName: \"x-ms-lease-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"locked\", \"unlocked\"],\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      acceptRanges: {\n        serializedName: \"accept-ranges\",\n        xmlName: \"accept-ranges\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobCommittedBlockCount: {\n        serializedName: \"x-ms-blob-committed-block-count\",\n        xmlName: \"x-ms-blob-committed-block-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-server-encrypted\",\n        xmlName: \"x-ms-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobContentMD5: {\n        serializedName: \"x-ms-blob-content-md5\",\n        xmlName: \"x-ms-blob-content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobQueryExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_queryExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobQueryExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobGetTagsHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_getTagsHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobGetTagsHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobGetTagsExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_getTagsExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobGetTagsExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetTagsHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setTagsHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetTagsHeaders\",\n    modelProperties: {\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlobSetTagsExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Blob_setTagsExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlobSetTagsExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobCreateHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_createHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobCreateHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobCreateExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_createExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobCreateExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobUploadPagesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_uploadPagesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobUploadPagesHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobUploadPagesExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_uploadPagesExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobUploadPagesExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobClearPagesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_clearPagesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobClearPagesHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobClearPagesExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_clearPagesExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobClearPagesExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobUploadPagesFromURLHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_uploadPagesFromURLHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobUploadPagesFromURLHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobUploadPagesFromURLExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"PageBlob_uploadPagesFromURLExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"PageBlobUploadPagesFromURLExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const PageBlobGetPageRangesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_getPageRangesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobGetPageRangesHeaders\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobContentLength: {\n        serializedName: \"x-ms-blob-content-length\",\n        xmlName: \"x-ms-blob-content-length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobGetPageRangesExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"PageBlob_getPageRangesExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"PageBlobGetPageRangesExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const PageBlobGetPageRangesDiffHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_getPageRangesDiffHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobGetPageRangesDiffHeaders\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobContentLength: {\n        serializedName: \"x-ms-blob-content-length\",\n        xmlName: \"x-ms-blob-content-length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobGetPageRangesDiffExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"PageBlob_getPageRangesDiffExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"PageBlobGetPageRangesDiffExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const PageBlobResizeHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_resizeHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobResizeHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobResizeExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_resizeExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobResizeExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobUpdateSequenceNumberHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_updateSequenceNumberHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobUpdateSequenceNumberHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobSequenceNumber: {\n        serializedName: \"x-ms-blob-sequence-number\",\n        xmlName: \"x-ms-blob-sequence-number\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobUpdateSequenceNumberExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"PageBlob_updateSequenceNumberExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"PageBlobUpdateSequenceNumberExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const PageBlobCopyIncrementalHeaders: coreClient.CompositeMapper = {\n  serializedName: \"PageBlob_copyIncrementalHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"PageBlobCopyIncrementalHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      copyId: {\n        serializedName: \"x-ms-copy-id\",\n        xmlName: \"x-ms-copy-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      copyStatus: {\n        serializedName: \"x-ms-copy-status\",\n        xmlName: \"x-ms-copy-status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"pending\", \"success\", \"aborted\", \"failed\"],\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const PageBlobCopyIncrementalExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"PageBlob_copyIncrementalExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"PageBlobCopyIncrementalExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const AppendBlobCreateHeaders: coreClient.CompositeMapper = {\n  serializedName: \"AppendBlob_createHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"AppendBlobCreateHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const AppendBlobCreateExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"AppendBlob_createExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"AppendBlobCreateExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const AppendBlobAppendBlockHeaders: coreClient.CompositeMapper = {\n  serializedName: \"AppendBlob_appendBlockHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"AppendBlobAppendBlockHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobAppendOffset: {\n        serializedName: \"x-ms-blob-append-offset\",\n        xmlName: \"x-ms-blob-append-offset\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobCommittedBlockCount: {\n        serializedName: \"x-ms-blob-committed-block-count\",\n        xmlName: \"x-ms-blob-committed-block-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const AppendBlobAppendBlockExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"AppendBlob_appendBlockExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"AppendBlobAppendBlockExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const AppendBlobAppendBlockFromUrlHeaders: coreClient.CompositeMapper = {\n  serializedName: \"AppendBlob_appendBlockFromUrlHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"AppendBlobAppendBlockFromUrlHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      blobAppendOffset: {\n        serializedName: \"x-ms-blob-append-offset\",\n        xmlName: \"x-ms-blob-append-offset\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobCommittedBlockCount: {\n        serializedName: \"x-ms-blob-committed-block-count\",\n        xmlName: \"x-ms-blob-committed-block-count\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const AppendBlobAppendBlockFromUrlExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"AppendBlob_appendBlockFromUrlExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"AppendBlobAppendBlockFromUrlExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const AppendBlobSealHeaders: coreClient.CompositeMapper = {\n  serializedName: \"AppendBlob_sealHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"AppendBlobSealHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isSealed: {\n        serializedName: \"x-ms-blob-sealed\",\n        xmlName: \"x-ms-blob-sealed\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n    },\n  },\n};\n\nexport const AppendBlobSealExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"AppendBlob_sealExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"AppendBlobSealExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobUploadHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_uploadHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobUploadHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobUploadExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_uploadExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobUploadExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobPutBlobFromUrlHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_putBlobFromUrlHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobPutBlobFromUrlHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobPutBlobFromUrlExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"BlockBlob_putBlobFromUrlExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlockBlobPutBlobFromUrlExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlockBlobStageBlockHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_stageBlockHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobStageBlockHeaders\",\n    modelProperties: {\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobStageBlockExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_stageBlockExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobStageBlockExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobStageBlockFromURLHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_stageBlockFromURLHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobStageBlockFromURLHeaders\",\n    modelProperties: {\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobStageBlockFromURLExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"BlockBlob_stageBlockFromURLExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlockBlobStageBlockFromURLExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlockBlobCommitBlockListHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_commitBlockListHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobCommitBlockListHeaders\",\n    modelProperties: {\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      contentMD5: {\n        serializedName: \"content-md5\",\n        xmlName: \"content-md5\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      xMsContentCrc64: {\n        serializedName: \"x-ms-content-crc64\",\n        xmlName: \"x-ms-content-crc64\",\n        type: {\n          name: \"ByteArray\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      versionId: {\n        serializedName: \"x-ms-version-id\",\n        xmlName: \"x-ms-version-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      isServerEncrypted: {\n        serializedName: \"x-ms-request-server-encrypted\",\n        xmlName: \"x-ms-request-server-encrypted\",\n        type: {\n          name: \"Boolean\",\n        },\n      },\n      encryptionKeySha256: {\n        serializedName: \"x-ms-encryption-key-sha256\",\n        xmlName: \"x-ms-encryption-key-sha256\",\n        type: {\n          name: \"String\",\n        },\n      },\n      encryptionScope: {\n        serializedName: \"x-ms-encryption-scope\",\n        xmlName: \"x-ms-encryption-scope\",\n        type: {\n          name: \"String\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobCommitBlockListExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"BlockBlob_commitBlockListExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlockBlobCommitBlockListExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n\nexport const BlockBlobGetBlockListHeaders: coreClient.CompositeMapper = {\n  serializedName: \"BlockBlob_getBlockListHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"BlockBlobGetBlockListHeaders\",\n    modelProperties: {\n      lastModified: {\n        serializedName: \"last-modified\",\n        xmlName: \"last-modified\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      etag: {\n        serializedName: \"etag\",\n        xmlName: \"etag\",\n        type: {\n          name: \"String\",\n        },\n      },\n      contentType: {\n        serializedName: \"content-type\",\n        xmlName: \"content-type\",\n        type: {\n          name: \"String\",\n        },\n      },\n      blobContentLength: {\n        serializedName: \"x-ms-blob-content-length\",\n        xmlName: \"x-ms-blob-content-length\",\n        type: {\n          name: \"Number\",\n        },\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\",\n        },\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\",\n        },\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\",\n        },\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\",\n        },\n      },\n    },\n  },\n};\n\nexport const BlockBlobGetBlockListExceptionHeaders: coreClient.CompositeMapper =\n  {\n    serializedName: \"BlockBlob_getBlockListExceptionHeaders\",\n    type: {\n      name: \"Composite\",\n      className: \"BlockBlobGetBlockListExceptionHeaders\",\n      modelProperties: {\n        errorCode: {\n          serializedName: \"x-ms-error-code\",\n          xmlName: \"x-ms-error-code\",\n          type: {\n            name: \"String\",\n          },\n        },\n      },\n    },\n  };\n"]}