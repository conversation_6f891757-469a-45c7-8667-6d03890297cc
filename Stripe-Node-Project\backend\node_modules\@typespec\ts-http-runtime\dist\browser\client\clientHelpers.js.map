{"version": 3, "file": "clientHelpers.js", "sourceRoot": "", "sources": ["../../../src/client/clientHelpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,uBAAuB,EAAE,MAAM,yBAAyB,CAAC;AAClE,OAAO,EAAE,yBAAyB,EAAE,MAAM,iCAAiC,CAAC;AAE5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EACL,kBAAkB,EAClB,iBAAiB,EACjB,uBAAuB,EACvB,uBAAuB,GACxB,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;AAC5F,OAAO,EAAE,yBAAyB,EAAE,MAAM,+CAA+C,CAAC;AAC1F,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;AAC5F,OAAO,EAAE,0BAA0B,EAAE,MAAM,gDAAgD,CAAC;AAE5F,IAAI,gBAAwC,CAAC;AAE7C;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,UAAyB,EAAE;IAC/D,MAAM,QAAQ,GAAG,yBAAyB,CAAC,OAAO,CAAC,CAAC;IAEpD,QAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;IAE9C,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,uBAAuB,EAAE,GAAG,OAAO,CAAC;IACrE,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,kBAAkB,CAAC,UAAU,CAAC,EAAE,CAAC;YACnC,QAAQ,CAAC,SAAS,CAChB,0BAA0B,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,uBAAuB,EAAE,CAAC,CACjF,CAAC;QACJ,CAAC;aAAM,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;YACzC,QAAQ,CAAC,SAAS,CAChB,yBAAyB,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,uBAAuB,EAAE,CAAC,CAChF,CAAC;QACJ,CAAC;aAAM,IAAI,uBAAuB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,QAAQ,CAAC,SAAS,CAChB,0BAA0B,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,uBAAuB,EAAE,CAAC,CACjF,CAAC;QACJ,CAAC;aAAM,IAAI,uBAAuB,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,QAAQ,CAAC,SAAS,CAChB,0BAA0B,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,uBAAuB,EAAE,CAAC,CACjF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,2BAA2B;IACzC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,GAAG,uBAAuB,EAAE,CAAC;IAC/C,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"../interfaces.js\";\nimport type { Pipeline } from \"../pipeline.js\";\nimport { createDefaultHttpClient } from \"../defaultHttpClient.js\";\nimport { createPipelineFromOptions } from \"../createPipelineFromOptions.js\";\nimport type { ClientOptions } from \"./common.js\";\nimport { apiVersionPolicy } from \"./apiVersionPolicy.js\";\nimport {\n  isApiKeyCredential,\n  isBasicCredential,\n  isBearerTokenCredential,\n  isOAuth2TokenCredential,\n} from \"../auth/credentials.js\";\nimport { apiKeyAuthenticationPolicy } from \"../policies/auth/apiKeyAuthenticationPolicy.js\";\nimport { basicAuthenticationPolicy } from \"../policies/auth/basicAuthenticationPolicy.js\";\nimport { bearerAuthenticationPolicy } from \"../policies/auth/bearerAuthenticationPolicy.js\";\nimport { oauth2AuthenticationPolicy } from \"../policies/auth/oauth2AuthenticationPolicy.js\";\n\nlet cachedHttpClient: HttpClient | undefined;\n\n/**\n * Creates a default rest pipeline to re-use accross Rest Level Clients\n */\nexport function createDefaultPipeline(options: ClientOptions = {}): Pipeline {\n  const pipeline = createPipelineFromOptions(options);\n\n  pipeline.addPolicy(apiVersionPolicy(options));\n\n  const { credential, authSchemes, allowInsecureConnection } = options;\n  if (credential) {\n    if (isApiKeyCredential(credential)) {\n      pipeline.addPolicy(\n        apiKeyAuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    } else if (isBasicCredential(credential)) {\n      pipeline.addPolicy(\n        basicAuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    } else if (isBearerTokenCredential(credential)) {\n      pipeline.addPolicy(\n        bearerAuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    } else if (isOAuth2TokenCredential(credential)) {\n      pipeline.addPolicy(\n        oauth2AuthenticationPolicy({ authSchemes, credential, allowInsecureConnection }),\n      );\n    }\n  }\n\n  return pipeline;\n}\n\nexport function getCachedDefaultHttpsClient(): HttpClient {\n  if (!cachedHttpClient) {\n    cachedHttpClient = createDefaultHttpClient();\n  }\n\n  return cachedHttpClient;\n}\n"]}