{"version": 3, "file": "BlobDownloadResponse.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/BlobDownloadResponse.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAClC,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAgB1C,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAG1E;;;;;;;;;GASG;AACH,MAAM,OAAO,oBAAoB;IAC/B;;;;;OAKG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;;OAKG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,kBAAkB;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;IAClD,CAAC;IAED;;;;;OAKG;IACH,IAAW,eAAe;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,IAAW,eAAe;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,IAAW,kBAAkB;QAC3B,OAAO,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC;IAClD,CAAC;IAED;;;;;OAKG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;IAC7C,CAAC;IAED;;;;;;;;;;;OAWG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;;OAKG;IACH,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;IAC3C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,eAAe;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;IACtC,CAAC;IAED;;;;;;;OAOG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,qBAAqB;QAC9B,OAAO,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;IAC7C,CAAC;IAED;;;;;OAKG;IACH,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;IAC1C,CAAC;IAED;;;;;OAKG;IACH,IAAW,WAAW;QACpB,OAAO,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;IAC3C,CAAC;IAED;;;;;OAKG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,IAAW,uBAAuB;QAChC,OAAO,IAAI,CAAC,gBAAgB,CAAC,uBAAuB,CAAC;IACvD,CAAC;IAED;;;;;OAKG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;;;;;OAQG;IACH,IAAW,iBAAiB;QAC1B,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC;IACjD,CAAC;IAED;;;;;;;;OAQG;IACH,IAAW,cAAc;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;;OAKG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACH,IAAW,eAAe;QACxB,OAAO,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;IAC/C,CAAC;IAED;;;;;OAKG;IACH,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;OAIG;IACH,IAAW,gBAAgB;QACzB,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACH,IAAW,mBAAmB;QAC5B,OAAO,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;IACnD,CAAC;IAED;;;;;OAKG;IACH,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,IAAW,oCAAoC;QAC7C,OAAO,IAAI,CAAC,gBAAgB,CAAC,oCAAoC,CAAC;IACpE,CAAC;IAED;;;;OAIG;IACH,IAAW,iCAAiC;QAC1C,OAAO,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC;IACjE,CAAC;IAED;;;;OAIG;IACH,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED;;;;OAIG;IACH,IAAW,2BAA2B;QACpC,OAAO,IAAI,CAAC,gBAAgB,CAAC,2BAA2B,CAAC;IAC3D,CAAC;IAED;;;;OAIG;IACH,IAAW,sBAAsB;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IACzC,CAAC;IAED;;;;;OAKG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IACxC,CAAC;IAED;;;;;;;OAOG;IACH,IAAW,kBAAkB;QAC3B,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;IACzC,CAAC;IAKD;;;;;;;;OAQG;IACH,YACE,gBAA4C,EAC5C,MAA4B,EAC5B,MAAc,EACd,KAAa,EACb,UAA0C,EAAE;QAE5C,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,kBAAkB,GAAG,IAAI,uBAAuB,CACnD,IAAI,CAAC,gBAAgB,CAAC,kBAAmB,EACzC,MAAM,EACN,MAAM,EACN,KAAK,EACL,OAAO,CACR,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nimport { isNode } from \"@azure/core-util\";\nimport type { BlobImmutabilityPolicyMode } from \"./generatedModels\";\n\nimport type {\n  BlobDownloadHeaders,\n  BlobType,\n  CopyStatusType,\n  LeaseDurationType,\n  LeaseStateType,\n  LeaseStatusType,\n} from \"./generatedModels\";\nimport type { BlobDownloadResponseParsed, Metadata, ObjectReplicationPolicy } from \"./models\";\nimport type {\n  ReadableStreamGetter,\n  RetriableReadableStreamOptions,\n} from \"./utils/RetriableReadableStream\";\nimport { RetriableReadableStream } from \"./utils/RetriableReadableStream\";\nimport type { ResponseWithHeaders } from \"./utils/utils.common\";\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * BlobDownloadResponse implements BlobDownloadResponseParsed interface, and in Node.js runtime it will\n * automatically retry when internal read stream unexpected ends. (This kind of unexpected ends cannot\n * trigger retries defined in pipeline retry policy.)\n *\n * The {@link readableStreamBody} stream will retry underlayer, you can just use it as a normal Node.js\n * Readable stream.\n */\nexport class BlobDownloadResponse implements BlobDownloadResponseParsed {\n  /**\n   * Indicates that the service supports\n   * requests for partial file content.\n   *\n   * @readonly\n   */\n  public get acceptRanges(): string | undefined {\n    return this.originalResponse.acceptRanges;\n  }\n\n  /**\n   * Returns if it was previously specified\n   * for the file.\n   *\n   * @readonly\n   */\n  public get cacheControl(): string | undefined {\n    return this.originalResponse.cacheControl;\n  }\n\n  /**\n   * Returns the value that was specified\n   * for the 'x-ms-content-disposition' header and specifies how to process the\n   * response.\n   *\n   * @readonly\n   */\n  public get contentDisposition(): string | undefined {\n    return this.originalResponse.contentDisposition;\n  }\n\n  /**\n   * Returns the value that was specified\n   * for the Content-Encoding request header.\n   *\n   * @readonly\n   */\n  public get contentEncoding(): string | undefined {\n    return this.originalResponse.contentEncoding;\n  }\n\n  /**\n   * Returns the value that was specified\n   * for the Content-Language request header.\n   *\n   * @readonly\n   */\n  public get contentLanguage(): string | undefined {\n    return this.originalResponse.contentLanguage;\n  }\n\n  /**\n   * The current sequence number for a\n   * page blob. This header is not returned for block blobs or append blobs.\n   *\n   * @readonly\n   */\n  public get blobSequenceNumber(): number | undefined {\n    return this.originalResponse.blobSequenceNumber;\n  }\n\n  /**\n   * The blob's type. Possible values include:\n   * 'BlockBlob', 'PageBlob', 'AppendBlob'.\n   *\n   * @readonly\n   */\n  public get blobType(): BlobType | undefined {\n    return this.originalResponse.blobType;\n  }\n\n  /**\n   * The number of bytes present in the\n   * response body.\n   *\n   * @readonly\n   */\n  public get contentLength(): number | undefined {\n    return this.originalResponse.contentLength;\n  }\n\n  /**\n   * If the file has an MD5 hash and the\n   * request is to read the full file, this response header is returned so that\n   * the client can check for message content integrity. If the request is to\n   * read a specified range and the 'x-ms-range-get-content-md5' is set to\n   * true, then the request returns an MD5 hash for the range, as long as the\n   * range size is less than or equal to 4 MB. If neither of these sets of\n   * conditions is true, then no value is returned for the 'Content-MD5'\n   * header.\n   *\n   * @readonly\n   */\n  public get contentMD5(): Uint8Array | undefined {\n    return this.originalResponse.contentMD5;\n  }\n\n  /**\n   * Indicates the range of bytes returned if\n   * the client requested a subset of the file by setting the Range request\n   * header.\n   *\n   * @readonly\n   */\n  public get contentRange(): string | undefined {\n    return this.originalResponse.contentRange;\n  }\n\n  /**\n   * The content type specified for the file.\n   * The default content type is 'application/octet-stream'\n   *\n   * @readonly\n   */\n  public get contentType(): string | undefined {\n    return this.originalResponse.contentType;\n  }\n\n  /**\n   * Conclusion time of the last attempted\n   * Copy File operation where this file was the destination file. This value\n   * can specify the time of a completed, aborted, or failed copy attempt.\n   *\n   * @readonly\n   */\n  public get copyCompletedOn(): Date | undefined {\n    return this.originalResponse.copyCompletedOn;\n  }\n\n  /**\n   * String identifier for the last attempted Copy\n   * File operation where this file was the destination file.\n   *\n   * @readonly\n   */\n  public get copyId(): string | undefined {\n    return this.originalResponse.copyId;\n  }\n\n  /**\n   * Contains the number of bytes copied and\n   * the total bytes in the source in the last attempted Copy File operation\n   * where this file was the destination file. Can show between 0 and\n   * Content-Length bytes copied.\n   *\n   * @readonly\n   */\n  public get copyProgress(): string | undefined {\n    return this.originalResponse.copyProgress;\n  }\n\n  /**\n   * URL up to 2KB in length that specifies the\n   * source file used in the last attempted Copy File operation where this file\n   * was the destination file.\n   *\n   * @readonly\n   */\n  public get copySource(): string | undefined {\n    return this.originalResponse.copySource;\n  }\n\n  /**\n   * State of the copy operation\n   * identified by 'x-ms-copy-id'. Possible values include: 'pending',\n   * 'success', 'aborted', 'failed'\n   *\n   * @readonly\n   */\n  public get copyStatus(): CopyStatusType | undefined {\n    return this.originalResponse.copyStatus;\n  }\n\n  /**\n   * Only appears when\n   * x-ms-copy-status is failed or pending. Describes cause of fatal or\n   * non-fatal copy operation failure.\n   *\n   * @readonly\n   */\n  public get copyStatusDescription(): string | undefined {\n    return this.originalResponse.copyStatusDescription;\n  }\n\n  /**\n   * When a blob is leased,\n   * specifies whether the lease is of infinite or fixed duration. Possible\n   * values include: 'infinite', 'fixed'.\n   *\n   * @readonly\n   */\n  public get leaseDuration(): LeaseDurationType | undefined {\n    return this.originalResponse.leaseDuration;\n  }\n\n  /**\n   * Lease state of the blob. Possible\n   * values include: 'available', 'leased', 'expired', 'breaking', 'broken'.\n   *\n   * @readonly\n   */\n  public get leaseState(): LeaseStateType | undefined {\n    return this.originalResponse.leaseState;\n  }\n\n  /**\n   * The current lease status of the\n   * blob. Possible values include: 'locked', 'unlocked'.\n   *\n   * @readonly\n   */\n  public get leaseStatus(): LeaseStatusType | undefined {\n    return this.originalResponse.leaseStatus;\n  }\n\n  /**\n   * A UTC date/time value generated by the service that\n   * indicates the time at which the response was initiated.\n   *\n   * @readonly\n   */\n  public get date(): Date | undefined {\n    return this.originalResponse.date;\n  }\n\n  /**\n   * The number of committed blocks\n   * present in the blob. This header is returned only for append blobs.\n   *\n   * @readonly\n   */\n  public get blobCommittedBlockCount(): number | undefined {\n    return this.originalResponse.blobCommittedBlockCount;\n  }\n\n  /**\n   * The ETag contains a value that you can use to\n   * perform operations conditionally, in quotes.\n   *\n   * @readonly\n   */\n  public get etag(): string | undefined {\n    return this.originalResponse.etag;\n  }\n\n  /**\n   * The number of tags associated with the blob\n   *\n   * @readonly\n   */\n  public get tagCount(): number | undefined {\n    return this.originalResponse.tagCount;\n  }\n\n  /**\n   * The error code.\n   *\n   * @readonly\n   */\n  public get errorCode(): string | undefined {\n    return this.originalResponse.errorCode;\n  }\n\n  /**\n   * The value of this header is set to\n   * true if the file data and application metadata are completely encrypted\n   * using the specified algorithm. Otherwise, the value is set to false (when\n   * the file is unencrypted, or if only parts of the file/application metadata\n   * are encrypted).\n   *\n   * @readonly\n   */\n  public get isServerEncrypted(): boolean | undefined {\n    return this.originalResponse.isServerEncrypted;\n  }\n\n  /**\n   * If the blob has a MD5 hash, and if\n   * request contains range header (Range or x-ms-range), this response header\n   * is returned with the value of the whole blob's MD5 value. This value may\n   * or may not be equal to the value returned in Content-MD5 header, with the\n   * latter calculated from the requested range.\n   *\n   * @readonly\n   */\n  public get blobContentMD5(): Uint8Array | undefined {\n    return this.originalResponse.blobContentMD5;\n  }\n\n  /**\n   * Returns the date and time the file was last\n   * modified. Any operation that modifies the file or its properties updates\n   * the last modified time.\n   *\n   * @readonly\n   */\n  public get lastModified(): Date | undefined {\n    return this.originalResponse.lastModified;\n  }\n\n  /**\n   * Returns the UTC date and time generated by the service that indicates the time at which the blob was\n   * last read or written to.\n   *\n   * @readonly\n   */\n  public get lastAccessed(): Date | undefined {\n    return this.originalResponse.lastAccessed;\n  }\n\n  /**\n   * Returns the date and time the blob was created.\n   *\n   * @readonly\n   */\n  public get createdOn(): Date | undefined {\n    return this.originalResponse.createdOn;\n  }\n\n  /**\n   * A name-value pair\n   * to associate with a file storage object.\n   *\n   * @readonly\n   */\n  public get metadata(): Metadata | undefined {\n    return this.originalResponse.metadata;\n  }\n\n  /**\n   * This header uniquely identifies the request\n   * that was made and can be used for troubleshooting the request.\n   *\n   * @readonly\n   */\n  public get requestId(): string | undefined {\n    return this.originalResponse.requestId;\n  }\n\n  /**\n   * If a client request id header is sent in the request, this header will be present in the\n   * response with the same value.\n   *\n   * @readonly\n   */\n  public get clientRequestId(): string | undefined {\n    return this.originalResponse.clientRequestId;\n  }\n\n  /**\n   * Indicates the version of the Blob service used\n   * to execute the request.\n   *\n   * @readonly\n   */\n  public get version(): string | undefined {\n    return this.originalResponse.version;\n  }\n\n  /**\n   * Indicates the versionId of the downloaded blob version.\n   *\n   * @readonly\n   */\n  public get versionId(): string | undefined {\n    return this.originalResponse.versionId;\n  }\n\n  /**\n   * Indicates whether version of this blob is a current version.\n   *\n   * @readonly\n   */\n  public get isCurrentVersion(): boolean | undefined {\n    return this.originalResponse.isCurrentVersion;\n  }\n\n  /**\n   * The SHA-256 hash of the encryption key used to encrypt the blob. This value is only returned\n   * when the blob was encrypted with a customer-provided key.\n   *\n   * @readonly\n   */\n  public get encryptionKeySha256(): string | undefined {\n    return this.originalResponse.encryptionKeySha256;\n  }\n\n  /**\n   * If the request is to read a specified range and the x-ms-range-get-content-crc64 is set to\n   * true, then the request returns a crc64 for the range, as long as the range size is less than\n   * or equal to 4 MB. If both x-ms-range-get-content-crc64 & x-ms-range-get-content-md5 is\n   * specified in the same request, it will fail with 400(Bad Request)\n   */\n  public get contentCrc64(): Uint8Array | undefined {\n    return this.originalResponse.contentCrc64;\n  }\n\n  /**\n   * Object Replication Policy Id of the destination blob.\n   *\n   * @readonly\n   */\n  public get objectReplicationDestinationPolicyId(): string | undefined {\n    return this.originalResponse.objectReplicationDestinationPolicyId;\n  }\n\n  /**\n   * Parsed Object Replication Policy Id, Rule Id(s) and status of the source blob.\n   *\n   * @readonly\n   */\n  public get objectReplicationSourceProperties(): ObjectReplicationPolicy[] | undefined {\n    return this.originalResponse.objectReplicationSourceProperties;\n  }\n\n  /**\n   * If this blob has been sealed.\n   *\n   * @readonly\n   */\n  public get isSealed(): boolean | undefined {\n    return this.originalResponse.isSealed;\n  }\n\n  /**\n   * UTC date/time value generated by the service that indicates the time at which the blob immutability policy will expire.\n   *\n   * @readonly\n   */\n  public get immutabilityPolicyExpiresOn(): Date | undefined {\n    return this.originalResponse.immutabilityPolicyExpiresOn;\n  }\n\n  /**\n   * Indicates immutability policy mode.\n   *\n   * @readonly\n   */\n  public get immutabilityPolicyMode(): BlobImmutabilityPolicyMode | undefined {\n    return this.originalResponse.immutabilityPolicyMode;\n  }\n\n  /**\n   * Indicates if a legal hold is present on the blob.\n   *\n   * @readonly\n   */\n  public get legalHold(): boolean | undefined {\n    return this.originalResponse.legalHold;\n  }\n\n  /**\n   * The response body as a browser Blob.\n   * Always undefined in node.js.\n   *\n   * @readonly\n   */\n  public get contentAsBlob(): Promise<Blob> | undefined {\n    return this.originalResponse.blobBody;\n  }\n\n  /**\n   * The response body as a node.js Readable stream.\n   * Always undefined in the browser.\n   *\n   * It will automatically retry when internal read stream unexpected ends.\n   *\n   * @readonly\n   */\n  public get readableStreamBody(): NodeJS.ReadableStream | undefined {\n    return isNode ? this.blobDownloadStream : undefined;\n  }\n\n  /**\n   * The HTTP response.\n   */\n  public get _response(): ResponseWithHeaders<BlobDownloadHeaders>[\"_response\"] {\n    return this.originalResponse._response;\n  }\n\n  private originalResponse: BlobDownloadResponseParsed;\n  private blobDownloadStream?: RetriableReadableStream;\n\n  /**\n   * Creates an instance of BlobDownloadResponse.\n   *\n   * @param originalResponse -\n   * @param getter -\n   * @param offset -\n   * @param count -\n   * @param options -\n   */\n  public constructor(\n    originalResponse: BlobDownloadResponseParsed,\n    getter: ReadableStreamGetter,\n    offset: number,\n    count: number,\n    options: RetriableReadableStreamOptions = {},\n  ) {\n    this.originalResponse = originalResponse;\n    this.blobDownloadStream = new RetriableReadableStream(\n      this.originalResponse.readableStreamBody!,\n      getter,\n      offset,\n      count,\n      options,\n    );\n  }\n}\n"]}