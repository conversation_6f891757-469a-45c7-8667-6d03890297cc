{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/models/index.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAq1EH,gFAAgF;AAChF,MAAM,CAAN,IAAY,4BAGX;AAHD,WAAY,4BAA4B;IACtC,aAAa;IACb,iDAAiB,CAAA;AACnB,CAAC,EAHW,4BAA4B,KAA5B,4BAA4B,QAGvC;AAWD,0EAA0E;AAC1E,MAAM,CAAN,IAAY,sBASX;AATD,WAAY,sBAAsB;IAChC,kBAAkB;IAClB,qDAA2B,CAAA;IAC3B,yBAAyB;IACzB,mEAAyC,CAAA;IACzC,oBAAoB;IACpB,yDAA+B,CAAA;IAC/B,eAAe;IACf,+CAAqB,CAAA;AACvB,CAAC,EATW,sBAAsB,KAAtB,sBAAsB,QASjC;AAcD,yEAAyE;AACzE,MAAM,CAAN,IAAY,qBAqOX;AArOD,WAAY,qBAAqB;IAC/B,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,mCAAmC;IACnC,sFAA6D,CAAA;IAC7D,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,qCAAqC;IACrC,0FAAiE,CAAA;IACjE,oBAAoB;IACpB,wDAA+B,CAAA;IAC/B,gCAAgC;IAChC,gFAAuD,CAAA;IACvD,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,mBAAmB;IACnB,sDAA6B,CAAA;IAC7B,iBAAiB;IACjB,kDAAyB,CAAA;IACzB,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,iCAAiC;IACjC,kFAAyD,CAAA;IACzD,mBAAmB;IACnB,sDAA6B,CAAA;IAC7B,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,iBAAiB;IACjB,kDAAyB,CAAA;IACzB,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,kBAAkB;IAClB,oDAA2B,CAAA;IAC3B,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,iCAAiC;IACjC,kFAAyD,CAAA;IACzD,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,2CAA2C;IAC3C,sGAA6E,CAAA;IAC7E,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,8BAA8B;IAC9B,4EAAmD,CAAA;IACnD,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,iBAAiB;IACjB,kDAAyB,CAAA;IACzB,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,gCAAgC;IAChC,gFAAuD,CAAA;IACvD,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,+BAA+B;IAC/B,8EAAqD,CAAA;IACrD,mBAAmB;IACnB,sDAA6B,CAAA;IAC7B,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,yCAAyC;IACzC,kGAAyE,CAAA;IACzE,0CAA0C;IAC1C,oGAA2E,CAAA;IAC3E,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,8BAA8B;IAC9B,4EAAmD,CAAA;IACnD,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,uCAAuC;IACvC,8FAAqE,CAAA;IACrE,qCAAqC;IACrC,0FAAiE,CAAA;IACjE,qBAAqB;IACrB,0DAAiC,CAAA;IACjC,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,kCAAkC;IAClC,oFAA2D,CAAA;IAC3D,wDAAwD;IACxD,gIAAuG,CAAA;IACvG,0CAA0C;IAC1C,oGAA2E,CAAA;IAC3E,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,qBAAqB;IACrB,0DAAiC,CAAA;IACjC,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,yCAAyC;IACzC,kGAAyE,CAAA;IACzE,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,uCAAuC;IACvC,8FAAqE,CAAA;IACrE,4CAA4C;IAC5C,wGAA+E,CAAA;IAC/E,wCAAwC;IACxC,gGAAuE,CAAA;IACvE,qBAAqB;IACrB,0DAAiC,CAAA;IACjC,yCAAyC;IACzC,kGAAyE,CAAA;IACzE,wCAAwC;IACxC,gGAAuE,CAAA;IACvE,sCAAsC;IACtC,4FAAmE,CAAA;IACnE,gBAAgB;IAChB,gDAAuB,CAAA;IACvB,uCAAuC;IACvC,8FAAqE,CAAA;IACrE,4CAA4C;IAC5C,wGAA+E,CAAA;IAC/E,wCAAwC;IACxC,gGAAuE,CAAA;IACvE,iCAAiC;IACjC,kFAAyD,CAAA;IACzD,kCAAkC;IAClC,oFAA2D,CAAA;IAC3D,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,+CAA+C;IAC/C,8GAAqF,CAAA;IACrF,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,+BAA+B;IAC/B,8EAAqD,CAAA;IACrD,4CAA4C;IAC5C,wGAA+E,CAAA;IAC/E,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,sCAAsC;IACtC,4FAAmE,CAAA;IACnE,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,kBAAkB;IAClB,oDAA2B,CAAA;IAC3B,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,gCAAgC;IAChC,gFAAuD,CAAA;IACvD,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,mBAAmB;IACnB,sDAA6B,CAAA;IAC7B,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,sCAAsC;IACtC,4FAAmE,CAAA;IACnE,mCAAmC;IACnC,sFAA6D,CAAA;IAC7D,wCAAwC;IACxC,gGAAuE,CAAA;IACvE,+CAA+C;IAC/C,8GAAqF,CAAA;AACvF,CAAC,EArOW,qBAAqB,KAArB,qBAAqB,QAqOhC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreHttpCompat from \"@azure/core-http-compat\";\n\n/** Storage Service Properties. */\nexport interface BlobServiceProperties {\n  /** Azure Analytics Logging settings. */\n  blobAnalyticsLogging?: Logging;\n  /** a summary of request statistics grouped by API in hour or minute aggregates for blobs */\n  hourMetrics?: Metrics;\n  /** a summary of request statistics grouped by API in hour or minute aggregates for blobs */\n  minuteMetrics?: Metrics;\n  /** The set of CORS rules. */\n  cors?: CorsRule[];\n  /** The default version to use for requests to the Blob service if an incoming request's version is not specified. Possible values include version 2008-10-27 and all more recent versions */\n  defaultServiceVersion?: string;\n  /** the retention policy which determines how long the associated data should persist */\n  deleteRetentionPolicy?: RetentionPolicy;\n  /** The properties that enable an account to host a static website */\n  staticWebsite?: StaticWebsite;\n}\n\n/** Azure Analytics Logging settings. */\nexport interface Logging {\n  /** The version of Storage Analytics to configure. */\n  version: string;\n  /** Indicates whether all delete requests should be logged. */\n  deleteProperty: boolean;\n  /** Indicates whether all read requests should be logged. */\n  read: boolean;\n  /** Indicates whether all write requests should be logged. */\n  write: boolean;\n  /** the retention policy which determines how long the associated data should persist */\n  retentionPolicy: RetentionPolicy;\n}\n\n/** the retention policy which determines how long the associated data should persist */\nexport interface RetentionPolicy {\n  /** Indicates whether a retention policy is enabled for the storage service */\n  enabled: boolean;\n  /** Indicates the number of days that metrics or logging or soft-deleted data should be retained. All data older than this value will be deleted */\n  days?: number;\n}\n\n/** a summary of request statistics grouped by API in hour or minute aggregates for blobs */\nexport interface Metrics {\n  /** The version of Storage Analytics to configure. */\n  version?: string;\n  /** Indicates whether metrics are enabled for the Blob service. */\n  enabled: boolean;\n  /** Indicates whether metrics should generate summary statistics for called API operations. */\n  includeAPIs?: boolean;\n  /** the retention policy which determines how long the associated data should persist */\n  retentionPolicy?: RetentionPolicy;\n}\n\n/** CORS is an HTTP feature that enables a web application running under one domain to access resources in another domain. Web browsers implement a security restriction known as same-origin policy that prevents a web page from calling APIs in a different domain; CORS provides a secure way to allow one domain (the origin domain) to call APIs in another domain */\nexport interface CorsRule {\n  /** The origin domains that are permitted to make a request against the storage service via CORS. The origin domain is the domain from which the request originates. Note that the origin must be an exact case-sensitive match with the origin that the user age sends to the service. You can also use the wildcard character '*' to allow all origin domains to make requests via CORS. */\n  allowedOrigins: string;\n  /** The methods (HTTP request verbs) that the origin domain may use for a CORS request. (comma separated) */\n  allowedMethods: string;\n  /** the request headers that the origin domain may specify on the CORS request. */\n  allowedHeaders: string;\n  /** The response headers that may be sent in the response to the CORS request and exposed by the browser to the request issuer */\n  exposedHeaders: string;\n  /** The maximum amount time that a browser should cache the preflight OPTIONS request. */\n  maxAgeInSeconds: number;\n}\n\n/** The properties that enable an account to host a static website */\nexport interface StaticWebsite {\n  /** Indicates whether this account is hosting a static website */\n  enabled: boolean;\n  /** The default name of the index page under each directory */\n  indexDocument?: string;\n  /** The absolute path of the custom 404 page */\n  errorDocument404Path?: string;\n  /** Absolute path of the default index page */\n  defaultIndexDocumentPath?: string;\n}\n\nexport interface StorageError {\n  message?: string;\n  code?: string;\n  authenticationErrorDetail?: string;\n}\n\n/** Stats for the storage service. */\nexport interface BlobServiceStatistics {\n  /** Geo-Replication information for the Secondary Storage Service */\n  geoReplication?: GeoReplication;\n}\n\n/** Geo-Replication information for the Secondary Storage Service */\nexport interface GeoReplication {\n  /** The status of the secondary location */\n  status: GeoReplicationStatusType;\n  /** A GMT date/time value, to the second. All primary writes preceding this value are guaranteed to be available for read operations at the secondary. Primary writes after this point in time may or may not be available for reads. */\n  lastSyncOn: Date;\n}\n\n/** An enumeration of containers */\nexport interface ListContainersSegmentResponse {\n  serviceEndpoint: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  containerItems: ContainerItem[];\n  continuationToken?: string;\n}\n\n/** An Azure Storage container */\nexport interface ContainerItem {\n  name: string;\n  deleted?: boolean;\n  version?: string;\n  /** Properties of a container */\n  properties: ContainerProperties;\n  /** Dictionary of <string> */\n  metadata?: { [propertyName: string]: string };\n}\n\n/** Properties of a container */\nexport interface ContainerProperties {\n  lastModified: Date;\n  etag: string;\n  leaseStatus?: LeaseStatusType;\n  leaseState?: LeaseStateType;\n  leaseDuration?: LeaseDurationType;\n  publicAccess?: PublicAccessType;\n  hasImmutabilityPolicy?: boolean;\n  hasLegalHold?: boolean;\n  defaultEncryptionScope?: string;\n  preventEncryptionScopeOverride?: boolean;\n  deletedOn?: Date;\n  remainingRetentionDays?: number;\n  /** Indicates if version level worm is enabled on this container. */\n  isImmutableStorageWithVersioningEnabled?: boolean;\n}\n\n/** Key information */\nexport interface KeyInfo {\n  /** The date-time the key is active in ISO 8601 UTC time */\n  startsOn: string;\n  /** The date-time the key expires in ISO 8601 UTC time */\n  expiresOn: string;\n}\n\n/** A user delegation key */\nexport interface UserDelegationKey {\n  /** The Azure Active Directory object ID in GUID format. */\n  signedObjectId: string;\n  /** The Azure Active Directory tenant ID in GUID format */\n  signedTenantId: string;\n  /** The date-time the key is active */\n  signedStartsOn: string;\n  /** The date-time the key expires */\n  signedExpiresOn: string;\n  /** Abbreviation of the Azure Storage service that accepts the key */\n  signedService: string;\n  /** The service version that created the key */\n  signedVersion: string;\n  /** The key as a base64 string */\n  value: string;\n}\n\n/** The result of a Filter Blobs API call */\nexport interface FilterBlobSegment {\n  serviceEndpoint: string;\n  where: string;\n  blobs: FilterBlobItem[];\n  continuationToken?: string;\n}\n\n/** Blob info from a Filter Blobs API call */\nexport interface FilterBlobItem {\n  name: string;\n  containerName: string;\n  /** Blob tags */\n  tags?: BlobTags;\n}\n\n/** Blob tags */\nexport interface BlobTags {\n  blobTagSet: BlobTag[];\n}\n\nexport interface BlobTag {\n  key: string;\n  value: string;\n}\n\n/** signed identifier */\nexport interface SignedIdentifier {\n  /** a unique id */\n  id: string;\n  /** An Access policy */\n  accessPolicy: AccessPolicy;\n}\n\n/** An Access policy */\nexport interface AccessPolicy {\n  /** the date-time the policy is active */\n  startsOn?: string;\n  /** the date-time the policy expires */\n  expiresOn?: string;\n  /** the permissions for the acl policy */\n  permissions?: string;\n}\n\n/** An enumeration of blobs */\nexport interface ListBlobsFlatSegmentResponse {\n  serviceEndpoint: string;\n  containerName: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  segment: BlobFlatListSegment;\n  continuationToken?: string;\n}\n\nexport interface BlobFlatListSegment {\n  blobItems: BlobItemInternal[];\n}\n\n/** An Azure Storage blob */\nexport interface BlobItemInternal {\n  name: BlobName;\n  deleted: boolean;\n  snapshot: string;\n  versionId?: string;\n  isCurrentVersion?: boolean;\n  /** Properties of a blob */\n  properties: BlobPropertiesInternal;\n  /** Dictionary of <string> */\n  metadata?: { [propertyName: string]: string };\n  /** Blob tags */\n  blobTags?: BlobTags;\n  /** Dictionary of <string> */\n  objectReplicationMetadata?: { [propertyName: string]: string };\n  /** Inactive root blobs which have any versions would have such tag with value true. */\n  hasVersionsOnly?: boolean;\n}\n\nexport interface BlobName {\n  /** Indicates if the blob name is encoded. */\n  encoded?: boolean;\n  /** The name of the blob. */\n  content?: string;\n}\n\n/** Properties of a blob */\nexport interface BlobPropertiesInternal {\n  createdOn?: Date;\n  lastModified: Date;\n  etag: string;\n  /** Size in bytes */\n  contentLength?: number;\n  contentType?: string;\n  contentEncoding?: string;\n  contentLanguage?: string;\n  contentMD5?: Uint8Array;\n  contentDisposition?: string;\n  cacheControl?: string;\n  blobSequenceNumber?: number;\n  blobType?: BlobType;\n  leaseStatus?: LeaseStatusType;\n  leaseState?: LeaseStateType;\n  leaseDuration?: LeaseDurationType;\n  copyId?: string;\n  copyStatus?: CopyStatusType;\n  copySource?: string;\n  copyProgress?: string;\n  copyCompletedOn?: Date;\n  copyStatusDescription?: string;\n  serverEncrypted?: boolean;\n  incrementalCopy?: boolean;\n  destinationSnapshot?: string;\n  deletedOn?: Date;\n  remainingRetentionDays?: number;\n  accessTier?: AccessTier;\n  accessTierInferred?: boolean;\n  archiveStatus?: ArchiveStatus;\n  customerProvidedKeySha256?: string;\n  /** The name of the encryption scope under which the blob is encrypted. */\n  encryptionScope?: string;\n  accessTierChangedOn?: Date;\n  tagCount?: number;\n  expiresOn?: Date;\n  isSealed?: boolean;\n  /** If an object is in rehydrate pending state then this header is returned with priority of rehydrate. Valid values are High and Standard. */\n  rehydratePriority?: RehydratePriority;\n  lastAccessedOn?: Date;\n  /** UTC date/time value generated by the service that indicates the time at which the blob immutability policy will expire. */\n  immutabilityPolicyExpiresOn?: Date;\n  /** Indicates immutability policy mode. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Indicates if a legal hold is present on the blob. */\n  legalHold?: boolean;\n}\n\n/** An enumeration of blobs */\nexport interface ListBlobsHierarchySegmentResponse {\n  serviceEndpoint: string;\n  containerName: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  delimiter?: string;\n  segment: BlobHierarchyListSegment;\n  continuationToken?: string;\n}\n\nexport interface BlobHierarchyListSegment {\n  blobPrefixes?: BlobPrefix[];\n  blobItems: BlobItemInternal[];\n}\n\nexport interface BlobPrefix {\n  name: BlobName;\n}\n\nexport interface BlockLookupList {\n  committed?: string[];\n  uncommitted?: string[];\n  latest?: string[];\n}\n\nexport interface BlockList {\n  committedBlocks?: Block[];\n  uncommittedBlocks?: Block[];\n}\n\n/** Represents a single block in a block blob.  It describes the block's ID and size. */\nexport interface Block {\n  /** The base64 encoded block ID. */\n  name: string;\n  /** The block size in bytes. */\n  size: number;\n}\n\n/** the list of pages */\nexport interface PageList {\n  pageRange?: PageRange[];\n  clearRange?: ClearRange[];\n  continuationToken?: string;\n}\n\nexport interface PageRange {\n  start: number;\n  end: number;\n}\n\nexport interface ClearRange {\n  start: number;\n  end: number;\n}\n\n/** Groups the set of query request settings. */\nexport interface QueryRequest {\n  /** Required. The type of the provided query expression. */\n  queryType: string;\n  /** The query expression in SQL. The maximum size of the query expression is 256KiB. */\n  expression: string;\n  inputSerialization?: QuerySerialization;\n  outputSerialization?: QuerySerialization;\n}\n\nexport interface QuerySerialization {\n  format: QueryFormat;\n}\n\nexport interface QueryFormat {\n  /** The quick query format type. */\n  type: QueryFormatType;\n  /** Groups the settings used for interpreting the blob data if the blob is delimited text formatted. */\n  delimitedTextConfiguration?: DelimitedTextConfiguration;\n  /** json text configuration */\n  jsonTextConfiguration?: JsonTextConfiguration;\n  /** Groups the settings used for formatting the response if the response should be Arrow formatted. */\n  arrowConfiguration?: ArrowConfiguration;\n  /** parquet configuration */\n  parquetTextConfiguration?: Record<string, unknown>;\n}\n\n/** Groups the settings used for interpreting the blob data if the blob is delimited text formatted. */\nexport interface DelimitedTextConfiguration {\n  /** The string used to separate columns. */\n  columnSeparator?: string;\n  /** The string used to quote a specific field. */\n  fieldQuote?: string;\n  /** The string used to separate records. */\n  recordSeparator?: string;\n  /** The string used as an escape character. */\n  escapeChar?: string;\n  /** Represents whether the data has headers. */\n  headersPresent?: boolean;\n}\n\n/** json text configuration */\nexport interface JsonTextConfiguration {\n  /** The string used to separate records. */\n  recordSeparator?: string;\n}\n\n/** Groups the settings used for formatting the response if the response should be Arrow formatted. */\nexport interface ArrowConfiguration {\n  schema: ArrowField[];\n}\n\n/** Groups settings regarding specific field of an arrow schema */\nexport interface ArrowField {\n  type: string;\n  name?: string;\n  precision?: number;\n  scale?: number;\n}\n\n/** Defines headers for Service_setProperties operation. */\nexport interface ServiceSetPropertiesHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_setProperties operation. */\nexport interface ServiceSetPropertiesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getProperties operation. */\nexport interface ServiceGetPropertiesHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getProperties operation. */\nexport interface ServiceGetPropertiesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getStatistics operation. */\nexport interface ServiceGetStatisticsHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getStatistics operation. */\nexport interface ServiceGetStatisticsExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_listContainersSegment operation. */\nexport interface ServiceListContainersSegmentHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_listContainersSegment operation. */\nexport interface ServiceListContainersSegmentExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getUserDelegationKey operation. */\nexport interface ServiceGetUserDelegationKeyHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getUserDelegationKey operation. */\nexport interface ServiceGetUserDelegationKeyExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getAccountInfo operation. */\nexport interface ServiceGetAccountInfoHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Identifies the sku name of the account */\n  skuName?: SkuName;\n  /** Identifies the account kind */\n  accountKind?: AccountKind;\n  /** Version 2019-07-07 and newer. Indicates if the account has a hierarchical namespace enabled. */\n  isHierarchicalNamespaceEnabled?: boolean;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getAccountInfo operation. */\nexport interface ServiceGetAccountInfoExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_submitBatch operation. */\nexport interface ServiceSubmitBatchHeaders {\n  /** The media type of the body of the response. For batch requests, this is multipart/mixed; boundary=batchresponse_GUID */\n  contentType?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_submitBatch operation. */\nexport interface ServiceSubmitBatchExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Service_filterBlobs operation. */\nexport interface ServiceFilterBlobsHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_filterBlobs operation. */\nexport interface ServiceFilterBlobsExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_create operation. */\nexport interface ContainerCreateHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_create operation. */\nexport interface ContainerCreateExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_getProperties operation. */\nexport interface ContainerGetPropertiesHeaders {\n  metadata?: { [propertyName: string]: string };\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** When a blob is leased, specifies whether the lease is of infinite or fixed duration. */\n  leaseDuration?: LeaseDurationType;\n  /** Lease state of the blob. */\n  leaseState?: LeaseStateType;\n  /** The current lease status of the blob. */\n  leaseStatus?: LeaseStatusType;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Indicated whether data in the container may be accessed publicly and the level of access */\n  blobPublicAccess?: PublicAccessType;\n  /** Indicates whether the container has an immutability policy set on it. */\n  hasImmutabilityPolicy?: boolean;\n  /** Indicates whether the container has a legal hold. */\n  hasLegalHold?: boolean;\n  /** The default encryption scope for the container. */\n  defaultEncryptionScope?: string;\n  /** Indicates whether the container's default encryption scope can be overriden. */\n  denyEncryptionScopeOverride?: boolean;\n  /** Indicates whether version level worm is enabled on a container. */\n  isImmutableStorageWithVersioningEnabled?: boolean;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_getProperties operation. */\nexport interface ContainerGetPropertiesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_delete operation. */\nexport interface ContainerDeleteHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_delete operation. */\nexport interface ContainerDeleteExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_setMetadata operation. */\nexport interface ContainerSetMetadataHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_setMetadata operation. */\nexport interface ContainerSetMetadataExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_getAccessPolicy operation. */\nexport interface ContainerGetAccessPolicyHeaders {\n  /** Indicated whether data in the container may be accessed publicly and the level of access */\n  blobPublicAccess?: PublicAccessType;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_getAccessPolicy operation. */\nexport interface ContainerGetAccessPolicyExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_setAccessPolicy operation. */\nexport interface ContainerSetAccessPolicyHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_setAccessPolicy operation. */\nexport interface ContainerSetAccessPolicyExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_restore operation. */\nexport interface ContainerRestoreHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_restore operation. */\nexport interface ContainerRestoreExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_rename operation. */\nexport interface ContainerRenameHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_rename operation. */\nexport interface ContainerRenameExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_submitBatch operation. */\nexport interface ContainerSubmitBatchHeaders {\n  /** The media type of the body of the response. For batch requests, this is multipart/mixed; boundary=batchresponse_GUID */\n  contentType?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n}\n\n/** Defines headers for Container_submitBatch operation. */\nexport interface ContainerSubmitBatchExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_filterBlobs operation. */\nexport interface ContainerFilterBlobsHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Container_filterBlobs operation. */\nexport interface ContainerFilterBlobsExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_acquireLease operation. */\nexport interface ContainerAcquireLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Uniquely identifies a container's lease */\n  leaseId?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Container_acquireLease operation. */\nexport interface ContainerAcquireLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_releaseLease operation. */\nexport interface ContainerReleaseLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Container_releaseLease operation. */\nexport interface ContainerReleaseLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_renewLease operation. */\nexport interface ContainerRenewLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Uniquely identifies a container's lease */\n  leaseId?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Container_renewLease operation. */\nexport interface ContainerRenewLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_breakLease operation. */\nexport interface ContainerBreakLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Approximate time remaining in the lease period, in seconds. */\n  leaseTime?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Container_breakLease operation. */\nexport interface ContainerBreakLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_changeLease operation. */\nexport interface ContainerChangeLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Uniquely identifies a container's lease */\n  leaseId?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Container_changeLease operation. */\nexport interface ContainerChangeLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_listBlobFlatSegment operation. */\nexport interface ContainerListBlobFlatSegmentHeaders {\n  /** The media type of the body of the response. For List Blobs this is 'application/xml' */\n  contentType?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_listBlobFlatSegment operation. */\nexport interface ContainerListBlobFlatSegmentExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_listBlobHierarchySegment operation. */\nexport interface ContainerListBlobHierarchySegmentHeaders {\n  /** The media type of the body of the response. For List Blobs this is 'application/xml' */\n  contentType?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Container_listBlobHierarchySegment operation. */\nexport interface ContainerListBlobHierarchySegmentExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Container_getAccountInfo operation. */\nexport interface ContainerGetAccountInfoHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Identifies the sku name of the account */\n  skuName?: SkuName;\n  /** Identifies the account kind */\n  accountKind?: AccountKind;\n  /** Version 2019-07-07 and newer. Indicates if the account has a hierarchical namespace enabled. */\n  isHierarchicalNamespaceEnabled?: boolean;\n}\n\n/** Defines headers for Container_getAccountInfo operation. */\nexport interface ContainerGetAccountInfoExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_download operation. */\nexport interface BlobDownloadHeaders {\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Returns the date and time the blob was created. */\n  createdOn?: Date;\n  metadata?: { [propertyName: string]: string };\n  /** Optional. Only valid when Object Replication is enabled for the storage container and on the destination blob of the replication. */\n  objectReplicationPolicyId?: string;\n  /** Optional. Only valid when Object Replication is enabled for the storage container and on the source blob of the replication. When retrieving this header, it will return the header with the policy id and rule id (e.g. x-ms-or-policyid_ruleid), and the value will be the status of the replication (e.g. complete, failed). */\n  objectReplicationRules?: { [propertyName: string]: string };\n  /** The number of bytes present in the response body. */\n  contentLength?: number;\n  /** The media type of the body of the response. For Download Blob this is 'application/octet-stream' */\n  contentType?: string;\n  /** Indicates the range of bytes returned in the event that the client requested a subset of the blob by setting the 'Range' request header. */\n  contentRange?: string;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header returns the value that was specified for the Content-Encoding request header */\n  contentEncoding?: string;\n  /** This header is returned if it was previously specified for the blob. */\n  cacheControl?: string;\n  /** This header returns the value that was specified for the 'x-ms-blob-content-disposition' header. The Content-Disposition response header field conveys additional information about how to process the response payload, and also can be used to attach additional metadata. For example, if set to attachment, it indicates that the user-agent should not display the response, but instead show a Save As dialog with a filename other than the blob name specified. */\n  contentDisposition?: string;\n  /** This header returns the value that was specified for the Content-Language request header. */\n  contentLanguage?: string;\n  /** The current sequence number for a page blob. This header is not returned for block blobs or append blobs */\n  blobSequenceNumber?: number;\n  /** The blob's type. */\n  blobType?: BlobType;\n  /** Conclusion time of the last attempted Copy Blob operation where this blob was the destination blob. This value can specify the time of a completed, aborted, or failed copy attempt. This header does not appear if a copy is pending, if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List. */\n  copyCompletedOn?: Date;\n  /** Only appears when x-ms-copy-status is failed or pending. Describes the cause of the last fatal or non-fatal copy operation failure. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List */\n  copyStatusDescription?: string;\n  /** String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy. */\n  copyId?: string;\n  /** Contains the number of bytes copied and the total bytes in the source in the last attempted Copy Blob operation where this blob was the destination blob. Can show between 0 and Content-Length bytes copied. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List */\n  copyProgress?: string;\n  /** URL up to 2 KB in length that specifies the source blob or file used in the last attempted Copy Blob operation where this blob was the destination blob. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List. */\n  copySource?: string;\n  /** State of the copy operation identified by x-ms-copy-id. */\n  copyStatus?: CopyStatusType;\n  /** When a blob is leased, specifies whether the lease is of infinite or fixed duration. */\n  leaseDuration?: LeaseDurationType;\n  /** Lease state of the blob. */\n  leaseState?: LeaseStateType;\n  /** The current lease status of the blob. */\n  leaseStatus?: LeaseStatusType;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** The value of this header indicates whether version of this blob is a current version, see also x-ms-version-id header. */\n  isCurrentVersion?: boolean;\n  /** Indicates that the service supports requests for partial blob content. */\n  acceptRanges?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The number of committed blocks present in the blob. This header is returned only for append blobs. */\n  blobCommittedBlockCount?: number;\n  /** The value of this header is set to true if the blob data and application metadata are completely encrypted using the specified algorithm. Otherwise, the value is set to false (when the blob is unencrypted, or if only parts of the blob/application metadata are encrypted). */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** If the blob has a MD5 hash, and if request contains range header (Range or x-ms-range), this response header is returned with the value of the whole blob's MD5 value. This value may or may not be equal to the value returned in Content-MD5 header, with the latter calculated from the requested range */\n  blobContentMD5?: Uint8Array;\n  /** The number of tags associated with the blob */\n  tagCount?: number;\n  /** If this blob has been sealed */\n  isSealed?: boolean;\n  /** UTC date/time value generated by the service that indicates the time at which the blob was last read or written to */\n  lastAccessed?: Date;\n  /** UTC date/time value generated by the service that indicates the time at which the blob immutability policy will expire. */\n  immutabilityPolicyExpiresOn?: Date;\n  /** Indicates immutability policy mode. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Indicates if a legal hold is present on the blob. */\n  legalHold?: boolean;\n  /** Error Code */\n  errorCode?: string;\n  /** If the request is to read a specified range and the x-ms-range-get-content-crc64 is set to true, then the request returns a crc64 for the range, as long as the range size is less than or equal to 4 MB. If both x-ms-range-get-content-crc64 & x-ms-range-get-content-md5 is specified in the same request, it will fail with 400(Bad Request). */\n  contentCrc64?: Uint8Array;\n}\n\n/** Defines headers for Blob_download operation. */\nexport interface BlobDownloadExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_getProperties operation. */\nexport interface BlobGetPropertiesHeaders {\n  /** Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Returns the date and time the blob was created. */\n  createdOn?: Date;\n  metadata?: { [propertyName: string]: string };\n  /** Optional. Only valid when Object Replication is enabled for the storage container and on the destination blob of the replication. */\n  objectReplicationPolicyId?: string;\n  /** Optional. Only valid when Object Replication is enabled for the storage container and on the source blob of the replication. When retrieving this header, it will return the header with the policy id and rule id (e.g. x-ms-or-policyid_ruleid), and the value will be the status of the replication (e.g. complete, failed). */\n  objectReplicationRules?: { [propertyName: string]: string };\n  /** The blob's type. */\n  blobType?: BlobType;\n  /** Conclusion time of the last attempted Copy Blob operation where this blob was the destination blob. This value can specify the time of a completed, aborted, or failed copy attempt. This header does not appear if a copy is pending, if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List. */\n  copyCompletedOn?: Date;\n  /** Only appears when x-ms-copy-status is failed or pending. Describes the cause of the last fatal or non-fatal copy operation failure. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List */\n  copyStatusDescription?: string;\n  /** String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy. */\n  copyId?: string;\n  /** Contains the number of bytes copied and the total bytes in the source in the last attempted Copy Blob operation where this blob was the destination blob. Can show between 0 and Content-Length bytes copied. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List */\n  copyProgress?: string;\n  /** URL up to 2 KB in length that specifies the source blob or file used in the last attempted Copy Blob operation where this blob was the destination blob. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List. */\n  copySource?: string;\n  /** State of the copy operation identified by x-ms-copy-id. */\n  copyStatus?: CopyStatusType;\n  /** Included if the blob is incremental copy blob. */\n  isIncrementalCopy?: boolean;\n  /** Included if the blob is incremental copy blob or incremental copy snapshot, if x-ms-copy-status is success. Snapshot time of the last successful incremental copy snapshot for this blob. */\n  destinationSnapshot?: string;\n  /** When a blob is leased, specifies whether the lease is of infinite or fixed duration. */\n  leaseDuration?: LeaseDurationType;\n  /** Lease state of the blob. */\n  leaseState?: LeaseStateType;\n  /** The current lease status of the blob. */\n  leaseStatus?: LeaseStatusType;\n  /** The size of the blob in bytes. For a page blob, this header returns the value of the x-ms-blob-content-length header that is stored with the blob. */\n  contentLength?: number;\n  /** The content type specified for the blob. The default content type is 'application/octet-stream' */\n  contentType?: string;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header returns the value that was specified for the Content-Encoding request header */\n  contentEncoding?: string;\n  /** This header returns the value that was specified for the 'x-ms-blob-content-disposition' header. The Content-Disposition response header field conveys additional information about how to process the response payload, and also can be used to attach additional metadata. For example, if set to attachment, it indicates that the user-agent should not display the response, but instead show a Save As dialog with a filename other than the blob name specified. */\n  contentDisposition?: string;\n  /** This header returns the value that was specified for the Content-Language request header. */\n  contentLanguage?: string;\n  /** This header is returned if it was previously specified for the blob. */\n  cacheControl?: string;\n  /** The current sequence number for a page blob. This header is not returned for block blobs or append blobs */\n  blobSequenceNumber?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Indicates that the service supports requests for partial blob content. */\n  acceptRanges?: string;\n  /** The number of committed blocks present in the blob. This header is returned only for append blobs. */\n  blobCommittedBlockCount?: number;\n  /** The value of this header is set to true if the blob data and application metadata are completely encrypted using the specified algorithm. Otherwise, the value is set to false (when the blob is unencrypted, or if only parts of the blob/application metadata are encrypted). */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the metadata. This header is only returned when the metadata was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** The tier of page blob on a premium storage account or tier of block blob on blob storage LRS accounts. For a list of allowed premium page blob tiers, see https://docs.microsoft.com/en-us/azure/virtual-machines/windows/premium-storage#features. For blob storage LRS accounts, valid values are Hot/Cool/Archive. */\n  accessTier?: string;\n  /** For page blobs on a premium storage account only. If the access tier is not explicitly set on the blob, the tier is inferred based on its content length and this header will be returned with true value. */\n  accessTierInferred?: boolean;\n  /** For blob storage LRS accounts, valid values are rehydrate-pending-to-hot/rehydrate-pending-to-cool. If the blob is being rehydrated and is not complete then this header is returned indicating that rehydrate is pending and also tells the destination tier. */\n  archiveStatus?: string;\n  /** The time the tier was changed on the object. This is only returned if the tier on the block blob was ever set. */\n  accessTierChangedOn?: Date;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** The value of this header indicates whether version of this blob is a current version, see also x-ms-version-id header. */\n  isCurrentVersion?: boolean;\n  /** The number of tags associated with the blob */\n  tagCount?: number;\n  /** The time this blob will expire. */\n  expiresOn?: Date;\n  /** If this blob has been sealed */\n  isSealed?: boolean;\n  /** If an object is in rehydrate pending state then this header is returned with priority of rehydrate. */\n  rehydratePriority?: RehydratePriority;\n  /** UTC date/time value generated by the service that indicates the time at which the blob was last read or written to */\n  lastAccessed?: Date;\n  /** UTC date/time value generated by the service that indicates the time at which the blob immutability policy will expire. */\n  immutabilityPolicyExpiresOn?: Date;\n  /** Indicates immutability policy mode. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Indicates if a legal hold is present on the blob. */\n  legalHold?: boolean;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_getProperties operation. */\nexport interface BlobGetPropertiesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_delete operation. */\nexport interface BlobDeleteHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_delete operation. */\nexport interface BlobDeleteExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_undelete operation. */\nexport interface BlobUndeleteHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated. */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_undelete operation. */\nexport interface BlobUndeleteExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setExpiry operation. */\nexport interface BlobSetExpiryHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated. */\n  date?: Date;\n}\n\n/** Defines headers for Blob_setExpiry operation. */\nexport interface BlobSetExpiryExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setHttpHeaders operation. */\nexport interface BlobSetHttpHeadersHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** The current sequence number for a page blob. This header is not returned for block blobs or append blobs */\n  blobSequenceNumber?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setHttpHeaders operation. */\nexport interface BlobSetHttpHeadersExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setImmutabilityPolicy operation. */\nexport interface BlobSetImmutabilityPolicyHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Indicates the time the immutability policy will expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Indicates immutability policy mode. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n}\n\n/** Defines headers for Blob_setImmutabilityPolicy operation. */\nexport interface BlobSetImmutabilityPolicyExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_deleteImmutabilityPolicy operation. */\nexport interface BlobDeleteImmutabilityPolicyHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Blob_deleteImmutabilityPolicy operation. */\nexport interface BlobDeleteImmutabilityPolicyExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setLegalHold operation. */\nexport interface BlobSetLegalHoldHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Indicates if the blob has a legal hold. */\n  legalHold?: boolean;\n}\n\n/** Defines headers for Blob_setLegalHold operation. */\nexport interface BlobSetLegalHoldExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setMetadata operation. */\nexport interface BlobSetMetadataHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the metadata. This header is only returned when the metadata was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setMetadata operation. */\nexport interface BlobSetMetadataExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_acquireLease operation. */\nexport interface BlobAcquireLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Uniquely identifies a blobs' lease */\n  leaseId?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Blob_acquireLease operation. */\nexport interface BlobAcquireLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_releaseLease operation. */\nexport interface BlobReleaseLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Blob_releaseLease operation. */\nexport interface BlobReleaseLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_renewLease operation. */\nexport interface BlobRenewLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Uniquely identifies a blobs' lease */\n  leaseId?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Blob_renewLease operation. */\nexport interface BlobRenewLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_changeLease operation. */\nexport interface BlobChangeLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Uniquely identifies a blobs' lease */\n  leaseId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Blob_changeLease operation. */\nexport interface BlobChangeLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_breakLease operation. */\nexport interface BlobBreakLeaseHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** Approximate time remaining in the lease period, in seconds. */\n  leaseTime?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n}\n\n/** Defines headers for Blob_breakLease operation. */\nexport interface BlobBreakLeaseExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_createSnapshot operation. */\nexport interface BlobCreateSnapshotHeaders {\n  /** Uniquely identifies the snapshot and indicates the snapshot version. It may be used in subsequent requests to access the snapshot */\n  snapshot?: string;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** True if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. For a snapshot request, this header is set to true when metadata was provided in the request and encrypted with a customer-provided key. */\n  isServerEncrypted?: boolean;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_createSnapshot operation. */\nexport interface BlobCreateSnapshotExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_startCopyFromURL operation. */\nexport interface BlobStartCopyFromURLHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy. */\n  copyId?: string;\n  /** State of the copy operation identified by x-ms-copy-id. */\n  copyStatus?: CopyStatusType;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_startCopyFromURL operation. */\nexport interface BlobStartCopyFromURLExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_copyFromURL operation. */\nexport interface BlobCopyFromURLHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** String identifier for this copy operation. */\n  copyId?: string;\n  /** State of the copy operation identified by x-ms-copy-id. */\n  copyStatus?: SyncCopyStatusType;\n  /** This response header is returned so that the client can check for the integrity of the copied content. This header is only returned if the source content MD5 was specified. */\n  contentMD5?: Uint8Array;\n  /** This response header is returned so that the client can check for the integrity of the copied content. */\n  xMsContentCrc64?: Uint8Array;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_copyFromURL operation. */\nexport interface BlobCopyFromURLExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_abortCopyFromURL operation. */\nexport interface BlobAbortCopyFromURLHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_abortCopyFromURL operation. */\nexport interface BlobAbortCopyFromURLExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setTier operation. */\nexport interface BlobSetTierHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and newer. */\n  version?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setTier operation. */\nexport interface BlobSetTierExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_getAccountInfo operation. */\nexport interface BlobGetAccountInfoHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Identifies the sku name of the account */\n  skuName?: SkuName;\n  /** Identifies the account kind */\n  accountKind?: AccountKind;\n  /** Version 2019-07-07 and newer. Indicates if the account has a hierarchical namespace enabled. */\n  isHierarchicalNamespaceEnabled?: boolean;\n}\n\n/** Defines headers for Blob_getAccountInfo operation. */\nexport interface BlobGetAccountInfoExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_query operation. */\nexport interface BlobQueryHeaders {\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  metadata?: { [propertyName: string]: string };\n  /** The number of bytes present in the response body. */\n  contentLength?: number;\n  /** The media type of the body of the response. For Download Blob this is 'application/octet-stream' */\n  contentType?: string;\n  /** Indicates the range of bytes returned in the event that the client requested a subset of the blob by setting the 'Range' request header. */\n  contentRange?: string;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header returns the value that was specified for the Content-Encoding request header */\n  contentEncoding?: string;\n  /** This header is returned if it was previously specified for the blob. */\n  cacheControl?: string;\n  /** This header returns the value that was specified for the 'x-ms-blob-content-disposition' header. The Content-Disposition response header field conveys additional information about how to process the response payload, and also can be used to attach additional metadata. For example, if set to attachment, it indicates that the user-agent should not display the response, but instead show a Save As dialog with a filename other than the blob name specified. */\n  contentDisposition?: string;\n  /** This header returns the value that was specified for the Content-Language request header. */\n  contentLanguage?: string;\n  /** The current sequence number for a page blob. This header is not returned for block blobs or append blobs */\n  blobSequenceNumber?: number;\n  /** The blob's type. */\n  blobType?: BlobType;\n  /** Conclusion time of the last attempted Copy Blob operation where this blob was the destination blob. This value can specify the time of a completed, aborted, or failed copy attempt. This header does not appear if a copy is pending, if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List. */\n  copyCompletionTime?: Date;\n  /** Only appears when x-ms-copy-status is failed or pending. Describes the cause of the last fatal or non-fatal copy operation failure. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List */\n  copyStatusDescription?: string;\n  /** String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy. */\n  copyId?: string;\n  /** Contains the number of bytes copied and the total bytes in the source in the last attempted Copy Blob operation where this blob was the destination blob. Can show between 0 and Content-Length bytes copied. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List */\n  copyProgress?: string;\n  /** URL up to 2 KB in length that specifies the source blob or file used in the last attempted Copy Blob operation where this blob was the destination blob. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List. */\n  copySource?: string;\n  /** State of the copy operation identified by x-ms-copy-id. */\n  copyStatus?: CopyStatusType;\n  /** When a blob is leased, specifies whether the lease is of infinite or fixed duration. */\n  leaseDuration?: LeaseDurationType;\n  /** Lease state of the blob. */\n  leaseState?: LeaseStateType;\n  /** The current lease status of the blob. */\n  leaseStatus?: LeaseStatusType;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** Indicates that the service supports requests for partial blob content. */\n  acceptRanges?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The number of committed blocks present in the blob. This header is returned only for append blobs. */\n  blobCommittedBlockCount?: number;\n  /** The value of this header is set to true if the blob data and application metadata are completely encrypted using the specified algorithm. Otherwise, the value is set to false (when the blob is unencrypted, or if only parts of the blob/application metadata are encrypted). */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** If the blob has a MD5 hash, and if request contains range header (Range or x-ms-range), this response header is returned with the value of the whole blob's MD5 value. This value may or may not be equal to the value returned in Content-MD5 header, with the latter calculated from the requested range */\n  blobContentMD5?: Uint8Array;\n  /** Error Code */\n  errorCode?: string;\n  /** If the request is to read a specified range and the x-ms-range-get-content-crc64 is set to true, then the request returns a crc64 for the range, as long as the range size is less than or equal to 4 MB. If both x-ms-range-get-content-crc64 & x-ms-range-get-content-md5 is specified in the same request, it will fail with 400(Bad Request). */\n  contentCrc64?: Uint8Array;\n}\n\n/** Defines headers for Blob_query operation. */\nexport interface BlobQueryExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_getTags operation. */\nexport interface BlobGetTagsHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_getTags operation. */\nexport interface BlobGetTagsExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setTags operation. */\nexport interface BlobSetTagsHeaders {\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Blob_setTags operation. */\nexport interface BlobSetTagsExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_create operation. */\nexport interface PageBlobCreateHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_create operation. */\nexport interface PageBlobCreateExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_uploadPages operation. */\nexport interface PageBlobUploadPagesHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** The current sequence number for the page blob. */\n  blobSequenceNumber?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the pages. This header is only returned when the pages were encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_uploadPages operation. */\nexport interface PageBlobUploadPagesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_clearPages operation. */\nexport interface PageBlobClearPagesHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** The current sequence number for the page blob. */\n  blobSequenceNumber?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_clearPages operation. */\nexport interface PageBlobClearPagesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_uploadPagesFromURL operation. */\nexport interface PageBlobUploadPagesFromURLHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** The current sequence number for the page blob. */\n  blobSequenceNumber?: number;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_uploadPagesFromURL operation. */\nexport interface PageBlobUploadPagesFromURLExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_getPageRanges operation. */\nexport interface PageBlobGetPageRangesHeaders {\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** The size of the blob in bytes. */\n  blobContentLength?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_getPageRanges operation. */\nexport interface PageBlobGetPageRangesExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_getPageRangesDiff operation. */\nexport interface PageBlobGetPageRangesDiffHeaders {\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** The size of the blob in bytes. */\n  blobContentLength?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_getPageRangesDiff operation. */\nexport interface PageBlobGetPageRangesDiffExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_resize operation. */\nexport interface PageBlobResizeHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** The current sequence number for a page blob. This header is not returned for block blobs or append blobs */\n  blobSequenceNumber?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_resize operation. */\nexport interface PageBlobResizeExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_updateSequenceNumber operation. */\nexport interface PageBlobUpdateSequenceNumberHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** The current sequence number for a page blob. This header is not returned for block blobs or append blobs */\n  blobSequenceNumber?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_updateSequenceNumber operation. */\nexport interface PageBlobUpdateSequenceNumberExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_copyIncremental operation. */\nexport interface PageBlobCopyIncrementalHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy. */\n  copyId?: string;\n  /** State of the copy operation identified by x-ms-copy-id. */\n  copyStatus?: CopyStatusType;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for PageBlob_copyIncremental operation. */\nexport interface PageBlobCopyIncrementalExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_create operation. */\nexport interface AppendBlobCreateHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_create operation. */\nexport interface AppendBlobCreateExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_appendBlock operation. */\nexport interface AppendBlobAppendBlockHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** This response header is returned only for append operations. It returns the offset at which the block was committed, in bytes. */\n  blobAppendOffset?: string;\n  /** The number of committed blocks present in the blob. This header is returned only for append blobs. */\n  blobCommittedBlockCount?: number;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the block. This header is only returned when the block was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_appendBlock operation. */\nexport interface AppendBlobAppendBlockExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_appendBlockFromUrl operation. */\nexport interface AppendBlobAppendBlockFromUrlHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** This response header is returned only for append operations. It returns the offset at which the block was committed, in bytes. */\n  blobAppendOffset?: string;\n  /** The number of committed blocks present in the blob. This header is returned only for append blobs. */\n  blobCommittedBlockCount?: number;\n  /** The SHA-256 hash of the encryption key used to encrypt the block. This header is only returned when the block was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_appendBlockFromUrl operation. */\nexport interface AppendBlobAppendBlockFromUrlExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for AppendBlob_seal operation. */\nexport interface AppendBlobSealHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If this blob has been sealed */\n  isSealed?: boolean;\n}\n\n/** Defines headers for AppendBlob_seal operation. */\nexport interface AppendBlobSealExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_upload operation. */\nexport interface BlockBlobUploadHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_upload operation. */\nexport interface BlockBlobUploadExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_putBlobFromUrl operation. */\nexport interface BlockBlobPutBlobFromUrlHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity. */\n  contentMD5?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_putBlobFromUrl operation. */\nexport interface BlockBlobPutBlobFromUrlExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_stageBlock operation. */\nexport interface BlockBlobStageBlockHeaders {\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  contentMD5?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the block. This header is only returned when the block was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_stageBlock operation. */\nexport interface BlockBlobStageBlockExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_stageBlockFromURL operation. */\nexport interface BlockBlobStageBlockFromURLHeaders {\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. The value of this header is computed by the Blob service; it is not necessarily the same value specified in the request headers. */\n  xMsContentCrc64?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the block. This header is only returned when the block was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_stageBlockFromURL operation. */\nexport interface BlockBlobStageBlockFromURLExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_commitBlockList operation. */\nexport interface BlockBlobCommitBlockListHeaders {\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** This header is returned so that the client can check for message content integrity. This header refers to the content of the request, meaning, in this case, the list of blocks, and not the content of the blob itself. */\n  contentMD5?: Uint8Array;\n  /** This header is returned so that the client can check for message content integrity. This header refers to the content of the request, meaning, in this case, the list of blocks, and not the content of the blob itself. */\n  xMsContentCrc64?: Uint8Array;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** A DateTime value returned by the service that uniquely identifies the blob. The value of this header indicates the blob version, and may be used in subsequent requests to access this version of the blob. */\n  versionId?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise. */\n  isServerEncrypted?: boolean;\n  /** The SHA-256 hash of the encryption key used to encrypt the blob. This header is only returned when the blob was encrypted with a customer-provided key. */\n  encryptionKeySha256?: string;\n  /** Returns the name of the encryption scope used to encrypt the blob contents and application metadata.  Note that the absence of this header implies use of the default account encryption scope. */\n  encryptionScope?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_commitBlockList operation. */\nexport interface BlockBlobCommitBlockListExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_getBlockList operation. */\nexport interface BlockBlobGetBlockListHeaders {\n  /** Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob. */\n  lastModified?: Date;\n  /** The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes. */\n  etag?: string;\n  /** The media type of the body of the response. For Get Block List this is 'application/xml' */\n  contentType?: string;\n  /** The size of the blob in bytes. */\n  blobContentLength?: number;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for BlockBlob_getBlockList operation. */\nexport interface BlockBlobGetBlockListExceptionHeaders {\n  errorCode?: string;\n}\n\n/** Parameter group */\nexport interface ContainerEncryptionScope {\n  /** Optional.  Version 2019-07-07 and later.  Specifies the default encryption scope to set on the container and use for all future writes. */\n  defaultEncryptionScope?: string;\n  /** Optional.  Version 2019-07-07 and newer.  If true, prevents any request from specifying a different encryption scope than the scope set on the container. */\n  preventEncryptionScopeOverride?: boolean;\n}\n\n/** Parameter group */\nexport interface LeaseAccessConditions {\n  /** If specified, the operation only succeeds if the resource's lease is active and matches this ID. */\n  leaseId?: string;\n}\n\n/** Parameter group */\nexport interface ModifiedAccessConditions {\n  /** Specify this header value to operate only on a blob if it has been modified since the specified date/time. */\n  ifModifiedSince?: Date;\n  /** Specify this header value to operate only on a blob if it has not been modified since the specified date/time. */\n  ifUnmodifiedSince?: Date;\n  /** Specify an ETag value to operate only on blobs with a matching value. */\n  ifMatch?: string;\n  /** Specify an ETag value to operate only on blobs without a matching value. */\n  ifNoneMatch?: string;\n  /** Specify a SQL where clause on blob tags to operate only on blobs with a matching value. */\n  ifTags?: string;\n}\n\n/** Parameter group */\nexport interface CpkInfo {\n  /** Optional. Specifies the encryption key to use to encrypt the data provided in the request. If not specified, encryption is performed with the root account encryption key.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionKey?: string;\n  /** The SHA-256 hash of the provided encryption key. Must be provided if the x-ms-encryption-key header is provided. */\n  encryptionKeySha256?: string;\n  /** The algorithm used to produce the encryption key hash. Currently, the only accepted value is \"AES256\". Must be provided if the x-ms-encryption-key header is provided. */\n  encryptionAlgorithm?: EncryptionAlgorithmType;\n}\n\n/** Parameter group */\nexport interface BlobHttpHeaders {\n  /** Optional. Sets the blob's cache control. If specified, this property is stored with the blob and returned with a read request. */\n  blobCacheControl?: string;\n  /** Optional. Sets the blob's content type. If specified, this property is stored with the blob and returned with a read request. */\n  blobContentType?: string;\n  /** Optional. An MD5 hash of the blob content. Note that this hash is not validated, as the hashes for the individual blocks were validated when each was uploaded. */\n  blobContentMD5?: Uint8Array;\n  /** Optional. Sets the blob's content encoding. If specified, this property is stored with the blob and returned with a read request. */\n  blobContentEncoding?: string;\n  /** Optional. Set the blob's content language. If specified, this property is stored with the blob and returned with a read request. */\n  blobContentLanguage?: string;\n  /** Optional. Sets the blob's Content-Disposition header. */\n  blobContentDisposition?: string;\n}\n\n/** Parameter group */\nexport interface SourceModifiedAccessConditions {\n  /** Specify this header value to operate only on a blob if it has been modified since the specified date/time. */\n  sourceIfModifiedSince?: Date;\n  /** Specify this header value to operate only on a blob if it has not been modified since the specified date/time. */\n  sourceIfUnmodifiedSince?: Date;\n  /** Specify an ETag value to operate only on blobs with a matching value. */\n  sourceIfMatch?: string;\n  /** Specify an ETag value to operate only on blobs without a matching value. */\n  sourceIfNoneMatch?: string;\n  /** Specify a SQL where clause on blob tags to operate only on blobs with a matching value. */\n  sourceIfTags?: string;\n}\n\n/** Parameter group */\nexport interface SequenceNumberAccessConditions {\n  /** Specify this header value to operate only on a blob if it has a sequence number less than or equal to the specified. */\n  ifSequenceNumberLessThanOrEqualTo?: number;\n  /** Specify this header value to operate only on a blob if it has a sequence number less than the specified. */\n  ifSequenceNumberLessThan?: number;\n  /** Specify this header value to operate only on a blob if it has the specified sequence number. */\n  ifSequenceNumberEqualTo?: number;\n}\n\n/** Parameter group */\nexport interface AppendPositionAccessConditions {\n  /** Optional conditional header. The max length in bytes permitted for the append blob. If the Append Block operation would cause the blob to exceed that limit or if the blob size is already greater than the value specified in this header, the request will fail with MaxBlobSizeConditionNotMet error (HTTP status code 412 - Precondition Failed). */\n  maxSize?: number;\n  /** Optional conditional header, used only for the Append Block operation. A number indicating the byte offset to compare. Append Block will succeed only if the append position is equal to this number. If it is not, the request will fail with the AppendPositionConditionNotMet error (HTTP status code 412 - Precondition Failed). */\n  appendPosition?: number;\n}\n\n/** Known values of {@link EncryptionAlgorithmType} that the service accepts. */\nexport enum KnownEncryptionAlgorithmType {\n  /** AES256 */\n  AES256 = \"AES256\",\n}\n\n/**\n * Defines values for EncryptionAlgorithmType. \\\n * {@link KnownEncryptionAlgorithmType} can be used interchangeably with EncryptionAlgorithmType,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **AES256**\n */\nexport type EncryptionAlgorithmType = string;\n\n/** Known values of {@link BlobExpiryOptions} that the service accepts. */\nexport enum KnownBlobExpiryOptions {\n  /** NeverExpire */\n  NeverExpire = \"NeverExpire\",\n  /** RelativeToCreation */\n  RelativeToCreation = \"RelativeToCreation\",\n  /** RelativeToNow */\n  RelativeToNow = \"RelativeToNow\",\n  /** Absolute */\n  Absolute = \"Absolute\",\n}\n\n/**\n * Defines values for BlobExpiryOptions. \\\n * {@link KnownBlobExpiryOptions} can be used interchangeably with BlobExpiryOptions,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **NeverExpire** \\\n * **RelativeToCreation** \\\n * **RelativeToNow** \\\n * **Absolute**\n */\nexport type BlobExpiryOptions = string;\n\n/** Known values of {@link StorageErrorCode} that the service accepts. */\nexport enum KnownStorageErrorCode {\n  /** AccountAlreadyExists */\n  AccountAlreadyExists = \"AccountAlreadyExists\",\n  /** AccountBeingCreated */\n  AccountBeingCreated = \"AccountBeingCreated\",\n  /** AccountIsDisabled */\n  AccountIsDisabled = \"AccountIsDisabled\",\n  /** AuthenticationFailed */\n  AuthenticationFailed = \"AuthenticationFailed\",\n  /** AuthorizationFailure */\n  AuthorizationFailure = \"AuthorizationFailure\",\n  /** ConditionHeadersNotSupported */\n  ConditionHeadersNotSupported = \"ConditionHeadersNotSupported\",\n  /** ConditionNotMet */\n  ConditionNotMet = \"ConditionNotMet\",\n  /** EmptyMetadataKey */\n  EmptyMetadataKey = \"EmptyMetadataKey\",\n  /** InsufficientAccountPermissions */\n  InsufficientAccountPermissions = \"InsufficientAccountPermissions\",\n  /** InternalError */\n  InternalError = \"InternalError\",\n  /** InvalidAuthenticationInfo */\n  InvalidAuthenticationInfo = \"InvalidAuthenticationInfo\",\n  /** InvalidHeaderValue */\n  InvalidHeaderValue = \"InvalidHeaderValue\",\n  /** InvalidHttpVerb */\n  InvalidHttpVerb = \"InvalidHttpVerb\",\n  /** InvalidInput */\n  InvalidInput = \"InvalidInput\",\n  /** InvalidMd5 */\n  InvalidMd5 = \"InvalidMd5\",\n  /** InvalidMetadata */\n  InvalidMetadata = \"InvalidMetadata\",\n  /** InvalidQueryParameterValue */\n  InvalidQueryParameterValue = \"InvalidQueryParameterValue\",\n  /** InvalidRange */\n  InvalidRange = \"InvalidRange\",\n  /** InvalidResourceName */\n  InvalidResourceName = \"InvalidResourceName\",\n  /** InvalidUri */\n  InvalidUri = \"InvalidUri\",\n  /** InvalidXmlDocument */\n  InvalidXmlDocument = \"InvalidXmlDocument\",\n  /** InvalidXmlNodeValue */\n  InvalidXmlNodeValue = \"InvalidXmlNodeValue\",\n  /** Md5Mismatch */\n  Md5Mismatch = \"Md5Mismatch\",\n  /** MetadataTooLarge */\n  MetadataTooLarge = \"MetadataTooLarge\",\n  /** MissingContentLengthHeader */\n  MissingContentLengthHeader = \"MissingContentLengthHeader\",\n  /** MissingRequiredQueryParameter */\n  MissingRequiredQueryParameter = \"MissingRequiredQueryParameter\",\n  /** MissingRequiredHeader */\n  MissingRequiredHeader = \"MissingRequiredHeader\",\n  /** MissingRequiredXmlNode */\n  MissingRequiredXmlNode = \"MissingRequiredXmlNode\",\n  /** MultipleConditionHeadersNotSupported */\n  MultipleConditionHeadersNotSupported = \"MultipleConditionHeadersNotSupported\",\n  /** OperationTimedOut */\n  OperationTimedOut = \"OperationTimedOut\",\n  /** OutOfRangeInput */\n  OutOfRangeInput = \"OutOfRangeInput\",\n  /** OutOfRangeQueryParameterValue */\n  OutOfRangeQueryParameterValue = \"OutOfRangeQueryParameterValue\",\n  /** RequestBodyTooLarge */\n  RequestBodyTooLarge = \"RequestBodyTooLarge\",\n  /** ResourceTypeMismatch */\n  ResourceTypeMismatch = \"ResourceTypeMismatch\",\n  /** RequestUrlFailedToParse */\n  RequestUrlFailedToParse = \"RequestUrlFailedToParse\",\n  /** ResourceAlreadyExists */\n  ResourceAlreadyExists = \"ResourceAlreadyExists\",\n  /** ResourceNotFound */\n  ResourceNotFound = \"ResourceNotFound\",\n  /** ServerBusy */\n  ServerBusy = \"ServerBusy\",\n  /** UnsupportedHeader */\n  UnsupportedHeader = \"UnsupportedHeader\",\n  /** UnsupportedXmlNode */\n  UnsupportedXmlNode = \"UnsupportedXmlNode\",\n  /** UnsupportedQueryParameter */\n  UnsupportedQueryParameter = \"UnsupportedQueryParameter\",\n  /** UnsupportedHttpVerb */\n  UnsupportedHttpVerb = \"UnsupportedHttpVerb\",\n  /** AppendPositionConditionNotMet */\n  AppendPositionConditionNotMet = \"AppendPositionConditionNotMet\",\n  /** BlobAlreadyExists */\n  BlobAlreadyExists = \"BlobAlreadyExists\",\n  /** BlobImmutableDueToPolicy */\n  BlobImmutableDueToPolicy = \"BlobImmutableDueToPolicy\",\n  /** BlobNotFound */\n  BlobNotFound = \"BlobNotFound\",\n  /** BlobOverwritten */\n  BlobOverwritten = \"BlobOverwritten\",\n  /** BlobTierInadequateForContentLength */\n  BlobTierInadequateForContentLength = \"BlobTierInadequateForContentLength\",\n  /** BlobUsesCustomerSpecifiedEncryption */\n  BlobUsesCustomerSpecifiedEncryption = \"BlobUsesCustomerSpecifiedEncryption\",\n  /** BlockCountExceedsLimit */\n  BlockCountExceedsLimit = \"BlockCountExceedsLimit\",\n  /** BlockListTooLong */\n  BlockListTooLong = \"BlockListTooLong\",\n  /** CannotChangeToLowerTier */\n  CannotChangeToLowerTier = \"CannotChangeToLowerTier\",\n  /** CannotVerifyCopySource */\n  CannotVerifyCopySource = \"CannotVerifyCopySource\",\n  /** ContainerAlreadyExists */\n  ContainerAlreadyExists = \"ContainerAlreadyExists\",\n  /** ContainerBeingDeleted */\n  ContainerBeingDeleted = \"ContainerBeingDeleted\",\n  /** ContainerDisabled */\n  ContainerDisabled = \"ContainerDisabled\",\n  /** ContainerNotFound */\n  ContainerNotFound = \"ContainerNotFound\",\n  /** ContentLengthLargerThanTierLimit */\n  ContentLengthLargerThanTierLimit = \"ContentLengthLargerThanTierLimit\",\n  /** CopyAcrossAccountsNotSupported */\n  CopyAcrossAccountsNotSupported = \"CopyAcrossAccountsNotSupported\",\n  /** CopyIdMismatch */\n  CopyIdMismatch = \"CopyIdMismatch\",\n  /** FeatureVersionMismatch */\n  FeatureVersionMismatch = \"FeatureVersionMismatch\",\n  /** IncrementalCopyBlobMismatch */\n  IncrementalCopyBlobMismatch = \"IncrementalCopyBlobMismatch\",\n  /** IncrementalCopyOfEarlierVersionSnapshotNotAllowed */\n  IncrementalCopyOfEarlierVersionSnapshotNotAllowed = \"IncrementalCopyOfEarlierVersionSnapshotNotAllowed\",\n  /** IncrementalCopySourceMustBeSnapshot */\n  IncrementalCopySourceMustBeSnapshot = \"IncrementalCopySourceMustBeSnapshot\",\n  /** InfiniteLeaseDurationRequired */\n  InfiniteLeaseDurationRequired = \"InfiniteLeaseDurationRequired\",\n  /** InvalidBlobOrBlock */\n  InvalidBlobOrBlock = \"InvalidBlobOrBlock\",\n  /** InvalidBlobTier */\n  InvalidBlobTier = \"InvalidBlobTier\",\n  /** InvalidBlobType */\n  InvalidBlobType = \"InvalidBlobType\",\n  /** InvalidBlockId */\n  InvalidBlockId = \"InvalidBlockId\",\n  /** InvalidBlockList */\n  InvalidBlockList = \"InvalidBlockList\",\n  /** InvalidOperation */\n  InvalidOperation = \"InvalidOperation\",\n  /** InvalidPageRange */\n  InvalidPageRange = \"InvalidPageRange\",\n  /** InvalidSourceBlobType */\n  InvalidSourceBlobType = \"InvalidSourceBlobType\",\n  /** InvalidSourceBlobUrl */\n  InvalidSourceBlobUrl = \"InvalidSourceBlobUrl\",\n  /** InvalidVersionForPageBlobOperation */\n  InvalidVersionForPageBlobOperation = \"InvalidVersionForPageBlobOperation\",\n  /** LeaseAlreadyPresent */\n  LeaseAlreadyPresent = \"LeaseAlreadyPresent\",\n  /** LeaseAlreadyBroken */\n  LeaseAlreadyBroken = \"LeaseAlreadyBroken\",\n  /** LeaseIdMismatchWithBlobOperation */\n  LeaseIdMismatchWithBlobOperation = \"LeaseIdMismatchWithBlobOperation\",\n  /** LeaseIdMismatchWithContainerOperation */\n  LeaseIdMismatchWithContainerOperation = \"LeaseIdMismatchWithContainerOperation\",\n  /** LeaseIdMismatchWithLeaseOperation */\n  LeaseIdMismatchWithLeaseOperation = \"LeaseIdMismatchWithLeaseOperation\",\n  /** LeaseIdMissing */\n  LeaseIdMissing = \"LeaseIdMissing\",\n  /** LeaseIsBreakingAndCannotBeAcquired */\n  LeaseIsBreakingAndCannotBeAcquired = \"LeaseIsBreakingAndCannotBeAcquired\",\n  /** LeaseIsBreakingAndCannotBeChanged */\n  LeaseIsBreakingAndCannotBeChanged = \"LeaseIsBreakingAndCannotBeChanged\",\n  /** LeaseIsBrokenAndCannotBeRenewed */\n  LeaseIsBrokenAndCannotBeRenewed = \"LeaseIsBrokenAndCannotBeRenewed\",\n  /** LeaseLost */\n  LeaseLost = \"LeaseLost\",\n  /** LeaseNotPresentWithBlobOperation */\n  LeaseNotPresentWithBlobOperation = \"LeaseNotPresentWithBlobOperation\",\n  /** LeaseNotPresentWithContainerOperation */\n  LeaseNotPresentWithContainerOperation = \"LeaseNotPresentWithContainerOperation\",\n  /** LeaseNotPresentWithLeaseOperation */\n  LeaseNotPresentWithLeaseOperation = \"LeaseNotPresentWithLeaseOperation\",\n  /** MaxBlobSizeConditionNotMet */\n  MaxBlobSizeConditionNotMet = \"MaxBlobSizeConditionNotMet\",\n  /** NoAuthenticationInformation */\n  NoAuthenticationInformation = \"NoAuthenticationInformation\",\n  /** NoPendingCopyOperation */\n  NoPendingCopyOperation = \"NoPendingCopyOperation\",\n  /** OperationNotAllowedOnIncrementalCopyBlob */\n  OperationNotAllowedOnIncrementalCopyBlob = \"OperationNotAllowedOnIncrementalCopyBlob\",\n  /** PendingCopyOperation */\n  PendingCopyOperation = \"PendingCopyOperation\",\n  /** PreviousSnapshotCannotBeNewer */\n  PreviousSnapshotCannotBeNewer = \"PreviousSnapshotCannotBeNewer\",\n  /** PreviousSnapshotNotFound */\n  PreviousSnapshotNotFound = \"PreviousSnapshotNotFound\",\n  /** PreviousSnapshotOperationNotSupported */\n  PreviousSnapshotOperationNotSupported = \"PreviousSnapshotOperationNotSupported\",\n  /** SequenceNumberConditionNotMet */\n  SequenceNumberConditionNotMet = \"SequenceNumberConditionNotMet\",\n  /** SequenceNumberIncrementTooLarge */\n  SequenceNumberIncrementTooLarge = \"SequenceNumberIncrementTooLarge\",\n  /** SnapshotCountExceeded */\n  SnapshotCountExceeded = \"SnapshotCountExceeded\",\n  /** SnapshotOperationRateExceeded */\n  SnapshotOperationRateExceeded = \"SnapshotOperationRateExceeded\",\n  /** SnapshotsPresent */\n  SnapshotsPresent = \"SnapshotsPresent\",\n  /** SourceConditionNotMet */\n  SourceConditionNotMet = \"SourceConditionNotMet\",\n  /** SystemInUse */\n  SystemInUse = \"SystemInUse\",\n  /** TargetConditionNotMet */\n  TargetConditionNotMet = \"TargetConditionNotMet\",\n  /** UnauthorizedBlobOverwrite */\n  UnauthorizedBlobOverwrite = \"UnauthorizedBlobOverwrite\",\n  /** BlobBeingRehydrated */\n  BlobBeingRehydrated = \"BlobBeingRehydrated\",\n  /** BlobArchived */\n  BlobArchived = \"BlobArchived\",\n  /** BlobNotArchived */\n  BlobNotArchived = \"BlobNotArchived\",\n  /** AuthorizationSourceIPMismatch */\n  AuthorizationSourceIPMismatch = \"AuthorizationSourceIPMismatch\",\n  /** AuthorizationProtocolMismatch */\n  AuthorizationProtocolMismatch = \"AuthorizationProtocolMismatch\",\n  /** AuthorizationPermissionMismatch */\n  AuthorizationPermissionMismatch = \"AuthorizationPermissionMismatch\",\n  /** AuthorizationServiceMismatch */\n  AuthorizationServiceMismatch = \"AuthorizationServiceMismatch\",\n  /** AuthorizationResourceTypeMismatch */\n  AuthorizationResourceTypeMismatch = \"AuthorizationResourceTypeMismatch\",\n  /** BlobAccessTierNotSupportedForAccountType */\n  BlobAccessTierNotSupportedForAccountType = \"BlobAccessTierNotSupportedForAccountType\",\n}\n\n/**\n * Defines values for StorageErrorCode. \\\n * {@link KnownStorageErrorCode} can be used interchangeably with StorageErrorCode,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **AccountAlreadyExists** \\\n * **AccountBeingCreated** \\\n * **AccountIsDisabled** \\\n * **AuthenticationFailed** \\\n * **AuthorizationFailure** \\\n * **ConditionHeadersNotSupported** \\\n * **ConditionNotMet** \\\n * **EmptyMetadataKey** \\\n * **InsufficientAccountPermissions** \\\n * **InternalError** \\\n * **InvalidAuthenticationInfo** \\\n * **InvalidHeaderValue** \\\n * **InvalidHttpVerb** \\\n * **InvalidInput** \\\n * **InvalidMd5** \\\n * **InvalidMetadata** \\\n * **InvalidQueryParameterValue** \\\n * **InvalidRange** \\\n * **InvalidResourceName** \\\n * **InvalidUri** \\\n * **InvalidXmlDocument** \\\n * **InvalidXmlNodeValue** \\\n * **Md5Mismatch** \\\n * **MetadataTooLarge** \\\n * **MissingContentLengthHeader** \\\n * **MissingRequiredQueryParameter** \\\n * **MissingRequiredHeader** \\\n * **MissingRequiredXmlNode** \\\n * **MultipleConditionHeadersNotSupported** \\\n * **OperationTimedOut** \\\n * **OutOfRangeInput** \\\n * **OutOfRangeQueryParameterValue** \\\n * **RequestBodyTooLarge** \\\n * **ResourceTypeMismatch** \\\n * **RequestUrlFailedToParse** \\\n * **ResourceAlreadyExists** \\\n * **ResourceNotFound** \\\n * **ServerBusy** \\\n * **UnsupportedHeader** \\\n * **UnsupportedXmlNode** \\\n * **UnsupportedQueryParameter** \\\n * **UnsupportedHttpVerb** \\\n * **AppendPositionConditionNotMet** \\\n * **BlobAlreadyExists** \\\n * **BlobImmutableDueToPolicy** \\\n * **BlobNotFound** \\\n * **BlobOverwritten** \\\n * **BlobTierInadequateForContentLength** \\\n * **BlobUsesCustomerSpecifiedEncryption** \\\n * **BlockCountExceedsLimit** \\\n * **BlockListTooLong** \\\n * **CannotChangeToLowerTier** \\\n * **CannotVerifyCopySource** \\\n * **ContainerAlreadyExists** \\\n * **ContainerBeingDeleted** \\\n * **ContainerDisabled** \\\n * **ContainerNotFound** \\\n * **ContentLengthLargerThanTierLimit** \\\n * **CopyAcrossAccountsNotSupported** \\\n * **CopyIdMismatch** \\\n * **FeatureVersionMismatch** \\\n * **IncrementalCopyBlobMismatch** \\\n * **IncrementalCopyOfEarlierVersionSnapshotNotAllowed** \\\n * **IncrementalCopySourceMustBeSnapshot** \\\n * **InfiniteLeaseDurationRequired** \\\n * **InvalidBlobOrBlock** \\\n * **InvalidBlobTier** \\\n * **InvalidBlobType** \\\n * **InvalidBlockId** \\\n * **InvalidBlockList** \\\n * **InvalidOperation** \\\n * **InvalidPageRange** \\\n * **InvalidSourceBlobType** \\\n * **InvalidSourceBlobUrl** \\\n * **InvalidVersionForPageBlobOperation** \\\n * **LeaseAlreadyPresent** \\\n * **LeaseAlreadyBroken** \\\n * **LeaseIdMismatchWithBlobOperation** \\\n * **LeaseIdMismatchWithContainerOperation** \\\n * **LeaseIdMismatchWithLeaseOperation** \\\n * **LeaseIdMissing** \\\n * **LeaseIsBreakingAndCannotBeAcquired** \\\n * **LeaseIsBreakingAndCannotBeChanged** \\\n * **LeaseIsBrokenAndCannotBeRenewed** \\\n * **LeaseLost** \\\n * **LeaseNotPresentWithBlobOperation** \\\n * **LeaseNotPresentWithContainerOperation** \\\n * **LeaseNotPresentWithLeaseOperation** \\\n * **MaxBlobSizeConditionNotMet** \\\n * **NoAuthenticationInformation** \\\n * **NoPendingCopyOperation** \\\n * **OperationNotAllowedOnIncrementalCopyBlob** \\\n * **PendingCopyOperation** \\\n * **PreviousSnapshotCannotBeNewer** \\\n * **PreviousSnapshotNotFound** \\\n * **PreviousSnapshotOperationNotSupported** \\\n * **SequenceNumberConditionNotMet** \\\n * **SequenceNumberIncrementTooLarge** \\\n * **SnapshotCountExceeded** \\\n * **SnapshotOperationRateExceeded** \\\n * **SnapshotsPresent** \\\n * **SourceConditionNotMet** \\\n * **SystemInUse** \\\n * **TargetConditionNotMet** \\\n * **UnauthorizedBlobOverwrite** \\\n * **BlobBeingRehydrated** \\\n * **BlobArchived** \\\n * **BlobNotArchived** \\\n * **AuthorizationSourceIPMismatch** \\\n * **AuthorizationProtocolMismatch** \\\n * **AuthorizationPermissionMismatch** \\\n * **AuthorizationServiceMismatch** \\\n * **AuthorizationResourceTypeMismatch** \\\n * **BlobAccessTierNotSupportedForAccountType**\n */\nexport type StorageErrorCode = string;\n/** Defines values for GeoReplicationStatusType. */\nexport type GeoReplicationStatusType = \"live\" | \"bootstrap\" | \"unavailable\";\n/** Defines values for ListContainersIncludeType. */\nexport type ListContainersIncludeType = \"metadata\" | \"deleted\" | \"system\";\n/** Defines values for LeaseStatusType. */\nexport type LeaseStatusType = \"locked\" | \"unlocked\";\n/** Defines values for LeaseStateType. */\nexport type LeaseStateType =\n  | \"available\"\n  | \"leased\"\n  | \"expired\"\n  | \"breaking\"\n  | \"broken\";\n/** Defines values for LeaseDurationType. */\nexport type LeaseDurationType = \"infinite\" | \"fixed\";\n/** Defines values for PublicAccessType. */\nexport type PublicAccessType = \"container\" | \"blob\";\n/** Defines values for SkuName. */\nexport type SkuName =\n  | \"Standard_LRS\"\n  | \"Standard_GRS\"\n  | \"Standard_RAGRS\"\n  | \"Standard_ZRS\"\n  | \"Premium_LRS\";\n/** Defines values for AccountKind. */\nexport type AccountKind =\n  | \"Storage\"\n  | \"BlobStorage\"\n  | \"StorageV2\"\n  | \"FileStorage\"\n  | \"BlockBlobStorage\";\n/** Defines values for ListBlobsIncludeItem. */\nexport type ListBlobsIncludeItem =\n  | \"copy\"\n  | \"deleted\"\n  | \"metadata\"\n  | \"snapshots\"\n  | \"uncommittedblobs\"\n  | \"versions\"\n  | \"tags\"\n  | \"immutabilitypolicy\"\n  | \"legalhold\"\n  | \"deletedwithversions\";\n/** Defines values for BlobType. */\nexport type BlobType = \"BlockBlob\" | \"PageBlob\" | \"AppendBlob\";\n/** Defines values for CopyStatusType. */\nexport type CopyStatusType = \"pending\" | \"success\" | \"aborted\" | \"failed\";\n/** Defines values for AccessTier. */\nexport type AccessTier =\n  | \"P4\"\n  | \"P6\"\n  | \"P10\"\n  | \"P15\"\n  | \"P20\"\n  | \"P30\"\n  | \"P40\"\n  | \"P50\"\n  | \"P60\"\n  | \"P70\"\n  | \"P80\"\n  | \"Hot\"\n  | \"Cool\"\n  | \"Archive\"\n  | \"Cold\";\n/** Defines values for ArchiveStatus. */\nexport type ArchiveStatus =\n  | \"rehydrate-pending-to-hot\"\n  | \"rehydrate-pending-to-cool\"\n  | \"rehydrate-pending-to-cold\";\n/** Defines values for RehydratePriority. */\nexport type RehydratePriority = \"High\" | \"Standard\";\n/** Defines values for BlobImmutabilityPolicyMode. */\nexport type BlobImmutabilityPolicyMode = \"Mutable\" | \"Unlocked\" | \"Locked\";\n/** Defines values for DeleteSnapshotsOptionType. */\nexport type DeleteSnapshotsOptionType = \"include\" | \"only\";\n/** Defines values for BlobCopySourceTags. */\nexport type BlobCopySourceTags = \"REPLACE\" | \"COPY\";\n/** Defines values for BlockListType. */\nexport type BlockListType = \"committed\" | \"uncommitted\" | \"all\";\n/** Defines values for SequenceNumberActionType. */\nexport type SequenceNumberActionType = \"max\" | \"update\" | \"increment\";\n/** Defines values for QueryFormatType. */\nexport type QueryFormatType = \"delimited\" | \"json\" | \"arrow\" | \"parquet\";\n/** Defines values for SyncCopyStatusType. */\nexport type SyncCopyStatusType = \"success\";\n\n/** Optional parameters. */\nexport interface ServiceSetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the setProperties operation. */\nexport type ServiceSetPropertiesResponse = ServiceSetPropertiesHeaders;\n\n/** Optional parameters. */\nexport interface ServiceGetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getProperties operation. */\nexport type ServiceGetPropertiesResponse = ServiceGetPropertiesHeaders &\n  BlobServiceProperties;\n\n/** Optional parameters. */\nexport interface ServiceGetStatisticsOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getStatistics operation. */\nexport type ServiceGetStatisticsResponse = ServiceGetStatisticsHeaders &\n  BlobServiceStatistics;\n\n/** Optional parameters. */\nexport interface ServiceListContainersSegmentOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Filters the results to return only containers whose name begins with the specified prefix. */\n  prefix?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** Include this parameter to specify that the container's metadata be returned as part of the response body. */\n  include?: ListContainersIncludeType[];\n}\n\n/** Contains response data for the listContainersSegment operation. */\nexport type ServiceListContainersSegmentResponse =\n  ServiceListContainersSegmentHeaders & ListContainersSegmentResponse;\n\n/** Optional parameters. */\nexport interface ServiceGetUserDelegationKeyOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getUserDelegationKey operation. */\nexport type ServiceGetUserDelegationKeyResponse =\n  ServiceGetUserDelegationKeyHeaders & UserDelegationKey;\n\n/** Optional parameters. */\nexport interface ServiceGetAccountInfoOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getAccountInfo operation. */\nexport type ServiceGetAccountInfoResponse = ServiceGetAccountInfoHeaders;\n\n/** Optional parameters. */\nexport interface ServiceSubmitBatchOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the submitBatch operation. */\nexport type ServiceSubmitBatchResponse = ServiceSubmitBatchHeaders & {\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser Blob.\n   * Always `undefined` in node.js.\n   */\n  blobBody?: Promise<Blob>;\n  /**\n   * NODEJS ONLY\n   *\n   * The response body as a node.js Readable stream.\n   * Always `undefined` in the browser.\n   */\n  readableStreamBody?: NodeJS.ReadableStream;\n};\n\n/** Optional parameters. */\nexport interface ServiceFilterBlobsOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** Filters the results to return only to return only blobs whose tags match the specified expression. */\n  where?: string;\n}\n\n/** Contains response data for the filterBlobs operation. */\nexport type ServiceFilterBlobsResponse = ServiceFilterBlobsHeaders &\n  FilterBlobSegment;\n\n/** Optional parameters. */\nexport interface ContainerCreateOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  containerEncryptionScope?: ContainerEncryptionScope;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies whether data in the container may be accessed publicly and the level of access */\n  access?: PublicAccessType;\n}\n\n/** Contains response data for the create operation. */\nexport type ContainerCreateResponse = ContainerCreateHeaders;\n\n/** Optional parameters. */\nexport interface ContainerGetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getProperties operation. */\nexport type ContainerGetPropertiesResponse = ContainerGetPropertiesHeaders;\n\n/** Optional parameters. */\nexport interface ContainerDeleteOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the delete operation. */\nexport type ContainerDeleteResponse = ContainerDeleteHeaders;\n\n/** Optional parameters. */\nexport interface ContainerSetMetadataOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n}\n\n/** Contains response data for the setMetadata operation. */\nexport type ContainerSetMetadataResponse = ContainerSetMetadataHeaders;\n\n/** Optional parameters. */\nexport interface ContainerGetAccessPolicyOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getAccessPolicy operation. */\nexport type ContainerGetAccessPolicyResponse = ContainerGetAccessPolicyHeaders &\n  SignedIdentifier[];\n\n/** Optional parameters. */\nexport interface ContainerSetAccessPolicyOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Specifies whether data in the container may be accessed publicly and the level of access */\n  access?: PublicAccessType;\n  /** the acls for the container */\n  containerAcl?: SignedIdentifier[];\n}\n\n/** Contains response data for the setAccessPolicy operation. */\nexport type ContainerSetAccessPolicyResponse = ContainerSetAccessPolicyHeaders;\n\n/** Optional parameters. */\nexport interface ContainerRestoreOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional.  Version 2019-12-12 and later.  Specifies the name of the deleted container to restore. */\n  deletedContainerName?: string;\n  /** Optional.  Version 2019-12-12 and later.  Specifies the version of the deleted container to restore. */\n  deletedContainerVersion?: string;\n}\n\n/** Contains response data for the restore operation. */\nexport type ContainerRestoreResponse = ContainerRestoreHeaders;\n\n/** Optional parameters. */\nexport interface ContainerRenameOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** A lease ID for the source path. If specified, the source path must have an active lease and the lease ID must match. */\n  sourceLeaseId?: string;\n}\n\n/** Contains response data for the rename operation. */\nexport type ContainerRenameResponse = ContainerRenameHeaders;\n\n/** Optional parameters. */\nexport interface ContainerSubmitBatchOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the submitBatch operation. */\nexport type ContainerSubmitBatchResponse = ContainerSubmitBatchHeaders & {\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser Blob.\n   * Always `undefined` in node.js.\n   */\n  blobBody?: Promise<Blob>;\n  /**\n   * NODEJS ONLY\n   *\n   * The response body as a node.js Readable stream.\n   * Always `undefined` in the browser.\n   */\n  readableStreamBody?: NodeJS.ReadableStream;\n};\n\n/** Optional parameters. */\nexport interface ContainerFilterBlobsOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** Filters the results to return only to return only blobs whose tags match the specified expression. */\n  where?: string;\n}\n\n/** Contains response data for the filterBlobs operation. */\nexport type ContainerFilterBlobsResponse = ContainerFilterBlobsHeaders &\n  FilterBlobSegment;\n\n/** Optional parameters. */\nexport interface ContainerAcquireLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Specifies the duration of the lease, in seconds, or negative one (-1) for a lease that never expires. A non-infinite lease can be between 15 and 60 seconds. A lease duration cannot be changed using renew or change. */\n  duration?: number;\n  /** Proposed lease ID, in a GUID string format. The Blob service returns 400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor (String) for a list of valid GUID string formats. */\n  proposedLeaseId?: string;\n}\n\n/** Contains response data for the acquireLease operation. */\nexport type ContainerAcquireLeaseResponse = ContainerAcquireLeaseHeaders;\n\n/** Optional parameters. */\nexport interface ContainerReleaseLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the releaseLease operation. */\nexport type ContainerReleaseLeaseResponse = ContainerReleaseLeaseHeaders;\n\n/** Optional parameters. */\nexport interface ContainerRenewLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the renewLease operation. */\nexport type ContainerRenewLeaseResponse = ContainerRenewLeaseHeaders;\n\n/** Optional parameters. */\nexport interface ContainerBreakLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** For a break operation, proposed duration the lease should continue before it is broken, in seconds, between 0 and 60. This break period is only used if it is shorter than the time remaining on the lease. If longer, the time remaining on the lease is used. A new lease will not be available before the break period has expired, but the lease may be held for longer than the break period. If this header does not appear with a break operation, a fixed-duration lease breaks after the remaining lease period elapses, and an infinite lease breaks immediately. */\n  breakPeriod?: number;\n}\n\n/** Contains response data for the breakLease operation. */\nexport type ContainerBreakLeaseResponse = ContainerBreakLeaseHeaders;\n\n/** Optional parameters. */\nexport interface ContainerChangeLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the changeLease operation. */\nexport type ContainerChangeLeaseResponse = ContainerChangeLeaseHeaders;\n\n/** Optional parameters. */\nexport interface ContainerListBlobFlatSegmentOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Filters the results to return only containers whose name begins with the specified prefix. */\n  prefix?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** Include this parameter to specify one or more datasets to include in the response. */\n  include?: ListBlobsIncludeItem[];\n}\n\n/** Contains response data for the listBlobFlatSegment operation. */\nexport type ContainerListBlobFlatSegmentResponse =\n  ContainerListBlobFlatSegmentHeaders & ListBlobsFlatSegmentResponse;\n\n/** Optional parameters. */\nexport interface ContainerListBlobHierarchySegmentOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Filters the results to return only containers whose name begins with the specified prefix. */\n  prefix?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** Include this parameter to specify one or more datasets to include in the response. */\n  include?: ListBlobsIncludeItem[];\n}\n\n/** Contains response data for the listBlobHierarchySegment operation. */\nexport type ContainerListBlobHierarchySegmentResponse =\n  ContainerListBlobHierarchySegmentHeaders & ListBlobsHierarchySegmentResponse;\n\n/** Optional parameters. */\nexport interface ContainerGetAccountInfoOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getAccountInfo operation. */\nexport type ContainerGetAccountInfoResponse = ContainerGetAccountInfoHeaders;\n\n/** Optional parameters. */\nexport interface BlobDownloadOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n  /** Return only the bytes of the blob in the specified range. */\n  range?: string;\n  /** When set to true and specified together with the Range, the service returns the MD5 hash for the range, as long as the range is less than or equal to 4 MB in size. */\n  rangeGetContentMD5?: boolean;\n  /** When set to true and specified together with the Range, the service returns the CRC64 hash for the range, as long as the range is less than or equal to 4 MB in size. */\n  rangeGetContentCRC64?: boolean;\n}\n\n/** Contains response data for the download operation. */\nexport type BlobDownloadResponse = BlobDownloadHeaders & {\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser Blob.\n   * Always `undefined` in node.js.\n   */\n  blobBody?: Promise<Blob>;\n  /**\n   * NODEJS ONLY\n   *\n   * The response body as a node.js Readable stream.\n   * Always `undefined` in the browser.\n   */\n  readableStreamBody?: NodeJS.ReadableStream;\n};\n\n/** Optional parameters. */\nexport interface BlobGetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n}\n\n/** Contains response data for the getProperties operation. */\nexport type BlobGetPropertiesResponse = BlobGetPropertiesHeaders;\n\n/** Optional parameters. */\nexport interface BlobDeleteOptionalParams extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n  /** Required if the blob has associated snapshots. Specify one of the following two options: include: Delete the base blob and all of its snapshots. only: Delete only the blob's snapshots and not the blob itself */\n  deleteSnapshots?: DeleteSnapshotsOptionType;\n  /** Optional.  Only possible value is 'permanent', which specifies to permanently delete a blob if blob soft delete is enabled. */\n  blobDeleteType?: string;\n}\n\n/** Contains response data for the delete operation. */\nexport type BlobDeleteResponse = BlobDeleteHeaders;\n\n/** Optional parameters. */\nexport interface BlobUndeleteOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the undelete operation. */\nexport type BlobUndeleteResponse = BlobUndeleteHeaders;\n\n/** Optional parameters. */\nexport interface BlobSetExpiryOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The time to set the blob to expiry */\n  expiresOn?: string;\n}\n\n/** Contains response data for the setExpiry operation. */\nexport type BlobSetExpiryResponse = BlobSetExpiryHeaders;\n\n/** Optional parameters. */\nexport interface BlobSetHttpHeadersOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  blobHttpHeaders?: BlobHttpHeaders;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the setHttpHeaders operation. */\nexport type BlobSetHttpHeadersResponse = BlobSetHttpHeadersHeaders;\n\n/** Optional parameters. */\nexport interface BlobSetImmutabilityPolicyOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n}\n\n/** Contains response data for the setImmutabilityPolicy operation. */\nexport type BlobSetImmutabilityPolicyResponse =\n  BlobSetImmutabilityPolicyHeaders;\n\n/** Optional parameters. */\nexport interface BlobDeleteImmutabilityPolicyOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n}\n\n/** Contains response data for the deleteImmutabilityPolicy operation. */\nexport type BlobDeleteImmutabilityPolicyResponse =\n  BlobDeleteImmutabilityPolicyHeaders;\n\n/** Optional parameters. */\nexport interface BlobSetLegalHoldOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n}\n\n/** Contains response data for the setLegalHold operation. */\nexport type BlobSetLegalHoldResponse = BlobSetLegalHoldHeaders;\n\n/** Optional parameters. */\nexport interface BlobSetMetadataOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n}\n\n/** Contains response data for the setMetadata operation. */\nexport type BlobSetMetadataResponse = BlobSetMetadataHeaders;\n\n/** Optional parameters. */\nexport interface BlobAcquireLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Specifies the duration of the lease, in seconds, or negative one (-1) for a lease that never expires. A non-infinite lease can be between 15 and 60 seconds. A lease duration cannot be changed using renew or change. */\n  duration?: number;\n  /** Proposed lease ID, in a GUID string format. The Blob service returns 400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor (String) for a list of valid GUID string formats. */\n  proposedLeaseId?: string;\n}\n\n/** Contains response data for the acquireLease operation. */\nexport type BlobAcquireLeaseResponse = BlobAcquireLeaseHeaders;\n\n/** Optional parameters. */\nexport interface BlobReleaseLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the releaseLease operation. */\nexport type BlobReleaseLeaseResponse = BlobReleaseLeaseHeaders;\n\n/** Optional parameters. */\nexport interface BlobRenewLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the renewLease operation. */\nexport type BlobRenewLeaseResponse = BlobRenewLeaseHeaders;\n\n/** Optional parameters. */\nexport interface BlobChangeLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the changeLease operation. */\nexport type BlobChangeLeaseResponse = BlobChangeLeaseHeaders;\n\n/** Optional parameters. */\nexport interface BlobBreakLeaseOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** For a break operation, proposed duration the lease should continue before it is broken, in seconds, between 0 and 60. This break period is only used if it is shorter than the time remaining on the lease. If longer, the time remaining on the lease is used. A new lease will not be available before the break period has expired, but the lease may be held for longer than the break period. If this header does not appear with a break operation, a fixed-duration lease breaks after the remaining lease period elapses, and an infinite lease breaks immediately. */\n  breakPeriod?: number;\n}\n\n/** Contains response data for the breakLease operation. */\nexport type BlobBreakLeaseResponse = BlobBreakLeaseHeaders;\n\n/** Optional parameters. */\nexport interface BlobCreateSnapshotOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n}\n\n/** Contains response data for the createSnapshot operation. */\nexport type BlobCreateSnapshotResponse = BlobCreateSnapshotHeaders;\n\n/** Optional parameters. */\nexport interface BlobStartCopyFromURLOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  sourceModifiedAccessConditions?: SourceModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Optional. Indicates the tier to be set on the blob. */\n  tier?: AccessTier;\n  /** Optional: Indicates the priority with which to rehydrate an archived blob. */\n  rehydratePriority?: RehydratePriority;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Overrides the sealed state of the destination blob.  Service version 2019-12-12 and newer. */\n  sealBlob?: boolean;\n  /** Specified if a legal hold should be set on the blob. */\n  legalHold?: boolean;\n}\n\n/** Contains response data for the startCopyFromURL operation. */\nexport type BlobStartCopyFromURLResponse = BlobStartCopyFromURLHeaders;\n\n/** Optional parameters. */\nexport interface BlobCopyFromURLOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  sourceModifiedAccessConditions?: SourceModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Optional. Indicates the tier to be set on the blob. */\n  tier?: AccessTier;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Specified if a legal hold should be set on the blob. */\n  legalHold?: boolean;\n  /** Specify the md5 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentMD5?: Uint8Array;\n  /** Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source. */\n  copySourceAuthorization?: string;\n  /** Optional, default 'replace'.  Indicates if source tags should be copied or replaced with the tags specified by x-ms-tags. */\n  copySourceTags?: BlobCopySourceTags;\n}\n\n/** Contains response data for the copyFromURL operation. */\nexport type BlobCopyFromURLResponse = BlobCopyFromURLHeaders;\n\n/** Optional parameters. */\nexport interface BlobAbortCopyFromURLOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the abortCopyFromURL operation. */\nexport type BlobAbortCopyFromURLResponse = BlobAbortCopyFromURLHeaders;\n\n/** Optional parameters. */\nexport interface BlobSetTierOptionalParams extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n  /** Optional: Indicates the priority with which to rehydrate an archived blob. */\n  rehydratePriority?: RehydratePriority;\n}\n\n/** Contains response data for the setTier operation. */\nexport type BlobSetTierResponse = BlobSetTierHeaders;\n\n/** Optional parameters. */\nexport interface BlobGetAccountInfoOptionalParams\n  extends coreClient.OperationOptions {\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getAccountInfo operation. */\nexport type BlobGetAccountInfoResponse = BlobGetAccountInfoHeaders;\n\n/** Optional parameters. */\nexport interface BlobQueryOptionalParams extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** the query request */\n  queryRequest?: QueryRequest;\n}\n\n/** Contains response data for the query operation. */\nexport type BlobQueryResponse = BlobQueryHeaders & {\n  /**\n   * BROWSER ONLY\n   *\n   * The response body as a browser Blob.\n   * Always `undefined` in node.js.\n   */\n  blobBody?: Promise<Blob>;\n  /**\n   * NODEJS ONLY\n   *\n   * The response body as a node.js Readable stream.\n   * Always `undefined` in the browser.\n   */\n  readableStreamBody?: NodeJS.ReadableStream;\n};\n\n/** Optional parameters. */\nexport interface BlobGetTagsOptionalParams extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n}\n\n/** Contains response data for the getTags operation. */\nexport type BlobGetTagsResponse = BlobGetTagsHeaders & BlobTags;\n\n/** Optional parameters. */\nexport interface BlobSetTagsOptionalParams extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The version id parameter is an opaque DateTime value that, when present, specifies the version of the blob to operate on. It's for service version 2019-10-10 and newer. */\n  versionId?: string;\n  /** Blob tags */\n  tags?: BlobTags;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the transactional crc64 for the body, to be validated by the service. */\n  transactionalContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the setTags operation. */\nexport type BlobSetTagsResponse = BlobSetTagsHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobCreateOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  blobHttpHeaders?: BlobHttpHeaders;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Optional. Indicates the tier to be set on the blob. */\n  tier?: AccessTier;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Specified if a legal hold should be set on the blob. */\n  legalHold?: boolean;\n  /** Set for page blobs only. The sequence number is a user-controlled value that you can use to track requests. The value of the sequence number must be between 0 and 2^63 - 1. */\n  blobSequenceNumber?: number;\n}\n\n/** Contains response data for the create operation. */\nexport type PageBlobCreateResponse = PageBlobCreateHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobUploadPagesOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  sequenceNumberAccessConditions?: SequenceNumberAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Return only the bytes of the blob in the specified range. */\n  range?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the transactional crc64 for the body, to be validated by the service. */\n  transactionalContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the uploadPages operation. */\nexport type PageBlobUploadPagesResponse = PageBlobUploadPagesHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobClearPagesOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  sequenceNumberAccessConditions?: SequenceNumberAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Return only the bytes of the blob in the specified range. */\n  range?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n}\n\n/** Contains response data for the clearPages operation. */\nexport type PageBlobClearPagesResponse = PageBlobClearPagesHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobUploadPagesFromURLOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  sourceModifiedAccessConditions?: SourceModifiedAccessConditions;\n  /** Parameter group */\n  sequenceNumberAccessConditions?: SequenceNumberAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Specify the md5 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentMD5?: Uint8Array;\n  /** Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source. */\n  copySourceAuthorization?: string;\n  /** Specify the crc64 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the uploadPagesFromURL operation. */\nexport type PageBlobUploadPagesFromURLResponse =\n  PageBlobUploadPagesFromURLHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobGetPageRangesOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** Return only the bytes of the blob in the specified range. */\n  range?: string;\n}\n\n/** Contains response data for the getPageRanges operation. */\nexport type PageBlobGetPageRangesResponse = PageBlobGetPageRangesHeaders &\n  PageList;\n\n/** Optional parameters. */\nexport interface PageBlobGetPageRangesDiffOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n  /** Return only the bytes of the blob in the specified range. */\n  range?: string;\n  /** Optional in version 2015-07-08 and newer. The prevsnapshot parameter is a DateTime value that specifies that the response will contain only pages that were changed between target blob and previous snapshot. Changed pages include both updated and cleared pages. The target blob may be a snapshot, as long as the snapshot specified by prevsnapshot is the older of the two. Note that incremental snapshots are currently supported only for blobs created on or after January 1, 2016. */\n  prevsnapshot?: string;\n  /** Optional. This header is only supported in service versions 2019-04-19 and after and specifies the URL of a previous snapshot of the target blob. The response will only contain pages that were changed between the target blob and its previous snapshot. */\n  prevSnapshotUrl?: string;\n}\n\n/** Contains response data for the getPageRangesDiff operation. */\nexport type PageBlobGetPageRangesDiffResponse =\n  PageBlobGetPageRangesDiffHeaders & PageList;\n\n/** Optional parameters. */\nexport interface PageBlobResizeOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n}\n\n/** Contains response data for the resize operation. */\nexport type PageBlobResizeResponse = PageBlobResizeHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobUpdateSequenceNumberOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Set for page blobs only. The sequence number is a user-controlled value that you can use to track requests. The value of the sequence number must be between 0 and 2^63 - 1. */\n  blobSequenceNumber?: number;\n}\n\n/** Contains response data for the updateSequenceNumber operation. */\nexport type PageBlobUpdateSequenceNumberResponse =\n  PageBlobUpdateSequenceNumberHeaders;\n\n/** Optional parameters. */\nexport interface PageBlobCopyIncrementalOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the copyIncremental operation. */\nexport type PageBlobCopyIncrementalResponse = PageBlobCopyIncrementalHeaders;\n\n/** Optional parameters. */\nexport interface AppendBlobCreateOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  blobHttpHeaders?: BlobHttpHeaders;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Specified if a legal hold should be set on the blob. */\n  legalHold?: boolean;\n}\n\n/** Contains response data for the create operation. */\nexport type AppendBlobCreateResponse = AppendBlobCreateHeaders;\n\n/** Optional parameters. */\nexport interface AppendBlobAppendBlockOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  appendPositionAccessConditions?: AppendPositionAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the transactional crc64 for the body, to be validated by the service. */\n  transactionalContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the appendBlock operation. */\nexport type AppendBlobAppendBlockResponse = AppendBlobAppendBlockHeaders;\n\n/** Optional parameters. */\nexport interface AppendBlobAppendBlockFromUrlOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  sourceModifiedAccessConditions?: SourceModifiedAccessConditions;\n  /** Parameter group */\n  appendPositionAccessConditions?: AppendPositionAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Specify the md5 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentMD5?: Uint8Array;\n  /** Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source. */\n  copySourceAuthorization?: string;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the crc64 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentCrc64?: Uint8Array;\n  /** Bytes of source data in the specified range. */\n  sourceRange?: string;\n}\n\n/** Contains response data for the appendBlockFromUrl operation. */\nexport type AppendBlobAppendBlockFromUrlResponse =\n  AppendBlobAppendBlockFromUrlHeaders;\n\n/** Optional parameters. */\nexport interface AppendBlobSealOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  appendPositionAccessConditions?: AppendPositionAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the seal operation. */\nexport type AppendBlobSealResponse = AppendBlobSealHeaders;\n\n/** Optional parameters. */\nexport interface BlockBlobUploadOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  blobHttpHeaders?: BlobHttpHeaders;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Optional. Indicates the tier to be set on the blob. */\n  tier?: AccessTier;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Specified if a legal hold should be set on the blob. */\n  legalHold?: boolean;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the transactional crc64 for the body, to be validated by the service. */\n  transactionalContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the upload operation. */\nexport type BlockBlobUploadResponse = BlockBlobUploadHeaders;\n\n/** Optional parameters. */\nexport interface BlockBlobPutBlobFromUrlOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  blobHttpHeaders?: BlobHttpHeaders;\n  /** Parameter group */\n  sourceModifiedAccessConditions?: SourceModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Optional. Indicates the tier to be set on the blob. */\n  tier?: AccessTier;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Specify the md5 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentMD5?: Uint8Array;\n  /** Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source. */\n  copySourceAuthorization?: string;\n  /** Optional, default 'replace'.  Indicates if source tags should be copied or replaced with the tags specified by x-ms-tags. */\n  copySourceTags?: BlobCopySourceTags;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Optional, default is true.  Indicates if properties from the source blob should be copied. */\n  copySourceBlobProperties?: boolean;\n}\n\n/** Contains response data for the putBlobFromUrl operation. */\nexport type BlockBlobPutBlobFromUrlResponse = BlockBlobPutBlobFromUrlHeaders;\n\n/** Optional parameters. */\nexport interface BlockBlobStageBlockOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the transactional crc64 for the body, to be validated by the service. */\n  transactionalContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the stageBlock operation. */\nexport type BlockBlobStageBlockResponse = BlockBlobStageBlockHeaders;\n\n/** Optional parameters. */\nexport interface BlockBlobStageBlockFromURLOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  sourceModifiedAccessConditions?: SourceModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Specify the md5 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentMD5?: Uint8Array;\n  /** Only Bearer type is supported. Credentials should be a valid OAuth access token to copy source. */\n  copySourceAuthorization?: string;\n  /** Specify the crc64 calculated for the range of bytes that must be read from the copy source. */\n  sourceContentCrc64?: Uint8Array;\n  /** Bytes of source data in the specified range. */\n  sourceRange?: string;\n}\n\n/** Contains response data for the stageBlockFromURL operation. */\nexport type BlockBlobStageBlockFromURLResponse =\n  BlockBlobStageBlockFromURLHeaders;\n\n/** Optional parameters. */\nexport interface BlockBlobCommitBlockListOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** Parameter group */\n  cpkInfo?: CpkInfo;\n  /** Parameter group */\n  blobHttpHeaders?: BlobHttpHeaders;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information. */\n  metadata?: { [propertyName: string]: string };\n  /** Specifies the date time when the blobs immutability policy is set to expire. */\n  immutabilityPolicyExpiry?: Date;\n  /** Specifies the immutability policy mode to set on the blob. */\n  immutabilityPolicyMode?: BlobImmutabilityPolicyMode;\n  /** Optional. Version 2019-07-07 and later.  Specifies the name of the encryption scope to use to encrypt the data provided in the request. If not specified, encryption is performed with the default account encryption scope.  For more information, see Encryption at Rest for Azure Storage Services. */\n  encryptionScope?: string;\n  /** Optional. Indicates the tier to be set on the blob. */\n  tier?: AccessTier;\n  /** Optional.  Used to set blob tags in various blob operations. */\n  blobTagsString?: string;\n  /** Specified if a legal hold should be set on the blob. */\n  legalHold?: boolean;\n  /** Specify the transactional md5 for the body, to be validated by the service. */\n  transactionalContentMD5?: Uint8Array;\n  /** Specify the transactional crc64 for the body, to be validated by the service. */\n  transactionalContentCrc64?: Uint8Array;\n}\n\n/** Contains response data for the commitBlockList operation. */\nexport type BlockBlobCommitBlockListResponse = BlockBlobCommitBlockListHeaders;\n\n/** Optional parameters. */\nexport interface BlockBlobGetBlockListOptionalParams\n  extends coreClient.OperationOptions {\n  /** Parameter group */\n  leaseAccessConditions?: LeaseAccessConditions;\n  /** Parameter group */\n  modifiedAccessConditions?: ModifiedAccessConditions;\n  /** The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a> */\n  snapshot?: string;\n}\n\n/** Contains response data for the getBlockList operation. */\nexport type BlockBlobGetBlockListResponse = BlockBlobGetBlockListHeaders &\n  BlockList;\n\n/** Optional parameters. */\nexport interface StorageClientOptionalParams\n  extends coreHttpCompat.ExtendedServiceClientOptions {\n  /** Specifies the version of the operation to use for this request. */\n  version?: string;\n  /** Overrides client endpoint. */\n  endpoint?: string;\n}\n"]}