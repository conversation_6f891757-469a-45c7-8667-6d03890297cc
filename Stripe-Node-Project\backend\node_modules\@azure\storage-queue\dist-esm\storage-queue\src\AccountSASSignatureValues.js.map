{"version": 3, "file": "AccountSASSignatureValues.js", "sourceRoot": "", "sources": ["../../../src/AccountSASSignatureValues.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAG1D,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AA8D5D;;;;;;;;;;GAUG;AACH,MAAM,UAAU,iCAAiC,CAC/C,yBAAoD,EACpD,mBAA+C;IAE/C,OAAO,yCAAyC,CAAC,yBAAyB,EAAE,mBAAmB,CAAC;SAC7F,kBAAkB,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,yCAAyC,CACvD,yBAAoD,EACpD,mBAA+C;IAE/C,MAAM,OAAO,GAAG,yBAAyB,CAAC,OAAO;QAC/C,CAAC,CAAC,yBAAyB,CAAC,OAAO;QACnC,CAAC,CAAC,eAAe,CAAC;IAEpB,MAAM,iBAAiB,GAAG,qBAAqB,CAAC,KAAK,CACnD,yBAAyB,CAAC,WAAW,CAAC,QAAQ,EAAE,CACjD,CAAC,QAAQ,EAAE,CAAC;IACb,MAAM,cAAc,GAAG,kBAAkB,CAAC,KAAK,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC/F,MAAM,mBAAmB,GAAG,uBAAuB,CAAC,KAAK,CACvD,yBAAyB,CAAC,aAAa,CACxC,CAAC,QAAQ,EAAE,CAAC;IAEb,IAAI,YAAoB,CAAC;IAEzB,IAAI,OAAO,IAAI,YAAY,EAAE,CAAC;QAC5B,YAAY,GAAG;YACb,mBAAmB,CAAC,WAAW;YAC/B,iBAAiB;YACjB,cAAc;YACd,mBAAmB;YACnB,yBAAyB,CAAC,QAAQ;gBAChC,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,QAAQ,EAAE,KAAK,CAAC;gBACjE,CAAC,CAAC,EAAE;YACN,oBAAoB,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC;YAChE,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3F,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5E,OAAO;YACP,EAAE,EAAE,gCAAgC;YACpC,EAAE,EAAE,uDAAuD;SAC5D,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;SAAM,CAAC;QACN,YAAY,GAAG;YACb,mBAAmB,CAAC,WAAW;YAC/B,iBAAiB;YACjB,cAAc;YACd,mBAAmB;YACnB,yBAAyB,CAAC,QAAQ;gBAChC,CAAC,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,QAAQ,EAAE,KAAK,CAAC;gBACjE,CAAC,CAAC,EAAE;YACN,oBAAoB,CAAC,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC;YAChE,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3F,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YAC5E,OAAO;YACP,EAAE,EAAE,uDAAuD;SAC5D,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACf,CAAC;IAED,MAAM,SAAS,GAAW,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAE9E,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,OAAO,EACP,SAAS,EACT,iBAAiB,EACjB,cAAc,EACd,mBAAmB,EACnB,yBAAyB,CAAC,QAAQ,EAClC,yBAAyB,CAAC,QAAQ,EAClC,yBAAyB,CAAC,SAAS,EACnC,yBAAyB,CAAC,OAAO,CAClC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AccountSASPermissions } from \"./AccountSASPermissions\";\nimport { AccountSASResourceTypes } from \"./AccountSASResourceTypes\";\nimport { AccountSASServices } from \"./AccountSASServices\";\nimport type { StorageSharedKeyCredential } from \"../../storage-blob/src/credentials/StorageSharedKeyCredential\";\nimport type { SasIPRange } from \"./SasIPRange\";\nimport { ipRangeToString } from \"./SasIPRange\";\nimport type { SASProtocol } from \"./SASQueryParameters\";\nimport { SASQueryParameters } from \"./SASQueryParameters\";\nimport { SERVICE_VERSION } from \"./utils/constants\";\nimport { truncatedISO8061Date } from \"./utils/utils.common\";\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * AccountSASSignatureValues is used to generate a Shared Access Signature (SAS) for an Azure Storage account. Once\n * all the values here are set appropriately, call generateSASQueryParameters() to obtain a representation of the SAS\n * which can actually be applied to queue urls. Note: that both this class and {@link SASQueryParameters} exist because\n * the former is mutable and a logical representation while the latter is immutable and used to generate actual REST\n * requests.\n *\n * @see https://learn.microsoft.com/en-us/azure/storage/common/storage-dotnet-shared-access-signature-part-1\n * for more conceptual information on SAS\n *\n * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n * for descriptions of the parameters, including which are required\n */\nexport interface AccountSASSignatureValues {\n  /**\n   * If not provided, this defaults to the service version targeted by this version of the library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols allowed.\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n\n  /**\n   * The time after which the SAS will no longer work.\n   */\n  expiresOn: Date;\n\n  /**\n   * Specifies which operations the SAS user may perform. Please refer to {@link AccountSASPermissions} for help\n   * constructing the permissions string.\n   */\n  permissions: AccountSASPermissions;\n\n  /**\n   * Optional. IP range allowed.\n   */\n  ipRange?: SasIPRange;\n\n  /**\n   * The values that indicate the services accessible with this SAS. Please refer to {@link AccountSASServices} to\n   * construct this value.\n   */\n  services: string;\n\n  /**\n   * The values that indicate the resource types accessible with this SAS. Please refer\n   * to {@link AccountSASResourceTypes} to construct this value.\n   */\n  resourceTypes: string;\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * Generates a {@link SASQueryParameters} object which contains all SAS query parameters needed to make an actual\n * REST request.\n *\n * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n *\n * @param accountSASSignatureValues - SAS Signature values of the account\n * @param sharedKeyCredential - Shared key credential.\n */\nexport function generateAccountSASQueryParameters(\n  accountSASSignatureValues: AccountSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): SASQueryParameters {\n  return generateAccountSASQueryParametersInternal(accountSASSignatureValues, sharedKeyCredential)\n    .sasQueryParameters;\n}\n\nexport function generateAccountSASQueryParametersInternal(\n  accountSASSignatureValues: AccountSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  const version = accountSASSignatureValues.version\n    ? accountSASSignatureValues.version\n    : SERVICE_VERSION;\n\n  const parsedPermissions = AccountSASPermissions.parse(\n    accountSASSignatureValues.permissions.toString(),\n  ).toString();\n  const parsedServices = AccountSASServices.parse(accountSASSignatureValues.services).toString();\n  const parsedResourceTypes = AccountSASResourceTypes.parse(\n    accountSASSignatureValues.resourceTypes,\n  ).toString();\n\n  let stringToSign: string;\n\n  if (version >= \"2020-12-06\") {\n    stringToSign = [\n      sharedKeyCredential.accountName,\n      parsedPermissions,\n      parsedServices,\n      parsedResourceTypes,\n      accountSASSignatureValues.startsOn\n        ? truncatedISO8061Date(accountSASSignatureValues.startsOn, false)\n        : \"\",\n      truncatedISO8061Date(accountSASSignatureValues.expiresOn, false),\n      accountSASSignatureValues.ipRange ? ipRangeToString(accountSASSignatureValues.ipRange) : \"\",\n      accountSASSignatureValues.protocol ? accountSASSignatureValues.protocol : \"\",\n      version,\n      \"\", // Reserved for encryption scope\n      \"\", // Account SAS requires an additional newline character\n    ].join(\"\\n\");\n  } else {\n    stringToSign = [\n      sharedKeyCredential.accountName,\n      parsedPermissions,\n      parsedServices,\n      parsedResourceTypes,\n      accountSASSignatureValues.startsOn\n        ? truncatedISO8061Date(accountSASSignatureValues.startsOn, false)\n        : \"\",\n      truncatedISO8061Date(accountSASSignatureValues.expiresOn, false),\n      accountSASSignatureValues.ipRange ? ipRangeToString(accountSASSignatureValues.ipRange) : \"\",\n      accountSASSignatureValues.protocol ? accountSASSignatureValues.protocol : \"\",\n      version,\n      \"\", // Account SAS requires an additional newline character\n    ].join(\"\\n\");\n  }\n\n  const signature: string = sharedKeyCredential.computeHMACSHA256(stringToSign);\n\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      version,\n      signature,\n      parsedPermissions,\n      parsedServices,\n      parsedResourceTypes,\n      accountSASSignatureValues.protocol,\n      accountSASSignatureValues.startsOn,\n      accountSASSignatureValues.expiresOn,\n      accountSASSignatureValues.ipRange,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n"]}