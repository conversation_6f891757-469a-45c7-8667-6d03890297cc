{"version": 3, "file": "BlobSASPermissions.js", "sourceRoot": "", "sources": ["../../../../src/sas/BlobSASPermissions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;GAQG;AACH,MAAM,OAAO,kBAAkB;IAA/B;QAiGE;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,UAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,kBAAa,GAAY,KAAK,CAAC;QAEtC;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;QAEhC;;WAEG;QACI,0BAAqB,GAAY,KAAK,CAAC;QAE9C;;WAEG;QACI,oBAAe,GAAY,KAAK,CAAC;IA6C1C,CAAC;IAlMC;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,WAAmB;QACrC,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAEpD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,GAAG;oBACN,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAC/B,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;oBAC9B,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACjC,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC;oBAChC,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACjC,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC;oBACxC,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;oBAC9B,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAC/B,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;oBAClC,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,qBAAqB,GAAG,IAAI,CAAC;oBAChD,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC1C,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,uBAAuB,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAI,CAAC,cAAsC;QACvD,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACpD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC;YACvB,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;QAChC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAC;QACnC,CAAC;QACD,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;YACzB,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC;QAClC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAC;QACnC,CAAC;QACD,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YACjC,kBAAkB,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1C,CAAC;QACD,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC;YACvB,kBAAkB,CAAC,GAAG,GAAG,IAAI,CAAC;QAChC,CAAC;QACD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC;QACpC,CAAC;QACD,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACzC,kBAAkB,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAClD,CAAC;QACD,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;YACnC,kBAAkB,CAAC,eAAe,GAAG,IAAI,CAAC;QAC5C,CAAC;QACD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAyDD;;;;;OAKG;IACI,QAAQ;QACb,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * This is a helper class to construct a string representing the permissions granted by a ServiceSAS to a blob. Setting\n * a value to true means that any SAS which uses these permissions will grant permissions for that operation. Once all\n * the values are set, this should be serialized with toString and set as the permissions field on a\n * {@link BlobSASSignatureValues} object. It is possible to construct the permissions string without this class, but\n * the order of the permissions is particular and this class guarantees correctness.\n */\nexport class BlobSASPermissions {\n  /**\n   * Creates a {@link BlobSASPermissions} from the specified permissions string. This method will throw an\n   * Error if it encounters a character that does not correspond to a valid permission.\n   *\n   * @param permissions -\n   */\n  public static parse(permissions: string): BlobSASPermissions {\n    const blobSASPermissions = new BlobSASPermissions();\n\n    for (const char of permissions) {\n      switch (char) {\n        case \"r\":\n          blobSASPermissions.read = true;\n          break;\n        case \"a\":\n          blobSASPermissions.add = true;\n          break;\n        case \"c\":\n          blobSASPermissions.create = true;\n          break;\n        case \"w\":\n          blobSASPermissions.write = true;\n          break;\n        case \"d\":\n          blobSASPermissions.delete = true;\n          break;\n        case \"x\":\n          blobSASPermissions.deleteVersion = true;\n          break;\n        case \"t\":\n          blobSASPermissions.tag = true;\n          break;\n        case \"m\":\n          blobSASPermissions.move = true;\n          break;\n        case \"e\":\n          blobSASPermissions.execute = true;\n          break;\n        case \"i\":\n          blobSASPermissions.setImmutabilityPolicy = true;\n          break;\n        case \"y\":\n          blobSASPermissions.permanentDelete = true;\n          break;\n        default:\n          throw new RangeError(`Invalid permission: ${char}`);\n      }\n    }\n\n    return blobSASPermissions;\n  }\n\n  /**\n   * Creates a {@link BlobSASPermissions} from a raw object which contains same keys as it\n   * and boolean values for them.\n   *\n   * @param permissionLike -\n   */\n  public static from(permissionLike: BlobSASPermissionsLike): BlobSASPermissions {\n    const blobSASPermissions = new BlobSASPermissions();\n    if (permissionLike.read) {\n      blobSASPermissions.read = true;\n    }\n    if (permissionLike.add) {\n      blobSASPermissions.add = true;\n    }\n    if (permissionLike.create) {\n      blobSASPermissions.create = true;\n    }\n    if (permissionLike.write) {\n      blobSASPermissions.write = true;\n    }\n    if (permissionLike.delete) {\n      blobSASPermissions.delete = true;\n    }\n    if (permissionLike.deleteVersion) {\n      blobSASPermissions.deleteVersion = true;\n    }\n    if (permissionLike.tag) {\n      blobSASPermissions.tag = true;\n    }\n    if (permissionLike.move) {\n      blobSASPermissions.move = true;\n    }\n    if (permissionLike.execute) {\n      blobSASPermissions.execute = true;\n    }\n    if (permissionLike.setImmutabilityPolicy) {\n      blobSASPermissions.setImmutabilityPolicy = true;\n    }\n    if (permissionLike.permanentDelete) {\n      blobSASPermissions.permanentDelete = true;\n    }\n    return blobSASPermissions;\n  }\n\n  /**\n   * Specifies Read access granted.\n   */\n  public read: boolean = false;\n\n  /**\n   * Specifies Add access granted.\n   */\n  public add: boolean = false;\n\n  /**\n   * Specifies Create access granted.\n   */\n  public create: boolean = false;\n\n  /**\n   * Specifies Write access granted.\n   */\n  public write: boolean = false;\n\n  /**\n   * Specifies Delete access granted.\n   */\n  public delete: boolean = false;\n\n  /**\n   * Specifies Delete version access granted.\n   */\n  public deleteVersion: boolean = false;\n\n  /**\n   * Specfies Tag access granted.\n   */\n  public tag: boolean = false;\n\n  /**\n   * Specifies Move access granted.\n   */\n  public move: boolean = false;\n\n  /**\n   * Specifies Execute access granted.\n   */\n  public execute: boolean = false;\n\n  /**\n   * Specifies SetImmutabilityPolicy access granted.\n   */\n  public setImmutabilityPolicy: boolean = false;\n\n  /**\n   * Specifies that Permanent Delete is permitted.\n   */\n  public permanentDelete: boolean = false;\n\n  /**\n   * Converts the given permissions to a string. Using this method will guarantee the permissions are in an\n   * order accepted by the service.\n   *\n   * @returns A string which represents the BlobSASPermissions\n   */\n  public toString(): string {\n    const permissions: string[] = [];\n    if (this.read) {\n      permissions.push(\"r\");\n    }\n    if (this.add) {\n      permissions.push(\"a\");\n    }\n    if (this.create) {\n      permissions.push(\"c\");\n    }\n    if (this.write) {\n      permissions.push(\"w\");\n    }\n    if (this.delete) {\n      permissions.push(\"d\");\n    }\n    if (this.deleteVersion) {\n      permissions.push(\"x\");\n    }\n    if (this.tag) {\n      permissions.push(\"t\");\n    }\n    if (this.move) {\n      permissions.push(\"m\");\n    }\n    if (this.execute) {\n      permissions.push(\"e\");\n    }\n    if (this.setImmutabilityPolicy) {\n      permissions.push(\"i\");\n    }\n    if (this.permanentDelete) {\n      permissions.push(\"y\");\n    }\n    return permissions.join(\"\");\n  }\n}\n\n/**\n * A type that looks like a Blob SAS permission.\n * Used in {@link BlobSASPermissions} to parse SAS permissions from raw objects.\n */\nexport interface BlobSASPermissionsLike {\n  /**\n   * Specifies Read access granted.\n   */\n  read?: boolean;\n\n  /**\n   * Specifies Add access granted.\n   */\n  add?: boolean;\n\n  /**\n   * Specifies Create access granted.\n   */\n  create?: boolean;\n\n  /**\n   * Specifies Write access granted.\n   */\n  write?: boolean;\n\n  /**\n   * Specifies Delete access granted.\n   */\n  delete?: boolean;\n\n  /**\n   * Specifies Delete version access granted.\n   */\n  deleteVersion?: boolean;\n\n  /**\n   * Specfies Tag access granted.\n   */\n  tag?: boolean;\n\n  /**\n   * Specifies Move access granted.\n   */\n  move?: boolean;\n\n  /**\n   * Specifies Execute access granted.\n   */\n  execute?: boolean;\n\n  /**\n   * Specifies SetImmutabilityPolicy access granted.\n   */\n  setImmutabilityPolicy?: boolean;\n\n  /**\n   * Specifies that Permanent Delete is permitted.\n   */\n  permanentDelete?: boolean;\n}\n"]}