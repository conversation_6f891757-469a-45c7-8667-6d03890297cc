{"version": 3, "file": "generatedModels.js", "sourceRoot": "", "sources": ["../../../src/generatedModels.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { WithResponse } from \"./utils/utils.common\";\nimport type {\n  ListQueuesSegmentResponse,\n  MessageIdDeleteHeaders,\n  MessageIdUpdateHeaders,\n  MessagesClearHeaders,\n  QueueCreateHeaders,\n  QueueDeleteHeaders,\n  QueueGetPropertiesHeaders,\n  QueueServiceProperties,\n  QueueServiceStatistics,\n  QueueSetAccessPolicyHeaders,\n  QueueSetMetadataHeaders,\n  ServiceGetPropertiesHeaders,\n  ServiceGetStatisticsHeaders,\n  ServiceListQueuesSegmentHeaders,\n  ServiceSetPropertiesHeaders,\n} from \"./generated/src\";\n\nexport {\n  AccessPolicy,\n  CorsRule,\n  DequeuedMessageItem,\n  EnqueuedMessage,\n  GeoReplication,\n  GeoReplicationStatusType,\n  ListQueuesSegmentResponse,\n  Logging,\n  MessagesDequeueHeaders,\n  MessagesEnqueueHeaders,\n  MessageIdDeleteHeaders,\n  MessageIdUpdateHeaders,\n  MessagesClearHeaders,\n  MessagesPeekHeaders,\n  Metrics,\n  PeekedMessageItem,\n  QueueCreateHeaders,\n  QueueDeleteHeaders,\n  QueueGetAccessPolicyHeaders,\n  QueueGetPropertiesHeaders,\n  QueueItem,\n  QueueServiceProperties,\n  QueueServiceStatistics,\n  QueueSetAccessPolicyHeaders,\n  QueueSetMetadataHeaders,\n  RetentionPolicy,\n  ServiceGetPropertiesHeaders,\n  ServiceGetStatisticsHeaders,\n  ServiceListQueuesSegmentHeaders,\n  ServiceSetPropertiesHeaders,\n  SignedIdentifier as SignedIdentifierModel,\n} from \"./generated/src/models\";\n\n/** Contains response data for the getProperties operation. */\nexport type ServiceGetPropertiesResponse = WithResponse<\n  ServiceGetPropertiesHeaders & QueueServiceProperties,\n  ServiceGetPropertiesHeaders,\n  QueueServiceProperties\n>;\n\n/**\n * Contains response data for the create operation.\n */\nexport declare type QueueCreateResponse = WithResponse<QueueCreateHeaders, QueueCreateHeaders>;\n\n/** Contains response data for the listQueuesSegment operation. */\nexport type ServiceListQueuesSegmentResponse = WithResponse<\n  ServiceListQueuesSegmentHeaders & ListQueuesSegmentResponse,\n  ServiceListQueuesSegmentHeaders,\n  ListQueuesSegmentResponse\n>;\n\n/** Contains response data for the setProperties operation. */\nexport type ServiceSetPropertiesResponse = WithResponse<\n  ServiceSetPropertiesHeaders,\n  ServiceSetPropertiesHeaders\n>;\n\n/** Contains response data for the getStatistics operation. */\nexport type ServiceGetStatisticsResponse = WithResponse<\n  ServiceGetStatisticsHeaders & QueueServiceStatistics,\n  ServiceGetStatisticsHeaders,\n  QueueServiceStatistics\n>;\n\n/** Contains response data for the setMetadata operation. */\nexport type QueueSetMetadataResponse = WithResponse<\n  QueueSetMetadataHeaders,\n  QueueSetMetadataHeaders\n>;\n\n/** Contains response data for the setAccessPolicy operation. */\nexport type QueueSetAccessPolicyResponse = WithResponse<\n  QueueSetAccessPolicyHeaders,\n  QueueSetAccessPolicyHeaders\n>;\n\n/** Contains response data for the getProperties operation. */\nexport type QueueGetPropertiesResponse = WithResponse<\n  QueueGetPropertiesHeaders,\n  QueueGetPropertiesHeaders\n>;\n\n/** Contains response data for the delete operation. */\nexport type QueueDeleteResponse = WithResponse<QueueDeleteHeaders, QueueDeleteHeaders>;\n\n/** Contains response data for the clear operation. */\nexport type MessagesClearResponse = WithResponse<MessagesClearHeaders, MessagesClearHeaders>;\n\n/** Contains response data for the update operation. */\nexport type MessageIdUpdateResponse = WithResponse<MessageIdUpdateHeaders, MessageIdUpdateHeaders>;\n\n/** Contains response data for the delete operation. */\nexport type MessageIdDeleteResponse = WithResponse<MessageIdDeleteHeaders, MessageIdDeleteHeaders>;\n"]}