{"version": 3, "file": "ContainerClient.js", "sourceRoot": "", "sources": ["../../../src/ContainerClient.ts"], "names": [], "mappings": ";AAIA,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AACpE,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAE1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAErD,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AACxE,OAAO,EAAE,0BAA0B,EAAE,MAAM,0CAA0C,CAAC;AAqCtF,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAEzD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,cAAc,EACd,gBAAgB,EAChB,qCAAqC,EACrC,0CAA0C,EAC1C,UAAU,EACV,4BAA4B,EAC5B,iBAAiB,EACjB,4BAA4B,EAC5B,MAAM,EACN,oBAAoB,GACrB,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EACL,8BAA8B,EAC9B,sCAAsC,GACvC,MAAM,8BAA8B,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAMpD,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAC1F,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AA+fpD;;GAEG;AACH,MAAM,OAAO,eAAgB,SAAQ,aAAa;IAQhD;;OAEG;IACH,IAAW,aAAa;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAmDD,YACE,qBAA6B,EAC7B,mCAKgB;IAChB,mFAAmF;IACnF,gEAAgE;IAChE,OAAgC;QAEhC,IAAI,QAAsB,CAAC;QAC3B,IAAI,GAAW,CAAC;QAChB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,cAAc,CAAC,mCAAmC,CAAC,EAAE,CAAC;YACxD,oCAAoC;YACpC,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,mCAAmC,CAAC;QACjD,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,mCAAmC,YAAY,0BAA0B,CAAC;YACrF,mCAAmC,YAAY,mBAAmB;YAClE,iBAAiB,CAAC,mCAAmC,CAAC,EACtD,CAAC;YACD,mIAAmI;YACnI,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,WAAW,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC;aAAM,IACL,CAAC,mCAAmC;YACpC,OAAO,mCAAmC,KAAK,QAAQ,EACvD,CAAC;YACD,mIAAmI;YACnI,+DAA+D;YAC/D,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,IACL,mCAAmC;YACnC,OAAO,mCAAmC,KAAK,QAAQ,EACvD,CAAC;YACD,wGAAwG;YACxG,MAAM,aAAa,GAAG,mCAAmC,CAAC;YAE1D,MAAM,cAAc,GAAG,4BAA4B,CAAC,qBAAqB,CAAC,CAAC;YAC3E,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC;oBAE7E,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1E,CAAC;oBAED,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACnD,GAAG;oBACD,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,kBAAkB,CAAC,aAAa,CAAC,CAAC;wBACtE,GAAG;wBACH,cAAc,CAAC,UAAU,CAAC;gBAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;QAC7E,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;QACrD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;IAC9D,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,KAAK,CAAC,MAAM,CAAC,UAAkC,EAAE;QACtD,OAAO,aAAa,CAAC,QAAQ,CAAC,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACxF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,CACnD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,iBAAiB,CAC5B,UAAkC,EAAE;QAEpC,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBAC9C,qCACE,SAAS,EAAE,IAAI,IACZ,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,IACxB;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,wBAAwB,EAAE,CAAC;oBACtD,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,CAAC;gBACV,CAAC;YACH,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,MAAM,CAAC,UAAkC,EAAE;QACtD,OAAO,aAAa,CAAC,QAAQ,CAAC,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACxF,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,aAAa,CAAC;oBACvB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;iBAC9C,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACzB,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACI,aAAa,CAAC,QAAgB;QACnC,OAAO,IAAI,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxF,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,QAAgB;QACzC,OAAO,IAAI,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9F,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,kBAAkB,CAAC,QAAgB;QACxC,OAAO,IAAI,eAAe,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC7F,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,QAAgB;QACvC,OAAO,IAAI,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5F,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,aAAa,CACxB,UAAyC,EAAE;QAE3C,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,+BAA+B,EAC/B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,aAAa,+BACvC,WAAW,EAAE,OAAO,CAAC,WAAW,IAC7B,OAAO,CAAC,UAAU,KACrB,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,MAAM,CACjB,UAAwC,EAAE;QAE1C,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAAC,wBAAwB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACxF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACjC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,EAAE,OAAO,CAAC,UAAU;gBAC5C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,cAAc,CACzB,UAAwC,EAAE;QAE1C,OAAO,aAAa,CAAC,QAAQ,CAC3B,gCAAgC,EAChC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBAC9C,qCACE,SAAS,EAAE,IAAI,IACZ,GAAG,KACN,SAAS,EAAE,GAAG,CAAC,SAAS,IACxB;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,mBAAmB,EAAE,CAAC;oBACjD,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,WAAW,CACtB,QAAmB,EACnB,UAAuC,EAAE;QAEzC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;YACzC,MAAM,IAAI,UAAU,CAClB,mGAAmG,CACpG,CAAC;QACJ,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,QAAQ;gBACR,wBAAwB,EAAE,OAAO,CAAC,UAAU;gBAC5C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,eAAe,CAC1B,UAA2C,EAAE;QAE7C,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;YACxB,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBAC1C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,MAAM,GAAG,GAAqC;gBAC5C,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;gBAC3C,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,YAAY,EAAE,QAAQ,CAAC,YAAY;gBACnC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,iBAAiB,EAAE,EAAE;gBACrB,OAAO,EAAE,QAAQ,CAAC,OAAO;aAC1B,CAAC;YAEF,KAAK,MAAM,UAAU,IAAI,QAAQ,EAAE,CAAC;gBAClC,IAAI,YAAY,GAAQ,SAAS,CAAC;gBAClC,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;oBAC5B,YAAY,GAAG;wBACb,WAAW,EAAE,UAAU,CAAC,YAAY,CAAC,WAAW;qBACjD,CAAC;oBAEF,IAAI,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;wBACtC,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBACvE,CAAC;oBAED,IAAI,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;wBACrC,YAAY,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;gBAED,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC;oBACzB,YAAY;oBACZ,EAAE,EAAE,UAAU,CAAC,EAAE;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,KAAK,CAAC,eAAe,CAC1B,MAAyB,EACzB,YAAiC,EACjC,UAA2C,EAAE;QAE7C,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC;QAC9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,GAAG,GAA4B,EAAE,CAAC;YACxC,KAAK,MAAM,UAAU,IAAI,YAAY,IAAI,EAAE,EAAE,CAAC;gBAC5C,GAAG,CAAC,IAAI,CAAC;oBACP,YAAY,EAAE;wBACZ,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC,SAAS;4BAC1C,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC;4BACzD,CAAC,CAAC,EAAE;wBACN,WAAW,EAAE,UAAU,CAAC,YAAY,CAAC,WAAW;wBAChD,QAAQ,EAAE,UAAU,CAAC,YAAY,CAAC,QAAQ;4BACxC,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;4BACxD,CAAC,CAAC,EAAE;qBACP;oBACD,EAAE,EAAE,UAAU,CAAC,EAAE;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC;gBAC1C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,MAAM;gBACN,YAAY,EAAE,GAAG;gBACjB,qBAAqB,EAAE,OAAO,CAAC,UAAU;gBACzC,wBAAwB,EAAE,OAAO,CAAC,UAAU;gBAC5C,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,kBAAkB,CAAC,cAAuB;QAC/C,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;OAqBG;IACI,KAAK,CAAC,eAAe,CAC1B,QAAgB,EAChB,IAAqB,EACrB,aAAqB,EACrB,UAAkC,EAAE;QAEpC,OAAO,aAAa,CAAC,QAAQ,CAC3B,iCAAiC,EACjC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAC1D,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC,IAAI,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;YACnF,OAAO;gBACL,eAAe;gBACf,QAAQ;aACT,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,UAAU,CACrB,QAAgB,EAChB,UAAsC,EAAE;QAExC,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC5F,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,UAAU,GAAG,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACzD,CAAC;YACD,OAAO,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IACK,KAAK,CAAC,mBAAmB,CAC/B,MAAe,EACf,UAA4C,EAAE;QAE9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,qCAAqC,EACrC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,+BAC7C,MAAM,IACH,OAAO,KACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;YAEF,MAAM,eAAe,mCAChB,QAAQ,KACX,SAAS,kCACJ,QAAQ,CAAC,SAAS,KACrB,UAAU,EAAE,qCAAqC,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,KAElF,OAAO,kCACF,QAAQ,CAAC,OAAO,KACnB,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE;wBAC7D,MAAM,QAAQ,mCACT,gBAAgB,KACnB,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAC7C,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EACvC,iCAAiC,EAAE,4BAA4B,CAC7D,gBAAgB,CAAC,yBAAyB,CAC3C,GACF,CAAC;wBACF,OAAO,QAAQ,CAAC;oBAClB,CAAC,CAAC,MAEL,CAAC;YACF,OAAO,eAAe,CAAC;QACzB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACK,KAAK,CAAC,wBAAwB,CACpC,SAAiB,EACjB,MAAe,EACf,UAA4C,EAAE;QAE9C,OAAO,aAAa,CAAC,QAAQ,CAC3B,0CAA0C,EAC1C,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,SAAS,gCAC5D,MAAM,IACH,OAAO,KACV,cAAc,EAAE,cAAc,CAAC,cAAc,IAC7C,CACH,CAAC;YAEF,MAAM,eAAe,mCAChB,QAAQ,KACX,SAAS,kCACJ,QAAQ,CAAC,SAAS,KACrB,UAAU,EAAE,0CAA0C,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,KAEvF,OAAO,kCACF,QAAQ,CAAC,OAAO,KACnB,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE;wBAC7D,MAAM,QAAQ,mCACT,gBAAgB,KACnB,IAAI,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAC7C,IAAI,EAAE,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EACvC,iCAAiC,EAAE,4BAA4B,CAC7D,gBAAgB,CAAC,yBAAyB,CAC3C,GACF,CAAC;wBACF,OAAO,QAAQ,CAAC;oBAClB,CAAC,CAAC,EACF,YAAY,EAAE,MAAA,QAAQ,CAAC,OAAO,CAAC,YAAY,0CAAE,GAAG,CAAC,CAAC,kBAAkB,EAAE,EAAE;wBACtE,MAAM,UAAU,mCACX,kBAAkB,KACrB,IAAI,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAChD,CAAC;wBACF,OAAO,UAAU,CAAC;oBACpB,CAAC,CAAC,MAEL,CAAC;YACF,OAAO,eAAe,CAAC;QACzB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACY,YAAY;0EACzB,MAAe,EACf,UAA4C,EAAE;YAE9C,IAAI,4BAA4B,CAAC;YACjC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,4BAA4B,GAAG,cAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,CAAC;oBAC/E,MAAM,GAAG,4BAA4B,CAAC,iBAAiB,CAAC;oBACxD,oBAAM,cAAM,4BAA4B,CAAA,CAAA,CAAC;gBAC3C,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;OAIG;IACY,SAAS;uEACtB,UAA4C,EAAE;;YAE9C,IAAI,MAA0B,CAAC;;gBAC/B,KAAiD,eAAA,KAAA,cAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,IAAA,+DAAE,CAAC;oBAArC,cAAkC;oBAAlC,WAAkC;oBAAxE,MAAM,4BAA4B,KAAA,CAAA;oBAC3C,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,4BAA4B,CAAC,OAAO,CAAC,SAAS,CAAA,CAAA,CAAA,CAAC;gBACxD,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAqEG;IACI,aAAa,CAClB,UAAqC,EAAE;QAEvC,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,OAAO,CAAC,0BAA0B,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,OAAO,CAAC,yBAAyB,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAM,cAAc,mCACf,OAAO,GACP,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CACpD,CAAC;QAEF,8CAA8C;QAC9C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC5C,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,kBACjD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,cAAc,EACjB,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;OAYG;IACY,qBAAqB;mFAClC,SAAiB,EACjB,MAAe,EACf,UAA4C,EAAE;YAE9C,IAAI,iCAAiC,CAAC;YACtC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,iCAAiC,GAAG,cAAM,IAAI,CAAC,wBAAwB,CACrE,SAAS,EACT,MAAM,EACN,OAAO,CACR,CAAA,CAAC;oBACF,MAAM,GAAG,iCAAiC,CAAC,iBAAiB,CAAC;oBAC7D,oBAAM,cAAM,iCAAiC,CAAA,CAAA,CAAC;gBAChD,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACY,oBAAoB;kFACjC,SAAiB,EACjB,UAA4C,EAAE;;YAE9C,IAAI,MAA0B,CAAC;;gBAC/B,KAAsD,eAAA,KAAA,cAAA,IAAI,CAAC,qBAAqB,CAC9E,SAAS,EACT,MAAM,EACN,OAAO,CACR,CAAA,IAAA,+DAAE,CAAC;oBAJkD,cAIrD;oBAJqD,WAIrD;oBAJU,MAAM,iCAAiC,KAAA,CAAA;oBAKhD,MAAM,OAAO,GAAG,iCAAiC,CAAC,OAAO,CAAC;oBAC1D,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;wBACzB,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;4BAC1C,oCACE,IAAI,EAAE,QAAQ,IACX,MAAM,EACV,CAAC;wBACJ,CAAC;oBACH,CAAC;oBACD,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;wBACrC,oCAAQ,IAAI,EAAE,MAAM,IAAK,IAAI,EAAE,CAAC;oBAClC,CAAC;gBACH,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA6EG;IACI,oBAAoB,CACzB,SAAiB,EACjB,UAAqC,EAAE;QAKvC,IAAI,SAAS,KAAK,EAAE,EAAE,CAAC;YACrB,MAAM,IAAI,UAAU,CAAC,iDAAiD,CAAC,CAAC;QAC1E,CAAC;QAED,MAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QACD,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACnC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;QACD,IAAI,OAAO,CAAC,0BAA0B,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,OAAO,CAAC,yBAAyB,EAAE,CAAC;YACtC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;QACD,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAM,cAAc,mCACf,OAAO,GACP,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CACpD,CAAC;QACF,gEAAgE;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAClE,OAAO;YACL;;eAEG;YACH,KAAK,CAAC,IAAI;gBACR,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,QAAQ,CAAC,iBAAiB,kBACrE,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,cAAc,EACjB,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACK,KAAK,CAAC,sBAAsB,CAClC,sBAA8B,EAC9B,MAAe,EACf,UAAkD,EAAE;QAEpD,OAAO,aAAa,CAAC,QAAQ,CAC3B,wCAAwC,EACxC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,KAAK,EAAE,sBAAsB;gBAC7B,MAAM;gBACN,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,MAAM,eAAe,mCAChB,QAAQ,KACX,SAAS,EAAE,QAAQ,CAAC,SAAS,EAC7B,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;;oBACjC,IAAI,QAAQ,GAAG,EAAE,CAAC;oBAClB,IAAI,CAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,UAAU,CAAC,MAAM,MAAK,CAAC,EAAE,CAAC;wBACvC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC3C,CAAC;oBACD,uCAAY,IAAI,KAAE,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,IAAG;gBACxD,CAAC,CAAC,GACH,CAAC;YACF,OAAO,eAAe,CAAC;QACzB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACY,uBAAuB;qFACpC,sBAA8B,EAC9B,MAAe,EACf,UAAkD,EAAE;YAEpD,IAAI,QAAQ,CAAC;YACb,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACrC,GAAG,CAAC;oBACF,QAAQ,GAAG,cAAM,IAAI,CAAC,sBAAsB,CAAC,sBAAsB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA,CAAC;oBACtF,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;oBACtC,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC;oBACpC,oBAAM,QAAQ,CAAA,CAAC;gBACjB,CAAC,QAAQ,MAAM,EAAE;YACnB,CAAC;QACH,CAAC;KAAA;IAED;;;;;;;;OAQG;IACY,oBAAoB;kFACjC,sBAA8B,EAC9B,UAAkD,EAAE;;YAEpD,IAAI,MAA0B,CAAC;;gBAC/B,KAA4B,eAAA,KAAA,cAAA,IAAI,CAAC,uBAAuB,CACtD,sBAAsB,EACtB,MAAM,EACN,OAAO,CACR,CAAA,IAAA,+DAAE,CAAC;oBAJwB,cAI3B;oBAJ2B,WAI3B;oBAJU,MAAM,OAAO,KAAA,CAAA;oBAKtB,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,OAAO,CAAC,KAAK,CAAA,CAAA,CAAA,CAAC;gBACvB,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4EG;IACI,eAAe,CACpB,sBAA8B,EAC9B,UAA0C,EAAE;QAE5C,8CAA8C;QAC9C,MAAM,kBAAkB,qBACnB,OAAO,CACX,CAAC;QAEF,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAC;QACnF,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,QAAQ,CAAC,iBAAiB,kBACpF,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,kBAAkB,EACrB,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,cAAc,CACzB,UAA0C,EAAE;QAE5C,OAAO,aAAa,CAAC,QAAQ,CAC3B,gCAAgC,EAChC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;gBACzC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,uBAAuB;QAC7B,IAAI,aAAa,CAAC;QAClB,IAAI,CAAC;YACH,mCAAmC;YACnC,mEAAmE;YACnE,yDAAyD;YACzD,+FAA+F;YAC/F,wDAAwD;YAExD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;gBAChD,2DAA2D;gBAC3D,4CAA4C;gBAC5C,+BAA+B;gBAC/B,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;iBAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxC,8FAA8F;gBAC9F,wHAAwH;gBACxH,gDAAgD;gBAChD,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACN,4CAA4C;gBAC5C,+BAA+B;gBAC/B,aAAa,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;YAED,mGAAmG;YACnG,aAAa,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAC;YAElD,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,OAAO,aAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACI,cAAc,CAAC,OAAuC;QAC3D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,UAAU,CAClB,uFAAuF,CACxF,CAAC;YACJ,CAAC;YAED,MAAM,GAAG,GAAG,8BAA8B,iBAEtC,aAAa,EAAE,IAAI,CAAC,cAAc,IAC/B,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,QAAQ,EAAE,CAAC;YAEb,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACH,gEAAgE;IACzD,uBAAuB,CAAC,OAAuC;QACpE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,IAAI,UAAU,CAClB,uFAAuF,CACxF,CAAC;QACJ,CAAC;QAED,OAAO,sCAAsC,iBAEzC,aAAa,EAAE,IAAI,CAAC,cAAc,IAC/B,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,YAAY,CAAC;IACjB,CAAC;IAED;;;;;;;;;OASG;IACI,4BAA4B,CACjC,OAAuC,EACvC,iBAAoC;QAEpC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,MAAM,GAAG,GAAG,8BAA8B,iBAEtC,aAAa,EAAE,IAAI,CAAC,cAAc,IAC/B,OAAO,GAEZ,iBAAiB,EACjB,IAAI,CAAC,WAAW,CACjB,CAAC,QAAQ,EAAE,CAAC;YAEb,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;OASG;IAEI,qCAAqC,CAC1C,OAAuC,EACvC,iBAAoC;QAEpC,OAAO,sCAAsC,iBAEzC,aAAa,EAAE,IAAI,CAAC,cAAc,IAC/B,OAAO,GAEZ,iBAAiB,EACjB,IAAI,CAAC,WAAW,CACjB,CAAC,YAAY,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACI,kBAAkB;QACvB,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { RequestBodyType as HttpRequestBody } from \"@azure/core-rest-pipeline\";\nimport { getDefaultProxySettings } from \"@azure/core-rest-pipeline\";\nimport { isNode } from \"@azure/core-util\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\nimport type { PagedAsyncIterableIterator, PageSettings } from \"@azure/core-paging\";\nimport { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport type { Container } from \"./generated/src/operationsInterfaces\";\nimport type {\n  BlobDeleteResponse,\n  BlobPrefix,\n  BlobProperties,\n  BlockBlobUploadResponse,\n  ContainerCreateResponse,\n  ContainerDeleteResponse,\n  ContainerEncryptionScope,\n  ContainerFilterBlobsHeaders,\n  ContainerFilterBlobsResponse,\n  ContainerGetAccessPolicyHeaders,\n  ContainerGetAccessPolicyResponseModel,\n  ContainerGetAccountInfoResponse,\n  ContainerGetPropertiesResponse,\n  ContainerListBlobFlatSegmentHeaders,\n  ContainerListBlobHierarchySegmentHeaders,\n  ContainerSetAccessPolicyResponse,\n  ContainerSetMetadataResponse,\n  FilterBlobItem,\n  FilterBlobSegment,\n  FilterBlobSegmentModel,\n  LeaseAccessConditions,\n  ListBlobsFlatSegmentResponseModel,\n  ListBlobsHierarchySegmentResponseModel,\n  PublicAccessType,\n  SignedIdentifierModel,\n} from \"./generatedModels\";\nimport type {\n  Metadata,\n  ObjectReplicationPolicy,\n  Tags,\n  ContainerRequestConditions,\n  ModifiedAccessConditions,\n} from \"./models\";\nimport type { PipelineLike, StoragePipelineOptions } from \"./Pipeline\";\nimport { newPipeline, isPipelineLike } from \"./Pipeline\";\nimport type { CommonOptions } from \"./StorageClient\";\nimport { StorageClient } from \"./StorageClient\";\nimport { tracingClient } from \"./utils/tracing\";\nimport type { WithResponse } from \"./utils/utils.common\";\nimport {\n  appendToURLPath,\n  appendToURLQuery,\n  assertResponse,\n  BlobNameToString,\n  ConvertInternalResponseOfListBlobFlat,\n  ConvertInternalResponseOfListBlobHierarchy,\n  EscapePath,\n  extractConnectionStringParts,\n  isIpEndpointStyle,\n  parseObjectReplicationRecord,\n  toTags,\n  truncatedISO8061Date,\n} from \"./utils/utils.common\";\nimport type { ContainerSASPermissions } from \"./sas/ContainerSASPermissions\";\nimport {\n  generateBlobSASQueryParameters,\n  generateBlobSASQueryParametersInternal,\n} from \"./sas/BlobSASSignatureValues\";\nimport { BlobLeaseClient } from \"./BlobLeaseClient\";\nimport type {\n  BlobDeleteOptions,\n  BlockBlobUploadOptions,\n  CommonGenerateSasUrlOptions,\n} from \"./Clients\";\nimport { AppendBlobClient, BlobClient, BlockBlobClient, PageBlobClient } from \"./Clients\";\nimport { BlobBatchClient } from \"./BlobBatchClient\";\nimport type {\n  ContainerCreateHeaders,\n  ListBlobsIncludeItem,\n  ContainerGetPropertiesHeaders,\n  ContainerDeleteHeaders,\n  ContainerSetMetadataHeaders,\n  ContainerSetAccessPolicyHeaders,\n  ListBlobsFlatSegmentResponse as ListBlobsFlatSegmentResponseInternal,\n  ListBlobsHierarchySegmentResponse as ListBlobsHierarchySegmentResponseInternal,\n  ContainerListBlobHierarchySegmentResponse as ContainerListBlobHierarchySegmentResponseModel,\n  ContainerGetAccountInfoHeaders,\n} from \"./generated/src\";\nimport type { UserDelegationKey } from \"./BlobServiceClient\";\n\n/**\n * Options to configure {@link ContainerClient.create} operation.\n */\nexport interface ContainerCreateOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * A collection of key-value string pair to associate with the container.\n   */\n  metadata?: Metadata;\n  /**\n   * Specifies whether data in the container may be accessed publicly and the level of access. Possible values include:\n   * - `container`: Specifies full public read access for container and blob data. Clients can enumerate blobs within the container via anonymous request, but cannot enumerate containers within the storage account.\n   * - `blob`: Specifies public read access for blobs. Blob data within this container can be read via anonymous request, but container data is not available. Clients cannot enumerate blobs within the container via anonymous request.\n   */\n  access?: PublicAccessType;\n  /**\n   * Container encryption scope info.\n   */\n  containerEncryptionScope?: ContainerEncryptionScope;\n}\n\n/**\n * Options to configure {@link ContainerClient.getProperties} operation.\n */\nexport interface ContainerGetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions;\n}\n\n/**\n * Options to configure {@link ContainerClient.delete} operation.\n */\nexport interface ContainerDeleteMethodOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when deleting the container.\n   */\n  conditions?: ContainerRequestConditions;\n}\n\n/**\n * Options to configure {@link ContainerClient.exists} operation.\n */\nexport interface ContainerExistsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link ContainerClient.setMetadata} operation.\n */\nexport interface ContainerSetMetadataOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: ContainerRequestConditions;\n}\n\n/**\n * Options to configure {@link ContainerClient.getAccessPolicy} operation.\n */\nexport interface ContainerGetAccessPolicyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * If specified, contains the lease id that must be matched and lease with this id\n   * must be active in order for the operation to succeed.\n   */\n  conditions?: LeaseAccessConditions;\n}\n\n/**\n * Signed identifier.\n */\nexport interface SignedIdentifier {\n  /**\n   * a unique id\n   */\n  id: string;\n  /**\n   * Access Policy\n   */\n  accessPolicy: {\n    /**\n     * Optional. The date-time the policy is active\n     */\n    startsOn?: Date;\n    /**\n     * Optional. The date-time the policy expires\n     */\n    expiresOn?: Date;\n    /**\n     * The permissions for the acl policy\n     * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-container-acl\n     */\n    permissions?: string;\n  };\n}\n\n/**\n * Contains response data for the {@link ContainerClient.getAccessPolicy} operation.\n */\nexport declare type ContainerGetAccessPolicyResponse = WithResponse<\n  {\n    signedIdentifiers: SignedIdentifier[];\n  } & ContainerGetAccessPolicyHeaders,\n  ContainerGetAccessPolicyHeaders,\n  SignedIdentifierModel\n>;\n\n/**\n * Options to configure {@link ContainerClient.setAccessPolicy} operation.\n */\nexport interface ContainerSetAccessPolicyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when setting the access policy.\n   */\n  conditions?: ContainerRequestConditions;\n}\n\n/**\n * Options to configure Container - Acquire Lease operation.\n */\nexport interface ContainerAcquireLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when acquiring the lease.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Container - Release Lease operation.\n */\nexport interface ContainerReleaseLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when releasing the lease.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Container - Renew Lease operation.\n */\nexport interface ContainerRenewLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when renewing the lease.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Container - Break Lease operation.\n */\nexport interface ContainerBreakLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when breaking the lease.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure Container - Change Lease operation.\n */\nexport interface ContainerChangeLeaseOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when changing the lease.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * Options to configure the {@link ContainerClient.deleteBlob} operation.\n */\nexport interface ContainerDeleteBlobOptions extends BlobDeleteOptions {\n  /**\n   * An opaque DateTime value that, when present, specifies the version\n   * of the blob to delete. It's for service version 2019-10-10 and newer.\n   */\n  versionId?: string;\n}\n\n/**\n * Options to configure Container - List Segment operations.\n *\n * See:\n * - {@link ContainerClient.listSegments}\n * - {@link ContainerClient.listBlobFlatSegment}\n * - {@link ContainerClient.listBlobHierarchySegment}\n * - {@link ContainerClient.listHierarchySegments}\n * - {@link ContainerClient.listItemsByHierarchy}\n */\ninterface ContainerListBlobsSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Filters the results to return only containers\n   * whose name begins with the specified prefix.\n   */\n  prefix?: string;\n  /**\n   * Specifies the maximum number of containers\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n  /**\n   * Include this parameter to\n   * specify one or more datasets to include in the response.\n   */\n  include?: ListBlobsIncludeItem[];\n}\n\n/**\n * An interface representing BlobHierarchyListSegment.\n */\nexport interface BlobHierarchyListSegment {\n  blobPrefixes?: BlobPrefix[];\n  blobItems: BlobItem[];\n}\n\n/**\n * An enumeration of blobs\n */\nexport interface ListBlobsHierarchySegmentResponse {\n  serviceEndpoint: string;\n  containerName: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  delimiter?: string;\n  segment: BlobHierarchyListSegment;\n  continuationToken?: string;\n}\n\n/**\n * Contains response data for the listBlobHierarchySegment operation.\n */\nexport type ContainerListBlobHierarchySegmentResponse = WithResponse<\n  ListBlobsHierarchySegmentResponse & ContainerListBlobHierarchySegmentHeaders,\n  ContainerListBlobHierarchySegmentHeaders,\n  ListBlobsHierarchySegmentResponseModel\n>;\n\n/**\n * An Azure Storage blob\n */\nexport interface BlobItem {\n  name: string;\n  deleted: boolean;\n  snapshot: string;\n  versionId?: string;\n  isCurrentVersion?: boolean;\n  properties: BlobProperties;\n  metadata?: { [propertyName: string]: string };\n  tags?: Tags;\n  objectReplicationSourceProperties?: ObjectReplicationPolicy[];\n  hasVersionsOnly?: boolean;\n}\n\n/**\n * An interface representing BlobFlatListSegment.\n */\nexport interface BlobFlatListSegment {\n  blobItems: BlobItem[];\n}\n\n/**\n * An enumeration of blobs\n */\nexport interface ListBlobsFlatSegmentResponse {\n  serviceEndpoint: string;\n  containerName: string;\n  prefix?: string;\n  marker?: string;\n  maxPageSize?: number;\n  segment: BlobFlatListSegment;\n  continuationToken?: string;\n}\n\n/**\n * Contains response data for the listBlobFlatSegment operation.\n */\nexport type ContainerListBlobFlatSegmentResponse = WithResponse<\n  ListBlobsFlatSegmentResponse & ContainerListBlobFlatSegmentHeaders,\n  ContainerListBlobFlatSegmentHeaders,\n  ListBlobsFlatSegmentResponseModel\n>;\n\n/**\n * Options to configure Container - List Blobs operations.\n *\n * See:\n * - {@link ContainerClient.listBlobsFlat}\n * - {@link ContainerClient.listBlobsByHierarchy}\n */\nexport interface ContainerListBlobsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Filters the results to return only containers\n   * whose name begins with the specified prefix.\n   */\n  prefix?: string;\n\n  /**\n   * Specifies whether metadata related to any current or previous Copy Blob operation should be included in the response.\n   */\n  includeCopy?: boolean;\n  /**\n   * Specifies whether soft deleted blobs should be included in the response.\n   */\n  includeDeleted?: boolean;\n  /**\n   * Specifies whether blob metadata be returned in the response.\n   */\n  includeMetadata?: boolean;\n  /**\n   * Specifies whether snapshots should be included in the enumeration. Snapshots are listed from oldest to newest in the response.\n   */\n  includeSnapshots?: boolean;\n  /**\n   * Specifies whether versions should be included in the enumeration. Versions are listed from oldest to newest in the response.\n   */\n  includeVersions?: boolean;\n  /**\n   * Specifies whether blobs for which blocks have been uploaded, but which have not been committed using Put Block List, be included in the response.\n   */\n  includeUncommitedBlobs?: boolean;\n  /**\n   * Specifies whether blob tags be returned in the response.\n   */\n  includeTags?: boolean;\n  /**\n   * Specifies whether deleted blob with versions be returned in the response.\n   */\n  includeDeletedWithVersions?: boolean;\n  /**\n   * Specifies whether blob immutability policy be returned in the response.\n   */\n  includeImmutabilityPolicy?: boolean;\n  /**\n   * Specifies whether blob legal hold be returned in the response.\n   */\n  includeLegalHold?: boolean;\n}\n\n/**\n * Contains response data for the {@link ContainerClient.createIfNotExists} operation.\n */\nexport interface ContainerCreateIfNotExistsResponse extends ContainerCreateResponse {\n  /**\n   * Indicate whether the container is successfully created. Is false when the container is not changed as it already exists.\n   */\n  succeeded: boolean;\n}\n\n/**\n * Contains response data for the {@link ContainerClient.deleteIfExists} operation.\n */\nexport interface ContainerDeleteIfExistsResponse extends ContainerDeleteResponse {\n  /**\n   * Indicate whether the container is successfully deleted. Is false if the container does not exist in the first place.\n   */\n  succeeded: boolean;\n}\n\n/**\n * Options to configure {@link ContainerClient.generateSasUrl} operation.\n */\nexport interface ContainerGenerateSasUrlOptions extends CommonGenerateSasUrlOptions {\n  /**\n   * Optional only when identifier is provided. Specifies the list of permissions to be associated with the SAS.\n   */\n  permissions?: ContainerSASPermissions;\n}\n\n/**\n * Options to configure the {@link ContainerClient.findBlobsByTagsSegment} operation.\n */\ninterface ContainerFindBlobsByTagsSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Specifies the maximum number of blobs\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n}\n\n/**\n * Options to configure the {@link BlobServiceClient.findBlobsByTags} operation.\n */\nexport interface ContainerFindBlobByTagsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * The response of {@link BlobServiceClient.findBlobsByTags} operation.\n */\nexport type ContainerFindBlobsByTagsSegmentResponse = WithResponse<\n  FilterBlobSegment & ContainerFilterBlobsHeaders,\n  ContainerFilterBlobsHeaders,\n  FilterBlobSegmentModel\n>;\n\n/**\n * Options to configure the {@link ContainerClient.getAccountInfo} operation.\n */\nexport interface ContainerGetAccountInfoOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * A ContainerClient represents a URL to the Azure Storage container allowing you to manipulate its blobs.\n */\nexport class ContainerClient extends StorageClient {\n  /**\n   * containerContext provided by protocol layer.\n   */\n  private containerContext: Container;\n\n  private _containerName: string;\n\n  /**\n   * The name of the container.\n   */\n  public get containerName(): string {\n    return this._containerName;\n  }\n  /**\n   *\n   * Creates an instance of ContainerClient.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param containerName - Container name.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n  /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n  constructor(connectionString: string, containerName: string, options?: StoragePipelineOptions);\n  /**\n   * Creates an instance of ContainerClient.\n   * This method accepts an URL pointing to a container.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage container, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Optional. Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of ContainerClient.\n   * This method accepts an URL pointing to a container.\n   * Encoded URL string will NOT be escaped twice, only special characters in URL path will be escaped.\n   * If a blob name includes ? or %, blob name must be encoded in the URL.\n   *\n   * @param url - A URL string pointing to Azure Storage container, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.blob.core.windows.net/mycontainer?sasString\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: PipelineLike);\n  constructor(\n    urlOrConnectionString: string,\n    credentialOrPipelineOrContainerName?:\n      | string\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | PipelineLike,\n    // Legacy, no fix for eslint error without breaking. Disable it for this interface.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n    options?: StoragePipelineOptions,\n  ) {\n    let pipeline: PipelineLike;\n    let url: string;\n    options = options || {};\n    if (isPipelineLike(credentialOrPipelineOrContainerName)) {\n      // (url: string, pipeline: Pipeline)\n      url = urlOrConnectionString;\n      pipeline = credentialOrPipelineOrContainerName;\n    } else if (\n      (isNode && credentialOrPipelineOrContainerName instanceof StorageSharedKeyCredential) ||\n      credentialOrPipelineOrContainerName instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipelineOrContainerName)\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      url = urlOrConnectionString;\n      pipeline = newPipeline(credentialOrPipelineOrContainerName, options);\n    } else if (\n      !credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName !== \"string\"\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      // The second parameter is undefined. Use anonymous credential.\n      url = urlOrConnectionString;\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else if (\n      credentialOrPipelineOrContainerName &&\n      typeof credentialOrPipelineOrContainerName === \"string\"\n    ) {\n      // (connectionString: string, containerName: string, blobName: string, options?: StoragePipelineOptions)\n      const containerName = credentialOrPipelineOrContainerName;\n\n      const extractedCreds = extractConnectionStringParts(urlOrConnectionString);\n      if (extractedCreds.kind === \"AccountConnString\") {\n        if (isNode) {\n          const sharedKeyCredential = new StorageSharedKeyCredential(\n            extractedCreds.accountName!,\n            extractedCreds.accountKey,\n          );\n          url = appendToURLPath(extractedCreds.url, encodeURIComponent(containerName));\n\n          if (!options.proxyOptions) {\n            options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n          }\n\n          pipeline = newPipeline(sharedKeyCredential, options);\n        } else {\n          throw new Error(\"Account connection string is only supported in Node.js environment\");\n        }\n      } else if (extractedCreds.kind === \"SASConnString\") {\n        url =\n          appendToURLPath(extractedCreds.url, encodeURIComponent(containerName)) +\n          \"?\" +\n          extractedCreds.accountSas;\n        pipeline = newPipeline(new AnonymousCredential(), options);\n      } else {\n        throw new Error(\n          \"Connection string must be either an Account connection string or a SAS connection string\",\n        );\n      }\n    } else {\n      throw new Error(\"Expecting non-empty strings for containerName parameter\");\n    }\n    super(url, pipeline);\n    this._containerName = this.getContainerNameFromUrl();\n    this.containerContext = this.storageClientContext.container;\n  }\n\n  /**\n   * Creates a new container under the specified account. If the container with\n   * the same name already exists, the operation fails.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-container\n   * Naming rules: @see https://learn.microsoft.com/rest/api/storageservices/naming-and-referencing-containers--blobs--and-metadata\n   *\n   * @param options - Options to Container Create operation.\n   *\n   *\n   * Example usage:\n   *\n   * ```js\n   * const containerClient = blobServiceClient.getContainerClient(\"<container name>\");\n   * const createContainerResponse = await containerClient.create();\n   * console.log(\"Container was created successfully\", createContainerResponse.requestId);\n   * ```\n   */\n  public async create(options: ContainerCreateOptions = {}): Promise<ContainerCreateResponse> {\n    return tracingClient.withSpan(\"ContainerClient-create\", options, async (updatedOptions) => {\n      return assertResponse<ContainerCreateHeaders, ContainerCreateHeaders>(\n        await this.containerContext.create(updatedOptions),\n      );\n    });\n  }\n\n  /**\n   * Creates a new container under the specified account. If the container with\n   * the same name already exists, it is not changed.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-container\n   * Naming rules: @see https://learn.microsoft.com/rest/api/storageservices/naming-and-referencing-containers--blobs--and-metadata\n   *\n   * @param options -\n   */\n  public async createIfNotExists(\n    options: ContainerCreateOptions = {},\n  ): Promise<ContainerCreateIfNotExistsResponse> {\n    return tracingClient.withSpan(\n      \"ContainerClient-createIfNotExists\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const res = await this.create(updatedOptions);\n          return {\n            succeeded: true,\n            ...res,\n            _response: res._response, // _response is made non-enumerable\n          };\n        } catch (e: any) {\n          if (e.details?.errorCode === \"ContainerAlreadyExists\") {\n            return {\n              succeeded: false,\n              ...e.response?.parsedHeaders,\n              _response: e.response,\n            };\n          } else {\n            throw e;\n          }\n        }\n      },\n    );\n  }\n\n  /**\n   * Returns true if the Azure container resource represented by this client exists; false otherwise.\n   *\n   * NOTE: use this function with care since an existing container might be deleted by other clients or\n   * applications. Vice versa new containers with the same name might be added by other clients or\n   * applications after this function completes.\n   *\n   * @param options -\n   */\n  public async exists(options: ContainerExistsOptions = {}): Promise<boolean> {\n    return tracingClient.withSpan(\"ContainerClient-exists\", options, async (updatedOptions) => {\n      try {\n        await this.getProperties({\n          abortSignal: options.abortSignal,\n          tracingOptions: updatedOptions.tracingOptions,\n        });\n        return true;\n      } catch (e: any) {\n        if (e.statusCode === 404) {\n          return false;\n        }\n        throw e;\n      }\n    });\n  }\n\n  /**\n   * Creates a {@link BlobClient}\n   *\n   * @param blobName - A blob name\n   * @returns A new BlobClient object for the given blob name.\n   */\n  public getBlobClient(blobName: string): BlobClient {\n    return new BlobClient(appendToURLPath(this.url, EscapePath(blobName)), this.pipeline);\n  }\n\n  /**\n   * Creates an {@link AppendBlobClient}\n   *\n   * @param blobName - An append blob name\n   */\n  public getAppendBlobClient(blobName: string): AppendBlobClient {\n    return new AppendBlobClient(appendToURLPath(this.url, EscapePath(blobName)), this.pipeline);\n  }\n\n  /**\n   * Creates a {@link BlockBlobClient}\n   *\n   * @param blobName - A block blob name\n   *\n   *\n   * Example usage:\n   *\n   * ```js\n   * const content = \"Hello world!\";\n   *\n   * const blockBlobClient = containerClient.getBlockBlobClient(\"<blob name>\");\n   * const uploadBlobResponse = await blockBlobClient.upload(content, content.length);\n   * ```\n   */\n  public getBlockBlobClient(blobName: string): BlockBlobClient {\n    return new BlockBlobClient(appendToURLPath(this.url, EscapePath(blobName)), this.pipeline);\n  }\n\n  /**\n   * Creates a {@link PageBlobClient}\n   *\n   * @param blobName - A page blob name\n   */\n  public getPageBlobClient(blobName: string): PageBlobClient {\n    return new PageBlobClient(appendToURLPath(this.url, EscapePath(blobName)), this.pipeline);\n  }\n\n  /**\n   * Returns all user-defined metadata and system properties for the specified\n   * container. The data returned does not include the container's list of blobs.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-container-properties\n   *\n   * WARNING: The `metadata` object returned in the response will have its keys in lowercase, even if\n   * they originally contained uppercase characters. This differs from the metadata keys returned by\n   * the `listContainers` method of {@link BlobServiceClient} using the `includeMetadata` option, which\n   * will retain their original casing.\n   *\n   * @param options - Options to Container Get Properties operation.\n   */\n  public async getProperties(\n    options: ContainerGetPropertiesOptions = {},\n  ): Promise<ContainerGetPropertiesResponse> {\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    return tracingClient.withSpan(\n      \"ContainerClient-getProperties\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ContainerGetPropertiesHeaders, ContainerGetPropertiesHeaders>(\n          await this.containerContext.getProperties({\n            abortSignal: options.abortSignal,\n            ...options.conditions,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Marks the specified container for deletion. The container and any blobs\n   * contained within it are later deleted during garbage collection.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-container\n   *\n   * @param options - Options to Container Delete operation.\n   */\n  public async delete(\n    options: ContainerDeleteMethodOptions = {},\n  ): Promise<ContainerDeleteResponse> {\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    return tracingClient.withSpan(\"ContainerClient-delete\", options, async (updatedOptions) => {\n      return assertResponse<ContainerDeleteHeaders, ContainerDeleteHeaders>(\n        await this.containerContext.delete({\n          abortSignal: options.abortSignal,\n          leaseAccessConditions: options.conditions,\n          modifiedAccessConditions: options.conditions,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Marks the specified container for deletion if it exists. The container and any blobs\n   * contained within it are later deleted during garbage collection.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-container\n   *\n   * @param options - Options to Container Delete operation.\n   */\n  public async deleteIfExists(\n    options: ContainerDeleteMethodOptions = {},\n  ): Promise<ContainerDeleteIfExistsResponse> {\n    return tracingClient.withSpan(\n      \"ContainerClient-deleteIfExists\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const res = await this.delete(updatedOptions);\n          return {\n            succeeded: true,\n            ...res,\n            _response: res._response,\n          };\n        } catch (e: any) {\n          if (e.details?.errorCode === \"ContainerNotFound\") {\n            return {\n              succeeded: false,\n              ...e.response?.parsedHeaders,\n              _response: e.response,\n            };\n          }\n          throw e;\n        }\n      },\n    );\n  }\n\n  /**\n   * Sets one or more user-defined name-value pairs for the specified container.\n   *\n   * If no option provided, or no metadata defined in the parameter, the container\n   * metadata will be removed.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-container-metadata\n   *\n   * @param metadata - Replace existing metadata with this value.\n   *                            If no value provided the existing metadata will be removed.\n   * @param options - Options to Container Set Metadata operation.\n   */\n  public async setMetadata(\n    metadata?: Metadata,\n    options: ContainerSetMetadataOptions = {},\n  ): Promise<ContainerSetMetadataResponse> {\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    if (options.conditions.ifUnmodifiedSince) {\n      throw new RangeError(\n        \"the IfUnmodifiedSince must have their default values because they are ignored by the blob service\",\n      );\n    }\n\n    return tracingClient.withSpan(\n      \"ContainerClient-setMetadata\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ContainerSetMetadataHeaders, ContainerSetMetadataHeaders>(\n          await this.containerContext.setMetadata({\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            metadata,\n            modifiedAccessConditions: options.conditions,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Gets the permissions for the specified container. The permissions indicate\n   * whether container data may be accessed publicly.\n   *\n   * WARNING: JavaScript Date will potentially lose precision when parsing startsOn and expiresOn strings.\n   * For example, new Date(\"2018-12-31T03:44:23.8827891Z\").toISOString() will get \"2018-12-31T03:44:23.882Z\".\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-container-acl\n   *\n   * @param options - Options to Container Get Access Policy operation.\n   */\n  public async getAccessPolicy(\n    options: ContainerGetAccessPolicyOptions = {},\n  ): Promise<ContainerGetAccessPolicyResponse> {\n    if (!options.conditions) {\n      options.conditions = {};\n    }\n\n    return tracingClient.withSpan(\n      \"ContainerClient-getAccessPolicy\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          ContainerGetAccessPolicyResponseModel,\n          ContainerGetAccessPolicyHeaders,\n          SignedIdentifierModel\n        >(\n          await this.containerContext.getAccessPolicy({\n            abortSignal: options.abortSignal,\n            leaseAccessConditions: options.conditions,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        const res: ContainerGetAccessPolicyResponse = {\n          _response: response._response,\n          blobPublicAccess: response.blobPublicAccess,\n          date: response.date,\n          etag: response.etag,\n          errorCode: response.errorCode,\n          lastModified: response.lastModified,\n          requestId: response.requestId,\n          clientRequestId: response.clientRequestId,\n          signedIdentifiers: [],\n          version: response.version,\n        };\n\n        for (const identifier of response) {\n          let accessPolicy: any = undefined;\n          if (identifier.accessPolicy) {\n            accessPolicy = {\n              permissions: identifier.accessPolicy.permissions,\n            };\n\n            if (identifier.accessPolicy.expiresOn) {\n              accessPolicy.expiresOn = new Date(identifier.accessPolicy.expiresOn);\n            }\n\n            if (identifier.accessPolicy.startsOn) {\n              accessPolicy.startsOn = new Date(identifier.accessPolicy.startsOn);\n            }\n          }\n\n          res.signedIdentifiers.push({\n            accessPolicy,\n            id: identifier.id,\n          });\n        }\n\n        return res;\n      },\n    );\n  }\n\n  /**\n   * Sets the permissions for the specified container. The permissions indicate\n   * whether blobs in a container may be accessed publicly.\n   *\n   * When you set permissions for a container, the existing permissions are replaced.\n   * If no access or containerAcl provided, the existing container ACL will be\n   * removed.\n   *\n   * When you establish a stored access policy on a container, it may take up to 30 seconds to take effect.\n   * During this interval, a shared access signature that is associated with the stored access policy will\n   * fail with status code 403 (Forbidden), until the access policy becomes active.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-container-acl\n   *\n   * @param access - The level of public access to data in the container.\n   * @param containerAcl - Array of elements each having a unique Id and details of the access policy.\n   * @param options - Options to Container Set Access Policy operation.\n   */\n  public async setAccessPolicy(\n    access?: PublicAccessType,\n    containerAcl?: SignedIdentifier[],\n    options: ContainerSetAccessPolicyOptions = {},\n  ): Promise<ContainerSetAccessPolicyResponse> {\n    options.conditions = options.conditions || {};\n    return tracingClient.withSpan(\n      \"ContainerClient-setAccessPolicy\",\n      options,\n      async (updatedOptions) => {\n        const acl: SignedIdentifierModel[] = [];\n        for (const identifier of containerAcl || []) {\n          acl.push({\n            accessPolicy: {\n              expiresOn: identifier.accessPolicy.expiresOn\n                ? truncatedISO8061Date(identifier.accessPolicy.expiresOn)\n                : \"\",\n              permissions: identifier.accessPolicy.permissions,\n              startsOn: identifier.accessPolicy.startsOn\n                ? truncatedISO8061Date(identifier.accessPolicy.startsOn)\n                : \"\",\n            },\n            id: identifier.id,\n          });\n        }\n\n        return assertResponse<ContainerSetAccessPolicyHeaders, ContainerSetAccessPolicyHeaders>(\n          await this.containerContext.setAccessPolicy({\n            abortSignal: options.abortSignal,\n            access,\n            containerAcl: acl,\n            leaseAccessConditions: options.conditions,\n            modifiedAccessConditions: options.conditions,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Get a {@link BlobLeaseClient} that manages leases on the container.\n   *\n   * @param proposeLeaseId - Initial proposed lease Id.\n   * @returns A new BlobLeaseClient object for managing leases on the container.\n   */\n  public getBlobLeaseClient(proposeLeaseId?: string): BlobLeaseClient {\n    return new BlobLeaseClient(this, proposeLeaseId);\n  }\n\n  /**\n   * Creates a new block blob, or updates the content of an existing block blob.\n   *\n   * Updating an existing block blob overwrites any existing metadata on the blob.\n   * Partial updates are not supported; the content of the existing blob is\n   * overwritten with the new content. To perform a partial update of a block blob's,\n   * use {@link BlockBlobClient.stageBlock} and {@link BlockBlobClient.commitBlockList}.\n   *\n   * This is a non-parallel uploading method, please use {@link BlockBlobClient.uploadFile},\n   * {@link BlockBlobClient.uploadStream} or {@link BlockBlobClient.uploadBrowserData} for better\n   * performance with concurrency uploading.\n   *\n   * @see https://learn.microsoft.com/rest/api/storageservices/put-blob\n   *\n   * @param blobName - Name of the block blob to create or update.\n   * @param body - Blob, string, ArrayBuffer, ArrayBufferView or a function\n   *                               which returns a new Readable stream whose offset is from data source beginning.\n   * @param contentLength - Length of body in bytes. Use Buffer.byteLength() to calculate body length for a\n   *                               string including non non-Base64/Hex-encoded characters.\n   * @param options - Options to configure the Block Blob Upload operation.\n   * @returns Block Blob upload response data and the corresponding BlockBlobClient instance.\n   */\n  public async uploadBlockBlob(\n    blobName: string,\n    body: HttpRequestBody,\n    contentLength: number,\n    options: BlockBlobUploadOptions = {},\n  ): Promise<{ blockBlobClient: BlockBlobClient; response: BlockBlobUploadResponse }> {\n    return tracingClient.withSpan(\n      \"ContainerClient-uploadBlockBlob\",\n      options,\n      async (updatedOptions) => {\n        const blockBlobClient = this.getBlockBlobClient(blobName);\n        const response = await blockBlobClient.upload(body, contentLength, updatedOptions);\n        return {\n          blockBlobClient,\n          response,\n        };\n      },\n    );\n  }\n\n  /**\n   * Marks the specified blob or snapshot for deletion. The blob is later deleted\n   * during garbage collection. Note that in order to delete a blob, you must delete\n   * all of its snapshots. You can delete both at the same time with the Delete\n   * Blob operation.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-blob\n   *\n   * @param blobName -\n   * @param options - Options to Blob Delete operation.\n   * @returns Block blob deletion response data.\n   */\n  public async deleteBlob(\n    blobName: string,\n    options: ContainerDeleteBlobOptions = {},\n  ): Promise<BlobDeleteResponse> {\n    return tracingClient.withSpan(\"ContainerClient-deleteBlob\", options, async (updatedOptions) => {\n      let blobClient = this.getBlobClient(blobName);\n      if (options.versionId) {\n        blobClient = blobClient.withVersion(options.versionId);\n      }\n      return blobClient.delete(updatedOptions);\n    });\n  }\n\n  /**\n   * listBlobFlatSegment returns a single segment of blobs starting from the\n   * specified Marker. Use an empty Marker to start enumeration from the beginning.\n   * After getting a segment, process it, and then call listBlobsFlatSegment again\n   * (passing the the previously-returned Marker) to get the next segment.\n   * @see https://learn.microsoft.com/rest/api/storageservices/list-blobs\n   *\n   * @param marker - A string value that identifies the portion of the list to be returned with the next list operation.\n   * @param options - Options to Container List Blob Flat Segment operation.\n   */\n  private async listBlobFlatSegment(\n    marker?: string,\n    options: ContainerListBlobsSegmentOptions = {},\n  ): Promise<ContainerListBlobFlatSegmentResponse> {\n    return tracingClient.withSpan(\n      \"ContainerClient-listBlobFlatSegment\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          ListBlobsFlatSegmentResponseInternal,\n          ContainerListBlobFlatSegmentHeaders,\n          ListBlobsFlatSegmentResponseInternal\n        >(\n          await this.containerContext.listBlobFlatSegment({\n            marker,\n            ...options,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        const wrappedResponse: ContainerListBlobFlatSegmentResponse = {\n          ...response,\n          _response: {\n            ...response._response,\n            parsedBody: ConvertInternalResponseOfListBlobFlat(response._response.parsedBody),\n          }, // _response is made non-enumerable\n          segment: {\n            ...response.segment,\n            blobItems: response.segment.blobItems.map((blobItemInternal) => {\n              const blobItem: BlobItem = {\n                ...blobItemInternal,\n                name: BlobNameToString(blobItemInternal.name),\n                tags: toTags(blobItemInternal.blobTags),\n                objectReplicationSourceProperties: parseObjectReplicationRecord(\n                  blobItemInternal.objectReplicationMetadata,\n                ),\n              };\n              return blobItem;\n            }),\n          },\n        };\n        return wrappedResponse;\n      },\n    );\n  }\n\n  /**\n   * listBlobHierarchySegment returns a single segment of blobs starting from\n   * the specified Marker. Use an empty Marker to start enumeration from the\n   * beginning. After getting a segment, process it, and then call listBlobsHierarchicalSegment\n   * again (passing the the previously-returned Marker) to get the next segment.\n   * @see https://learn.microsoft.com/rest/api/storageservices/list-blobs\n   *\n   * @param delimiter - The character or string used to define the virtual hierarchy\n   * @param marker - A string value that identifies the portion of the list to be returned with the next list operation.\n   * @param options - Options to Container List Blob Hierarchy Segment operation.\n   */\n  private async listBlobHierarchySegment(\n    delimiter: string,\n    marker?: string,\n    options: ContainerListBlobsSegmentOptions = {},\n  ): Promise<ContainerListBlobHierarchySegmentResponse> {\n    return tracingClient.withSpan(\n      \"ContainerClient-listBlobHierarchySegment\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          ContainerListBlobHierarchySegmentResponseModel,\n          ContainerListBlobHierarchySegmentHeaders,\n          ListBlobsHierarchySegmentResponseInternal\n        >(\n          await this.containerContext.listBlobHierarchySegment(delimiter, {\n            marker,\n            ...options,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        const wrappedResponse: ContainerListBlobHierarchySegmentResponse = {\n          ...response,\n          _response: {\n            ...response._response,\n            parsedBody: ConvertInternalResponseOfListBlobHierarchy(response._response.parsedBody),\n          }, // _response is made non-enumerable\n          segment: {\n            ...response.segment,\n            blobItems: response.segment.blobItems.map((blobItemInternal) => {\n              const blobItem: BlobItem = {\n                ...blobItemInternal,\n                name: BlobNameToString(blobItemInternal.name),\n                tags: toTags(blobItemInternal.blobTags),\n                objectReplicationSourceProperties: parseObjectReplicationRecord(\n                  blobItemInternal.objectReplicationMetadata,\n                ),\n              };\n              return blobItem;\n            }),\n            blobPrefixes: response.segment.blobPrefixes?.map((blobPrefixInternal) => {\n              const blobPrefix: BlobPrefix = {\n                ...blobPrefixInternal,\n                name: BlobNameToString(blobPrefixInternal.name),\n              };\n              return blobPrefix;\n            }),\n          },\n        };\n        return wrappedResponse;\n      },\n    );\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for ContainerListBlobFlatSegmentResponse\n   *\n   * @param marker - A string value that identifies the portion of\n   *                          the list of blobs to be returned with the next listing operation. The\n   *                          operation returns the ContinuationToken value within the response body if the\n   *                          listing operation did not return all blobs remaining to be listed\n   *                          with the current page. The ContinuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of list\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to list blobs operation.\n   */\n  private async *listSegments(\n    marker?: string,\n    options: ContainerListBlobsSegmentOptions = {},\n  ): AsyncIterableIterator<ContainerListBlobFlatSegmentResponse> {\n    let listBlobsFlatSegmentResponse;\n    if (!!marker || marker === undefined) {\n      do {\n        listBlobsFlatSegmentResponse = await this.listBlobFlatSegment(marker, options);\n        marker = listBlobsFlatSegmentResponse.continuationToken;\n        yield await listBlobsFlatSegmentResponse;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator of {@link BlobItem} objects\n   *\n   * @param options - Options to list blobs operation.\n   */\n  private async *listItems(\n    options: ContainerListBlobsSegmentOptions = {},\n  ): AsyncIterableIterator<BlobItem> {\n    let marker: string | undefined;\n    for await (const listBlobsFlatSegmentResponse of this.listSegments(marker, options)) {\n      yield* listBlobsFlatSegmentResponse.segment.blobItems;\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to list all the blobs\n   * under the specified account.\n   *\n   * .byPage() returns an async iterable iterator to list the blobs in pages.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * // Get the containerClient before you run these snippets,\n   * // Can be obtained from `blobServiceClient.getContainerClient(\"<your-container-name>\");`\n   * let i = 1;\n   * for await (const blob of containerClient.listBlobsFlat()) {\n   *   console.log(`Blob ${i++}: ${blob.name}`);\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * let iter = containerClient.listBlobsFlat();\n   * let blobItem = await iter.next();\n   * while (!blobItem.done) {\n   *   console.log(`Blob ${i++}: ${blobItem.value.name}`);\n   *   blobItem = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const response of containerClient.listBlobsFlat().byPage({ maxPageSize: 20 })) {\n   *   for (const blob of response.segment.blobItems) {\n   *     console.log(`Blob ${i++}: ${blob.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = containerClient.listBlobsFlat().byPage({ maxPageSize: 2 });\n   * let response = (await iterator.next()).value;\n   *\n   * // Prints 2 blob names\n   * for (const blob of response.segment.blobItems) {\n   *   console.log(`Blob ${i++}: ${blob.name}`);\n   * }\n   *\n   * // Gets next marker\n   * let marker = response.continuationToken;\n   *\n   * // Passing next marker as continuationToken\n   *\n   * iterator = containerClient.listBlobsFlat().byPage({ continuationToken: marker, maxPageSize: 10 });\n   * response = (await iterator.next()).value;\n   *\n   * // Prints 10 blob names\n   * for (const blob of response.segment.blobItems) {\n   *   console.log(`Blob ${i++}: ${blob.name}`);\n   * }\n   * ```\n   *\n   * @param options - Options to list blobs.\n   * @returns An asyncIterableIterator that supports paging.\n   */\n  public listBlobsFlat(\n    options: ContainerListBlobsOptions = {},\n  ): PagedAsyncIterableIterator<BlobItem, ContainerListBlobFlatSegmentResponse> {\n    const include: ListBlobsIncludeItem[] = [];\n    if (options.includeCopy) {\n      include.push(\"copy\");\n    }\n    if (options.includeDeleted) {\n      include.push(\"deleted\");\n    }\n    if (options.includeMetadata) {\n      include.push(\"metadata\");\n    }\n    if (options.includeSnapshots) {\n      include.push(\"snapshots\");\n    }\n    if (options.includeVersions) {\n      include.push(\"versions\");\n    }\n    if (options.includeUncommitedBlobs) {\n      include.push(\"uncommittedblobs\");\n    }\n    if (options.includeTags) {\n      include.push(\"tags\");\n    }\n    if (options.includeDeletedWithVersions) {\n      include.push(\"deletedwithversions\");\n    }\n    if (options.includeImmutabilityPolicy) {\n      include.push(\"immutabilitypolicy\");\n    }\n    if (options.includeLegalHold) {\n      include.push(\"legalhold\");\n    }\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    const updatedOptions: ContainerListBlobsSegmentOptions = {\n      ...options,\n      ...(include.length > 0 ? { include: include } : {}),\n    };\n\n    // AsyncIterableIterator to iterate over blobs\n    const iter = this.listItems(updatedOptions);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.listSegments(settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...updatedOptions,\n        });\n      },\n    };\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for ContainerListBlobHierarchySegmentResponse\n   *\n   * @param delimiter - The character or string used to define the virtual hierarchy\n   * @param marker - A string value that identifies the portion of\n   *                          the list of blobs to be returned with the next listing operation. The\n   *                          operation returns the ContinuationToken value within the response body if the\n   *                          listing operation did not return all blobs remaining to be listed\n   *                          with the current page. The ContinuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of list\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to list blobs operation.\n   */\n  private async *listHierarchySegments(\n    delimiter: string,\n    marker?: string,\n    options: ContainerListBlobsSegmentOptions = {},\n  ): AsyncIterableIterator<ContainerListBlobHierarchySegmentResponse> {\n    let listBlobsHierarchySegmentResponse;\n    if (!!marker || marker === undefined) {\n      do {\n        listBlobsHierarchySegmentResponse = await this.listBlobHierarchySegment(\n          delimiter,\n          marker,\n          options,\n        );\n        marker = listBlobsHierarchySegmentResponse.continuationToken;\n        yield await listBlobsHierarchySegmentResponse;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for {@link BlobPrefix} and {@link BlobItem} objects.\n   *\n   * @param delimiter - The character or string used to define the virtual hierarchy\n   * @param options - Options to list blobs operation.\n   */\n  private async *listItemsByHierarchy(\n    delimiter: string,\n    options: ContainerListBlobsSegmentOptions = {},\n  ): AsyncIterableIterator<({ kind: \"prefix\" } & BlobPrefix) | ({ kind: \"blob\" } & BlobItem)> {\n    let marker: string | undefined;\n    for await (const listBlobsHierarchySegmentResponse of this.listHierarchySegments(\n      delimiter,\n      marker,\n      options,\n    )) {\n      const segment = listBlobsHierarchySegmentResponse.segment;\n      if (segment.blobPrefixes) {\n        for (const prefix of segment.blobPrefixes) {\n          yield {\n            kind: \"prefix\",\n            ...prefix,\n          };\n        }\n      }\n      for (const blob of segment.blobItems) {\n        yield { kind: \"blob\", ...blob };\n      }\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to list all the blobs by hierarchy.\n   * under the specified account.\n   *\n   * .byPage() returns an async iterable iterator to list the blobs by hierarchy in pages.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * for await (const item of containerClient.listBlobsByHierarchy(\"/\")) {\n   *   if (item.kind === \"prefix\") {\n   *     console.log(`\\tBlobPrefix: ${item.name}`);\n   *   } else {\n   *     console.log(`\\tBlobItem: name - ${item.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let iter = containerClient.listBlobsByHierarchy(\"/\", { prefix: \"prefix1/\" });\n   * let entity = await iter.next();\n   * while (!entity.done) {\n   *   let item = entity.value;\n   *   if (item.kind === \"prefix\") {\n   *     console.log(`\\tBlobPrefix: ${item.name}`);\n   *   } else {\n   *     console.log(`\\tBlobItem: name - ${item.name}`);\n   *   }\n   *   entity = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * console.log(\"Listing blobs by hierarchy by page\");\n   * for await (const response of containerClient.listBlobsByHierarchy(\"/\").byPage()) {\n   *   const segment = response.segment;\n   *   if (segment.blobPrefixes) {\n   *     for (const prefix of segment.blobPrefixes) {\n   *       console.log(`\\tBlobPrefix: ${prefix.name}`);\n   *     }\n   *   }\n   *   for (const blob of response.segment.blobItems) {\n   *     console.log(`\\tBlobItem: name - ${blob.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a max page size:\n   *\n   * ```js\n   * console.log(\"Listing blobs by hierarchy by page, specifying a prefix and a max page size\");\n   *\n   * let i = 1;\n   * for await (const response of containerClient\n   *   .listBlobsByHierarchy(\"/\", { prefix: \"prefix2/sub1/\" })\n   *   .byPage({ maxPageSize: 2 })) {\n   *   console.log(`Page ${i++}`);\n   *   const segment = response.segment;\n   *\n   *   if (segment.blobPrefixes) {\n   *     for (const prefix of segment.blobPrefixes) {\n   *       console.log(`\\tBlobPrefix: ${prefix.name}`);\n   *     }\n   *   }\n   *\n   *   for (const blob of response.segment.blobItems) {\n   *     console.log(`\\tBlobItem: name - ${blob.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * @param delimiter - The character or string used to define the virtual hierarchy\n   * @param options - Options to list blobs operation.\n   */\n  public listBlobsByHierarchy(\n    delimiter: string,\n    options: ContainerListBlobsOptions = {},\n  ): PagedAsyncIterableIterator<\n    ({ kind: \"prefix\" } & BlobPrefix) | ({ kind: \"blob\" } & BlobItem),\n    ContainerListBlobHierarchySegmentResponse\n  > {\n    if (delimiter === \"\") {\n      throw new RangeError(\"delimiter should contain one or more characters\");\n    }\n\n    const include: ListBlobsIncludeItem[] = [];\n    if (options.includeCopy) {\n      include.push(\"copy\");\n    }\n    if (options.includeDeleted) {\n      include.push(\"deleted\");\n    }\n    if (options.includeMetadata) {\n      include.push(\"metadata\");\n    }\n    if (options.includeSnapshots) {\n      include.push(\"snapshots\");\n    }\n    if (options.includeVersions) {\n      include.push(\"versions\");\n    }\n    if (options.includeUncommitedBlobs) {\n      include.push(\"uncommittedblobs\");\n    }\n    if (options.includeTags) {\n      include.push(\"tags\");\n    }\n    if (options.includeDeletedWithVersions) {\n      include.push(\"deletedwithversions\");\n    }\n    if (options.includeImmutabilityPolicy) {\n      include.push(\"immutabilitypolicy\");\n    }\n    if (options.includeLegalHold) {\n      include.push(\"legalhold\");\n    }\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    const updatedOptions: ContainerListBlobsSegmentOptions = {\n      ...options,\n      ...(include.length > 0 ? { include: include } : {}),\n    };\n    // AsyncIterableIterator to iterate over blob prefixes and blobs\n    const iter = this.listItemsByHierarchy(delimiter, updatedOptions);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      async next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.listHierarchySegments(delimiter, settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...updatedOptions,\n        });\n      },\n    };\n  }\n\n  /**\n   * The Filter Blobs operation enables callers to list blobs in the container whose tags\n   * match a given search expression.\n   *\n   * @param tagFilterSqlExpression - The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                        The given expression must evaluate to true for a blob to be returned in the results.\n   *                                        The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                        however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param marker - A string value that identifies the portion of\n   *                          the list of blobs to be returned with the next listing operation. The\n   *                          operation returns the continuationToken value within the response body if the\n   *                          listing operation did not return all blobs remaining to be listed\n   *                          with the current page. The continuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of list\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to find blobs by tags.\n   */\n  private async findBlobsByTagsSegment(\n    tagFilterSqlExpression: string,\n    marker?: string,\n    options: ContainerFindBlobsByTagsSegmentOptions = {},\n  ): Promise<ContainerFindBlobsByTagsSegmentResponse> {\n    return tracingClient.withSpan(\n      \"ContainerClient-findBlobsByTagsSegment\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          ContainerFilterBlobsResponse,\n          ContainerFilterBlobsHeaders,\n          FilterBlobSegmentModel\n        >(\n          await this.containerContext.filterBlobs({\n            abortSignal: options.abortSignal,\n            where: tagFilterSqlExpression,\n            marker,\n            maxPageSize: options.maxPageSize,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        const wrappedResponse: ContainerFindBlobsByTagsSegmentResponse = {\n          ...response,\n          _response: response._response, // _response is made non-enumerable\n          blobs: response.blobs.map((blob) => {\n            let tagValue = \"\";\n            if (blob.tags?.blobTagSet.length === 1) {\n              tagValue = blob.tags.blobTagSet[0].value;\n            }\n            return { ...blob, tags: toTags(blob.tags), tagValue };\n          }),\n        };\n        return wrappedResponse;\n      },\n    );\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for ContainerFindBlobsByTagsSegmentResponse.\n   *\n   * @param tagFilterSqlExpression -  The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                         The given expression must evaluate to true for a blob to be returned in the results.\n   *                                         The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                         however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param marker - A string value that identifies the portion of\n   *                          the list of blobs to be returned with the next listing operation. The\n   *                          operation returns the continuationToken value within the response body if the\n   *                          listing operation did not return all blobs remaining to be listed\n   *                          with the current page. The continuationToken value can be used as the value for\n   *                          the marker parameter in a subsequent call to request the next page of list\n   *                          items. The marker value is opaque to the client.\n   * @param options - Options to find blobs by tags.\n   */\n  private async *findBlobsByTagsSegments(\n    tagFilterSqlExpression: string,\n    marker?: string,\n    options: ContainerFindBlobsByTagsSegmentOptions = {},\n  ): AsyncIterableIterator<ContainerFindBlobsByTagsSegmentResponse> {\n    let response;\n    if (!!marker || marker === undefined) {\n      do {\n        response = await this.findBlobsByTagsSegment(tagFilterSqlExpression, marker, options);\n        response.blobs = response.blobs || [];\n        marker = response.continuationToken;\n        yield response;\n      } while (marker);\n    }\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for blobs.\n   *\n   * @param tagFilterSqlExpression -  The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                         The given expression must evaluate to true for a blob to be returned in the results.\n   *                                         The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                         however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param options - Options to findBlobsByTagsItems.\n   */\n  private async *findBlobsByTagsItems(\n    tagFilterSqlExpression: string,\n    options: ContainerFindBlobsByTagsSegmentOptions = {},\n  ): AsyncIterableIterator<FilterBlobItem> {\n    let marker: string | undefined;\n    for await (const segment of this.findBlobsByTagsSegments(\n      tagFilterSqlExpression,\n      marker,\n      options,\n    )) {\n      yield* segment.blobs;\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to find all blobs with specified tag\n   * under the specified container.\n   *\n   * .byPage() returns an async iterable iterator to list the blobs in pages.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * let i = 1;\n   * for await (const blob of containerClient.findBlobsByTags(\"tagkey='tagvalue'\")) {\n   *   console.log(`Blob ${i++}: ${blob.name}`);\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * const iter = containerClient.findBlobsByTags(\"tagkey='tagvalue'\");\n   * let blobItem = await iter.next();\n   * while (!blobItem.done) {\n   *   console.log(`Blob ${i++}: ${blobItem.value.name}`);\n   *   blobItem = await iter.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const response of containerClient.findBlobsByTags(\"tagkey='tagvalue'\").byPage({ maxPageSize: 20 })) {\n   *   if (response.blobs) {\n   *     for (const blob of response.blobs) {\n   *       console.log(`Blob ${i++}: ${blob.name}`);\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = containerClient.findBlobsByTags(\"tagkey='tagvalue'\").byPage({ maxPageSize: 2 });\n   * let response = (await iterator.next()).value;\n   *\n   * // Prints 2 blob names\n   * if (response.blobs) {\n   *   for (const blob of response.blobs) {\n   *     console.log(`Blob ${i++}: ${blob.name}`);\n   *   }\n   * }\n   *\n   * // Gets next marker\n   * let marker = response.continuationToken;\n   * // Passing next marker as continuationToken\n   * iterator = containerClient\n   *   .findBlobsByTags(\"tagkey='tagvalue'\")\n   *   .byPage({ continuationToken: marker, maxPageSize: 10 });\n   * response = (await iterator.next()).value;\n   *\n   * // Prints blob names\n   * if (response.blobs) {\n   *   for (const blob of response.blobs) {\n   *      console.log(`Blob ${i++}: ${blob.name}`);\n   *   }\n   * }\n   * ```\n   *\n   * @param tagFilterSqlExpression -  The where parameter enables the caller to query blobs whose tags match a given expression.\n   *                                         The given expression must evaluate to true for a blob to be returned in the results.\n   *                                         The[OData - ABNF] filter syntax rule defines the formal grammar for the value of the where query parameter;\n   *                                         however, only a subset of the OData filter syntax is supported in the Blob service.\n   * @param options - Options to find blobs by tags.\n   */\n  public findBlobsByTags(\n    tagFilterSqlExpression: string,\n    options: ContainerFindBlobByTagsOptions = {},\n  ): PagedAsyncIterableIterator<FilterBlobItem, ContainerFindBlobsByTagsSegmentResponse> {\n    // AsyncIterableIterator to iterate over blobs\n    const listSegmentOptions: ContainerFindBlobsByTagsSegmentOptions = {\n      ...options,\n    };\n\n    const iter = this.findBlobsByTagsItems(tagFilterSqlExpression, listSegmentOptions);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.findBlobsByTagsSegments(tagFilterSqlExpression, settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...listSegmentOptions,\n        });\n      },\n    };\n  }\n\n  /**\n   * The Get Account Information operation returns the sku name and account kind\n   * for the specified account.\n   * The Get Account Information operation is available on service versions beginning\n   * with version 2018-03-28.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-account-information\n   *\n   * @param options - Options to the Service Get Account Info operation.\n   * @returns Response data for the Service Get Account Info operation.\n   */\n  public async getAccountInfo(\n    options: ContainerGetAccountInfoOptions = {},\n  ): Promise<ContainerGetAccountInfoResponse> {\n    return tracingClient.withSpan(\n      \"ContainerClient-getAccountInfo\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ContainerGetAccountInfoHeaders, ContainerGetAccountInfoHeaders>(\n          await this.containerContext.getAccountInfo({\n            abortSignal: options.abortSignal,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  private getContainerNameFromUrl(): string {\n    let containerName;\n    try {\n      //  URL may look like the following\n      // \"https://myaccount.blob.core.windows.net/mycontainer?sasString\";\n      // \"https://myaccount.blob.core.windows.net/mycontainer\";\n      // IPv4/IPv6 address hosts, Endpoints - `http://127.0.0.1:10000/devstoreaccount1/containername`\n      // http://localhost:10001/devstoreaccount1/containername\n\n      const parsedUrl = new URL(this.url);\n\n      if (parsedUrl.hostname.split(\".\")[1] === \"blob\") {\n        // \"https://myaccount.blob.core.windows.net/containername\".\n        // \"https://customdomain.com/containername\".\n        // .getPath() -> /containername\n        containerName = parsedUrl.pathname.split(\"/\")[1];\n      } else if (isIpEndpointStyle(parsedUrl)) {\n        // IPv4/IPv6 address hosts... Example - http://**********:10001/devstoreaccount1/containername\n        // Single word domain without a [dot] in the endpoint... Example - http://localhost:10001/devstoreaccount1/containername\n        // .getPath() -> /devstoreaccount1/containername\n        containerName = parsedUrl.pathname.split(\"/\")[2];\n      } else {\n        // \"https://customdomain.com/containername\".\n        // .getPath() -> /containername\n        containerName = parsedUrl.pathname.split(\"/\")[1];\n      }\n\n      // decode the encoded containerName - to get all the special characters that might be present in it\n      containerName = decodeURIComponent(containerName);\n\n      if (!containerName) {\n        throw new Error(\"Provided containerName is invalid.\");\n      }\n\n      return containerName;\n    } catch (error: any) {\n      throw new Error(\"Unable to extract containerName with provided information.\");\n    }\n  }\n\n  /**\n   * Only available for ContainerClient constructed with a shared key credential.\n   *\n   * Generates a Blob Container Service Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateSasUrl(options: ContainerGenerateSasUrlOptions): Promise<string> {\n    return new Promise((resolve) => {\n      if (!(this.credential instanceof StorageSharedKeyCredential)) {\n        throw new RangeError(\n          \"Can only generate the SAS when the client is initialized with a shared key credential\",\n        );\n      }\n\n      const sas = generateBlobSASQueryParameters(\n        {\n          containerName: this._containerName,\n          ...options,\n        },\n        this.credential,\n      ).toString();\n\n      resolve(appendToURLQuery(this.url, sas));\n    });\n  }\n\n  /**\n   * Only available for ContainerClient constructed with a shared key credential.\n   *\n   * Generates string to sign for a Blob Container Service Shared Access Signature (SAS) URI\n   * based on the client properties and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n  public generateSasStringToSign(options: ContainerGenerateSasUrlOptions): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw new RangeError(\n        \"Can only generate the SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    return generateBlobSASQueryParametersInternal(\n      {\n        containerName: this._containerName,\n        ...options,\n      },\n      this.credential,\n    ).stringToSign;\n  }\n\n  /**\n   * Generates a Blob Container Service Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the input user delegation key.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @param userDelegationKey -  Return value of `blobServiceClient.getUserDelegationKey()`\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateUserDelegationSasUrl(\n    options: ContainerGenerateSasUrlOptions,\n    userDelegationKey: UserDelegationKey,\n  ): Promise<string> {\n    return new Promise((resolve) => {\n      const sas = generateBlobSASQueryParameters(\n        {\n          containerName: this._containerName,\n          ...options,\n        },\n        userDelegationKey,\n        this.accountName,\n      ).toString();\n\n      resolve(appendToURLQuery(this.url, sas));\n    });\n  }\n\n  /**\n   * Generates string to sign for a Blob Container Service Shared Access Signature (SAS) URI\n   * based on the client properties and parameters passed in. The SAS is signed by the input user delegation key.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @param userDelegationKey -  Return value of `blobServiceClient.getUserDelegationKey()`\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n\n  public generateUserDelegationSasStringToSign(\n    options: ContainerGenerateSasUrlOptions,\n    userDelegationKey: UserDelegationKey,\n  ): string {\n    return generateBlobSASQueryParametersInternal(\n      {\n        containerName: this._containerName,\n        ...options,\n      },\n      userDelegationKey,\n      this.accountName,\n    ).stringToSign;\n  }\n\n  /**\n   * Creates a BlobBatchClient object to conduct batch operations.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch\n   *\n   * @returns A new BlobBatchClient object for this container.\n   */\n  public getBlobBatchClient(): BlobBatchClient {\n    return new BlobBatchClient(this.url, this.pipeline);\n  }\n}\n"]}