{"version": 3, "file": "StorageCorrectContentLengthPolicy.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/policies/StorageCorrectContentLengthPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,qCAAqC,GAAG,mCAAmC,CAAC;AAEzF;;GAEG;AACH,MAAM,UAAU,iCAAiC;IAC/C,SAAS,oBAAoB,CAAC,OAAwB;QACpD,IACE,OAAO,CAAC,IAAI;YACZ,CAAC,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnE,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACvB,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACvF,CAAC;IACH,CAAC;IAED,OAAO;QACL,IAAI,EAAE,qCAAqC;QAC3C,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  PipelinePolicy,\n} from \"@azure/core-rest-pipeline\";\nimport { HeaderConstants } from \"../utils/constants\";\n\n/**\n * The programmatic identifier of the storageCorrectContentLengthPolicy.\n */\nexport const storageCorrectContentLengthPolicyName = \"StorageCorrectContentLengthPolicy\";\n\n/**\n * storageCorrectContentLengthPolicy to correctly set Content-Length header with request body length.\n */\nexport function storageCorrectContentLengthPolicy(): PipelinePolicy {\n  function correctContentLength(request: PipelineRequest): void {\n    if (\n      request.body &&\n      (typeof request.body === \"string\" || Buffer.isBuffer(request.body)) &&\n      request.body.length > 0\n    ) {\n      request.headers.set(HeaderConstants.CONTENT_LENGTH, Buffer.byteLength(request.body));\n    }\n  }\n\n  return {\n    name: storageCorrectContentLengthPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      correctContentLength(request);\n      return next(request);\n    },\n  };\n}\n"]}