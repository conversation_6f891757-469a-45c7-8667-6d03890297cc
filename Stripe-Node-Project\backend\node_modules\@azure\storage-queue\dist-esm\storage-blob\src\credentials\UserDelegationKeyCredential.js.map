{"version": 3, "file": "UserDelegationKeyCredential.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/credentials/UserDelegationKeyCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAGpC;;;;;GAKG;AACH,MAAM,OAAO,2BAA2B;IAgBtC;;;;OAIG;IACH,YAAY,WAAmB,EAAE,iBAAoC;QACnE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,YAAoB;QAC3C,gEAAgE;QAEhE,OAAO,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACtF,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createHmac } from \"crypto\";\nimport type { UserDelegationKey } from \"../BlobServiceClient\";\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * UserDelegationKeyCredential is only used for generation of user delegation SAS.\n * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-user-delegation-sas\n */\nexport class UserDelegationKeyCredential {\n  /**\n   * Azure Storage account name; readonly.\n   */\n  public readonly accountName: string;\n\n  /**\n   * Azure Storage user delegation key; readonly.\n   */\n  public readonly userDelegationKey: UserDelegationKey;\n\n  /**\n   * Key value in Buffer type.\n   */\n  private readonly key: Buffer;\n\n  /**\n   * Creates an instance of UserDelegationKeyCredential.\n   * @param accountName -\n   * @param userDelegationKey -\n   */\n  constructor(accountName: string, userDelegationKey: UserDelegationKey) {\n    this.accountName = accountName;\n    this.userDelegationKey = userDelegationKey;\n    this.key = Buffer.from(userDelegationKey.value, \"base64\");\n  }\n\n  /**\n   * Generates a hash signature for an HTTP request or for a SAS.\n   *\n   * @param stringToSign -\n   */\n  public computeHMACSHA256(stringToSign: string): string {\n    // console.log(`stringToSign: ${JSON.stringify(stringToSign)}`);\n\n    return createHmac(\"sha256\", this.key).update(stringToSign, \"utf8\").digest(\"base64\");\n  }\n}\n"]}