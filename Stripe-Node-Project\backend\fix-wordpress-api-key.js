const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Load environment variables
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function fixWordPressApiKey() {
  try {
    console.log('🔧 Fixing WordPress API Key...');
    
    // First, check existing API keys
    console.log('\n📋 Checking existing API keys...');
    const { data: existingKeys, error: checkError } = await supabase
      .from('api_keys')
      .select('*');
    
    if (checkError) {
      console.error('❌ Error checking existing keys:', checkError);
      return;
    }
    
    console.log(`Found ${existingKeys.length} existing API keys:`);
    existingKeys.forEach(key => {
      console.log(`- ${key.key_hash.substring(0, 20)}... (Active: ${key.is_active}, User: ${key.user_id})`);
    });

    // Get the user_id from existing key
    const existingUserId = existingKeys.length > 0 ? existingKeys[0].user_id : null;
    
    // Deactivate all existing keys
    console.log('\n🔄 Deactivating existing API keys...');
    const { error: deactivateError } = await supabase
      .from('api_keys')
      .update({ is_active: false })
      .eq('is_active', true); // Update all active records
    
    if (deactivateError) {
      console.error('❌ Error deactivating keys:', deactivateError);
      return;
    }
    
    // Generate new API key
    console.log('\n🔑 Generating new API key...');
    const apiKey = 'sk_api_' + crypto.randomBytes(32).toString('hex');
    const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');
    
    // Insert new API key
    const { data: newKey, error: insertError } = await supabase
      .from('api_keys')
      .insert({
        user_id: existingUserId,
        key_hash: keyHash,
        wordpress_site: 'http://localhost/Stripe-Node-Project/wordpress',
        permissions: ['payments', 'commissions', 'vendors'],
        description: 'WordPress Plugin Integration - Fixed',
        is_active: true
      })
      .select()
      .single();
    
    if (insertError) {
      console.error('❌ Error creating new API key:', insertError);
      return;
    }
    
    console.log('✅ New API key created successfully!');
    console.log('📋 API Key Details:');
    console.log(`   Key: ${apiKey}`);
    console.log(`   Hash: ${keyHash.substring(0, 20)}...`);
    console.log(`   ID: ${newKey.id}`);
    
    // Test the new API key
    console.log('\n🧪 Testing new API key...');
    const testResponse = await fetch('http://localhost:3000/api/vendors', {
      headers: {
        'X-API-Key': apiKey
      }
    });
    
    if (testResponse.ok) {
      console.log('✅ API key test successful!');
    } else {
      console.log('❌ API key test failed:', await testResponse.text());
    }
    
    console.log('\n📝 Next Steps:');
    console.log('1. Copy this API key to WordPress admin settings');
    console.log('2. Or run the WordPress update script');
    console.log(`\nAPI Key to copy: ${apiKey}`);
    
    return apiKey;
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the fix
fixWordPressApiKey().catch(console.error);
