{"version": 3, "file": "StorageSharedKeyCredentialPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageSharedKeyCredentialPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACrD,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AAClE,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAE7D;;GAEG;AACH,MAAM,OAAO,gCAAiC,SAAQ,gBAAgB;IAMpE;;;;;OAKG;IACH,YACE,UAAyB,EACzB,OAA6B,EAC7B,OAAmC;QAEnC,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACO,WAAW,CAAC,OAAoB;QACxC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAEzE,IACE,OAAO,CAAC,IAAI;YACZ,CAAC,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAK,OAAO,CAAC,IAAe,KAAK,SAAS,CAAC;YAC5E,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACvB,CAAC;YACD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,YAAY,GAChB;YACE,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE;YAC5B,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,gBAAgB,CAAC;YACpE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,gBAAgB,CAAC;YACpE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,cAAc,CAAC;YAClE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,WAAW,CAAC;YAC/D,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,YAAY,CAAC;YAChE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,IAAI,CAAC;YACxD,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,iBAAiB,CAAC;YACrE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,QAAQ,CAAC;YAC5D,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,aAAa,CAAC;YACjE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,mBAAmB,CAAC;YACvE,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,eAAe,CAAC,KAAK,CAAC;SAC1D,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,IAAI;YACJ,IAAI,CAAC,6BAA6B,CAAC,OAAO,CAAC;YAC3C,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,CAAC;QAE/C,MAAM,SAAS,GAAW,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACvE,OAAO,CAAC,OAAO,CAAC,GAAG,CACjB,eAAe,CAAC,aAAa,EAC7B,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,SAAS,EAAE,CACrD,CAAC;QAEF,uCAAuC;QACvC,0DAA0D;QAC1D,mEAAmE;QACnE,+EAA+E;QAC/E,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;OAMG;IACK,oBAAoB,CAAC,OAAoB,EAAE,UAAkB;QACnE,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAE9C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,0EAA0E;QAC1E,sEAAsE;QACtE,0FAA0F;QAC1F,IAAI,UAAU,KAAK,eAAe,CAAC,cAAc,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YACnE,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;;;;;;;OAYG;IACK,6BAA6B,CAAC,OAAoB;QACxD,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YACjE,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,kBAAkB,CAAC,CAAC;QACjF,CAAC,CAAC,CAAC;QAEH,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAU,EAAE;YACjC,OAAO,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE;YACzD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gBAClF,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,IAAI,gCAAgC,GAAW,EAAE,CAAC;QAClD,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAC9B,gCAAgC,IAAI,GAAG,MAAM,CAAC,IAAI;iBAC/C,WAAW,EAAE;iBACb,SAAS,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,OAAO,gCAAgC,CAAC;IAC1C,CAAC;IAED;;;;OAIG;IACK,8BAA8B,CAAC,OAAoB;QACzD,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;QAE5C,IAAI,2BAA2B,GAAW,EAAE,CAAC;QAC7C,2BAA2B,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,EAAE,CAAC;QAErE,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3C,MAAM,gBAAgB,GAA8B,EAAE,CAAC;QACvD,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,SAAS,GAAa,EAAE,CAAC;YAC/B,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;gBAC1B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC;oBACvD,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;oBACvC,gBAAgB,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;oBAC9C,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,SAAS,CAAC,IAAI,EAAE,CAAC;YACjB,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC5B,2BAA2B,IAAI,KAAK,GAAG,IAAI,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACzF,CAAC;QACH,CAAC;QAED,OAAO,2BAA2B,CAAC;IACrC,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n  WebResourceLike as WebResource,\n} from \"@azure/core-http-compat\";\nimport type { StorageSharedKeyCredential } from \"../credentials/StorageSharedKeyCredential\";\nimport { HeaderConstants } from \"../utils/constants\";\nimport { getURLPath, getURLQueries } from \"../utils/utils.common\";\nimport { CredentialPolicy } from \"./CredentialPolicy\";\nimport { compareHeader } from \"../utils/SharedKeyComparator\";\n\n/**\n * StorageSharedKeyCredentialPolicy is a policy used to sign HTTP request with a shared key.\n */\nexport class StorageSharedKeyCredentialPolicy extends CredentialPolicy {\n  /**\n   * Reference to StorageSharedKeyCredential which generates StorageSharedKeyCredentialPolicy\n   */\n  private readonly factory: StorageSharedKeyCredential;\n\n  /**\n   * Creates an instance of StorageSharedKeyCredentialPolicy.\n   * @param nextPolicy -\n   * @param options -\n   * @param factory -\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    factory: StorageSharedKeyCredential,\n  ) {\n    super(nextPolicy, options);\n    this.factory = factory;\n  }\n\n  /**\n   * Signs request.\n   *\n   * @param request -\n   */\n  protected signRequest(request: WebResource): WebResource {\n    request.headers.set(HeaderConstants.X_MS_DATE, new Date().toUTCString());\n\n    if (\n      request.body &&\n      (typeof request.body === \"string\" || (request.body as Buffer) !== undefined) &&\n      request.body.length > 0\n    ) {\n      request.headers.set(HeaderConstants.CONTENT_LENGTH, Buffer.byteLength(request.body));\n    }\n\n    const stringToSign: string =\n      [\n        request.method.toUpperCase(),\n        this.getHeaderValueToSign(request, HeaderConstants.CONTENT_LANGUAGE),\n        this.getHeaderValueToSign(request, HeaderConstants.CONTENT_ENCODING),\n        this.getHeaderValueToSign(request, HeaderConstants.CONTENT_LENGTH),\n        this.getHeaderValueToSign(request, HeaderConstants.CONTENT_MD5),\n        this.getHeaderValueToSign(request, HeaderConstants.CONTENT_TYPE),\n        this.getHeaderValueToSign(request, HeaderConstants.DATE),\n        this.getHeaderValueToSign(request, HeaderConstants.IF_MODIFIED_SINCE),\n        this.getHeaderValueToSign(request, HeaderConstants.IF_MATCH),\n        this.getHeaderValueToSign(request, HeaderConstants.IF_NONE_MATCH),\n        this.getHeaderValueToSign(request, HeaderConstants.IF_UNMODIFIED_SINCE),\n        this.getHeaderValueToSign(request, HeaderConstants.RANGE),\n      ].join(\"\\n\") +\n      \"\\n\" +\n      this.getCanonicalizedHeadersString(request) +\n      this.getCanonicalizedResourceString(request);\n\n    const signature: string = this.factory.computeHMACSHA256(stringToSign);\n    request.headers.set(\n      HeaderConstants.AUTHORIZATION,\n      `SharedKey ${this.factory.accountName}:${signature}`,\n    );\n\n    // console.log(`[URL]:${request.url}`);\n    // console.log(`[HEADERS]:${request.headers.toString()}`);\n    // console.log(`[STRING TO SIGN]:${JSON.stringify(stringToSign)}`);\n    // console.log(`[KEY]: ${request.headers.get(HeaderConstants.AUTHORIZATION)}`);\n    return request;\n  }\n\n  /**\n   * Retrieve header value according to shared key sign rules.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/authenticate-with-shared-key\n   *\n   * @param request -\n   * @param headerName -\n   */\n  private getHeaderValueToSign(request: WebResource, headerName: string): string {\n    const value = request.headers.get(headerName);\n\n    if (!value) {\n      return \"\";\n    }\n\n    // When using version 2015-02-21 or later, if Content-Length is zero, then\n    // set the Content-Length part of the StringToSign to an empty string.\n    // https://learn.microsoft.com/en-us/rest/api/storageservices/authenticate-with-shared-key\n    if (headerName === HeaderConstants.CONTENT_LENGTH && value === \"0\") {\n      return \"\";\n    }\n\n    return value;\n  }\n\n  /**\n   * To construct the CanonicalizedHeaders portion of the signature string, follow these steps:\n   * 1. Retrieve all headers for the resource that begin with x-ms-, including the x-ms-date header.\n   * 2. Convert each HTTP header name to lowercase.\n   * 3. Sort the headers lexicographically by header name, in ascending order.\n   *    Each header may appear only once in the string.\n   * 4. Replace any linear whitespace in the header value with a single space.\n   * 5. Trim any whitespace around the colon in the header.\n   * 6. Finally, append a new-line character to each canonicalized header in the resulting list.\n   *    Construct the CanonicalizedHeaders string by concatenating all headers in this list into a single string.\n   *\n   * @param request -\n   */\n  private getCanonicalizedHeadersString(request: WebResource): string {\n    let headersArray = request.headers.headersArray().filter((value) => {\n      return value.name.toLowerCase().startsWith(HeaderConstants.PREFIX_FOR_STORAGE);\n    });\n\n    headersArray.sort((a, b): number => {\n      return compareHeader(a.name.toLowerCase(), b.name.toLowerCase());\n    });\n\n    // Remove duplicate headers\n    headersArray = headersArray.filter((value, index, array) => {\n      if (index > 0 && value.name.toLowerCase() === array[index - 1].name.toLowerCase()) {\n        return false;\n      }\n      return true;\n    });\n\n    let canonicalizedHeadersStringToSign: string = \"\";\n    headersArray.forEach((header) => {\n      canonicalizedHeadersStringToSign += `${header.name\n        .toLowerCase()\n        .trimRight()}:${header.value.trimLeft()}\\n`;\n    });\n\n    return canonicalizedHeadersStringToSign;\n  }\n\n  /**\n   * Retrieves the webResource canonicalized resource string.\n   *\n   * @param request -\n   */\n  private getCanonicalizedResourceString(request: WebResource): string {\n    const path = getURLPath(request.url) || \"/\";\n\n    let canonicalizedResourceString: string = \"\";\n    canonicalizedResourceString += `/${this.factory.accountName}${path}`;\n\n    const queries = getURLQueries(request.url);\n    const lowercaseQueries: { [key: string]: string } = {};\n    if (queries) {\n      const queryKeys: string[] = [];\n      for (const key in queries) {\n        if (Object.prototype.hasOwnProperty.call(queries, key)) {\n          const lowercaseKey = key.toLowerCase();\n          lowercaseQueries[lowercaseKey] = queries[key];\n          queryKeys.push(lowercaseKey);\n        }\n      }\n\n      queryKeys.sort();\n      for (const key of queryKeys) {\n        canonicalizedResourceString += `\\n${key}:${decodeURIComponent(lowercaseQueries[key])}`;\n      }\n    }\n\n    return canonicalizedResourceString;\n  }\n}\n"]}