# =============================================================================
# STRIPE NODE PROJECT - ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains all the environment variables needed for the backend API
# Copy this file and update the values with your actual credentials
# =============================================================================

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
NODE_ENV=development
PORT=3000

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project dashboard: https://app.supabase.com
# Project Settings > API > Project URL
SUPABASE_URL=your_supabase_project_url_here

# Project Settings > API > Project API keys > anon public
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Project Settings > API > Project API keys > service_role (keep this secret!)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================
# Get these from your Stripe dashboard: https://dashboard.stripe.com/test/apikeys
# Secret key (starts with sk_test_ for test mode)
STRIPE_SECRET_KEY=your_stripe_secret_key_here

# Publishable key (starts with pk_test_ for test mode)
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key_here

# Webhook endpoint secret (get this from Stripe webhook settings)
# Dashboard > Developers > Webhooks > Select your endpoint > Signing secret
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret - Generate a strong random string (minimum 32 characters)
# You can use: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET=your_jwt_secret_minimum_32_characters_long

# API Secret - Generate a strong random string (minimum 32 characters)
# You can use: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
API_SECRET=your_api_secret_minimum_32_characters_long

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Comma-separated list of allowed origins for CORS
# For development, you might want: http://localhost:3000,http://localhost:8080
# For production, use your actual domain(s)
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# =============================================================================
# EXAMPLE VALUES (REPLACE WITH YOUR ACTUAL VALUES)
# =============================================================================
# Here are example formats to help you understand what each value should look like:
#
# SUPABASE_URL=https://your-project-ref.supabase.co
# SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# STRIPE_SECRET_KEY=sk_test_51234567890abcdef...
# STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef...
# STRIPE_WEBHOOK_SECRET=whsec_1234567890abcdef...
# JWT_SECRET=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6
# API_SECRET=z6y5x4w3v2u1t0s9r8q7p6o5n4m3l2k1j0i9h8g7f6e5d4c3b2a1

# =============================================================================
# QUICK SETUP INSTRUCTIONS
# =============================================================================
# 1. Create a Supabase project at https://app.supabase.com
# 2. Get your project URL and API keys from Project Settings > API
# 3. Create a Stripe account at https://stripe.com
# 4. Get your API keys from https://dashboard.stripe.com/test/apikeys
# 5. Generate strong secrets for JWT_SECRET and API_SECRET using:
#    node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
# 6. Replace all the placeholder values above with your actual values
# 7. Save this file and restart your backend server
# =============================================================================
