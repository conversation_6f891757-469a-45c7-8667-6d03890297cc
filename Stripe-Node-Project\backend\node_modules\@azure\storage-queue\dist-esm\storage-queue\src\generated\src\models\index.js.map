{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/models/index.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAggBH,yEAAyE;AACzE,MAAM,CAAN,IAAY,qBAmHX;AAnHD,WAAY,qBAAqB;IAC/B,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,mCAAmC;IACnC,sFAA6D,CAAA;IAC7D,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,qCAAqC;IACrC,0FAAiE,CAAA;IACjE,oBAAoB;IACpB,wDAA+B,CAAA;IAC/B,gCAAgC;IAChC,gFAAuD,CAAA;IACvD,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,mBAAmB;IACnB,sDAA6B,CAAA;IAC7B,iBAAiB;IACjB,kDAAyB,CAAA;IACzB,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,iCAAiC;IACjC,kFAAyD,CAAA;IACzD,mBAAmB;IACnB,sDAA6B,CAAA;IAC7B,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,iBAAiB;IACjB,kDAAyB,CAAA;IACzB,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,kBAAkB;IAClB,oDAA2B,CAAA;IAC3B,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,iCAAiC;IACjC,kFAAyD,CAAA;IACzD,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,6BAA6B;IAC7B,0EAAiD,CAAA;IACjD,2CAA2C;IAC3C,sGAA6E,CAAA;IAC7E,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,2BAA2B;IAC3B,sEAA6C,CAAA;IAC7C,8BAA8B;IAC9B,4EAAmD,CAAA;IACnD,4BAA4B;IAC5B,wEAA+C,CAAA;IAC/C,uBAAuB;IACvB,8DAAqC,CAAA;IACrC,iBAAiB;IACjB,kDAAyB,CAAA;IACzB,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,gCAAgC;IAChC,gFAAuD,CAAA;IACvD,0BAA0B;IAC1B,oEAA2C,CAAA;IAC3C,oBAAoB;IACpB,wDAA+B,CAAA;IAC/B,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,sBAAsB;IACtB,4DAAmC,CAAA;IACnC,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,yBAAyB;IACzB,kEAAyC,CAAA;IACzC,wBAAwB;IACxB,gEAAuC,CAAA;IACvC,oBAAoB;IACpB,wDAA+B,CAAA;IAC/B,oBAAoB;IACpB,wDAA+B,CAAA;IAC/B,oBAAoB;IACpB,wDAA+B,CAAA;IAC/B,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,oCAAoC;IACpC,wFAA+D,CAAA;IAC/D,sCAAsC;IACtC,4FAAmE,CAAA;IACnE,mCAAmC;IACnC,sFAA6D,CAAA;IAC7D,wCAAwC;IACxC,gGAAuE,CAAA;IACvE,6BAA6B;IAC7B,0EAAiD,CAAA;AACnD,CAAC,EAnHW,qBAAqB,KAArB,qBAAqB,QAmHhC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreHttpCompat from \"@azure/core-http-compat\";\n\n/** Storage Service Properties. */\nexport interface QueueServiceProperties {\n  /** Azure Analytics Logging settings */\n  queueAnalyticsLogging?: Logging;\n  /** A summary of request statistics grouped by API in hourly aggregates for queues */\n  hourMetrics?: Metrics;\n  /** a summary of request statistics grouped by API in minute aggregates for queues */\n  minuteMetrics?: Metrics;\n  /** The set of CORS rules. */\n  cors?: CorsRule[];\n}\n\n/** Azure Analytics Logging settings. */\nexport interface Logging {\n  /** The version of Storage Analytics to configure. */\n  version: string;\n  /** Indicates whether all delete requests should be logged. */\n  deleteProperty: boolean;\n  /** Indicates whether all read requests should be logged. */\n  read: boolean;\n  /** Indicates whether all write requests should be logged. */\n  write: boolean;\n  /** the retention policy */\n  retentionPolicy: RetentionPolicy;\n}\n\n/** the retention policy */\nexport interface RetentionPolicy {\n  /** Indicates whether a retention policy is enabled for the storage service */\n  enabled: boolean;\n  /** Indicates the number of days that metrics or logging or soft-deleted data should be retained. All data older than this value will be deleted */\n  days?: number;\n}\n\n/** An interface representing Metrics. */\nexport interface Metrics {\n  /** The version of Storage Analytics to configure. */\n  version?: string;\n  /** Indicates whether metrics are enabled for the Queue service. */\n  enabled: boolean;\n  /** Indicates whether metrics should generate summary statistics for called API operations. */\n  includeAPIs?: boolean;\n  /** the retention policy */\n  retentionPolicy?: RetentionPolicy;\n}\n\n/** CORS is an HTTP feature that enables a web application running under one domain to access resources in another domain. Web browsers implement a security restriction known as same-origin policy that prevents a web page from calling APIs in a different domain; CORS provides a secure way to allow one domain (the origin domain) to call APIs in another domain */\nexport interface CorsRule {\n  /** The origin domains that are permitted to make a request against the storage service via CORS. The origin domain is the domain from which the request originates. Note that the origin must be an exact case-sensitive match with the origin that the user age sends to the service. You can also use the wildcard character '*' to allow all origin domains to make requests via CORS. */\n  allowedOrigins: string;\n  /** The methods (HTTP request verbs) that the origin domain may use for a CORS request. (comma separated) */\n  allowedMethods: string;\n  /** the request headers that the origin domain may specify on the CORS request. */\n  allowedHeaders: string;\n  /** The response headers that may be sent in the response to the CORS request and exposed by the browser to the request issuer */\n  exposedHeaders: string;\n  /** The maximum amount time that a browser should cache the preflight OPTIONS request. */\n  maxAgeInSeconds: number;\n}\n\nexport interface StorageError {\n  message?: string;\n  code?: string;\n  authenticationErrorDetail?: string;\n}\n\n/** Stats for the storage service. */\nexport interface QueueServiceStatistics {\n  /** Geo-Replication information for the Secondary Storage Service */\n  geoReplication?: GeoReplication;\n}\n\n/** Geo-Replication information for the Secondary Storage Service */\nexport interface GeoReplication {\n  /** The status of the secondary location */\n  status: GeoReplicationStatusType;\n  /** A GMT date/time value, to the second. All primary writes preceding this value are guaranteed to be available for read operations at the secondary. Primary writes after this point in time may or may not be available for reads. */\n  lastSyncOn: Date;\n}\n\n/** The object returned when calling List Queues on a Queue Service. */\nexport interface ListQueuesSegmentResponse {\n  serviceEndpoint: string;\n  prefix: string;\n  marker?: string;\n  maxPageSize: number;\n  queueItems?: QueueItem[];\n  continuationToken: string;\n}\n\n/** An Azure Storage Queue. */\nexport interface QueueItem {\n  /** The name of the Queue. */\n  name: string;\n  /** Dictionary of <string> */\n  metadata?: { [propertyName: string]: string };\n}\n\n/** signed identifier */\nexport interface SignedIdentifier {\n  /** a unique id */\n  id: string;\n  /** The access policy */\n  accessPolicy: AccessPolicy;\n}\n\n/** An Access policy */\nexport interface AccessPolicy {\n  /** the date-time the policy is active */\n  startsOn?: string;\n  /** the date-time the policy expires */\n  expiresOn?: string;\n  /** the permissions for the acl policy */\n  permissions?: string;\n}\n\n/** The object returned in the QueueMessageList array when calling Get Messages on a Queue. */\nexport interface DequeuedMessageItem {\n  /** The Id of the Message. */\n  messageId: string;\n  /** The time the Message was inserted into the Queue. */\n  insertedOn: Date;\n  /** The time that the Message will expire and be automatically deleted. */\n  expiresOn: Date;\n  /** This value is required to delete the Message. If deletion fails using this popreceipt then the message has been dequeued by another client. */\n  popReceipt: string;\n  /** The time that the message will again become visible in the Queue. */\n  nextVisibleOn: Date;\n  /** The number of times the message has been dequeued. */\n  dequeueCount: number;\n  /** The content of the Message. */\n  messageText: string;\n}\n\n/** A Message object which can be stored in a Queue */\nexport interface QueueMessage {\n  /** The content of the message */\n  messageText: string;\n}\n\n/** The object returned in the QueueMessageList array when calling Put Message on a Queue */\nexport interface EnqueuedMessage {\n  /** The Id of the Message. */\n  messageId: string;\n  /** The time the Message was inserted into the Queue. */\n  insertedOn: Date;\n  /** The time that the Message will expire and be automatically deleted. */\n  expiresOn: Date;\n  /** This value is required to delete the Message. If deletion fails using this popreceipt then the message has been dequeued by another client. */\n  popReceipt: string;\n  /** The time that the message will again become visible in the Queue. */\n  nextVisibleOn: Date;\n}\n\n/** The object returned in the QueueMessageList array when calling Peek Messages on a Queue */\nexport interface PeekedMessageItem {\n  /** The Id of the Message. */\n  messageId: string;\n  /** The time the Message was inserted into the Queue. */\n  insertedOn: Date;\n  /** The time that the Message will expire and be automatically deleted. */\n  expiresOn: Date;\n  /** The number of times the message has been dequeued. */\n  dequeueCount: number;\n  /** The content of the Message. */\n  messageText: string;\n}\n\n/** Defines headers for Service_setProperties operation. */\nexport interface ServiceSetPropertiesHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_setProperties operation. */\nexport interface ServiceSetPropertiesExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Service_getProperties operation. */\nexport interface ServiceGetPropertiesHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getProperties operation. */\nexport interface ServiceGetPropertiesExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Service_getStatistics operation. */\nexport interface ServiceGetStatisticsHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_getStatistics operation. */\nexport interface ServiceGetStatisticsExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Service_listQueuesSegment operation. */\nexport interface ServiceListQueuesSegmentHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Service_listQueuesSegment operation. */\nexport interface ServiceListQueuesSegmentExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Queue_create operation. */\nexport interface QueueCreateHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Queue_create operation. */\nexport interface QueueCreateExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Queue_delete operation. */\nexport interface QueueDeleteHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Queue_delete operation. */\nexport interface QueueDeleteExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Queue_getProperties operation. */\nexport interface QueueGetPropertiesHeaders {\n  metadata?: { [propertyName: string]: string };\n  /** The approximate number of messages in the queue. This number is not lower than the actual number of messages in the queue, but could be higher. */\n  approximateMessagesCount?: number;\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Queue_getProperties operation. */\nexport interface QueueGetPropertiesExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Queue_setMetadata operation. */\nexport interface QueueSetMetadataHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Queue_setMetadata operation. */\nexport interface QueueSetMetadataExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Queue_getAccessPolicy operation. */\nexport interface QueueGetAccessPolicyHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Queue_getAccessPolicy operation. */\nexport interface QueueGetAccessPolicyExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Queue_setAccessPolicy operation. */\nexport interface QueueSetAccessPolicyHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Queue_setAccessPolicy operation. */\nexport interface QueueSetAccessPolicyExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Messages_dequeue operation. */\nexport interface MessagesDequeueHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Messages_dequeue operation. */\nexport interface MessagesDequeueExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Messages_clear operation. */\nexport interface MessagesClearHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Messages_clear operation. */\nexport interface MessagesClearExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Messages_enqueue operation. */\nexport interface MessagesEnqueueHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Messages_enqueue operation. */\nexport interface MessagesEnqueueExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for Messages_peek operation. */\nexport interface MessagesPeekHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for Messages_peek operation. */\nexport interface MessagesPeekExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for MessageId_update operation. */\nexport interface MessageIdUpdateHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** The pop receipt of the queue message. */\n  popReceipt?: string;\n  /** A UTC date/time value that represents when the message will be visible on the queue. */\n  nextVisibleOn?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for MessageId_update operation. */\nexport interface MessageIdUpdateExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Defines headers for MessageId_delete operation. */\nexport interface MessageIdDeleteHeaders {\n  /** This header uniquely identifies the request that was made and can be used for troubleshooting the request. */\n  requestId?: string;\n  /** Indicates the version of the Queue service used to execute the request. This header is returned for requests made against version 2009-09-19 and above. */\n  version?: string;\n  /** UTC date/time value generated by the service that indicates the time at which the response was initiated */\n  date?: Date;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n  /** Error Code */\n  errorCode?: string;\n}\n\n/** Defines headers for MessageId_delete operation. */\nexport interface MessageIdDeleteExceptionHeaders {\n  errorCode?: string;\n  /** If a client request id header is sent in the request, this header will be present in the response with the same value. */\n  clientRequestId?: string;\n}\n\n/** Known values of {@link StorageErrorCode} that the service accepts. */\nexport enum KnownStorageErrorCode {\n  /** AccountAlreadyExists */\n  AccountAlreadyExists = \"AccountAlreadyExists\",\n  /** AccountBeingCreated */\n  AccountBeingCreated = \"AccountBeingCreated\",\n  /** AccountIsDisabled */\n  AccountIsDisabled = \"AccountIsDisabled\",\n  /** AuthenticationFailed */\n  AuthenticationFailed = \"AuthenticationFailed\",\n  /** AuthorizationFailure */\n  AuthorizationFailure = \"AuthorizationFailure\",\n  /** ConditionHeadersNotSupported */\n  ConditionHeadersNotSupported = \"ConditionHeadersNotSupported\",\n  /** ConditionNotMet */\n  ConditionNotMet = \"ConditionNotMet\",\n  /** EmptyMetadataKey */\n  EmptyMetadataKey = \"EmptyMetadataKey\",\n  /** InsufficientAccountPermissions */\n  InsufficientAccountPermissions = \"InsufficientAccountPermissions\",\n  /** InternalError */\n  InternalError = \"InternalError\",\n  /** InvalidAuthenticationInfo */\n  InvalidAuthenticationInfo = \"InvalidAuthenticationInfo\",\n  /** InvalidHeaderValue */\n  InvalidHeaderValue = \"InvalidHeaderValue\",\n  /** InvalidHttpVerb */\n  InvalidHttpVerb = \"InvalidHttpVerb\",\n  /** InvalidInput */\n  InvalidInput = \"InvalidInput\",\n  /** InvalidMd5 */\n  InvalidMd5 = \"InvalidMd5\",\n  /** InvalidMetadata */\n  InvalidMetadata = \"InvalidMetadata\",\n  /** InvalidQueryParameterValue */\n  InvalidQueryParameterValue = \"InvalidQueryParameterValue\",\n  /** InvalidRange */\n  InvalidRange = \"InvalidRange\",\n  /** InvalidResourceName */\n  InvalidResourceName = \"InvalidResourceName\",\n  /** InvalidUri */\n  InvalidUri = \"InvalidUri\",\n  /** InvalidXmlDocument */\n  InvalidXmlDocument = \"InvalidXmlDocument\",\n  /** InvalidXmlNodeValue */\n  InvalidXmlNodeValue = \"InvalidXmlNodeValue\",\n  /** Md5Mismatch */\n  Md5Mismatch = \"Md5Mismatch\",\n  /** MetadataTooLarge */\n  MetadataTooLarge = \"MetadataTooLarge\",\n  /** MissingContentLengthHeader */\n  MissingContentLengthHeader = \"MissingContentLengthHeader\",\n  /** MissingRequiredQueryParameter */\n  MissingRequiredQueryParameter = \"MissingRequiredQueryParameter\",\n  /** MissingRequiredHeader */\n  MissingRequiredHeader = \"MissingRequiredHeader\",\n  /** MissingRequiredXmlNode */\n  MissingRequiredXmlNode = \"MissingRequiredXmlNode\",\n  /** MultipleConditionHeadersNotSupported */\n  MultipleConditionHeadersNotSupported = \"MultipleConditionHeadersNotSupported\",\n  /** OperationTimedOut */\n  OperationTimedOut = \"OperationTimedOut\",\n  /** OutOfRangeInput */\n  OutOfRangeInput = \"OutOfRangeInput\",\n  /** OutOfRangeQueryParameterValue */\n  OutOfRangeQueryParameterValue = \"OutOfRangeQueryParameterValue\",\n  /** RequestBodyTooLarge */\n  RequestBodyTooLarge = \"RequestBodyTooLarge\",\n  /** ResourceTypeMismatch */\n  ResourceTypeMismatch = \"ResourceTypeMismatch\",\n  /** RequestUrlFailedToParse */\n  RequestUrlFailedToParse = \"RequestUrlFailedToParse\",\n  /** ResourceAlreadyExists */\n  ResourceAlreadyExists = \"ResourceAlreadyExists\",\n  /** ResourceNotFound */\n  ResourceNotFound = \"ResourceNotFound\",\n  /** ServerBusy */\n  ServerBusy = \"ServerBusy\",\n  /** UnsupportedHeader */\n  UnsupportedHeader = \"UnsupportedHeader\",\n  /** UnsupportedXmlNode */\n  UnsupportedXmlNode = \"UnsupportedXmlNode\",\n  /** UnsupportedQueryParameter */\n  UnsupportedQueryParameter = \"UnsupportedQueryParameter\",\n  /** UnsupportedHttpVerb */\n  UnsupportedHttpVerb = \"UnsupportedHttpVerb\",\n  /** InvalidMarker */\n  InvalidMarker = \"InvalidMarker\",\n  /** MessageNotFound */\n  MessageNotFound = \"MessageNotFound\",\n  /** MessageTooLarge */\n  MessageTooLarge = \"MessageTooLarge\",\n  /** PopReceiptMismatch */\n  PopReceiptMismatch = \"PopReceiptMismatch\",\n  /** QueueAlreadyExists */\n  QueueAlreadyExists = \"QueueAlreadyExists\",\n  /** QueueBeingDeleted */\n  QueueBeingDeleted = \"QueueBeingDeleted\",\n  /** QueueDisabled */\n  QueueDisabled = \"QueueDisabled\",\n  /** QueueNotEmpty */\n  QueueNotEmpty = \"QueueNotEmpty\",\n  /** QueueNotFound */\n  QueueNotFound = \"QueueNotFound\",\n  /** AuthorizationSourceIPMismatch */\n  AuthorizationSourceIPMismatch = \"AuthorizationSourceIPMismatch\",\n  /** AuthorizationProtocolMismatch */\n  AuthorizationProtocolMismatch = \"AuthorizationProtocolMismatch\",\n  /** AuthorizationPermissionMismatch */\n  AuthorizationPermissionMismatch = \"AuthorizationPermissionMismatch\",\n  /** AuthorizationServiceMismatch */\n  AuthorizationServiceMismatch = \"AuthorizationServiceMismatch\",\n  /** AuthorizationResourceTypeMismatch */\n  AuthorizationResourceTypeMismatch = \"AuthorizationResourceTypeMismatch\",\n  /** FeatureVersionMismatch */\n  FeatureVersionMismatch = \"FeatureVersionMismatch\"\n}\n\n/**\n * Defines values for StorageErrorCode. \\\n * {@link KnownStorageErrorCode} can be used interchangeably with StorageErrorCode,\n *  this enum contains the known values that the service supports.\n * ### Known values supported by the service\n * **AccountAlreadyExists** \\\n * **AccountBeingCreated** \\\n * **AccountIsDisabled** \\\n * **AuthenticationFailed** \\\n * **AuthorizationFailure** \\\n * **ConditionHeadersNotSupported** \\\n * **ConditionNotMet** \\\n * **EmptyMetadataKey** \\\n * **InsufficientAccountPermissions** \\\n * **InternalError** \\\n * **InvalidAuthenticationInfo** \\\n * **InvalidHeaderValue** \\\n * **InvalidHttpVerb** \\\n * **InvalidInput** \\\n * **InvalidMd5** \\\n * **InvalidMetadata** \\\n * **InvalidQueryParameterValue** \\\n * **InvalidRange** \\\n * **InvalidResourceName** \\\n * **InvalidUri** \\\n * **InvalidXmlDocument** \\\n * **InvalidXmlNodeValue** \\\n * **Md5Mismatch** \\\n * **MetadataTooLarge** \\\n * **MissingContentLengthHeader** \\\n * **MissingRequiredQueryParameter** \\\n * **MissingRequiredHeader** \\\n * **MissingRequiredXmlNode** \\\n * **MultipleConditionHeadersNotSupported** \\\n * **OperationTimedOut** \\\n * **OutOfRangeInput** \\\n * **OutOfRangeQueryParameterValue** \\\n * **RequestBodyTooLarge** \\\n * **ResourceTypeMismatch** \\\n * **RequestUrlFailedToParse** \\\n * **ResourceAlreadyExists** \\\n * **ResourceNotFound** \\\n * **ServerBusy** \\\n * **UnsupportedHeader** \\\n * **UnsupportedXmlNode** \\\n * **UnsupportedQueryParameter** \\\n * **UnsupportedHttpVerb** \\\n * **InvalidMarker** \\\n * **MessageNotFound** \\\n * **MessageTooLarge** \\\n * **PopReceiptMismatch** \\\n * **QueueAlreadyExists** \\\n * **QueueBeingDeleted** \\\n * **QueueDisabled** \\\n * **QueueNotEmpty** \\\n * **QueueNotFound** \\\n * **AuthorizationSourceIPMismatch** \\\n * **AuthorizationProtocolMismatch** \\\n * **AuthorizationPermissionMismatch** \\\n * **AuthorizationServiceMismatch** \\\n * **AuthorizationResourceTypeMismatch** \\\n * **FeatureVersionMismatch**\n */\nexport type StorageErrorCode = string;\n/** Defines values for GeoReplicationStatusType. */\nexport type GeoReplicationStatusType = \"live\" | \"bootstrap\" | \"unavailable\";\n\n/** Optional parameters. */\nexport interface ServiceSetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the setProperties operation. */\nexport type ServiceSetPropertiesResponse = ServiceSetPropertiesHeaders;\n\n/** Optional parameters. */\nexport interface ServiceGetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getProperties operation. */\nexport type ServiceGetPropertiesResponse = ServiceGetPropertiesHeaders &\n  QueueServiceProperties;\n\n/** Optional parameters. */\nexport interface ServiceGetStatisticsOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getStatistics operation. */\nexport type ServiceGetStatisticsResponse = ServiceGetStatisticsHeaders &\n  QueueServiceStatistics;\n\n/** Optional parameters. */\nexport interface ServiceListQueuesSegmentOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Filters the results to return only queues whose name begins with the specified prefix. */\n  prefix?: string;\n  /** A string value that identifies the portion of the list of queues to be returned with the next listing operation. The operation returns the ContinuationToken value within the response body if the listing operation did not return all queues remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client. */\n  marker?: string;\n  /** Specifies the maximum number of queues to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000. */\n  maxPageSize?: number;\n  /** Include this parameter to specify that the queues' metadata be returned as part of the response body. */\n  include?: string[];\n}\n\n/** Contains response data for the listQueuesSegment operation. */\nexport type ServiceListQueuesSegmentResponse = ServiceListQueuesSegmentHeaders &\n  ListQueuesSegmentResponse;\n\n/** Optional parameters. */\nexport interface QueueCreateOptionalParams extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Include this parameter to specify that the queue's metadata be returned as part of the response body. Note that metadata requested with this parameter must be stored in accordance with the naming restrictions imposed by the 2009-09-19 version of the Queue service. Beginning with this version, all metadata names must adhere to the naming conventions for C# identifiers. */\n  metadata?: { [propertyName: string]: string };\n}\n\n/** Contains response data for the create operation. */\nexport type QueueCreateResponse = QueueCreateHeaders;\n\n/** Optional parameters. */\nexport interface QueueDeleteOptionalParams extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the delete operation. */\nexport type QueueDeleteResponse = QueueDeleteHeaders;\n\n/** Optional parameters. */\nexport interface QueueGetPropertiesOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getProperties operation. */\nexport type QueueGetPropertiesResponse = QueueGetPropertiesHeaders;\n\n/** Optional parameters. */\nexport interface QueueSetMetadataOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. Include this parameter to specify that the queue's metadata be returned as part of the response body. Note that metadata requested with this parameter must be stored in accordance with the naming restrictions imposed by the 2009-09-19 version of the Queue service. Beginning with this version, all metadata names must adhere to the naming conventions for C# identifiers. */\n  metadata?: { [propertyName: string]: string };\n}\n\n/** Contains response data for the setMetadata operation. */\nexport type QueueSetMetadataResponse = QueueSetMetadataHeaders;\n\n/** Optional parameters. */\nexport interface QueueGetAccessPolicyOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the getAccessPolicy operation. */\nexport type QueueGetAccessPolicyResponse = QueueGetAccessPolicyHeaders &\n  SignedIdentifier[];\n\n/** Optional parameters. */\nexport interface QueueSetAccessPolicyOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** the acls for the queue */\n  queueAcl?: SignedIdentifier[];\n}\n\n/** Contains response data for the setAccessPolicy operation. */\nexport type QueueSetAccessPolicyResponse = QueueSetAccessPolicyHeaders;\n\n/** Optional parameters. */\nexport interface MessagesDequeueOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. A nonzero integer value that specifies the number of messages to retrieve from the queue, up to a maximum of 32. If fewer are visible, the visible messages are returned. By default, a single message is retrieved from the queue with this operation. */\n  numberOfMessages?: number;\n  /** Optional. Specifies the new visibility timeout value, in seconds, relative to server time. The default value is 30 seconds. A specified value must be larger than or equal to 1 second, and cannot be larger than 7 days, or larger than 2 hours on REST protocol versions prior to version 2011-08-18. The visibility timeout of a message can be set to a value later than the expiry time. */\n  visibilityTimeout?: number;\n}\n\n/** Contains response data for the dequeue operation. */\nexport type MessagesDequeueResponse = MessagesDequeueHeaders &\n  DequeuedMessageItem[];\n\n/** Optional parameters. */\nexport interface MessagesClearOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the clear operation. */\nexport type MessagesClearResponse = MessagesClearHeaders;\n\n/** Optional parameters. */\nexport interface MessagesEnqueueOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. If specified, the request must be made using an x-ms-version of 2011-08-18 or later. If not specified, the default value is 0. Specifies the new visibility timeout value, in seconds, relative to server time. The new value must be larger than or equal to 0, and cannot be larger than 7 days. The visibility timeout of a message cannot be set to a value later than the expiry time. visibilitytimeout should be set to a value smaller than the time-to-live value. */\n  visibilityTimeout?: number;\n  /** Optional. Specifies the time-to-live interval for the message, in seconds. Prior to version 2017-07-29, the maximum time-to-live allowed is 7 days. For version 2017-07-29 or later, the maximum time-to-live can be any positive number, as well as -1 indicating that the message does not expire. If this parameter is omitted, the default time-to-live is 7 days. */\n  messageTimeToLive?: number;\n}\n\n/** Contains response data for the enqueue operation. */\nexport type MessagesEnqueueResponse = MessagesEnqueueHeaders &\n  EnqueuedMessage[];\n\n/** Optional parameters. */\nexport interface MessagesPeekOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. A nonzero integer value that specifies the number of messages to retrieve from the queue, up to a maximum of 32. If fewer are visible, the visible messages are returned. By default, a single message is retrieved from the queue with this operation. */\n  numberOfMessages?: number;\n}\n\n/** Contains response data for the peek operation. */\nexport type MessagesPeekResponse = MessagesPeekHeaders & PeekedMessageItem[];\n\n/** Optional parameters. */\nexport interface MessageIdUpdateOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** A Message object which can be stored in a Queue */\n  queueMessage?: QueueMessage;\n}\n\n/** Contains response data for the update operation. */\nexport type MessageIdUpdateResponse = MessageIdUpdateHeaders;\n\n/** Optional parameters. */\nexport interface MessageIdDeleteOptionalParams\n  extends coreClient.OperationOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations>Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n}\n\n/** Contains response data for the delete operation. */\nexport type MessageIdDeleteResponse = MessageIdDeleteHeaders;\n\n/** Optional parameters. */\nexport interface StorageClientOptionalParams\n  extends coreHttpCompat.ExtendedServiceClientOptions {\n  /** Specifies the version of the operation to use for this request. */\n  version?: string;\n  /** Overrides client endpoint. */\n  endpoint?: string;\n}\n"]}