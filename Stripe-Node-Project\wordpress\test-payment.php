<?php
/**
 * Test Payment Form for Stripe Integration
 * Place this file in your WordPress root and access via browser
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

// Get plugin settings
$options = get_option('stripe_integration_options', array());
$api_base_url = $options['api_base_url'] ?? 'http://localhost:3000';
$api_key = $options['api_key'] ?? '';
$vendor_id = $options['vendor_id'] ?? '';
$test_mode = $options['test_mode'] ?? true;

?>
<!DOCTYPE html>
<html>
<head>
    <title>🧪 Test Stripe Payment Integration</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #0073aa; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .config-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .config-table th, .config-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        .config-table th { background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>🧪 Test Stripe Payment Integration</h1>

    <h2>📋 Current Configuration</h2>
    <table class="config-table">
        <tr><th>Setting</th><th>Value</th><th>Status</th></tr>
        <tr>
            <td><strong>API Base URL</strong></td>
            <td><?php echo esc_html($api_base_url); ?></td>
            <td><?php echo !empty($api_base_url) ? '✅' : '❌'; ?></td>
        </tr>
        <tr>
            <td><strong>API Key</strong></td>
            <td><?php echo !empty($api_key) ? substr($api_key, 0, 15) . '...' : 'Not set'; ?></td>
            <td><?php echo !empty($api_key) ? '✅' : '❌'; ?></td>
        </tr>
        <tr>
            <td><strong>Vendor ID</strong></td>
            <td><?php echo esc_html($vendor_id); ?></td>
            <td><?php echo !empty($vendor_id) ? '✅' : '❌'; ?></td>
        </tr>
        <tr>
            <td><strong>Test Mode</strong></td>
            <td><?php echo $test_mode ? 'Enabled' : 'Disabled'; ?></td>
            <td>✅</td>
        </tr>
    </table>

    <?php if (empty($api_key) || empty($vendor_id)): ?>
        <div class="status error">
            ❌ <strong>Configuration Incomplete!</strong> Please ensure API Key and Vendor ID are set in WordPress admin.
        </div>
    <?php endif; ?>

    <h2>💳 Test Payment Form</h2>
    <div class="status info">
        <strong>Test Mode:</strong> Use test card number <code>****************</code> with any future expiry date and any 3-digit CVC.
    </div>

    <form id="payment-form">
        <div class="form-group">
            <label for="amount">Amount (USD)</label>
            <input type="number" id="amount" name="amount" value="10.00" step="0.01" min="0.50" required>
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <input type="text" id="description" name="description" value="Test Payment" required>
        </div>

        <div class="form-group">
            <label for="customer_email">Customer Email</label>
            <input type="email" id="customer_email" name="customer_email" value="<EMAIL>" required>
        </div>

        <div class="form-group">
            <label for="card_number">Card Number</label>
            <input type="text" id="card_number" name="card_number" value="****************" placeholder="4242 4242 4242 4242" required>
        </div>

        <div style="display: flex; gap: 10px;">
            <div class="form-group" style="flex: 1;">
                <label for="exp_month">Expiry Month</label>
                <select id="exp_month" name="exp_month" required>
                    <?php for($i = 1; $i <= 12; $i++): ?>
                        <option value="<?php echo sprintf('%02d', $i); ?>" <?php echo $i == 12 ? 'selected' : ''; ?>>
                            <?php echo sprintf('%02d', $i); ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>

            <div class="form-group" style="flex: 1;">
                <label for="exp_year">Expiry Year</label>
                <select id="exp_year" name="exp_year" required>
                    <?php for($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                        <option value="<?php echo $i; ?>" <?php echo $i == (date('Y') + 1) ? 'selected' : ''; ?>>
                            <?php echo $i; ?>
                        </option>
                    <?php endfor; ?>
                </select>
            </div>

            <div class="form-group" style="flex: 1;">
                <label for="cvc">CVC</label>
                <input type="text" id="cvc" name="cvc" value="123" placeholder="123" maxlength="4" required>
            </div>
        </div>

        <button type="submit" id="submit-button">💳 Process Test Payment</button>
    </form>

    <div id="payment-result" style="margin-top: 20px;"></div>

    <script>
        // Test if JavaScript is working
        console.log('JavaScript loaded successfully!');

        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM ready!');

            const form = document.getElementById('payment-form');
            if (!form) {
                console.error('Form not found!');
                return;
            }

            console.log('Form found, attaching event listener...');

            form.addEventListener('submit', async function(e) {
            e.preventDefault();

            console.log('Form submitted - JavaScript is working!');

            const submitButton = document.getElementById('submit-button');
            const resultDiv = document.getElementById('payment-result');

            // Check if required configuration is available
            const vendorId = '<?php echo esc_js($vendor_id); ?>';
            const apiKey = '<?php echo esc_js($api_key); ?>';

            if (!vendorId || !apiKey) {
                resultDiv.innerHTML = `
                    <div class="status error">
                        <h3>❌ Configuration Error</h3>
                        <p>Missing required configuration. Please check WordPress admin settings.</p>
                        <p>Vendor ID: ${vendorId ? 'Set' : 'Missing'}</p>
                        <p>API Key: ${apiKey ? 'Set' : 'Missing'}</p>
                    </div>
                `;
                return;
            }

            // Disable button and show loading
            submitButton.disabled = true;
            submitButton.textContent = '⏳ Processing...';
            resultDiv.innerHTML = '<div class="status info">Processing payment...</div>';

            try {
                // Collect form data
                const formData = {
                    amount: parseFloat(document.getElementById('amount').value),
                    description: document.getElementById('description').value,
                    customer_email: document.getElementById('customer_email').value,
                    customer_name: 'Test Customer', // Required field
                    niche: 'other', // Required field
                    order_id: 'WP-' + Date.now(), // Required field
                    card: {
                        number: document.getElementById('card_number').value.replace(/\s/g, ''),
                        exp_month: parseInt(document.getElementById('exp_month').value),
                        exp_year: parseInt(document.getElementById('exp_year').value),
                        cvc: document.getElementById('cvc').value
                    },
                    vendor_id: '<?php echo esc_js($vendor_id); ?>'
                };

                console.log('Sending payment request:', formData);

                // Make API request
                const response = await fetch('<?php echo esc_js($api_base_url); ?>/api/payments/process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': '<?php echo esc_js($api_key); ?>'
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();
                console.log('Payment response:', result);

                if (response.ok && result.success) {
                    resultDiv.innerHTML = `
                        <div class="status success">
                            <h3>✅ Payment Successful!</h3>
                            <p><strong>Transaction ID:</strong> ${result.transaction_id}</p>
                            <p><strong>Amount:</strong> $${result.amount}</p>
                            <p><strong>Commission:</strong> $${result.commission_amount}</p>
                            <p><strong>Vendor Payout:</strong> $${result.vendor_amount}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="status error">
                            <h3>❌ Payment Failed</h3>
                            <p><strong>Error:</strong> ${result.error || result.message || 'Unknown error'}</p>
                            ${result.details ? `<p><strong>Details:</strong> ${result.details}</p>` : ''}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Payment error:', error);
                resultDiv.innerHTML = `
                    <div class="status error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Please check that your backend server is running on ${<?php echo esc_js($api_base_url); ?>}</p>
                    </div>
                `;
            } finally {
                // Re-enable button
                submitButton.disabled = false;
                submitButton.textContent = '💳 Process Test Payment';
            }
        });

        }); // End DOMContentLoaded
    </script>
</body>
</html>