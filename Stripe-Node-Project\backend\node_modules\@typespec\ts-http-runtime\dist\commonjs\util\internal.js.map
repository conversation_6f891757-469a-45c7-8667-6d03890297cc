{"version": 3, "file": "internal.js", "sourceRoot": "", "sources": ["../../../src/util/internal.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAElC,uCAAiD;AAAxC,+GAAA,mBAAmB,OAAA;AAC5B,yCAAwD;AAA/C,sHAAA,yBAAyB,OAAA;AAClC,yCAA2D;AAAlD,qGAAA,QAAQ,OAAA;AACjB,uCAAqC;AAA5B,mGAAA,OAAO,OAAA;AAChB,yCAAmE;AAA1D,8GAAA,iBAAiB,OAAA;AAAE,8GAAA,iBAAiB,OAAA;AAC7C,+CAA4C;AAAnC,0GAAA,UAAU,OAAA;AACnB,6DAQ+B;AAP7B,gHAAA,SAAS,OAAA;AACT,4GAAA,KAAK,OAAA;AACL,iHAAA,UAAU,OAAA;AACV,oHAAA,aAAa,OAAA;AACb,6GAAA,MAAM,OAAA;AACN,oHAAA,aAAa,OAAA;AACb,kHAAA,WAAW,OAAA;AAEb,uDAA+F;AAAtF,sHAAA,kBAAkB,OAAA;AAAE,sHAAA,kBAAkB,OAAA;AAC/C,+CAAkE;AAAzD,yGAAA,SAAS,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { calculateRetryDelay } from \"./delay.js\";\nexport { getRandomIntegerInclusive } from \"./random.js\";\nexport { isObject, type UnknownObject } from \"./object.js\";\nexport { isError } from \"./error.js\";\nexport { computeSha256Hash, computeSha256Hmac } from \"./sha256.js\";\nexport { randomUUID } from \"./uuidUtils.js\";\nexport {\n  isBrowser,\n  isBun,\n  isNodeLike,\n  isNodeRuntime,\n  isDeno,\n  isReactNative,\n  isWebWorker,\n} from \"./checkEnvironment.js\";\nexport { stringToUint8Array, uint8ArrayToString, type EncodingType } from \"./bytesEncoding.js\";\nexport { Sanitizer, type SanitizerOptions } from \"./sanitizer.js\";\n"]}