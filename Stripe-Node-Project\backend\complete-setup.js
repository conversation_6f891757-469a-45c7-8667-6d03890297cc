// Complete setup: Create user, vendor, and API key properly
require('dotenv').config();
const { generateApiKey } = require('./src/middleware/auth');
const { supabaseAdmin } = require('./src/config/database');
const crypto = require('crypto');

async function completeSetup() {
    console.log('🚀 Starting complete setup...\n');
    
    try {
        // Step 1: Create user
        console.log('👤 Creating user...');
        const userId = crypto.randomUUID();
        const userEmail = 'wordpress@localhost';
        const wordpressSite = 'http://localhost/Stripe-Node-Project/wordpress';
        
        const { data: userData, error: userError } = await supabaseAdmin
            .from('users')
            .insert({
                id: userId,
                email: userEmail,
                wordpress_site: wordpressSite,
                created_at: new Date().toISOString()
            })
            .select()
            .single();
            
        if (userError) {
            console.error('❌ Error creating user:', userError);
            return;
        }
        
        console.log('✅ User created successfully!');
        console.log(`   ID: ${userData.id}`);
        console.log(`   Email: ${userData.email}`);
        
        // Step 2: Create vendor
        console.log('\n🏪 Creating vendor...');
        const vendorId = '4b65134b-88f1-40a8-951b-694876338cd8'; // Use the same vendor ID
        
        const { data: vendorData, error: vendorError } = await supabaseAdmin
            .from('vendors')
            .insert({
                id: vendorId,
                user_id: userId,
                business_name: 'Test WordPress Store',
                business_email: userEmail,
                business_niche: 'other',
                status: 'active',
                created_at: new Date().toISOString()
            })
            .select()
            .single();
            
        if (vendorError) {
            console.error('❌ Error creating vendor:', vendorError);
            return;
        }
        
        console.log('✅ Vendor created successfully!');
        console.log(`   ID: ${vendorData.id}`);
        console.log(`   Business Name: ${vendorData.business_name}`);
        
        // Step 3: Generate API key
        console.log('\n🔑 Generating API key...');
        const apiKeyData = {
            user_id: userId,
            wordpress_site: wordpressSite,
            permissions: ['payments', 'commissions', 'vendors']
        };
        
        const apiKey = await generateApiKey(apiKeyData);
        
        console.log('✅ API key generated successfully!');
        console.log(`🔑 API Key: ${apiKey}`);
        
        // Step 4: Verify everything
        console.log('\n🔍 Verifying setup...');
        
        // Check user
        const { data: userCheck } = await supabaseAdmin
            .from('users')
            .select('*')
            .eq('id', userId)
            .single();

        // Check vendor
        const { data: vendorCheck } = await supabaseAdmin
            .from('vendors')
            .select('*')
            .eq('id', vendorId)
            .single();

        // Check API key
        const { data: apiKeyCheck } = await supabaseAdmin
            .from('api_keys')
            .select('*')
            .eq('key_hash', apiKey)
            .single();
            
        if (userCheck && vendorCheck && apiKeyCheck) {
            console.log('✅ All components verified successfully!');
            
            console.log('\n📋 Setup Summary:');
            console.log('==================');
            console.log(`User ID: ${userCheck.id}`);
            console.log(`User Email: ${userCheck.email}`);
            console.log(`Vendor ID: ${vendorCheck.id}`);
            console.log(`Business Name: ${vendorCheck.business_name}`);
            console.log(`API Key: ${apiKey}`);
            console.log(`WordPress Site: ${userCheck.wordpress_site}`);
            
            console.log('\n🎯 WordPress Plugin Settings:');
            console.log('==============================');
            console.log(`API Base URL: http://localhost:3000`);
            console.log(`API Key: ${apiKey}`);
            console.log(`Vendor ID: ${vendorId}`);
            console.log(`Business Niche: other`);
            console.log(`Test Mode: true`);
            
        } else {
            console.log('❌ Verification failed - some components missing');
        }
        
    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        console.error(error.stack);
    }
}

completeSetup();
