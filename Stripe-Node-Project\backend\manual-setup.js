#!/usr/bin/env node

/**
 * Manual setup script to create API key and vendor directly in Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const crypto = require('crypto');

// Load environment variables
require('dotenv').config();

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function manualSetup() {
    console.log('🚀 Manual WordPress Integration Setup...\n');

    try {
        // Step 1: Create user record
        console.log('📝 Step 1: Creating user record...');
        
        let user;
        const { data: userData, error: userError } = await supabase
            .from('users')
            .insert({
                wordpress_site: 'http://localhost/Stripe-Node-Project/wordpress',
                email: 'admin@localhost'
            })
            .select()
            .single();

        if (userError) {
            console.log('User might already exist, trying to fetch...');
            const { data: existingUser, error: fetchError } = await supabase
                .from('users')
                .select('*')
                .eq('wordpress_site', 'http://localhost/Stripe-Node-Project/wordpress')
                .single();
            
            if (fetchError) {
                throw fetchError;
            }
            user = existingUser;
        } else {
            user = userData;
        }

        console.log('✅ User created/found:', user.id);

        // Step 2: Generate API key
        console.log('📝 Step 2: Generating API key...');
        
        const apiKey = 'sk_api_' + crypto.randomBytes(32).toString('hex');
        const keyHash = crypto.createHash('sha256').update(apiKey).digest('hex');

        const { data: apiKeyRecord, error: apiKeyError } = await supabase
            .from('api_keys')
            .insert({
                user_id: user.id,
                key_hash: keyHash,
                wordpress_site: 'http://localhost/Stripe-Node-Project/wordpress',
                permissions: ['payments', 'commissions', 'vendors'],
                description: 'WordPress Plugin Integration - Manual Setup'
            })
            .select()
            .single();

        if (apiKeyError) {
            throw apiKeyError;
        }

        console.log('✅ API Key generated successfully!');

        // Step 3: Create vendor
        console.log('📝 Step 3: Creating vendor account...');
        
        let vendor;
        const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .insert({
                name: 'dkheterpal',
                email: '<EMAIL>',
                niche: 'other',
                country: 'US',
                business_type: 'individual'
            })
            .select()
            .single();

        if (vendorError) {
            console.log('Vendor might already exist, trying to fetch...');
            const { data: existingVendor, error: fetchVendorError } = await supabase
                .from('vendors')
                .select('*')
                .eq('email', '<EMAIL>')
                .single();
            
            if (fetchVendorError) {
                throw fetchVendorError;
            }
            vendor = existingVendor;
        } else {
            vendor = vendorData;
        }

        console.log('✅ Vendor created/found:', vendor.id);

        // Display results
        console.log('\n🎯 WordPress Plugin Configuration:');
        console.log('=====================================');
        console.log(`API Base URL: http://localhost:3000`);
        console.log(`API Key: ${apiKey}`);
        console.log(`Vendor ID: ${vendor.id}`);
        console.log(`Business Niche: Other`);
        console.log(`Test Mode: ✅ Enabled`);
        console.log('=====================================\n');

        console.log('📋 Next Steps:');
        console.log('1. Copy the API Key and Vendor ID above');
        console.log('2. Go to your WordPress admin: http://localhost/Stripe-Node-Project/wordpress/wp-admin/');
        console.log('3. Navigate to the Stripe Integration Settings');
        console.log('4. Paste the API Key and Vendor ID');
        console.log('5. Save the settings');
        console.log('6. Test a payment!\n');

        console.log('🎉 Manual setup completed successfully!');

    } catch (error) {
        console.error('❌ Setup failed:', error);
    }
}

// Run the setup
manualSetup();
