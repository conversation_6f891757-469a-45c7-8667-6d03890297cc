// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
/**
 * The programmatic identifier of the storageSharedKeyCredentialPolicy.
 */
export const storageSharedKeyCredentialPolicyName = "storageSharedKeyCredentialPolicy";
/**
 * storageSharedKeyCredentialPolicy handles signing requests using storage account keys.
 */
export function storageSharedKeyCredentialPolicy(_options) {
    return {
        name: storageSharedKeyCredentialPolicyName,
        async sendRequest(request, next) {
            return next(request);
        },
    };
}
//# sourceMappingURL=StorageSharedKeyCredentialPolicyV2.browser.js.map