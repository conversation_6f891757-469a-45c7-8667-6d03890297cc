# Stripe Integration API Documentation

## Overview

The Stripe Integration API provides secure payment processing with dynamic commission handling based on business niches. This RESTful API supports Stripe Connect with Express accounts for vendor payouts.

**Base URL**: `http://localhost:3000/api` (development) | `https://your-domain.com/api` (production)

## Authentication

### JWT Token Authentication
```http
Authorization: Bearer <jwt_token>
```

### API Key Authentication (WordPress Plugin)
```http
X-API-Key: <api_key>
```

### Request Signature (Optional)
```http
X-Signature: <hmac_sha256_signature>
```

## Rate Limiting

- **General API**: 100 requests per 15 minutes
- **Payment Endpoints**: 20 requests per 15 minutes  
- **Authentication**: 5 requests per 15 minutes
- **Webhooks**: 50 requests per minute
- **Commission**: 30 requests per 15 minutes

## Endpoints

### Health Check

#### GET /health
Check API server status.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "environment": "development"
}
```

### API Information

#### GET /api
Get API information and available endpoints.

**Response:**
```json
{
  "name": "Stripe Integration API",
  "version": "1.0.0",
  "description": "Secure payment processing with commission handling",
  "endpoints": [
    "/health",
    "/api/auth",
    "/api/vendors",
    "/api/payment-intents",
    "/api/commission-rates",
    "/api/webhooks/stripe"
  ]
}
```

### Authentication

#### POST /api/auth/login
Authenticate user and get JWT token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "role": "admin"
  },
  "expires_in": 86400
}
```

#### POST /api/auth/api-key
Generate API key for WordPress integration.

**Headers:** `Authorization: Bearer <jwt_token>`

**Request:**
```json
{
  "name": "WordPress Site",
  "permissions": ["payment_intents", "commission_rates"]
}
```

**Response:**
```json
{
  "success": true,
  "api_key": "sk_api_1234567890abcdef",
  "permissions": ["payment_intents", "commission_rates"],
  "created_at": "2024-01-15T10:30:00Z"
}
```

### Vendors

#### POST /api/vendors
Create a new vendor.

**Headers:** `Authorization: Bearer <jwt_token>`

**Request:**
```json
{
  "name": "Restaurant ABC",
  "email": "<EMAIL>",
  "niche": "restaurant",
  "country": "US",
  "business_type": "company",
  "commission_rate": 0.03
}
```

**Response:**
```json
{
  "success": true,
  "vendor": {
    "id": "vendor_123",
    "name": "Restaurant ABC",
    "email": "<EMAIL>",
    "niche": "restaurant",
    "country": "US",
    "business_type": "company",
    "commission_rate": 0.03,
    "stripe_account_id": "acct_1234567890",
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

#### GET /api/vendors
List all vendors.

**Headers:** `Authorization: Bearer <jwt_token>`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `niche` (optional): Filter by business niche

**Response:**
```json
{
  "success": true,
  "vendors": [
    {
      "id": "vendor_123",
      "name": "Restaurant ABC",
      "niche": "restaurant",
      "commission_rate": 0.03,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

#### GET /api/vendors/:id
Get vendor details.

**Headers:** `Authorization: Bearer <jwt_token>`

**Response:**
```json
{
  "success": true,
  "vendor": {
    "id": "vendor_123",
    "name": "Restaurant ABC",
    "email": "<EMAIL>",
    "niche": "restaurant",
    "commission_rate": 0.03,
    "stripe_account_id": "acct_1234567890",
    "total_transactions": 150,
    "total_revenue": 45000.00,
    "total_commission": 1350.00
  }
}
```

### Payment Intents

#### POST /api/payment-intents
Create a new payment intent.

**Headers:** 
- `Authorization: Bearer <jwt_token>` OR `X-API-Key: <api_key>`

**Request:**
```json
{
  "amount": 2500,
  "currency": "usd",
  "vendor_id": "vendor_123",
  "order_id": "order_456",
  "customer_email": "<EMAIL>",
  "customer_name": "John Doe",
  "description": "Restaurant order #456",
  "metadata": {
    "order_type": "delivery",
    "restaurant_id": "rest_789"
  }
}
```

**Response:**
```json
{
  "success": true,
  "payment_intent": {
    "id": "pi_1234567890",
    "client_secret": "pi_1234567890_secret_abcdef",
    "amount": 2500,
    "currency": "usd",
    "status": "requires_payment_method"
  },
  "commission": {
    "rate": 0.03,
    "amount": 75,
    "vendor_amount": 2425
  },
  "vendor": {
    "id": "vendor_123",
    "name": "Restaurant ABC"
  }
}
```

#### GET /api/payment-intents/:id
Get payment intent details.

**Headers:** `Authorization: Bearer <jwt_token>`

**Response:**
```json
{
  "success": true,
  "payment_intent": {
    "id": "pi_1234567890",
    "amount": 2500,
    "currency": "usd",
    "status": "succeeded",
    "vendor_id": "vendor_123",
    "order_id": "order_456",
    "commission_amount": 75,
    "vendor_amount": 2425,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
}
```

### Commission Rates

#### GET /api/commission-rates
Get commission rates by business niche.

**Headers:** `X-API-Key: <api_key>` (WordPress integration)

**Response:**
```json
{
  "success": true,
  "commission_rates": {
    "grocery": 0.025,
    "restaurant": 0.03,
    "catering": 0.035,
    "retail": 0.028,
    "other": 0.03
  },
  "default_rate": 0.03
}
```

#### POST /api/commission-rates/calculate
Calculate commission for a payment amount.

**Headers:** `X-API-Key: <api_key>`

**Request:**
```json
{
  "amount": 2500,
  "vendor_id": "vendor_123",
  "niche": "restaurant"
}
```

**Response:**
```json
{
  "success": true,
  "calculation": {
    "amount": 2500,
    "commission_rate": 0.03,
    "commission_amount": 75,
    "vendor_amount": 2425,
    "niche": "restaurant"
  }
}
```

### Webhooks

#### POST /api/webhooks/stripe
Handle Stripe webhook events.

**Headers:** `Stripe-Signature: <stripe_signature>`

**Request:** (Stripe webhook payload)

**Response:**
```json
{
  "received": true
}
```

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": {
      "field": "amount",
      "issue": "Amount must be greater than 0"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes

- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **422**: Validation Error
- **429**: Rate Limit Exceeded
- **500**: Internal Server Error

### Error Codes

- `VALIDATION_ERROR`: Invalid request data
- `AUTHENTICATION_FAILED`: Invalid credentials
- `AUTHORIZATION_FAILED`: Insufficient permissions
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `VENDOR_NOT_FOUND`: Vendor does not exist
- `PAYMENT_FAILED`: Payment processing failed
- `STRIPE_ERROR`: Stripe API error
- `DATABASE_ERROR`: Database operation failed

## Security Headers

All responses include security headers:
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

## Request/Response Examples

### Complete Payment Flow

1. **Get Commission Rate:**
```bash
curl -X GET "http://localhost:3000/api/commission-rates" \
  -H "X-API-Key: your_api_key"
```

2. **Create Payment Intent:**
```bash
curl -X POST "http://localhost:3000/api/payment-intents" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "amount": 2500,
    "currency": "usd",
    "vendor_id": "vendor_123",
    "order_id": "order_456",
    "customer_email": "<EMAIL>"
  }'
```

3. **Confirm Payment (Frontend):**
```javascript
const {error} = await stripe.confirmCardPayment(clientSecret, {
  payment_method: {
    card: cardElement,
    billing_details: {
      name: 'John Doe',
      email: '<EMAIL>'
    }
  }
});
```

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  headers: {
    'X-API-Key': 'your_api_key',
    'Content-Type': 'application/json'
  }
});

// Create payment intent
const response = await api.post('/payment-intents', {
  amount: 2500,
  currency: 'usd',
  vendor_id: 'vendor_123',
  order_id: 'order_456',
  customer_email: '<EMAIL>'
});
```

### PHP (WordPress)
```php
$response = wp_remote_post('http://localhost:3000/api/payment-intents', [
    'headers' => [
        'X-API-Key' => 'your_api_key',
        'Content-Type' => 'application/json'
    ],
    'body' => json_encode([
        'amount' => 2500,
        'currency' => 'usd',
        'vendor_id' => 'vendor_123',
        'order_id' => 'order_456',
        'customer_email' => '<EMAIL>'
    ])
]);
```

## Testing

### Test Environment
- **Base URL**: `http://localhost:3000/api`
- **Test API Key**: Use generated test keys
- **Stripe Test Mode**: Use test credit cards

### Test Credit Cards
- **Success**: ****************
- **Declined**: ****************
- **Insufficient Funds**: ****************

## Support

- **Documentation**: See project README.md
- **Security**: See SECURITY.md
- **Testing**: See TESTING_GUIDE.md
