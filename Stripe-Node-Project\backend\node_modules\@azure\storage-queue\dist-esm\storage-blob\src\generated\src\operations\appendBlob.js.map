{"version": 3, "file": "appendBlob.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/operations/appendBlob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AAEjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAanD,8CAA8C;AAC9C,MAAM,OAAO,cAAc;IAGzB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACH,MAAM,CACJ,aAAqB,EACrB,OAAwC;QAExC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,OAAO,EAAE,EAC1B,mBAAmB,CACpB,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACH,WAAW,CACT,aAAqB,EACrB,IAAsC,EACtC,OAA6C;QAE7C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,EAChC,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACH,kBAAkB,CAChB,SAAiB,EACjB,aAAqB,EACrB,OAAoD;QAEpD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,EACrC,+BAA+B,CAChC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,IAAI,CACF,OAAsC;QAEtC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC1E,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,uBAAuB;SAC/C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,KAAK;IAC7B,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,yBAAyB;QACpC,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,cAAc;KAC1B;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,QAAQ;IACnB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,+BAA+B,GAA6B;IAChE,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4CAA4C;SACpE;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,qBAAqB;QAChC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,kBAAkB;QAC7B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,YAAY;KACxB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,iBAAiB,GAA6B;IAClD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,qBAAqB;SAC7C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,cAAc;KAC1B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { AppendBlob } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  AppendBlobCreateOptionalParams,\n  AppendBlobCreateResponse,\n  AppendBlobAppendBlockOptionalParams,\n  AppendBlobAppendBlockResponse,\n  AppendBlobAppendBlockFromUrlOptionalParams,\n  AppendBlobAppendBlockFromUrlResponse,\n  AppendBlobSealOptionalParams,\n  AppendBlobSealResponse,\n} from \"../models\";\n\n/** Class containing AppendBlob operations. */\nexport class AppendBlobImpl implements AppendBlob {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class AppendBlob class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * The Create Append Blob operation creates a new append blob.\n   * @param contentLength The length of the request.\n   * @param options The options parameters.\n   */\n  create(\n    contentLength: number,\n    options?: AppendBlobCreateOptionalParams,\n  ): Promise<AppendBlobCreateResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, options },\n      createOperationSpec,\n    );\n  }\n\n  /**\n   * The Append Block operation commits a new block of data to the end of an existing append blob. The\n   * Append Block operation is permitted only if the blob was created with x-ms-blob-type set to\n   * AppendBlob. Append Block is supported only on version 2015-02-21 version or later.\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  appendBlock(\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: AppendBlobAppendBlockOptionalParams,\n  ): Promise<AppendBlobAppendBlockResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, body, options },\n      appendBlockOperationSpec,\n    );\n  }\n\n  /**\n   * The Append Block operation commits a new block of data to the end of an existing append blob where\n   * the contents are read from a source url. The Append Block operation is permitted only if the blob\n   * was created with x-ms-blob-type set to AppendBlob. Append Block is supported only on version\n   * 2015-02-21 version or later.\n   * @param sourceUrl Specify a URL to the copy source.\n   * @param contentLength The length of the request.\n   * @param options The options parameters.\n   */\n  appendBlockFromUrl(\n    sourceUrl: string,\n    contentLength: number,\n    options?: AppendBlobAppendBlockFromUrlOptionalParams,\n  ): Promise<AppendBlobAppendBlockFromUrlResponse> {\n    return this.client.sendOperationRequest(\n      { sourceUrl, contentLength, options },\n      appendBlockFromUrlOperationSpec,\n    );\n  }\n\n  /**\n   * The Seal operation seals the Append Blob to make it read-only. Seal is supported only on version\n   * 2019-12-12 version or later.\n   * @param options The options parameters.\n   */\n  seal(\n    options?: AppendBlobSealOptionalParams,\n  ): Promise<AppendBlobSealResponse> {\n    return this.client.sendOperationRequest({ options }, sealOperationSpec);\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst createOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.AppendBlobCreateHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.AppendBlobCreateExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobCacheControl,\n    Parameters.blobContentType,\n    Parameters.blobContentMD5,\n    Parameters.blobContentEncoding,\n    Parameters.blobContentLanguage,\n    Parameters.blobContentDisposition,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n    Parameters.encryptionScope,\n    Parameters.blobTagsString,\n    Parameters.legalHold1,\n    Parameters.blobType1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst appendBlockOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.AppendBlobAppendBlockHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.AppendBlobAppendBlockExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.body1,\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp22],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n    Parameters.transactionalContentMD5,\n    Parameters.transactionalContentCrc64,\n    Parameters.contentType1,\n    Parameters.accept2,\n    Parameters.maxSize,\n    Parameters.appendPosition,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"binary\",\n  serializer: xmlSerializer,\n};\nconst appendBlockFromUrlOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.AppendBlobAppendBlockFromUrlHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.AppendBlobAppendBlockFromUrlExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp22],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n    Parameters.sourceIfModifiedSince,\n    Parameters.sourceIfUnmodifiedSince,\n    Parameters.sourceIfMatch,\n    Parameters.sourceIfNoneMatch,\n    Parameters.sourceContentMD5,\n    Parameters.copySourceAuthorization,\n    Parameters.transactionalContentMD5,\n    Parameters.sourceUrl,\n    Parameters.sourceContentCrc64,\n    Parameters.maxSize,\n    Parameters.appendPosition,\n    Parameters.sourceRange1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst sealOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.AppendBlobSealHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.AppendBlobSealExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp23],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.appendPosition,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\n"]}