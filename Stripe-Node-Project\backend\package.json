{"name": "stripe-integration-backend", "version": "1.0.0", "description": "Backend API for WordPress Stripe integration with commission handling", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:security": "jest tests/security.test.js", "test:integration": "jest tests/integration.test.js", "test:load": "artillery run tests/load-test.yml", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "audit": "npm audit", "audit:fix": "npm audit fix"}, "keywords": ["stripe", "wordpress", "payments", "commission", "api"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.38.4", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "stripe": "^14.9.0", "validator": "^13.15.15"}, "devDependencies": {"artillery": "^2.0.0", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}