{"version": 3, "file": "index.browser.js", "sourceRoot": "", "sources": ["../../../src/index.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AAEtD,cAAc,qBAAqB,CAAC;AACpC,cAAc,WAAW,CAAC;AAC1B,cAAc,mBAAmB,CAAC;AAClC,cAAc,mBAAmB,CAAC;AAClC,cAAc,aAAa,CAAC;AAC5B,cAAc,mBAAmB,CAAC;AAClC,cAAc,iBAAiB,CAAC;AAChC,cAAc,+BAA+B,CAAC;AAC9C,cAAc,mCAAmC,CAAC;AAClD,cAAc,0BAA0B,CAAC;AAGzC,OAAO,EACL,aAAa,EAEb,mBAAmB,GAOpB,MAAM,UAAU,CAAC;AAClB,OAAO,EACL,QAAQ,EAGR,cAAc,EACd,WAAW,EAUX,kBAAkB,GAEnB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,cAAc,sCAAsC,CAAC;AACrD,cAAc,6BAA6B,CAAC;AAC5C,cAAc,6BAA6B,CAAC;AAE5C,cAAc,mBAAmB,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,CAAC;AAMrB,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { RestError } from \"@azure/core-rest-pipeline\";\n\nexport * from \"./BlobServiceClient\";\nexport * from \"./Clients\";\nexport * from \"./ContainerClient\";\nexport * from \"./BlobLeaseClient\";\nexport * from \"./BlobBatch\";\nexport * from \"./BlobBatchClient\";\nexport * from \"./BatchResponse\";\nexport * from \"./StorageBrowserPolicyFactory\";\nexport * from \"./credentials/AnonymousCredential\";\nexport * from \"./credentials/Credential\";\nexport { SasIPRange } from \"./sas/SasIPRange\";\nexport { Range } from \"./Range\";\nexport {\n  BlockBlobTier,\n  BlobImmutabilityPolicy,\n  PremiumPageBlobTier,\n  Tags,\n  TagConditions,\n  ContainerRequestConditions,\n  HttpAuthorization,\n  ModificationConditions,\n  MatchConditions,\n} from \"./models\";\nexport {\n  Pipeline,\n  PipelineLike,\n  PipelineOptions,\n  isPipelineLike,\n  newPipeline,\n  StoragePipelineOptions,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions,\n  WebResource,\n  HttpOperationResponse,\n  HttpHeaders,\n  HttpRequestBody,\n  IHttpClient,\n  StorageOAuthScopes,\n  ServiceClientOptions,\n} from \"./Pipeline\";\nexport { BaseRequestPolicy } from \"./policies/RequestPolicy\";\nexport * from \"./policies/AnonymousCredentialPolicy\";\nexport * from \"./policies/CredentialPolicy\";\nexport * from \"./StorageRetryPolicyFactory\";\nexport { CommonOptions } from \"./StorageClient\";\nexport * from \"./generatedModels\";\nexport { RestError };\nexport {\n  PageBlobGetPageRangesDiffResponse,\n  PageBlobGetPageRangesResponse,\n  PageList,\n} from \"./PageBlobRangeResponse\";\nexport { logger } from \"./log\";\n"]}