<?php
/**
 * Debug WordPress Stripe Integration Settings
 * Place this file in your WordPress root and access via browser
 */

// Load WordPress
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>🔍 WordPress Stripe Integration Debug</h1>";

// Get the settings using WordPress functions
$options = get_option('stripe_integration_options', array());

echo "<h2>📋 Current Settings</h2>";
if (!empty($options)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Setting</th><th>Value</th></tr>";
    
    foreach ($options as $key => $value) {
        echo "<tr>";
        echo "<td><strong>" . esc_html(ucfirst(str_replace('_', ' ', $key))) . "</strong></td>";
        
        if ($key === 'api_key' && !empty($value)) {
            echo "<td>" . esc_html(substr($value, 0, 15)) . "... (hidden for security)</td>";
        } else {
            echo "<td>" . esc_html($value) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No settings found!</p>";
}

echo "<h2>🎯 Expected Values</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Setting</th><th>Expected Value</th></tr>";
echo "<tr><td><strong>API Base URL</strong></td><td>http://localhost:3000</td></tr>";
echo "<tr><td><strong>API Key</strong></td><td>sk_api_7036c6f38cab187f402a7c37e701554be2cd1a246c21bb384ad7ca594759c0ae</td></tr>";
echo "<tr><td><strong>Vendor ID</strong></td><td>4b65134b-88f1-40a8-951b-694876338cd8</td></tr>";
echo "<tr><td><strong>Business Niche</strong></td><td>other</td></tr>";
echo "<tr><td><strong>Test Mode</strong></td><td>1 (true)</td></tr>";
echo "</table>";

echo "<h2>🔧 Quick Fix</h2>";
echo "<p>If settings are empty, click the button below to manually set them:</p>";

if (isset($_POST['fix_settings'])) {
    $new_options = array(
        'api_base_url' => 'http://localhost:3000',
        'api_key' => 'sk_api_7036c6f38cab187f402a7c37e701554be2cd1a246c21bb384ad7ca594759c0ae',
        'vendor_id' => '4b65134b-88f1-40a8-951b-694876338cd8',
        'niche' => 'other',
        'test_mode' => true
    );
    
    $result = update_option('stripe_integration_options', $new_options);
    
    if ($result) {
        echo "<p style='color: green;'>✅ Settings updated successfully! Refresh this page to see the changes.</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to update settings.</p>";
    }
}

echo "<form method='post'>";
echo "<input type='submit' name='fix_settings' value='Fix Settings Now' style='background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer;' />";
echo "</form>";

echo "<h2>🔍 Database Info</h2>";
global $wpdb;
echo "<p><strong>Database Name:</strong> " . DB_NAME . "</p>";
echo "<p><strong>Table Prefix:</strong> " . $wpdb->prefix . "</p>";
echo "<p><strong>Options Table:</strong> " . $wpdb->prefix . "options</p>";

// Check if options table exists
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->prefix}options'");
if ($table_exists) {
    echo "<p style='color: green;'>✅ Options table exists</p>";
    
    // Count total options
    $option_count = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}options");
    echo "<p><strong>Total options in database:</strong> " . $option_count . "</p>";
} else {
    echo "<p style='color: red;'>❌ Options table does not exist</p>";
}
?>
