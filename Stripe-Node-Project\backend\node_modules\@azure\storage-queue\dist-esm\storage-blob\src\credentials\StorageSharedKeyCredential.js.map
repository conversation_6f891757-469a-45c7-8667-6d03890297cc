{"version": 3, "file": "StorageSharedKeyCredential.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/credentials/StorageSharedKeyCredential.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAMpC,OAAO,EAAE,gCAAgC,EAAE,MAAM,8CAA8C,CAAC;AAChG,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAE1C;;;;GAIG;AACH,MAAM,OAAO,0BAA2B,SAAQ,UAAU;IAWxD;;;;OAIG;IACH,YAAY,WAAmB,EAAE,UAAkB;QACjD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACI,MAAM,CACX,UAAyB,EACzB,OAA6B;QAE7B,OAAO,IAAI,gCAAgC,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACI,iBAAiB,CAAC,YAAoB;QAC3C,OAAO,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC7F,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { createHmac } from \"crypto\";\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n} from \"@azure/core-http-compat\";\n\nimport { StorageSharedKeyCredentialPolicy } from \"../policies/StorageSharedKeyCredentialPolicy\";\nimport { Credential } from \"./Credential\";\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * StorageSharedKeyCredential for account key authorization of Azure Storage service.\n */\nexport class StorageSharedKeyCredential extends Credential {\n  /**\n   * Azure Storage account name; readonly.\n   */\n  public readonly accountName: string;\n\n  /**\n   * Azure Storage account key; readonly.\n   */\n  private readonly accountKey: Buffer;\n\n  /**\n   * Creates an instance of StorageSharedKeyCredential.\n   * @param accountName -\n   * @param accountKey -\n   */\n  constructor(accountName: string, accountKey: string) {\n    super();\n    this.accountName = accountName;\n    this.accountKey = Buffer.from(accountKey, \"base64\");\n  }\n\n  /**\n   * Creates a StorageSharedKeyCredentialPolicy object.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  public create(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n  ): StorageSharedKeyCredentialPolicy {\n    return new StorageSharedKeyCredentialPolicy(nextPolicy, options, this);\n  }\n\n  /**\n   * Generates a hash signature for an HTTP request or for a SAS.\n   *\n   * @param stringToSign -\n   */\n  public computeHMACSHA256(stringToSign: string): string {\n    return createHmac(\"sha256\", this.accountKey).update(stringToSign, \"utf8\").digest(\"base64\");\n  }\n}\n"]}