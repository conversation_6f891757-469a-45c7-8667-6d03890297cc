{"version": 3, "file": "AccountSASPermissions.js", "sourceRoot": "", "sources": ["../../../src/AccountSASPermissions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;GAQG;AACH,MAAM,OAAO,qBAAqB;IAAlC;QA2CE;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,UAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;IA2ClC,CAAC;IA3HC;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,WAAmB;QACrC,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAE1D,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC;YAC5B,QAAQ,CAAC,EAAE,CAAC;gBACV,KAAK,GAAG;oBACN,qBAAqB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAClC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC;oBACnC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAClC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,GAAG,GAAG,IAAI,CAAC;oBACjC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACrC,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IA0CD;;;;;;;;;OASG;IACI,QAAQ;QACb,iFAAiF;QACjF,yFAAyF;QACzF,iFAAiF;QACjF,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * This is a helper class to construct a string representing the permissions granted by an AccountSAS. Setting a value\n * to true means that any SAS which uses these permissions will grant permissions for that operation. Once all the\n * values are set, this should be serialized with toString and set as the permissions field on an\n * {@link AccountSASSignatureValues} object. It is possible to construct the permissions string without this class, but\n * the order of the permissions is particular and this class guarantees correctness.\n */\nexport class AccountSASPermissions {\n  /**\n   * Parse initializes the AccountSASPermissions fields from a string.\n   *\n   * @param permissions -\n   */\n  public static parse(permissions: string): AccountSASPermissions {\n    const accountSASPermissions = new AccountSASPermissions();\n\n    for (const c of permissions) {\n      switch (c) {\n        case \"r\":\n          accountSASPermissions.read = true;\n          break;\n        case \"w\":\n          accountSASPermissions.write = true;\n          break;\n        case \"d\":\n          accountSASPermissions.delete = true;\n          break;\n        case \"l\":\n          accountSASPermissions.list = true;\n          break;\n        case \"a\":\n          accountSASPermissions.add = true;\n          break;\n        case \"c\":\n          accountSASPermissions.create = true;\n          break;\n        case \"u\":\n          accountSASPermissions.update = true;\n          break;\n        case \"p\":\n          accountSASPermissions.process = true;\n          break;\n        default:\n          throw new RangeError(`Invalid permission character: ${c}`);\n      }\n    }\n\n    return accountSASPermissions;\n  }\n\n  /**\n   * Permission to read resources granted.\n   */\n  public read: boolean = false;\n\n  /**\n   * Permission to write resources granted.\n   */\n  public write: boolean = false;\n\n  /**\n   * Permission to delete queues and messages granted.\n   */\n  public delete: boolean = false;\n\n  /**\n   * Permission to list queues granted.\n   */\n  public list: boolean = false;\n\n  /**\n   * Permission to add messages, table entities, and append to blobs granted.\n   */\n  public add: boolean = false;\n\n  /**\n   * Permission to create queues, blobs and files granted.\n   */\n  public create: boolean = false;\n\n  /**\n   * Permissions to update messages and table entities granted.\n   */\n  public update: boolean = false;\n\n  /**\n   * Permission to get and delete messages granted.\n   */\n  public process: boolean = false;\n\n  /**\n   * Produces the SAS permissions string for an Azure Storage account.\n   * Call this method to set AccountSASSignatureValues Permissions field.\n   *\n   * Using this method will guarantee the resource types are in\n   * an order accepted by the service.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n   *\n   */\n  public toString(): string {\n    // The order of the characters should be as specified here to ensure correctness:\n    // https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n    // Use a string array instead of string concatenating += operator for performance\n    const permissions: string[] = [];\n    if (this.read) {\n      permissions.push(\"r\");\n    }\n    if (this.write) {\n      permissions.push(\"w\");\n    }\n    if (this.delete) {\n      permissions.push(\"d\");\n    }\n    if (this.list) {\n      permissions.push(\"l\");\n    }\n    if (this.add) {\n      permissions.push(\"a\");\n    }\n    if (this.create) {\n      permissions.push(\"c\");\n    }\n    if (this.update) {\n      permissions.push(\"u\");\n    }\n    if (this.process) {\n      permissions.push(\"p\");\n    }\n    return permissions.join(\"\");\n  }\n}\n"]}