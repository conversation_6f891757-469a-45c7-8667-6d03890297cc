{"version": 3, "file": "StorageSharedKeyCredentialPolicyV2.browser.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageSharedKeyCredentialPolicyV2.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAQlC;;GAEG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG,kCAAkC,CAAC;AAUvF;;GAEG;AACH,MAAM,UAAU,gCAAgC,CAC9C,QAAiD;IAEjD,OAAO;QACL,IAAI,EAAE,oCAAoC;QAC1C,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  PipelinePolicy,\n} from \"@azure/core-rest-pipeline\";\n/**\n * The programmatic identifier of the storageSharedKeyCredentialPolicy.\n */\nexport const storageSharedKeyCredentialPolicyName = \"storageSharedKeyCredentialPolicy\";\n\n/**\n * Options used to configure StorageSharedKeyCredentialPolicy.\n */\nexport interface StorageSharedKeyCredentialPolicyOptions {\n  accountName: string;\n  accountKey: Buffer;\n}\n\n/**\n * storageSharedKeyCredentialPolicy handles signing requests using storage account keys.\n */\nexport function storageSharedKeyCredentialPolicy(\n  _options: StorageSharedKeyCredentialPolicyOptions,\n): PipelinePolicy {\n  return {\n    name: storageSharedKeyCredentialPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      return next(request);\n    },\n  };\n}\n"]}