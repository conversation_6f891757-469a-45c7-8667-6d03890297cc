{"version": 3, "file": "Range.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/Range.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAkBlC;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAAC,MAAa;IACzC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,IAAI,UAAU,CAAC,wCAAwC,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,UAAU,CAClB,mGAAmG,CACpG,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC,KAAK;QACjB,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE;QAC9D,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,GAAG,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Range for Blob Service Operations.\n * @see https://learn.microsoft.com/en-us/rest/api/storageservices/specifying-the-range-header-for-blob-service-operations\n */\nexport interface Range {\n  /**\n   * StartByte, larger than or equal 0.\n   */\n  offset: number;\n  /**\n   * Optional. Count of bytes, larger than 0.\n   * If not provided, will return bytes from offset to the end.\n   */\n  count?: number;\n}\n\n/**\n * Generate a range string. For example:\n *\n * \"bytes=255-\" or \"bytes=0-511\"\n *\n * @param iRange -\n */\nexport function rangeToString(iRange: Range): string {\n  if (iRange.offset < 0) {\n    throw new RangeError(`Range.offset cannot be smaller than 0.`);\n  }\n  if (iRange.count && iRange.count <= 0) {\n    throw new RangeError(\n      `Range.count must be larger than 0. Leave it undefined if you want a range from offset to the end.`,\n    );\n  }\n  return iRange.count\n    ? `bytes=${iRange.offset}-${iRange.offset + iRange.count - 1}`\n    : `bytes=${iRange.offset}-`;\n}\n"]}