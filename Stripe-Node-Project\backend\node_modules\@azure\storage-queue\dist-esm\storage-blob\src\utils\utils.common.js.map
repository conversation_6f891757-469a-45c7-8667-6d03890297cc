{"version": 3, "file": "utils.common.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/utils/utils.common.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAiB1C,OAAO,EACL,2BAA2B,EAC3B,eAAe,EACf,cAAc,EACd,YAAY,GACb,MAAM,aAAa,CAAC;AAkBrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACvC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;IAC9B,IAAI,GAAG,IAAI,IAAI,GAAG,CAAC;IAEnB,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IACpB,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAE1B,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAWD,SAAS,4BAA4B,CAAC,gBAAwB;IAC5D,gCAAgC;IAChC,uKAAuK;IACvK,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,gBAAgB,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAClE,4FAA4F;QAC5F,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrD,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,6BAA6B,CAAC,EAAE,CAAC;gBAC7D,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAE,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,gBAAwB,EACxB,QAM2B;IAE3B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAE,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,4BAA4B,CAAC,gBAAwB;IACnE,IAAI,QAAQ,GAAG,EAAE,CAAC;IAElB,IAAI,gBAAgB,CAAC,UAAU,CAAC,4BAA4B,CAAC,EAAE,CAAC;QAC9D,gCAAgC;QAChC,QAAQ,GAAG,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;QAC1D,gBAAgB,GAAG,2BAA2B,CAAC;IACjD,CAAC;IAED,yDAAyD;IACzD,IAAI,YAAY,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;IAC1E,uCAAuC;IACvC,kGAAkG;IAClG,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAErF,IACE,gBAAgB,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;QAC3D,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAC7C,CAAC;QACD,4BAA4B;QAE5B,IAAI,wBAAwB,GAAG,EAAE,CAAC;QAClC,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,2BAA2B;QAC3B,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QACpE,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,YAAY,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEzF,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,+DAA+D;YAC/D,6FAA6F;YAE7F,wBAAwB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;YAC9F,MAAM,QAAQ,GAAG,wBAAyB,CAAC,WAAW,EAAE,CAAC;YACzD,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;gBAChD,MAAM,IAAI,KAAK,CACb,iGAAiG,CAClG,CAAC;YACJ,CAAC;YAED,cAAc,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;YAC1E,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC9E,CAAC;YACD,YAAY,GAAG,GAAG,wBAAwB,MAAM,WAAW,SAAS,cAAc,EAAE,CAAC;QACvF,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO;YACL,IAAI,EAAE,mBAAmB;YACzB,GAAG,EAAE,YAAY;YACjB,WAAW;YACX,UAAU;YACV,QAAQ;SACT,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,wBAAwB;QAExB,IAAI,UAAU,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;QACjF,IAAI,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QACxE,4DAA4D;QAC5D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,WAAW,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;QACpD,CAAC;QACD,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;QAChF,CAAC;aAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACzF,CAAC;QAED,gEAAgE;QAChE,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;IAC/E,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,MAAM,CAAC,IAAY;IAC1B,OAAO,kBAAkB,CAAC,IAAI,CAAC;SAC5B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,uBAAuB;SAC5C,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,iBAAiB;SACtC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;SACrB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,qBAAqB;AAChD,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,IAAY;IACvD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;IAC9B,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACjF,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAE1B,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,IAAY,EAAE,KAAc;IACvE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACnE,kFAAkF;IAClF,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;IAEtE,MAAM,YAAY,GAAa,EAAE,CAAC;IAElC,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QACpD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;gBACxB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,YAAY,CAAC,IAAI,CAAC,GAAG,WAAW,IAAI,YAAY,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE3E,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,IAAY;;IACvD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,MAAA,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,mCAAI,SAAS,CAAC;AACvD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW,EAAE,IAAY;IAClD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC1B,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,SAAS,CAAC,QAAQ,CAAC;IAC5B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,GAAW;IACtC,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC;IACjG,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,kBAAkB,CAAC,GAAW;IAC5C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC;IACtC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,MAAM,IAAI,UAAU,CAAC,iCAAiC,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,WAAW,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;IACzC,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;IACjC,IAAI,WAAW,KAAK,EAAE,EAAE,CAAC;QACvB,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,CAAC,qCAAqC;IACpH,CAAC;IAED,OAAO,GAAG,UAAU,GAAG,WAAW,EAAE,CAAC;AACvC,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACvC,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACtC,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;IACjC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;IAEnF,IAAI,eAAe,GAAa,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvD,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,KAAa,EAAE,EAAE;QACzD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAChD,OAAO,CACL,YAAY,GAAG,CAAC,IAAI,YAAY,KAAK,gBAAgB,IAAI,gBAAgB,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAC7F,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAA8B,EAAE,CAAC;IAC9C,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,GAAG,GAAW,YAAY,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,GAAW,YAAY,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAAW,EAAE,UAAkB;IAC9D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;IAC7B,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC;IAC5B,CAAC;SAAM,CAAC;QACN,KAAK,GAAG,UAAU,CAAC;IACrB,CAAC;IAED,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;IACzB,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAAU,EAAE,mBAA4B,IAAI;IAC/E,iEAAiE;IACjE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAEtC,OAAO,gBAAgB;QACrB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG;QAC/D,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC3D,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,OAAe;IAC1C,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC3E,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAAC,aAAqB;IAChD,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;AACzF,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,aAAqB,EAAE,UAAkB;IACvE,mEAAmE;IACnE,MAAM,qBAAqB,GAAG,EAAE,CAAC;IAEjC,4EAA4E;IAC5E,MAAM,mBAAmB,GAAG,CAAC,CAAC;IAE9B,MAAM,6BAA6B,GAAG,qBAAqB,GAAG,mBAAmB,CAAC;IAElF,IAAI,aAAa,CAAC,MAAM,GAAG,6BAA6B,EAAE,CAAC;QACzD,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,6BAA6B,CAAC,CAAC;IACxE,CAAC;IACD,MAAM,GAAG,GACP,aAAa;QACb,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,qBAAqB,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACrF,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,KAAK,CACzB,QAAgB,EAChB,OAAyB,EACzB,UAAkB;IAElB,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,2CAA2C;QAC3C,IAAI,OAAY,CAAC;QAEjB,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,YAAY,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC;YACD,MAAM,CAAC,UAAU,CAAC,CAAC;QACrB,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;QAEF,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE/C,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,QAAQ,CACtB,aAAqB,EACrB,YAAoB,EACpB,YAAoB,GAAG;IAEvB,+EAA+E;IAC/E,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,aAAa,CAAC,QAAQ,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;IACzD,CAAC;IAED,SAAS,GAAG,SAAS,IAAI,GAAG,CAAC;IAC7B,IAAI,aAAa,CAAC,MAAM,GAAG,YAAY,EAAE,CAAC;QACxC,OAAO,aAAa,CAAC;IACvB,CAAC;SAAM,CAAC;QACN,YAAY,GAAG,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC;QACnD,IAAI,YAAY,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YACpC,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,aAAa,CAAC;IAC1D,CAAC;AACH,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,GAAW;IACrC,IAAI,OAAO,GAAW,GAAG,CAAC;IAC1B,IAAI,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAChE,OAAO,GAAG,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,cAA2B;IACzD,MAAM,OAAO,GAAgB,iBAAiB,EAAE,CAAC;IACjD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,EAAE,CAAC;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC;YACvE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AACD;;;;;GAKG;AACH,MAAM,UAAU,MAAM,CAAC,IAAY,EAAE,IAAY;IAC/C,OAAO,IAAI,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/D,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CAAC,GAAW;IAC/C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,WAAW,CAAC;IAChB,IAAI,CAAC;QACH,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;YAChD,yEAAyE;YACzE,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;YACxC,iFAAiF;YACjF,2GAA2G;YAC3G,mCAAmC;YACnC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,qEAAqE;YACrE,WAAW,GAAG,EAAE,CAAC;QACnB,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;IAC9E,CAAC;AACH,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,SAAc;IAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;IAE5B,sFAAsF;IACtF,wFAAwF;IACxF,wEAAwE;IACxE,wFAAwF;IACxF,OAAO,CACL,mJAAmJ,CAAC,IAAI,CACtJ,IAAI,CACL;QACD,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CACrE,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAAC,IAAW;IAC1C,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,QAAQ,GAAG,EAAE,CAAC;IACpB,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CAAC,IAAW;IACpC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,GAAG,GAAa;QACpB,UAAU,EAAE,EAAE;KACf,CAAC;IAEF,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;gBAClB,GAAG;gBACH,KAAK;aACN,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,MAAM,CAAC,IAAe;IACpC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,GAAG,GAAS,EAAE,CAAC;IACrB,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACtC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC;IACnC,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,oBAAoB,CAClC,iBAIiC;IAEjC,IAAI,iBAAiB,KAAK,SAAS,EAAE,CAAC;QACpC,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,QAAQ,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAC/B,KAAK,KAAK;YACR,OAAO;gBACL,MAAM,EAAE;oBACN,IAAI,EAAE,WAAW;oBACjB,0BAA0B,EAAE;wBAC1B,eAAe,EAAE,iBAAiB,CAAC,eAAe,IAAI,GAAG;wBACzD,UAAU,EAAE,iBAAiB,CAAC,UAAU,IAAI,EAAE;wBAC9C,eAAe,EAAE,iBAAiB,CAAC,eAAe;wBAClD,UAAU,EAAE,iBAAiB,CAAC,eAAe,IAAI,EAAE;wBACnD,cAAc,EAAE,iBAAiB,CAAC,UAAU,IAAI,KAAK;qBACtD;iBACF;aACF,CAAC;QACJ,KAAK,MAAM;YACT,OAAO;gBACL,MAAM,EAAE;oBACN,IAAI,EAAE,MAAM;oBACZ,qBAAqB,EAAE;wBACrB,eAAe,EAAE,iBAAiB,CAAC,eAAe;qBACnD;iBACF;aACF,CAAC;QACJ,KAAK,OAAO;YACV,OAAO;gBACL,MAAM,EAAE;oBACN,IAAI,EAAE,OAAO;oBACb,kBAAkB,EAAE;wBAClB,MAAM,EAAE,iBAAiB,CAAC,MAAM;qBACjC;iBACF;aACF,CAAC;QACJ,KAAK,SAAS;YACZ,OAAO;gBACL,MAAM,EAAE;oBACN,IAAI,EAAE,SAAS;iBAChB;aACF,CAAC;QAEJ;YACE,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;IACvD,CAAC;AACH,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,uBAAgD;IAEhD,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7B,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,WAAW,IAAI,uBAAuB,EAAE,CAAC;QAC3C,+FAA+F;QAC/F,sFAAsF;QACtF,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM,YAAY,GAA8B,EAAE,CAAC;IACnD,KAAK,MAAM,GAAG,IAAI,uBAAuB,EAAE,CAAC;QAC1C,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAM,YAAY,GAAG,KAAK,CAAC;QAC3B,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACpC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACjD,CAAC;QACD,MAAM,IAAI,GAA0B;YAClC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YACd,iBAAiB,EAAE,uBAAuB,CAAC,GAAG,CAA4B;SAC3E,CAAC;QACF,MAAM,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACnF,IAAI,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC;YACrB,YAAY,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,YAAY,CAAC,IAAI,CAAC;gBAChB,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;gBAChB,KAAK,EAAE,CAAC,IAAI,CAAC;aACd,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IACD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,gBAAgB,CAAI,KAAQ,EAAE,UAA2B;IACtE,KAAa,CAAC,UAAU,GAAG,UAAU,CAAC;IACvC,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,yBAAyB,CACvC,iBAAqC;IAErC,OAAO,iBAAiB,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,GAAG,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;AAClG,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,IAAc;IAC7C,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,OAAO,kBAAkB,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;IAC3C,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC,OAAQ,CAAC;IACvB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,qCAAqC,CACnD,gBAA8C;IAE9C,uCACK,gBAAgB,KACnB,OAAO,EAAE;YACP,SAAS,EAAE,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE;gBACpE,MAAM,QAAQ,mCACT,eAAe,KAClB,IAAI,EAAE,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,GAC7C,CAAC;gBACF,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC;SACH,IACD;AACJ,CAAC;AAED,MAAM,UAAU,0CAA0C,CACxD,gBAAmD;;IAEnD,uCACK,gBAAgB,KACnB,OAAO,EAAE;YACP,YAAY,EAAE,MAAA,gBAAgB,CAAC,OAAO,CAAC,YAAY,0CAAE,GAAG,CAAC,CAAC,kBAAkB,EAAE,EAAE;gBAC9E,MAAM,UAAU,mCACX,kBAAkB,KACrB,IAAI,EAAE,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAChD,CAAC;gBACF,OAAO,UAAU,CAAC;YACpB,CAAC,CAAC;YACF,SAAS,EAAE,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE;gBACpE,MAAM,QAAQ,mCACT,eAAe,KAClB,IAAI,EAAE,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,GAC7C,CAAC;gBACF,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC;SACH,IACD;AACJ,CAAC;AAED,MAAM,SAAS,CAAC,CAAC,yBAAyB,CACxC,oBAA4D;IAE5D,IAAI,SAAS,GAAgB,EAAE,CAAC;IAChC,IAAI,UAAU,GAAiB,EAAE,CAAC;IAElC,IAAI,oBAAoB,CAAC,SAAS;QAAE,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC;IAC/E,IAAI,oBAAoB,CAAC,UAAU;QAAE,UAAU,GAAG,oBAAoB,CAAC,UAAU,CAAC;IAElF,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,IAAI,eAAe,GAAG,CAAC,CAAC;IAExB,OAAO,cAAc,GAAG,SAAS,CAAC,MAAM,IAAI,eAAe,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;QAChF,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC,KAAK,GAAG,UAAU,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,CAAC;YACxE,MAAM;gBACJ,KAAK,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC,KAAK;gBACtC,GAAG,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC,GAAG;gBAClC,OAAO,EAAE,KAAK;aACf,CAAC;YACF,EAAE,cAAc,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,MAAM;gBACJ,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,KAAK;gBACxC,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG;gBACpC,OAAO,EAAE,IAAI;aACd,CAAC;YACF,EAAE,eAAe,CAAC;QACpB,CAAC;IACH,CAAC;IAED,OAAO,cAAc,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,cAAc,EAAE,CAAC;QAC3D,MAAM;YACJ,KAAK,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC,KAAK;YACtC,GAAG,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC,GAAG;YAClC,OAAO,EAAE,KAAK;SACf,CAAC;IACJ,CAAC;IAED,OAAO,eAAe,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,eAAe,EAAE,CAAC;QAC9D,MAAM;YACJ,KAAK,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,KAAK;YACxC,GAAG,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG;YACpC,OAAO,EAAE,IAAI;SACd,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,QAAgB;IACzC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,KAAK,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1C,CAAC;IACD,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,CAAC;AAkFD;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAC5B,QAAW;IAEX,IAAI,WAAW,IAAI,QAAQ,EAAE,CAAC;QAC5B,OAAO,QAA0C,CAAC;IACpD,CAAC;IAED,MAAM,IAAI,SAAS,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;AAChE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport type { HttpHeaders } from \"@azure/core-rest-pipeline\";\nimport { createHttpHeaders } from \"@azure/core-rest-pipeline\";\nimport { isNode } from \"@azure/core-util\";\n\nimport type {\n  BlobQueryArrowConfiguration,\n  BlobQueryCsvTextConfiguration,\n  BlobQueryJsonTextConfiguration,\n  BlobQueryParquetConfiguration,\n} from \"../Clients\";\nimport type {\n  QuerySerialization,\n  BlobTags,\n  BlobName,\n  ListBlobsFlatSegmentResponse,\n  ListBlobsHierarchySegmentResponse,\n  PageRange,\n  ClearRange,\n} from \"../generated/src/models\";\nimport {\n  DevelopmentConnectionString,\n  HeaderConstants,\n  PathStylePorts,\n  URLConstants,\n} from \"./constants\";\nimport type {\n  Tags,\n  ObjectReplicationPolicy,\n  ObjectReplicationRule,\n  ObjectReplicationStatus,\n  HttpAuthorization,\n} from \"../models\";\nimport type {\n  ListBlobsFlatSegmentResponseModel,\n  BlobItemInternal as BlobItemInternalModel,\n  ListBlobsHierarchySegmentResponseModel,\n  BlobPrefix as BlobPrefixModel,\n  PageBlobGetPageRangesDiffResponseModel,\n  PageRangeInfo,\n} from \"../generatedModels\";\nimport type { HttpHeadersLike, WebResourceLike } from \"@azure/core-http-compat\";\n\n/**\n * Reserved URL characters must be properly escaped for Storage services like Blob or File.\n *\n * ## URL encode and escape strategy for JS SDKs\n *\n * When customers pass a URL string into XxxClient classes constructor, the URL string may already be URL encoded or not.\n * But before sending to Azure Storage server, the URL must be encoded. However, it's hard for a SDK to guess whether the URL\n * string has been encoded or not. We have 2 potential strategies, and chose strategy two for the XxxClient constructors.\n *\n * ### Strategy One: Assume the customer URL string is not encoded, and always encode URL string in SDK.\n *\n * This is what legacy V2 SDK does, simple and works for most of the cases.\n * - When customer URL string is \"http://account.blob.core.windows.net/con/b:\",\n *   SDK will encode it to \"http://account.blob.core.windows.net/con/b%3A\" and send to server. A blob named \"b:\" will be created.\n * - When customer URL string is \"http://account.blob.core.windows.net/con/b%3A\",\n *   SDK will encode it to \"http://account.blob.core.windows.net/con/b%253A\" and send to server. A blob named \"b%3A\" will be created.\n *\n * But this strategy will make it not possible to create a blob with \"?\" in it's name. Because when customer URL string is\n * \"http://account.blob.core.windows.net/con/blob?name\", the \"?name\" will be treated as URL paramter instead of blob name.\n * If customer URL string is \"http://account.blob.core.windows.net/con/blob%3Fname\", a blob named \"blob%3Fname\" will be created.\n * V2 SDK doesn't have this issue because it doesn't allow customer pass in a full URL, it accepts a separate blob name and encodeURIComponent for it.\n * We cannot accept a SDK cannot create a blob name with \"?\". So we implement strategy two:\n *\n * ### Strategy Two: SDK doesn't assume the URL has been encoded or not. It will just escape the special characters.\n *\n * This is what V10 Blob Go SDK does. It accepts a URL type in Go, and call url.EscapedPath() to escape the special chars unescaped.\n * - When customer URL string is \"http://account.blob.core.windows.net/con/b:\",\n *   SDK will escape \":\" like \"http://account.blob.core.windows.net/con/b%3A\" and send to server. A blob named \"b:\" will be created.\n * - When customer URL string is \"http://account.blob.core.windows.net/con/b%3A\",\n *   There is no special characters, so send \"http://account.blob.core.windows.net/con/b%3A\" to server. A blob named \"b:\" will be created.\n * - When customer URL string is \"http://account.blob.core.windows.net/con/b%253A\",\n *   There is no special characters, so send \"http://account.blob.core.windows.net/con/b%253A\" to server. A blob named \"b%3A\" will be created.\n *\n * This strategy gives us flexibility to create with any special characters. But \"%\" will be treated as a special characters, if the URL string\n * is not encoded, there shouldn't a \"%\" in the URL string, otherwise the URL is not a valid URL.\n * If customer needs to create a blob with \"%\" in it's blob name, use \"%25\" instead of \"%\". Just like above 3rd sample.\n * And following URL strings are invalid:\n * - \"http://account.blob.core.windows.net/con/b%\"\n * - \"http://account.blob.core.windows.net/con/b%2\"\n * - \"http://account.blob.core.windows.net/con/b%G\"\n *\n * Another special character is \"?\", use \"%2F\" to represent a blob name with \"?\" in a URL string.\n *\n * ### Strategy for containerName, blobName or other specific XXXName parameters in methods such as `containerClient.getBlobClient(blobName)`\n *\n * We will apply strategy one, and call encodeURIComponent for these parameters like blobName. Because what customers passes in is a plain name instead of a URL.\n *\n * @see https://learn.microsoft.com/en-us/rest/api/storageservices/naming-and-referencing-containers--blobs--and-metadata\n * @see https://learn.microsoft.com/en-us/rest/api/storageservices/naming-and-referencing-shares--directories--files--and-metadata\n *\n * @param url -\n */\nexport function escapeURLPath(url: string): string {\n  const urlParsed = new URL(url);\n\n  let path = urlParsed.pathname;\n  path = path || \"/\";\n\n  path = escape(path);\n  urlParsed.pathname = path;\n\n  return urlParsed.toString();\n}\n\nexport interface ConnectionString {\n  kind: \"AccountConnString\" | \"SASConnString\";\n  url: string;\n  accountName: string;\n  accountKey?: any;\n  accountSas?: string;\n  proxyUri?: string; // Development Connection String may contain proxyUri\n}\n\nfunction getProxyUriFromDevConnString(connectionString: string): string {\n  // Development Connection String\n  // https://learn.microsoft.com/en-us/azure/storage/common/storage-configure-connection-string#connect-to-the-emulator-account-using-the-well-known-account-name-and-key\n  let proxyUri = \"\";\n  if (connectionString.search(\"DevelopmentStorageProxyUri=\") !== -1) {\n    // CONNECTION_STRING=UseDevelopmentStorage=true;DevelopmentStorageProxyUri=http://myProxyUri\n    const matchCredentials = connectionString.split(\";\");\n    for (const element of matchCredentials) {\n      if (element.trim().startsWith(\"DevelopmentStorageProxyUri=\")) {\n        proxyUri = element.trim().match(\"DevelopmentStorageProxyUri=(.*)\")![1];\n      }\n    }\n  }\n  return proxyUri;\n}\n\nexport function getValueInConnString(\n  connectionString: string,\n  argument:\n    | \"BlobEndpoint\"\n    | \"AccountName\"\n    | \"AccountKey\"\n    | \"DefaultEndpointsProtocol\"\n    | \"EndpointSuffix\"\n    | \"SharedAccessSignature\",\n): string {\n  const elements = connectionString.split(\";\");\n  for (const element of elements) {\n    if (element.trim().startsWith(argument)) {\n      return element.trim().match(argument + \"=(.*)\")![1];\n    }\n  }\n  return \"\";\n}\n\n/**\n * Extracts the parts of an Azure Storage account connection string.\n *\n * @param connectionString - Connection string.\n * @returns String key value pairs of the storage account's url and credentials.\n */\nexport function extractConnectionStringParts(connectionString: string): ConnectionString {\n  let proxyUri = \"\";\n\n  if (connectionString.startsWith(\"UseDevelopmentStorage=true\")) {\n    // Development connection string\n    proxyUri = getProxyUriFromDevConnString(connectionString);\n    connectionString = DevelopmentConnectionString;\n  }\n\n  // Matching BlobEndpoint in the Account connection string\n  let blobEndpoint = getValueInConnString(connectionString, \"BlobEndpoint\");\n  // Slicing off '/' at the end if exists\n  // (The methods that use `extractConnectionStringParts` expect the url to not have `/` at the end)\n  blobEndpoint = blobEndpoint.endsWith(\"/\") ? blobEndpoint.slice(0, -1) : blobEndpoint;\n\n  if (\n    connectionString.search(\"DefaultEndpointsProtocol=\") !== -1 &&\n    connectionString.search(\"AccountKey=\") !== -1\n  ) {\n    // Account connection string\n\n    let defaultEndpointsProtocol = \"\";\n    let accountName = \"\";\n    let accountKey = Buffer.from(\"accountKey\", \"base64\");\n    let endpointSuffix = \"\";\n\n    // Get account name and key\n    accountName = getValueInConnString(connectionString, \"AccountName\");\n    accountKey = Buffer.from(getValueInConnString(connectionString, \"AccountKey\"), \"base64\");\n\n    if (!blobEndpoint) {\n      // BlobEndpoint is not present in the Account connection string\n      // Can be obtained from `${defaultEndpointsProtocol}://${accountName}.blob.${endpointSuffix}`\n\n      defaultEndpointsProtocol = getValueInConnString(connectionString, \"DefaultEndpointsProtocol\");\n      const protocol = defaultEndpointsProtocol!.toLowerCase();\n      if (protocol !== \"https\" && protocol !== \"http\") {\n        throw new Error(\n          \"Invalid DefaultEndpointsProtocol in the provided Connection String. Expecting 'https' or 'http'\",\n        );\n      }\n\n      endpointSuffix = getValueInConnString(connectionString, \"EndpointSuffix\");\n      if (!endpointSuffix) {\n        throw new Error(\"Invalid EndpointSuffix in the provided Connection String\");\n      }\n      blobEndpoint = `${defaultEndpointsProtocol}://${accountName}.blob.${endpointSuffix}`;\n    }\n\n    if (!accountName) {\n      throw new Error(\"Invalid AccountName in the provided Connection String\");\n    } else if (accountKey.length === 0) {\n      throw new Error(\"Invalid AccountKey in the provided Connection String\");\n    }\n\n    return {\n      kind: \"AccountConnString\",\n      url: blobEndpoint,\n      accountName,\n      accountKey,\n      proxyUri,\n    };\n  } else {\n    // SAS connection string\n\n    let accountSas = getValueInConnString(connectionString, \"SharedAccessSignature\");\n    let accountName = getValueInConnString(connectionString, \"AccountName\");\n    // if accountName is empty, try to read it from BlobEndpoint\n    if (!accountName) {\n      accountName = getAccountNameFromUrl(blobEndpoint);\n    }\n    if (!blobEndpoint) {\n      throw new Error(\"Invalid BlobEndpoint in the provided SAS Connection String\");\n    } else if (!accountSas) {\n      throw new Error(\"Invalid SharedAccessSignature in the provided SAS Connection String\");\n    }\n\n    // client constructors assume accountSas does *not* start with ?\n    if (accountSas.startsWith(\"?\")) {\n      accountSas = accountSas.substring(1);\n    }\n\n    return { kind: \"SASConnString\", url: blobEndpoint, accountName, accountSas };\n  }\n}\n\n/**\n * Internal escape method implemented Strategy Two mentioned in escapeURL() description.\n *\n * @param text -\n */\nfunction escape(text: string): string {\n  return encodeURIComponent(text)\n    .replace(/%2F/g, \"/\") // Don't escape for \"/\"\n    .replace(/'/g, \"%27\") // Escape for \"'\"\n    .replace(/\\+/g, \"%20\")\n    .replace(/%25/g, \"%\"); // Revert encoded \"%\"\n}\n\n/**\n * Append a string to URL path. Will remove duplicated \"/\" in front of the string\n * when URL path ends with a \"/\".\n *\n * @param url - Source URL string\n * @param name - String to be appended to URL\n * @returns An updated URL string\n */\nexport function appendToURLPath(url: string, name: string): string {\n  const urlParsed = new URL(url);\n\n  let path = urlParsed.pathname;\n  path = path ? (path.endsWith(\"/\") ? `${path}${name}` : `${path}/${name}`) : name;\n  urlParsed.pathname = path;\n\n  return urlParsed.toString();\n}\n\n/**\n * Set URL parameter name and value. If name exists in URL parameters, old value\n * will be replaced by name key. If not provide value, the parameter will be deleted.\n *\n * @param url - Source URL string\n * @param name - Parameter name\n * @param value - Parameter value\n * @returns An updated URL string\n */\nexport function setURLParameter(url: string, name: string, value?: string): string {\n  const urlParsed = new URL(url);\n  const encodedName = encodeURIComponent(name);\n  const encodedValue = value ? encodeURIComponent(value) : undefined;\n  // mutating searchParams will change the encoding, so we have to do this ourselves\n  const searchString = urlParsed.search === \"\" ? \"?\" : urlParsed.search;\n\n  const searchPieces: string[] = [];\n\n  for (const pair of searchString.slice(1).split(\"&\")) {\n    if (pair) {\n      const [key] = pair.split(\"=\", 2);\n      if (key !== encodedName) {\n        searchPieces.push(pair);\n      }\n    }\n  }\n  if (encodedValue) {\n    searchPieces.push(`${encodedName}=${encodedValue}`);\n  }\n\n  urlParsed.search = searchPieces.length ? `?${searchPieces.join(\"&\")}` : \"\";\n\n  return urlParsed.toString();\n}\n\n/**\n * Get URL parameter by name.\n *\n * @param url -\n * @param name -\n */\nexport function getURLParameter(url: string, name: string): string | string[] | undefined {\n  const urlParsed = new URL(url);\n  return urlParsed.searchParams.get(name) ?? undefined;\n}\n\n/**\n * Set URL host.\n *\n * @param url - Source URL string\n * @param host - New host string\n * @returns An updated URL string\n */\nexport function setURLHost(url: string, host: string): string {\n  const urlParsed = new URL(url);\n  urlParsed.hostname = host;\n  return urlParsed.toString();\n}\n\n/**\n * Get URL path from an URL string.\n *\n * @param url - Source URL string\n */\nexport function getURLPath(url: string): string | undefined {\n  try {\n    const urlParsed = new URL(url);\n    return urlParsed.pathname;\n  } catch (e) {\n    return undefined;\n  }\n}\n\n/**\n * Get URL scheme from an URL string.\n *\n * @param url - Source URL string\n */\nexport function getURLScheme(url: string): string | undefined {\n  try {\n    const urlParsed = new URL(url);\n    return urlParsed.protocol.endsWith(\":\") ? urlParsed.protocol.slice(0, -1) : urlParsed.protocol;\n  } catch (e) {\n    return undefined;\n  }\n}\n\n/**\n * Get URL path and query from an URL string.\n *\n * @param url - Source URL string\n */\nexport function getURLPathAndQuery(url: string): string | undefined {\n  const urlParsed = new URL(url);\n  const pathString = urlParsed.pathname;\n  if (!pathString) {\n    throw new RangeError(\"Invalid url without valid path.\");\n  }\n\n  let queryString = urlParsed.search || \"\";\n  queryString = queryString.trim();\n  if (queryString !== \"\") {\n    queryString = queryString.startsWith(\"?\") ? queryString : `?${queryString}`; // Ensure query string start with '?'\n  }\n\n  return `${pathString}${queryString}`;\n}\n\n/**\n * Get URL query key value pairs from an URL string.\n *\n * @param url -\n */\nexport function getURLQueries(url: string): { [key: string]: string } {\n  let queryString = new URL(url).search;\n  if (!queryString) {\n    return {};\n  }\n\n  queryString = queryString.trim();\n  queryString = queryString.startsWith(\"?\") ? queryString.substring(1) : queryString;\n\n  let querySubStrings: string[] = queryString.split(\"&\");\n  querySubStrings = querySubStrings.filter((value: string) => {\n    const indexOfEqual = value.indexOf(\"=\");\n    const lastIndexOfEqual = value.lastIndexOf(\"=\");\n    return (\n      indexOfEqual > 0 && indexOfEqual === lastIndexOfEqual && lastIndexOfEqual < value.length - 1\n    );\n  });\n\n  const queries: { [key: string]: string } = {};\n  for (const querySubString of querySubStrings) {\n    const splitResults = querySubString.split(\"=\");\n    const key: string = splitResults[0];\n    const value: string = splitResults[1];\n    queries[key] = value;\n  }\n\n  return queries;\n}\n\n/**\n * Append a string to URL query.\n *\n * @param url - Source URL string.\n * @param queryParts - String to be appended to the URL query.\n * @returns An updated URL string.\n */\nexport function appendToURLQuery(url: string, queryParts: string): string {\n  const urlParsed = new URL(url);\n\n  let query = urlParsed.search;\n  if (query) {\n    query += \"&\" + queryParts;\n  } else {\n    query = queryParts;\n  }\n\n  urlParsed.search = query;\n  return urlParsed.toString();\n}\n\n/**\n * Rounds a date off to seconds.\n *\n * @param date -\n * @param withMilliseconds - If true, YYYY-MM-DDThh:mm:ss.fffffffZ will be returned;\n *                                          If false, YYYY-MM-DDThh:mm:ssZ will be returned.\n * @returns Date string in ISO8061 format, with or without 7 milliseconds component\n */\nexport function truncatedISO8061Date(date: Date, withMilliseconds: boolean = true): string {\n  // Date.toISOString() will return like \"2018-10-29T06:34:36.139Z\"\n  const dateString = date.toISOString();\n\n  return withMilliseconds\n    ? dateString.substring(0, dateString.length - 1) + \"0000\" + \"Z\"\n    : dateString.substring(0, dateString.length - 5) + \"Z\";\n}\n\n/**\n * Base64 encode.\n *\n * @param content -\n */\nexport function base64encode(content: string): string {\n  return !isNode ? btoa(content) : Buffer.from(content).toString(\"base64\");\n}\n\n/**\n * Base64 decode.\n *\n * @param encodedString -\n */\nexport function base64decode(encodedString: string): string {\n  return !isNode ? atob(encodedString) : Buffer.from(encodedString, \"base64\").toString();\n}\n\n/**\n * Generate a 64 bytes base64 block ID string.\n *\n * @param blockIndex -\n */\nexport function generateBlockID(blockIDPrefix: string, blockIndex: number): string {\n  // To generate a 64 bytes base64 string, source string should be 48\n  const maxSourceStringLength = 48;\n\n  // A blob can have a maximum of 100,000 uncommitted blocks at any given time\n  const maxBlockIndexLength = 6;\n\n  const maxAllowedBlockIDPrefixLength = maxSourceStringLength - maxBlockIndexLength;\n\n  if (blockIDPrefix.length > maxAllowedBlockIDPrefixLength) {\n    blockIDPrefix = blockIDPrefix.slice(0, maxAllowedBlockIDPrefixLength);\n  }\n  const res =\n    blockIDPrefix +\n    padStart(blockIndex.toString(), maxSourceStringLength - blockIDPrefix.length, \"0\");\n  return base64encode(res);\n}\n\n/**\n * Delay specified time interval.\n *\n * @param timeInMs -\n * @param aborter -\n * @param abortError -\n */\nexport async function delay(\n  timeInMs: number,\n  aborter?: AbortSignalLike,\n  abortError?: Error,\n): Promise<void> {\n  return new Promise<void>((resolve, reject) => {\n    /* eslint-disable-next-line prefer-const */\n    let timeout: any;\n\n    const abortHandler = () => {\n      if (timeout !== undefined) {\n        clearTimeout(timeout);\n      }\n      reject(abortError);\n    };\n\n    const resolveHandler = () => {\n      if (aborter !== undefined) {\n        aborter.removeEventListener(\"abort\", abortHandler);\n      }\n      resolve();\n    };\n\n    timeout = setTimeout(resolveHandler, timeInMs);\n\n    if (aborter !== undefined) {\n      aborter.addEventListener(\"abort\", abortHandler);\n    }\n  });\n}\n\n/**\n * String.prototype.padStart()\n *\n * @param currentString -\n * @param targetLength -\n * @param padString -\n */\nexport function padStart(\n  currentString: string,\n  targetLength: number,\n  padString: string = \" \",\n): string {\n  // @ts-expect-error: TS doesn't know this code needs to run downlevel sometimes\n  if (String.prototype.padStart) {\n    return currentString.padStart(targetLength, padString);\n  }\n\n  padString = padString || \" \";\n  if (currentString.length > targetLength) {\n    return currentString;\n  } else {\n    targetLength = targetLength - currentString.length;\n    if (targetLength > padString.length) {\n      padString += padString.repeat(targetLength / padString.length);\n    }\n    return padString.slice(0, targetLength) + currentString;\n  }\n}\n\nexport function sanitizeURL(url: string): string {\n  let safeURL: string = url;\n  if (getURLParameter(safeURL, URLConstants.Parameters.SIGNATURE)) {\n    safeURL = setURLParameter(safeURL, URLConstants.Parameters.SIGNATURE, \"*****\");\n  }\n\n  return safeURL;\n}\n\nexport function sanitizeHeaders(originalHeader: HttpHeaders): HttpHeaders {\n  const headers: HttpHeaders = createHttpHeaders();\n  for (const [name, value] of originalHeader) {\n    if (name.toLowerCase() === HeaderConstants.AUTHORIZATION.toLowerCase()) {\n      headers.set(name, \"*****\");\n    } else if (name.toLowerCase() === HeaderConstants.X_MS_COPY_SOURCE) {\n      headers.set(name, sanitizeURL(value));\n    } else {\n      headers.set(name, value);\n    }\n  }\n\n  return headers;\n}\n/**\n * If two strings are equal when compared case insensitive.\n *\n * @param str1 -\n * @param str2 -\n */\nexport function iEqual(str1: string, str2: string): boolean {\n  return str1.toLocaleLowerCase() === str2.toLocaleLowerCase();\n}\n\n/**\n * Extracts account name from the url\n * @param url - url to extract the account name from\n * @returns with the account name\n */\nexport function getAccountNameFromUrl(url: string): string {\n  const parsedUrl = new URL(url);\n  let accountName;\n  try {\n    if (parsedUrl.hostname.split(\".\")[1] === \"blob\") {\n      // `${defaultEndpointsProtocol}://${accountName}.blob.${endpointSuffix}`;\n      accountName = parsedUrl.hostname.split(\".\")[0];\n    } else if (isIpEndpointStyle(parsedUrl)) {\n      // IPv4/IPv6 address hosts... Example - http://**********:10001/devstoreaccount1/\n      // Single word domain without a [dot] in the endpoint... Example - http://localhost:10001/devstoreaccount1/\n      // .getPath() -> /devstoreaccount1/\n      accountName = parsedUrl.pathname.split(\"/\")[1];\n    } else {\n      // Custom domain case: \"https://customdomain.com/containername/blob\".\n      accountName = \"\";\n    }\n    return accountName;\n  } catch (error: any) {\n    throw new Error(\"Unable to extract accountName with provided information.\");\n  }\n}\n\nexport function isIpEndpointStyle(parsedUrl: URL): boolean {\n  const host = parsedUrl.host;\n\n  // Case 1: Ipv6, use a broad regex to find out candidates whose host contains two ':'.\n  // Case 2: localhost(:port) or host.docker.internal, use broad regex to match port part.\n  // Case 3: Ipv4, use broad regex which just check if host contains Ipv4.\n  // For valid host please refer to https://man7.org/linux/man-pages/man7/hostname.7.html.\n  return (\n    /^.*:.*:.*$|^(localhost|host.docker.internal)(:[0-9]+)?$|^(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])){3}(:[0-9]+)?$/.test(\n      host,\n    ) ||\n    (Boolean(parsedUrl.port) && PathStylePorts.includes(parsedUrl.port))\n  );\n}\n\n/**\n * Convert Tags to encoded string.\n *\n * @param tags -\n */\nexport function toBlobTagsString(tags?: Tags): string | undefined {\n  if (tags === undefined) {\n    return undefined;\n  }\n\n  const tagPairs = [];\n  for (const key in tags) {\n    if (Object.prototype.hasOwnProperty.call(tags, key)) {\n      const value = tags[key];\n      tagPairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);\n    }\n  }\n\n  return tagPairs.join(\"&\");\n}\n\n/**\n * Convert Tags type to BlobTags.\n *\n * @param tags -\n */\nexport function toBlobTags(tags?: Tags): BlobTags | undefined {\n  if (tags === undefined) {\n    return undefined;\n  }\n\n  const res: BlobTags = {\n    blobTagSet: [],\n  };\n\n  for (const key in tags) {\n    if (Object.prototype.hasOwnProperty.call(tags, key)) {\n      const value = tags[key];\n      res.blobTagSet.push({\n        key,\n        value,\n      });\n    }\n  }\n  return res;\n}\n\n/**\n * Covert BlobTags to Tags type.\n *\n * @param tags -\n */\nexport function toTags(tags?: BlobTags): Tags | undefined {\n  if (tags === undefined) {\n    return undefined;\n  }\n\n  const res: Tags = {};\n  for (const blobTag of tags.blobTagSet) {\n    res[blobTag.key] = blobTag.value;\n  }\n  return res;\n}\n\n/**\n * Convert BlobQueryTextConfiguration to QuerySerialization type.\n *\n * @param textConfiguration -\n */\nexport function toQuerySerialization(\n  textConfiguration?:\n    | BlobQueryJsonTextConfiguration\n    | BlobQueryCsvTextConfiguration\n    | BlobQueryArrowConfiguration\n    | BlobQueryParquetConfiguration,\n): QuerySerialization | undefined {\n  if (textConfiguration === undefined) {\n    return undefined;\n  }\n\n  switch (textConfiguration.kind) {\n    case \"csv\":\n      return {\n        format: {\n          type: \"delimited\",\n          delimitedTextConfiguration: {\n            columnSeparator: textConfiguration.columnSeparator || \",\",\n            fieldQuote: textConfiguration.fieldQuote || \"\",\n            recordSeparator: textConfiguration.recordSeparator,\n            escapeChar: textConfiguration.escapeCharacter || \"\",\n            headersPresent: textConfiguration.hasHeaders || false,\n          },\n        },\n      };\n    case \"json\":\n      return {\n        format: {\n          type: \"json\",\n          jsonTextConfiguration: {\n            recordSeparator: textConfiguration.recordSeparator,\n          },\n        },\n      };\n    case \"arrow\":\n      return {\n        format: {\n          type: \"arrow\",\n          arrowConfiguration: {\n            schema: textConfiguration.schema,\n          },\n        },\n      };\n    case \"parquet\":\n      return {\n        format: {\n          type: \"parquet\",\n        },\n      };\n\n    default:\n      throw Error(\"Invalid BlobQueryTextConfiguration.\");\n  }\n}\n\nexport function parseObjectReplicationRecord(\n  objectReplicationRecord?: Record<string, string>,\n): ObjectReplicationPolicy[] | undefined {\n  if (!objectReplicationRecord) {\n    return undefined;\n  }\n\n  if (\"policy-id\" in objectReplicationRecord) {\n    // If the dictionary contains a key with policy id, we are not required to do any parsing since\n    // the policy id should already be stored in the ObjectReplicationDestinationPolicyId.\n    return undefined;\n  }\n\n  const orProperties: ObjectReplicationPolicy[] = [];\n  for (const key in objectReplicationRecord) {\n    const ids = key.split(\"_\");\n    const policyPrefix = \"or-\";\n    if (ids[0].startsWith(policyPrefix)) {\n      ids[0] = ids[0].substring(policyPrefix.length);\n    }\n    const rule: ObjectReplicationRule = {\n      ruleId: ids[1],\n      replicationStatus: objectReplicationRecord[key] as ObjectReplicationStatus,\n    };\n    const policyIndex = orProperties.findIndex((policy) => policy.policyId === ids[0]);\n    if (policyIndex > -1) {\n      orProperties[policyIndex].rules.push(rule);\n    } else {\n      orProperties.push({\n        policyId: ids[0],\n        rules: [rule],\n      });\n    }\n  }\n  return orProperties;\n}\n\n/**\n * Attach a TokenCredential to an object.\n *\n * @param thing -\n * @param credential -\n */\nexport function attachCredential<T>(thing: T, credential: TokenCredential): T {\n  (thing as any).credential = credential;\n  return thing;\n}\n\nexport function httpAuthorizationToString(\n  httpAuthorization?: HttpAuthorization,\n): string | undefined {\n  return httpAuthorization ? httpAuthorization.scheme + \" \" + httpAuthorization.value : undefined;\n}\n\nexport function BlobNameToString(name: BlobName): string {\n  if (name.encoded) {\n    return decodeURIComponent(name.content!);\n  } else {\n    return name.content!;\n  }\n}\n\nexport function ConvertInternalResponseOfListBlobFlat(\n  internalResponse: ListBlobsFlatSegmentResponse,\n): ListBlobsFlatSegmentResponseModel {\n  return {\n    ...internalResponse,\n    segment: {\n      blobItems: internalResponse.segment.blobItems.map((blobItemInteral) => {\n        const blobItem: BlobItemInternalModel = {\n          ...blobItemInteral,\n          name: BlobNameToString(blobItemInteral.name),\n        };\n        return blobItem;\n      }),\n    },\n  };\n}\n\nexport function ConvertInternalResponseOfListBlobHierarchy(\n  internalResponse: ListBlobsHierarchySegmentResponse,\n): ListBlobsHierarchySegmentResponseModel {\n  return {\n    ...internalResponse,\n    segment: {\n      blobPrefixes: internalResponse.segment.blobPrefixes?.map((blobPrefixInternal) => {\n        const blobPrefix: BlobPrefixModel = {\n          ...blobPrefixInternal,\n          name: BlobNameToString(blobPrefixInternal.name),\n        };\n        return blobPrefix;\n      }),\n      blobItems: internalResponse.segment.blobItems.map((blobItemInteral) => {\n        const blobItem: BlobItemInternalModel = {\n          ...blobItemInteral,\n          name: BlobNameToString(blobItemInteral.name),\n        };\n        return blobItem;\n      }),\n    },\n  };\n}\n\nexport function* ExtractPageRangeInfoItems(\n  getPageRangesSegment: PageBlobGetPageRangesDiffResponseModel,\n): IterableIterator<PageRangeInfo> {\n  let pageRange: PageRange[] = [];\n  let clearRange: ClearRange[] = [];\n\n  if (getPageRangesSegment.pageRange) pageRange = getPageRangesSegment.pageRange;\n  if (getPageRangesSegment.clearRange) clearRange = getPageRangesSegment.clearRange;\n\n  let pageRangeIndex = 0;\n  let clearRangeIndex = 0;\n\n  while (pageRangeIndex < pageRange.length && clearRangeIndex < clearRange.length) {\n    if (pageRange[pageRangeIndex].start < clearRange[clearRangeIndex].start) {\n      yield {\n        start: pageRange[pageRangeIndex].start,\n        end: pageRange[pageRangeIndex].end,\n        isClear: false,\n      };\n      ++pageRangeIndex;\n    } else {\n      yield {\n        start: clearRange[clearRangeIndex].start,\n        end: clearRange[clearRangeIndex].end,\n        isClear: true,\n      };\n      ++clearRangeIndex;\n    }\n  }\n\n  for (; pageRangeIndex < pageRange.length; ++pageRangeIndex) {\n    yield {\n      start: pageRange[pageRangeIndex].start,\n      end: pageRange[pageRangeIndex].end,\n      isClear: false,\n    };\n  }\n\n  for (; clearRangeIndex < clearRange.length; ++clearRangeIndex) {\n    yield {\n      start: clearRange[clearRangeIndex].start,\n      end: clearRange[clearRangeIndex].end,\n      isClear: true,\n    };\n  }\n}\n\n/**\n * Escape the blobName but keep path separator ('/').\n */\nexport function EscapePath(blobName: string): string {\n  const split = blobName.split(\"/\");\n  for (let i = 0; i < split.length; i++) {\n    split[i] = encodeURIComponent(split[i]);\n  }\n  return split.join(\"/\");\n}\n\n/**\n * A representation of an HTTP response that\n * includes a reference to the request that\n * originated it.\n */\nexport interface HttpResponse {\n  /**\n   * The headers from the response.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * The original request that resulted in this response.\n   */\n  request: WebResourceLike;\n  /**\n   * The HTTP status code returned from the service.\n   */\n  status: number;\n}\n\n/**\n * An object with a _response property that has\n * headers already parsed into a typed object.\n */\nexport interface ResponseWithHeaders<Headers> {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: Headers;\n  };\n}\n\n/**\n * An object with a _response property that has body\n * and headers already parsed into known types.\n */\nexport interface ResponseWithBody<Headers, Body> {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: Headers;\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: Body;\n  };\n}\n\n/**\n * An object with a simple _response property.\n */\nexport interface ResponseLike {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: HttpResponse;\n}\n\n/**\n * A type that represents an operation result with a known _response property.\n */\nexport type WithResponse<T, Headers = undefined, Body = undefined> = T &\n  (Body extends object\n    ? ResponseWithBody<Headers, Body>\n    : Headers extends object\n      ? ResponseWithHeaders<Headers>\n      : ResponseLike);\n\n/**\n * A typesafe helper for ensuring that a given response object has\n * the original _response attached.\n * @param response - A response object from calling a client operation\n * @returns The same object, but with known _response property\n */\nexport function assertResponse<T extends object, Headers = undefined, Body = undefined>(\n  response: T,\n): WithResponse<T, Headers, Body> {\n  if (`_response` in response) {\n    return response as WithResponse<T, Headers, Body>;\n  }\n\n  throw new TypeError(`Unexpected response object ${response}`);\n}\n"]}