{"version": 3, "file": "index.browser.js", "sourceRoot": "", "sources": ["../../../src/index.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AAEtD,cAAc,wDAAwD,CAAC;AACvE,cAAc,+CAA+C,CAAC;AAE9D,OAAO,EACL,QAAQ,EAGR,cAAc,EACd,WAAW,GAGZ,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,iBAAiB,EAAE,MAAM,+CAA+C,CAAC;AAClF,cAAc,2DAA2D,CAAC;AAC1E,cAAc,kDAAkD,CAAC;AACjE,cAAc,kDAAkD,CAAC;AACjE,cAAc,oDAAoD,CAAC;AAEnE,cAAc,eAAe,CAAC;AAC9B,cAAc,uBAAuB,CAAC;AACtC,cAAc,sBAAsB,CAAC;AAErC,cAAc,mBAAmB,CAAC;AAQlC,OAAO,EAAE,SAAS,EAAE,CAAC;AACrB,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { RestError } from \"@azure/core-rest-pipeline\";\n\nexport * from \"../../storage-blob/src/credentials/AnonymousCredential\";\nexport * from \"../../storage-blob/src/credentials/Credential\";\nexport { SasIPRange } from \"./SasIPRange\";\nexport {\n  Pipeline,\n  PipelineLike,\n  PipelineOptions,\n  isPipelineLike,\n  newPipeline,\n  StoragePipelineOptions,\n  ServiceClientOptions,\n} from \"./Pipeline\";\nexport { BaseRequestPolicy } from \"../../storage-blob/src/policies/RequestPolicy\";\nexport * from \"../../storage-blob/src/policies/AnonymousCredentialPolicy\";\nexport * from \"../../storage-blob/src/policies/CredentialPolicy\";\nexport * from \"../../storage-blob/src/StorageRetryPolicyFactory\";\nexport * from \"../../storage-blob/src/StorageBrowserPolicyFactory\";\nexport { Metadata } from \"./models\";\nexport * from \"./QueueClient\";\nexport * from \"./QueueSASPermissions\";\nexport * from \"./QueueServiceClient\";\nexport { CommonOptions } from \"./StorageClient\";\nexport * from \"./generatedModels\";\nexport {\n  WithResponse,\n  ResponseLike,\n  ResponseWithBody,\n  ResponseWithHeaders,\n  HttpResponse,\n} from \"./utils/utils.common\";\nexport { RestError };\nexport { logger } from \"./log\";\n"]}