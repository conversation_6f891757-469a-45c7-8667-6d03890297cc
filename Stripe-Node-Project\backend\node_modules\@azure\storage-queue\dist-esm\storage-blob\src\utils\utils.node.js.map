{"version": 3, "file": "utils.node.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/utils/utils.node.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAE9C;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,MAA6B,EAC7B,MAAc,EACd,MAAc,EACd,GAAW,EACX,QAAyB;IAEzB,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,qBAAqB;IAClC,MAAM,KAAK,GAAG,GAAG,GAAG,MAAM,CAAC,CAAC,wCAAwC;IAEpE,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,MAAM,OAAO,GAAG,UAAU,CACxB,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC,EACxE,eAAe,CAChB,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;YACzB,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,OAAO,EAAE,CAAC;gBACV,OAAO;YACT,CAAC;YAED,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;YACT,CAAC;YACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACvC,CAAC;YAED,qCAAqC;YACrC,MAAM,WAAW,GAAG,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;YAE5E,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,MAAM,GAAG,GAAG,EAAE,MAAM,GAAG,GAAG,GAAG,WAAW,CAAC,CAAC;YACnF,GAAG,IAAI,WAAW,CAAC;QACrB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACpB,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,IAAI,GAAG,GAAG,KAAK,EAAE,CAAC;gBAChB,MAAM,CACJ,IAAI,KAAK,CACP,+DAA+D,GAAG,gBAAgB,KAAK,EAAE,CAC1F,CACF,CAAC;YACJ,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YACzB,YAAY,CAAC,OAAO,CAAC,CAAC;YACtB,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CACnC,MAA6B,EAC7B,MAAc,EACd,QAAyB;IAEzB,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,qBAAqB;IAClC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;IAEjC,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;YACzB,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,OAAO;YACT,CAAC;YACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAC9B,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACvC,CAAC;YAED,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;gBACpC,MAAM,CAAC,IAAI,KAAK,CAAC,4CAA4C,UAAU,EAAE,CAAC,CAAC,CAAC;gBAC5E,OAAO;YACT,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;YAC5C,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACpB,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CACnC,cAAqC,EACrC,QAAyB;IAEzB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAqB,EAAE,EAAE;YAClD,MAAM,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC7E,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAC5B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CACzC,EAAyB,EACzB,IAAY;IAEZ,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,MAAM,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAEtC,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;YAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAU,EAAE,EAAE;YAC5B,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAExB,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAE9C,MAAM,CAAC,MAAM,kBAAkB,GAAG,EAAE,CAAC,gBAAgB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as fs from \"fs\";\nimport * as util from \"util\";\nimport { REQUEST_TIMEOUT } from \"./constants\";\n\n/**\n * Reads a readable stream into buffer. Fill the buffer from offset to end.\n *\n * @param stream - A Node.js Readable stream\n * @param buffer - Buffer to be filled, length must greater than or equal to offset\n * @param offset - From which position in the buffer to be filled, inclusive\n * @param end - To which position in the buffer to be filled, exclusive\n * @param encoding - Encoding of the Readable stream\n */\nexport async function streamToBuffer(\n  stream: NodeJS.ReadableStream,\n  buffer: Buffer,\n  offset: number,\n  end: number,\n  encoding?: BufferEncoding,\n): Promise<void> {\n  let pos = 0; // Position in stream\n  const count = end - offset; // Total amount of data needed in stream\n\n  return new Promise<void>((resolve, reject) => {\n    const timeout = setTimeout(\n      () => reject(new Error(`The operation cannot be completed in timeout.`)),\n      REQUEST_TIMEOUT,\n    );\n\n    stream.on(\"readable\", () => {\n      if (pos >= count) {\n        clearTimeout(timeout);\n        resolve();\n        return;\n      }\n\n      let chunk = stream.read();\n      if (!chunk) {\n        return;\n      }\n      if (typeof chunk === \"string\") {\n        chunk = Buffer.from(chunk, encoding);\n      }\n\n      // How much data needed in this chunk\n      const chunkLength = pos + chunk.length > count ? count - pos : chunk.length;\n\n      buffer.fill(chunk.slice(0, chunkLength), offset + pos, offset + pos + chunkLength);\n      pos += chunkLength;\n    });\n\n    stream.on(\"end\", () => {\n      clearTimeout(timeout);\n      if (pos < count) {\n        reject(\n          new Error(\n            `Stream drains before getting enough data needed. Data read: ${pos}, data need: ${count}`,\n          ),\n        );\n      }\n      resolve();\n    });\n\n    stream.on(\"error\", (msg) => {\n      clearTimeout(timeout);\n      reject(msg);\n    });\n  });\n}\n\n/**\n * Reads a readable stream into buffer entirely.\n *\n * @param stream - A Node.js Readable stream\n * @param buffer - Buffer to be filled, length must greater than or equal to offset\n * @param encoding - Encoding of the Readable stream\n * @returns with the count of bytes read.\n * @throws `RangeError` If buffer size is not big enough.\n */\nexport async function streamToBuffer2(\n  stream: NodeJS.ReadableStream,\n  buffer: Buffer,\n  encoding?: BufferEncoding,\n): Promise<number> {\n  let pos = 0; // Position in stream\n  const bufferSize = buffer.length;\n\n  return new Promise<number>((resolve, reject) => {\n    stream.on(\"readable\", () => {\n      let chunk = stream.read();\n      if (!chunk) {\n        return;\n      }\n      if (typeof chunk === \"string\") {\n        chunk = Buffer.from(chunk, encoding);\n      }\n\n      if (pos + chunk.length > bufferSize) {\n        reject(new Error(`Stream exceeds buffer size. Buffer size: ${bufferSize}`));\n        return;\n      }\n\n      buffer.fill(chunk, pos, pos + chunk.length);\n      pos += chunk.length;\n    });\n\n    stream.on(\"end\", () => {\n      resolve(pos);\n    });\n\n    stream.on(\"error\", reject);\n  });\n}\n\n/**\n * Reads a readable stream into a buffer.\n *\n * @param stream - A Node.js Readable stream\n * @param encoding - Encoding of the Readable stream\n * @returns with the count of bytes read.\n */\nexport async function streamToBuffer3(\n  readableStream: NodeJS.ReadableStream,\n  encoding?: BufferEncoding,\n): Promise<Buffer> {\n  return new Promise((resolve, reject) => {\n    const chunks: Buffer[] = [];\n    readableStream.on(\"data\", (data: Buffer | string) => {\n      chunks.push(typeof data === \"string\" ? Buffer.from(data, encoding) : data);\n    });\n    readableStream.on(\"end\", () => {\n      resolve(Buffer.concat(chunks));\n    });\n    readableStream.on(\"error\", reject);\n  });\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * Writes the content of a readstream to a local file. Returns a Promise which is completed after the file handle is closed.\n *\n * @param rs - The read stream.\n * @param file - Destination file path.\n */\nexport async function readStreamToLocalFile(\n  rs: NodeJS.ReadableStream,\n  file: string,\n): Promise<void> {\n  return new Promise<void>((resolve, reject) => {\n    const ws = fs.createWriteStream(file);\n\n    rs.on(\"error\", (err: Error) => {\n      reject(err);\n    });\n\n    ws.on(\"error\", (err: Error) => {\n      reject(err);\n    });\n\n    ws.on(\"close\", resolve);\n\n    rs.pipe(ws);\n  });\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * Promisified version of fs.stat().\n */\nexport const fsStat = util.promisify(fs.stat);\n\nexport const fsCreateReadStream = fs.createReadStream;\n"]}