/*! This file is auto-generated */
window.wp=window.wp||{},wp.shortcode={next:function(t,e,n){var s=wp.shortcode.regexp(t);if(s.lastIndex=n||0,n=s.exec(e))return"["===n[1]&&"]"===n[7]?wp.shortcode.next(t,e,s.lastIndex):(t={index:n.index,content:n[0],shortcode:wp.shortcode.fromMatch(n)},n[1]&&(t.content=t.content.slice(1),t.index++),n[7]&&(t.content=t.content.slice(0,-1)),t)},replace:function(t,e,h){return e.replace(wp.shortcode.regexp(t),function(t,e,n,s,r,o,i,c){var a;return("["!==e||"]"!==c)&&(a=h(wp.shortcode.fromMatch(arguments)))?e+a+c:t})},string:function(t){return new wp.shortcode(t).string()},regexp:_.memoize(function(t){return new RegExp("\\[(\\[?)("+t+")(?![\\w-])([^\\]\\/]*(?:\\/(?!\\])[^\\]\\/]*)*?)(?:(\\/)\\]|\\](?:([^\\[]*(?:\\[(?!\\/\\2\\])[^\\[]*)*)(\\[\\/\\2\\]))?)(\\]?)","g")}),attrs:_.memoize(function(t){var e,n={},s=[],r=/([\w-]+)\s*=\s*"([^"]*)"(?:\s|$)|([\w-]+)\s*=\s*'([^']*)'(?:\s|$)|([\w-]+)\s*=\s*([^\s'"]+)(?:\s|$)|"([^"]*)"(?:\s|$)|'([^']*)'(?:\s|$)|(\S+)(?:\s|$)/g;for(t=t.replace(/[\u00a0\u200b]/g," ");e=r.exec(t);)e[1]?n[e[1].toLowerCase()]=e[2]:e[3]?n[e[3].toLowerCase()]=e[4]:e[5]?n[e[5].toLowerCase()]=e[6]:e[7]?s.push(e[7]):e[8]?s.push(e[8]):e[9]&&s.push(e[9]);return{named:n,numeric:s}}),fromMatch:function(t){var e=t[4]?"self-closing":t[6]?"closed":"single";return new wp.shortcode({tag:t[2],attrs:t[3],type:e,content:t[5]})}},wp.shortcode=_.extend(function(t){_.extend(this,_.pick(t||{},"tag","attrs","type","content"));var e=this.attrs;this.attrs={named:{},numeric:[]},e&&(_.isString(e)?this.attrs=wp.shortcode.attrs(e):0===_.difference(_.keys(e),["named","numeric"]).length?this.attrs=_.defaults(e,this.attrs):_.each(t.attrs,function(t,e){this.set(e,t)},this))},wp.shortcode),_.extend(wp.shortcode.prototype,{get:function(t){return this.attrs[_.isNumber(t)?"numeric":"named"][t]},set:function(t,e){return this.attrs[_.isNumber(t)?"numeric":"named"][t]=e,this},string:function(){var n="["+this.tag;return _.each(this.attrs.numeric,function(t){/\s/.test(t)?n+=' "'+t+'"':n+=" "+t}),_.each(this.attrs.named,function(t,e){n+=" "+e+'="'+t+'"'}),"single"===this.type?n+"]":"self-closing"===this.type?n+" /]":(n+="]",this.content&&(n+=this.content),n+"[/"+this.tag+"]")}}),wp.html=_.extend(wp.html||{},{attrs:function(t){var e;return"/"===t[t.length-1]&&(t=t.slice(0,-1)),t=wp.shortcode.attrs(t),e=t.named,_.each(t.numeric,function(t){/\s/.test(t)||(e[t]="")}),e},string:function(t){var n="<"+t.tag,e=t.content||"";return _.each(t.attrs,function(t,e){n+=" "+e,_.isBoolean(t)&&(t=t?"true":"false"),n+='="'+t+'"'}),t.single?n+" />":(n=(n+=">")+(_.isObject(e)?wp.html.string(e):e))+"</"+t.tag+">"}});