import {Request} from '../lib/request';
import {Response} from '../lib/response';
import {AWSError} from '../lib/error';
import {Service} from '../lib/service';
import {ServiceConfigurationOptions} from '../lib/service';
import {ConfigBase as Config} from '../lib/config-base';
import {EventStream} from '../lib/event-stream/event-stream';
interface Blob {}
declare class CloudWatchLogs extends Service {
  /**
   * Constructs a service object. This object has one method for each API operation.
   */
  constructor(options?: CloudWatchLogs.Types.ClientConfiguration)
  config: Config & CloudWatchLogs.Types.ClientConfiguration;
  /**
   * Associates the specified KMS key with either one log group in the account, or with all stored CloudWatch Logs query insights results in the account. When you use AssociateKmsKey, you specify either the logGroupName parameter or the resourceIdentifier parameter. You can't specify both of those parameters in the same operation.   Specify the logGroupName parameter to cause all log events stored in the log group to be encrypted with that key. Only the log events ingested after the key is associated are encrypted with that key. Associating a KMS key with a log group overrides any existing associations between the log group and a KMS key. After a KMS key is associated with a log group, all newly ingested data for the log group is encrypted using the KMS key. This association is stored as long as the data encrypted with the KMS key is still within CloudWatch Logs. This enables CloudWatch Logs to decrypt this data whenever it is requested. Associating a key with a log group does not cause the results of queries of that log group to be encrypted with that key. To have query results encrypted with a KMS key, you must use an AssociateKmsKey operation with the resourceIdentifier parameter that specifies a query-result resource.    Specify the resourceIdentifier parameter with a query-result resource, to use that key to encrypt the stored results of all future StartQuery operations in the account. The response from a GetQueryResults operation will still return the query results in plain text. Even if you have not associated a key with your query results, the query results are encrypted when stored, using the default CloudWatch Logs method. If you run a query from a monitoring account that queries logs in a source account, the query results key from the monitoring account, if any, is used.    If you delete the key that is used to encrypt log events or log group query results, then all the associated stored log events or query results that were encrypted with that key will be unencryptable and unusable.   CloudWatch Logs supports only symmetric KMS keys. Do not use an associate an asymmetric KMS key with your log group or query results. For more information, see Using Symmetric and Asymmetric Keys.  It can take up to 5 minutes for this operation to take effect. If you attempt to associate a KMS key with a log group but the KMS key does not exist or the KMS key is disabled, you receive an InvalidParameterException error. 
   */
  associateKmsKey(params: CloudWatchLogs.Types.AssociateKmsKeyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Associates the specified KMS key with either one log group in the account, or with all stored CloudWatch Logs query insights results in the account. When you use AssociateKmsKey, you specify either the logGroupName parameter or the resourceIdentifier parameter. You can't specify both of those parameters in the same operation.   Specify the logGroupName parameter to cause all log events stored in the log group to be encrypted with that key. Only the log events ingested after the key is associated are encrypted with that key. Associating a KMS key with a log group overrides any existing associations between the log group and a KMS key. After a KMS key is associated with a log group, all newly ingested data for the log group is encrypted using the KMS key. This association is stored as long as the data encrypted with the KMS key is still within CloudWatch Logs. This enables CloudWatch Logs to decrypt this data whenever it is requested. Associating a key with a log group does not cause the results of queries of that log group to be encrypted with that key. To have query results encrypted with a KMS key, you must use an AssociateKmsKey operation with the resourceIdentifier parameter that specifies a query-result resource.    Specify the resourceIdentifier parameter with a query-result resource, to use that key to encrypt the stored results of all future StartQuery operations in the account. The response from a GetQueryResults operation will still return the query results in plain text. Even if you have not associated a key with your query results, the query results are encrypted when stored, using the default CloudWatch Logs method. If you run a query from a monitoring account that queries logs in a source account, the query results key from the monitoring account, if any, is used.    If you delete the key that is used to encrypt log events or log group query results, then all the associated stored log events or query results that were encrypted with that key will be unencryptable and unusable.   CloudWatch Logs supports only symmetric KMS keys. Do not use an associate an asymmetric KMS key with your log group or query results. For more information, see Using Symmetric and Asymmetric Keys.  It can take up to 5 minutes for this operation to take effect. If you attempt to associate a KMS key with a log group but the KMS key does not exist or the KMS key is disabled, you receive an InvalidParameterException error. 
   */
  associateKmsKey(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Cancels the specified export task. The task must be in the PENDING or RUNNING state.
   */
  cancelExportTask(params: CloudWatchLogs.Types.CancelExportTaskRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Cancels the specified export task. The task must be in the PENDING or RUNNING state.
   */
  cancelExportTask(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates a delivery. A delivery is a connection between a logical delivery source and a logical delivery destination that you have already created. Only some Amazon Web Services services support being configured as a delivery source using this operation. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  A delivery destination can represent a log group in CloudWatch Logs, an Amazon S3 bucket, or a delivery stream in Firehose. To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:   Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see PutDeliverySource.   Create a delivery destination, which is a logical object that represents the actual delivery destination. For more information, see PutDeliveryDestination.   If you are delivering logs cross-account, you must use PutDeliveryDestinationPolicy in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Use CreateDelivery to create a delivery by pairing exactly one delivery source and one delivery destination.    You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination. You can't update an existing delivery. You can only create and delete deliveries.
   */
  createDelivery(params: CloudWatchLogs.Types.CreateDeliveryRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.CreateDeliveryResponse) => void): Request<CloudWatchLogs.Types.CreateDeliveryResponse, AWSError>;
  /**
   * Creates a delivery. A delivery is a connection between a logical delivery source and a logical delivery destination that you have already created. Only some Amazon Web Services services support being configured as a delivery source using this operation. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  A delivery destination can represent a log group in CloudWatch Logs, an Amazon S3 bucket, or a delivery stream in Firehose. To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:   Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see PutDeliverySource.   Create a delivery destination, which is a logical object that represents the actual delivery destination. For more information, see PutDeliveryDestination.   If you are delivering logs cross-account, you must use PutDeliveryDestinationPolicy in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Use CreateDelivery to create a delivery by pairing exactly one delivery source and one delivery destination.    You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination. You can't update an existing delivery. You can only create and delete deliveries.
   */
  createDelivery(callback?: (err: AWSError, data: CloudWatchLogs.Types.CreateDeliveryResponse) => void): Request<CloudWatchLogs.Types.CreateDeliveryResponse, AWSError>;
  /**
   * Creates an export task so that you can efficiently export data from a log group to an Amazon S3 bucket. When you perform a CreateExportTask operation, you must use credentials that have permission to write to the S3 bucket that you specify as the destination. Exporting log data to S3 buckets that are encrypted by KMS is supported. Exporting log data to Amazon S3 buckets that have S3 Object Lock enabled with a retention period is also supported. Exporting to S3 buckets that are encrypted with AES-256 is supported.  This is an asynchronous call. If all the required information is provided, this operation initiates an export task and responds with the ID of the task. After the task has started, you can use DescribeExportTasks to get the status of the export task. Each account can only have one active (RUNNING or PENDING) export task at a time. To cancel an export task, use CancelExportTask. You can export logs from multiple log groups or multiple time ranges to the same S3 bucket. To separate log data for each export task, specify a prefix to be used as the Amazon S3 key prefix for all exported objects.  Time-based sorting on chunks of log data inside an exported file is not guaranteed. You can sort the exported log field data by using Linux utilities. 
   */
  createExportTask(params: CloudWatchLogs.Types.CreateExportTaskRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.CreateExportTaskResponse) => void): Request<CloudWatchLogs.Types.CreateExportTaskResponse, AWSError>;
  /**
   * Creates an export task so that you can efficiently export data from a log group to an Amazon S3 bucket. When you perform a CreateExportTask operation, you must use credentials that have permission to write to the S3 bucket that you specify as the destination. Exporting log data to S3 buckets that are encrypted by KMS is supported. Exporting log data to Amazon S3 buckets that have S3 Object Lock enabled with a retention period is also supported. Exporting to S3 buckets that are encrypted with AES-256 is supported.  This is an asynchronous call. If all the required information is provided, this operation initiates an export task and responds with the ID of the task. After the task has started, you can use DescribeExportTasks to get the status of the export task. Each account can only have one active (RUNNING or PENDING) export task at a time. To cancel an export task, use CancelExportTask. You can export logs from multiple log groups or multiple time ranges to the same S3 bucket. To separate log data for each export task, specify a prefix to be used as the Amazon S3 key prefix for all exported objects.  Time-based sorting on chunks of log data inside an exported file is not guaranteed. You can sort the exported log field data by using Linux utilities. 
   */
  createExportTask(callback?: (err: AWSError, data: CloudWatchLogs.Types.CreateExportTaskResponse) => void): Request<CloudWatchLogs.Types.CreateExportTaskResponse, AWSError>;
  /**
   * Creates an anomaly detector that regularly scans one or more log groups and look for patterns and anomalies in the logs. An anomaly detector can help surface issues by automatically discovering anomalies in your log event traffic. An anomaly detector uses machine learning algorithms to scan log events and find patterns. A pattern is a shared text structure that recurs among your log fields. Patterns provide a useful tool for analyzing large sets of logs because a large number of log events can often be compressed into a few patterns. The anomaly detector uses pattern recognition to find anomalies, which are unusual log events. It uses the evaluationFrequency to compare current log events and patterns with trained baselines.  Fields within a pattern are called tokens. Fields that vary within a pattern, such as a request ID or timestamp, are referred to as dynamic tokens and represented by &lt;*&gt;.  The following is an example of a pattern:  [INFO] Request time: &lt;*&gt; ms  This pattern represents log events like [INFO] Request time: 327 ms and other similar log events that differ only by the number, in this csse 327. When the pattern is displayed, the different numbers are replaced by &lt;*&gt;   Any parts of log events that are masked as sensitive data are not scanned for anomalies. For more information about masking sensitive data, see Help protect sensitive log data with masking.  
   */
  createLogAnomalyDetector(params: CloudWatchLogs.Types.CreateLogAnomalyDetectorRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.CreateLogAnomalyDetectorResponse) => void): Request<CloudWatchLogs.Types.CreateLogAnomalyDetectorResponse, AWSError>;
  /**
   * Creates an anomaly detector that regularly scans one or more log groups and look for patterns and anomalies in the logs. An anomaly detector can help surface issues by automatically discovering anomalies in your log event traffic. An anomaly detector uses machine learning algorithms to scan log events and find patterns. A pattern is a shared text structure that recurs among your log fields. Patterns provide a useful tool for analyzing large sets of logs because a large number of log events can often be compressed into a few patterns. The anomaly detector uses pattern recognition to find anomalies, which are unusual log events. It uses the evaluationFrequency to compare current log events and patterns with trained baselines.  Fields within a pattern are called tokens. Fields that vary within a pattern, such as a request ID or timestamp, are referred to as dynamic tokens and represented by &lt;*&gt;.  The following is an example of a pattern:  [INFO] Request time: &lt;*&gt; ms  This pattern represents log events like [INFO] Request time: 327 ms and other similar log events that differ only by the number, in this csse 327. When the pattern is displayed, the different numbers are replaced by &lt;*&gt;   Any parts of log events that are masked as sensitive data are not scanned for anomalies. For more information about masking sensitive data, see Help protect sensitive log data with masking.  
   */
  createLogAnomalyDetector(callback?: (err: AWSError, data: CloudWatchLogs.Types.CreateLogAnomalyDetectorResponse) => void): Request<CloudWatchLogs.Types.CreateLogAnomalyDetectorResponse, AWSError>;
  /**
   * Creates a log group with the specified name. You can create up to 1,000,000 log groups per Region per account. You must use the following guidelines when naming a log group:   Log group names must be unique within a Region for an Amazon Web Services account.   Log group names can be between 1 and 512 characters long.   Log group names consist of the following characters: a-z, A-Z, 0-9, '_' (underscore), '-' (hyphen), '/' (forward slash), '.' (period), and '#' (number sign)   Log group names can't start with the string aws/    When you create a log group, by default the log events in the log group do not expire. To set a retention policy so that events expire and are deleted after a specified time, use PutRetentionPolicy. If you associate an KMS key with the log group, ingested data is encrypted using the KMS key. This association is stored as long as the data encrypted with the KMS key is still within CloudWatch Logs. This enables CloudWatch Logs to decrypt this data whenever it is requested. If you attempt to associate a KMS key with the log group but the KMS key does not exist or the KMS key is disabled, you receive an InvalidParameterException error.   CloudWatch Logs supports only symmetric KMS keys. Do not associate an asymmetric KMS key with your log group. For more information, see Using Symmetric and Asymmetric Keys. 
   */
  createLogGroup(params: CloudWatchLogs.Types.CreateLogGroupRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates a log group with the specified name. You can create up to 1,000,000 log groups per Region per account. You must use the following guidelines when naming a log group:   Log group names must be unique within a Region for an Amazon Web Services account.   Log group names can be between 1 and 512 characters long.   Log group names consist of the following characters: a-z, A-Z, 0-9, '_' (underscore), '-' (hyphen), '/' (forward slash), '.' (period), and '#' (number sign)   Log group names can't start with the string aws/    When you create a log group, by default the log events in the log group do not expire. To set a retention policy so that events expire and are deleted after a specified time, use PutRetentionPolicy. If you associate an KMS key with the log group, ingested data is encrypted using the KMS key. This association is stored as long as the data encrypted with the KMS key is still within CloudWatch Logs. This enables CloudWatch Logs to decrypt this data whenever it is requested. If you attempt to associate a KMS key with the log group but the KMS key does not exist or the KMS key is disabled, you receive an InvalidParameterException error.   CloudWatch Logs supports only symmetric KMS keys. Do not associate an asymmetric KMS key with your log group. For more information, see Using Symmetric and Asymmetric Keys. 
   */
  createLogGroup(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates a log stream for the specified log group. A log stream is a sequence of log events that originate from a single source, such as an application instance or a resource that is being monitored. There is no limit on the number of log streams that you can create for a log group. There is a limit of 50 TPS on CreateLogStream operations, after which transactions are throttled. You must use the following guidelines when naming a log stream:   Log stream names must be unique within the log group.   Log stream names can be between 1 and 512 characters long.   Don't use ':' (colon) or '*' (asterisk) characters.  
   */
  createLogStream(params: CloudWatchLogs.Types.CreateLogStreamRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates a log stream for the specified log group. A log stream is a sequence of log events that originate from a single source, such as an application instance or a resource that is being monitored. There is no limit on the number of log streams that you can create for a log group. There is a limit of 50 TPS on CreateLogStream operations, after which transactions are throttled. You must use the following guidelines when naming a log stream:   Log stream names must be unique within the log group.   Log stream names can be between 1 and 512 characters long.   Don't use ':' (colon) or '*' (asterisk) characters.  
   */
  createLogStream(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a CloudWatch Logs account policy. This stops the policy from applying to all log groups or a subset of log groups in the account. Log-group level policies will still be in effect. To use this operation, you must be signed on with the correct permissions depending on the type of policy that you are deleting.   To delete a data protection policy, you must have the logs:DeleteDataProtectionPolicy and logs:DeleteAccountPolicy permissions.   To delete a subscription filter policy, you must have the logs:DeleteSubscriptionFilter and logs:DeleteAccountPolicy permissions.  
   */
  deleteAccountPolicy(params: CloudWatchLogs.Types.DeleteAccountPolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a CloudWatch Logs account policy. This stops the policy from applying to all log groups or a subset of log groups in the account. Log-group level policies will still be in effect. To use this operation, you must be signed on with the correct permissions depending on the type of policy that you are deleting.   To delete a data protection policy, you must have the logs:DeleteDataProtectionPolicy and logs:DeleteAccountPolicy permissions.   To delete a subscription filter policy, you must have the logs:DeleteSubscriptionFilter and logs:DeleteAccountPolicy permissions.  
   */
  deleteAccountPolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the data protection policy from the specified log group.  For more information about data protection policies, see PutDataProtectionPolicy.
   */
  deleteDataProtectionPolicy(params: CloudWatchLogs.Types.DeleteDataProtectionPolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the data protection policy from the specified log group.  For more information about data protection policies, see PutDataProtectionPolicy.
   */
  deleteDataProtectionPolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes s delivery. A delivery is a connection between a logical delivery source and a logical delivery destination. Deleting a delivery only deletes the connection between the delivery source and delivery destination. It does not delete the delivery destination or the delivery source.
   */
  deleteDelivery(params: CloudWatchLogs.Types.DeleteDeliveryRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes s delivery. A delivery is a connection between a logical delivery source and a logical delivery destination. Deleting a delivery only deletes the connection between the delivery source and delivery destination. It does not delete the delivery destination or the delivery source.
   */
  deleteDelivery(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a delivery destination. A delivery is a connection between a logical delivery source and a logical delivery destination. You can't delete a delivery destination if any current deliveries are associated with it. To find whether any deliveries are associated with this delivery destination, use the DescribeDeliveries operation and check the deliveryDestinationArn field in the results.
   */
  deleteDeliveryDestination(params: CloudWatchLogs.Types.DeleteDeliveryDestinationRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a delivery destination. A delivery is a connection between a logical delivery source and a logical delivery destination. You can't delete a delivery destination if any current deliveries are associated with it. To find whether any deliveries are associated with this delivery destination, use the DescribeDeliveries operation and check the deliveryDestinationArn field in the results.
   */
  deleteDeliveryDestination(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a delivery destination policy. For more information about these policies, see PutDeliveryDestinationPolicy.
   */
  deleteDeliveryDestinationPolicy(params: CloudWatchLogs.Types.DeleteDeliveryDestinationPolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a delivery destination policy. For more information about these policies, see PutDeliveryDestinationPolicy.
   */
  deleteDeliveryDestinationPolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a delivery source. A delivery is a connection between a logical delivery source and a logical delivery destination. You can't delete a delivery source if any current deliveries are associated with it. To find whether any deliveries are associated with this delivery source, use the DescribeDeliveries operation and check the deliverySourceName field in the results.
   */
  deleteDeliverySource(params: CloudWatchLogs.Types.DeleteDeliverySourceRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a delivery source. A delivery is a connection between a logical delivery source and a logical delivery destination. You can't delete a delivery source if any current deliveries are associated with it. To find whether any deliveries are associated with this delivery source, use the DescribeDeliveries operation and check the deliverySourceName field in the results.
   */
  deleteDeliverySource(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified destination, and eventually disables all the subscription filters that publish to it. This operation does not delete the physical resource encapsulated by the destination.
   */
  deleteDestination(params: CloudWatchLogs.Types.DeleteDestinationRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified destination, and eventually disables all the subscription filters that publish to it. This operation does not delete the physical resource encapsulated by the destination.
   */
  deleteDestination(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified CloudWatch Logs anomaly detector.
   */
  deleteLogAnomalyDetector(params: CloudWatchLogs.Types.DeleteLogAnomalyDetectorRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified CloudWatch Logs anomaly detector.
   */
  deleteLogAnomalyDetector(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified log group and permanently deletes all the archived log events associated with the log group.
   */
  deleteLogGroup(params: CloudWatchLogs.Types.DeleteLogGroupRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified log group and permanently deletes all the archived log events associated with the log group.
   */
  deleteLogGroup(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified log stream and permanently deletes all the archived log events associated with the log stream.
   */
  deleteLogStream(params: CloudWatchLogs.Types.DeleteLogStreamRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified log stream and permanently deletes all the archived log events associated with the log stream.
   */
  deleteLogStream(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified metric filter.
   */
  deleteMetricFilter(params: CloudWatchLogs.Types.DeleteMetricFilterRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified metric filter.
   */
  deleteMetricFilter(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a saved CloudWatch Logs Insights query definition. A query definition contains details about a saved CloudWatch Logs Insights query. Each DeleteQueryDefinition operation can delete one query definition. You must have the logs:DeleteQueryDefinition permission to be able to perform this operation.
   */
  deleteQueryDefinition(params: CloudWatchLogs.Types.DeleteQueryDefinitionRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DeleteQueryDefinitionResponse) => void): Request<CloudWatchLogs.Types.DeleteQueryDefinitionResponse, AWSError>;
  /**
   * Deletes a saved CloudWatch Logs Insights query definition. A query definition contains details about a saved CloudWatch Logs Insights query. Each DeleteQueryDefinition operation can delete one query definition. You must have the logs:DeleteQueryDefinition permission to be able to perform this operation.
   */
  deleteQueryDefinition(callback?: (err: AWSError, data: CloudWatchLogs.Types.DeleteQueryDefinitionResponse) => void): Request<CloudWatchLogs.Types.DeleteQueryDefinitionResponse, AWSError>;
  /**
   * Deletes a resource policy from this account. This revokes the access of the identities in that policy to put log events to this account.
   */
  deleteResourcePolicy(params: CloudWatchLogs.Types.DeleteResourcePolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes a resource policy from this account. This revokes the access of the identities in that policy to put log events to this account.
   */
  deleteResourcePolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified retention policy. Log events do not expire if they belong to log groups without a retention policy.
   */
  deleteRetentionPolicy(params: CloudWatchLogs.Types.DeleteRetentionPolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified retention policy. Log events do not expire if they belong to log groups without a retention policy.
   */
  deleteRetentionPolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified subscription filter.
   */
  deleteSubscriptionFilter(params: CloudWatchLogs.Types.DeleteSubscriptionFilterRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Deletes the specified subscription filter.
   */
  deleteSubscriptionFilter(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Returns a list of all CloudWatch Logs account policies in the account.
   */
  describeAccountPolicies(params: CloudWatchLogs.Types.DescribeAccountPoliciesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeAccountPoliciesResponse) => void): Request<CloudWatchLogs.Types.DescribeAccountPoliciesResponse, AWSError>;
  /**
   * Returns a list of all CloudWatch Logs account policies in the account.
   */
  describeAccountPolicies(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeAccountPoliciesResponse) => void): Request<CloudWatchLogs.Types.DescribeAccountPoliciesResponse, AWSError>;
  /**
   * Use this operation to return the valid and default values that are used when creating delivery sources, delivery destinations, and deliveries. For more information about deliveries, see CreateDelivery.
   */
  describeConfigurationTemplates(params: CloudWatchLogs.Types.DescribeConfigurationTemplatesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeConfigurationTemplatesResponse) => void): Request<CloudWatchLogs.Types.DescribeConfigurationTemplatesResponse, AWSError>;
  /**
   * Use this operation to return the valid and default values that are used when creating delivery sources, delivery destinations, and deliveries. For more information about deliveries, see CreateDelivery.
   */
  describeConfigurationTemplates(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeConfigurationTemplatesResponse) => void): Request<CloudWatchLogs.Types.DescribeConfigurationTemplatesResponse, AWSError>;
  /**
   * Retrieves a list of the deliveries that have been created in the account. A delivery is a connection between a  delivery source  and a  delivery destination . A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. Only some Amazon Web Services services support being configured as a delivery source. These services are listed in Enable logging from Amazon Web Services services. 
   */
  describeDeliveries(params: CloudWatchLogs.Types.DescribeDeliveriesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDeliveriesResponse) => void): Request<CloudWatchLogs.Types.DescribeDeliveriesResponse, AWSError>;
  /**
   * Retrieves a list of the deliveries that have been created in the account. A delivery is a connection between a  delivery source  and a  delivery destination . A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. Only some Amazon Web Services services support being configured as a delivery source. These services are listed in Enable logging from Amazon Web Services services. 
   */
  describeDeliveries(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDeliveriesResponse) => void): Request<CloudWatchLogs.Types.DescribeDeliveriesResponse, AWSError>;
  /**
   * Retrieves a list of the delivery destinations that have been created in the account.
   */
  describeDeliveryDestinations(params: CloudWatchLogs.Types.DescribeDeliveryDestinationsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDeliveryDestinationsResponse) => void): Request<CloudWatchLogs.Types.DescribeDeliveryDestinationsResponse, AWSError>;
  /**
   * Retrieves a list of the delivery destinations that have been created in the account.
   */
  describeDeliveryDestinations(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDeliveryDestinationsResponse) => void): Request<CloudWatchLogs.Types.DescribeDeliveryDestinationsResponse, AWSError>;
  /**
   * Retrieves a list of the delivery sources that have been created in the account.
   */
  describeDeliverySources(params: CloudWatchLogs.Types.DescribeDeliverySourcesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDeliverySourcesResponse) => void): Request<CloudWatchLogs.Types.DescribeDeliverySourcesResponse, AWSError>;
  /**
   * Retrieves a list of the delivery sources that have been created in the account.
   */
  describeDeliverySources(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDeliverySourcesResponse) => void): Request<CloudWatchLogs.Types.DescribeDeliverySourcesResponse, AWSError>;
  /**
   * Lists all your destinations. The results are ASCII-sorted by destination name.
   */
  describeDestinations(params: CloudWatchLogs.Types.DescribeDestinationsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDestinationsResponse) => void): Request<CloudWatchLogs.Types.DescribeDestinationsResponse, AWSError>;
  /**
   * Lists all your destinations. The results are ASCII-sorted by destination name.
   */
  describeDestinations(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeDestinationsResponse) => void): Request<CloudWatchLogs.Types.DescribeDestinationsResponse, AWSError>;
  /**
   * Lists the specified export tasks. You can list all your export tasks or filter the results based on task ID or task status.
   */
  describeExportTasks(params: CloudWatchLogs.Types.DescribeExportTasksRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeExportTasksResponse) => void): Request<CloudWatchLogs.Types.DescribeExportTasksResponse, AWSError>;
  /**
   * Lists the specified export tasks. You can list all your export tasks or filter the results based on task ID or task status.
   */
  describeExportTasks(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeExportTasksResponse) => void): Request<CloudWatchLogs.Types.DescribeExportTasksResponse, AWSError>;
  /**
   * Lists the specified log groups. You can list all your log groups or filter the results by prefix. The results are ASCII-sorted by log group name. CloudWatch Logs doesn’t support IAM policies that control access to the DescribeLogGroups action by using the aws:ResourceTag/key-name  condition key. Other CloudWatch Logs actions do support the use of the aws:ResourceTag/key-name  condition key to control access. For more information about using tags to control access, see Controlling access to Amazon Web Services resources using tags. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  describeLogGroups(params: CloudWatchLogs.Types.DescribeLogGroupsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeLogGroupsResponse) => void): Request<CloudWatchLogs.Types.DescribeLogGroupsResponse, AWSError>;
  /**
   * Lists the specified log groups. You can list all your log groups or filter the results by prefix. The results are ASCII-sorted by log group name. CloudWatch Logs doesn’t support IAM policies that control access to the DescribeLogGroups action by using the aws:ResourceTag/key-name  condition key. Other CloudWatch Logs actions do support the use of the aws:ResourceTag/key-name  condition key to control access. For more information about using tags to control access, see Controlling access to Amazon Web Services resources using tags. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  describeLogGroups(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeLogGroupsResponse) => void): Request<CloudWatchLogs.Types.DescribeLogGroupsResponse, AWSError>;
  /**
   * Lists the log streams for the specified log group. You can list all the log streams or filter the results by prefix. You can also control how the results are ordered. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must include one of these two parameters, but you can't include both.  This operation has a limit of five transactions per second, after which transactions are throttled. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  describeLogStreams(params: CloudWatchLogs.Types.DescribeLogStreamsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeLogStreamsResponse) => void): Request<CloudWatchLogs.Types.DescribeLogStreamsResponse, AWSError>;
  /**
   * Lists the log streams for the specified log group. You can list all the log streams or filter the results by prefix. You can also control how the results are ordered. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must include one of these two parameters, but you can't include both.  This operation has a limit of five transactions per second, after which transactions are throttled. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  describeLogStreams(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeLogStreamsResponse) => void): Request<CloudWatchLogs.Types.DescribeLogStreamsResponse, AWSError>;
  /**
   * Lists the specified metric filters. You can list all of the metric filters or filter the results by log name, prefix, metric name, or metric namespace. The results are ASCII-sorted by filter name.
   */
  describeMetricFilters(params: CloudWatchLogs.Types.DescribeMetricFiltersRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeMetricFiltersResponse) => void): Request<CloudWatchLogs.Types.DescribeMetricFiltersResponse, AWSError>;
  /**
   * Lists the specified metric filters. You can list all of the metric filters or filter the results by log name, prefix, metric name, or metric namespace. The results are ASCII-sorted by filter name.
   */
  describeMetricFilters(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeMetricFiltersResponse) => void): Request<CloudWatchLogs.Types.DescribeMetricFiltersResponse, AWSError>;
  /**
   * Returns a list of CloudWatch Logs Insights queries that are scheduled, running, or have been run recently in this account. You can request all queries or limit it to queries of a specific log group or queries with a certain status.
   */
  describeQueries(params: CloudWatchLogs.Types.DescribeQueriesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeQueriesResponse) => void): Request<CloudWatchLogs.Types.DescribeQueriesResponse, AWSError>;
  /**
   * Returns a list of CloudWatch Logs Insights queries that are scheduled, running, or have been run recently in this account. You can request all queries or limit it to queries of a specific log group or queries with a certain status.
   */
  describeQueries(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeQueriesResponse) => void): Request<CloudWatchLogs.Types.DescribeQueriesResponse, AWSError>;
  /**
   * This operation returns a paginated list of your saved CloudWatch Logs Insights query definitions. You can retrieve query definitions from the current account or from a source account that is linked to the current account. You can use the queryDefinitionNamePrefix parameter to limit the results to only the query definitions that have names that start with a certain string.
   */
  describeQueryDefinitions(params: CloudWatchLogs.Types.DescribeQueryDefinitionsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeQueryDefinitionsResponse) => void): Request<CloudWatchLogs.Types.DescribeQueryDefinitionsResponse, AWSError>;
  /**
   * This operation returns a paginated list of your saved CloudWatch Logs Insights query definitions. You can retrieve query definitions from the current account or from a source account that is linked to the current account. You can use the queryDefinitionNamePrefix parameter to limit the results to only the query definitions that have names that start with a certain string.
   */
  describeQueryDefinitions(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeQueryDefinitionsResponse) => void): Request<CloudWatchLogs.Types.DescribeQueryDefinitionsResponse, AWSError>;
  /**
   * Lists the resource policies in this account.
   */
  describeResourcePolicies(params: CloudWatchLogs.Types.DescribeResourcePoliciesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeResourcePoliciesResponse) => void): Request<CloudWatchLogs.Types.DescribeResourcePoliciesResponse, AWSError>;
  /**
   * Lists the resource policies in this account.
   */
  describeResourcePolicies(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeResourcePoliciesResponse) => void): Request<CloudWatchLogs.Types.DescribeResourcePoliciesResponse, AWSError>;
  /**
   * Lists the subscription filters for the specified log group. You can list all the subscription filters or filter the results by prefix. The results are ASCII-sorted by filter name.
   */
  describeSubscriptionFilters(params: CloudWatchLogs.Types.DescribeSubscriptionFiltersRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeSubscriptionFiltersResponse) => void): Request<CloudWatchLogs.Types.DescribeSubscriptionFiltersResponse, AWSError>;
  /**
   * Lists the subscription filters for the specified log group. You can list all the subscription filters or filter the results by prefix. The results are ASCII-sorted by filter name.
   */
  describeSubscriptionFilters(callback?: (err: AWSError, data: CloudWatchLogs.Types.DescribeSubscriptionFiltersResponse) => void): Request<CloudWatchLogs.Types.DescribeSubscriptionFiltersResponse, AWSError>;
  /**
   * Disassociates the specified KMS key from the specified log group or from all CloudWatch Logs Insights query results in the account. When you use DisassociateKmsKey, you specify either the logGroupName parameter or the resourceIdentifier parameter. You can't specify both of those parameters in the same operation.   Specify the logGroupName parameter to stop using the KMS key to encrypt future log events ingested and stored in the log group. Instead, they will be encrypted with the default CloudWatch Logs method. The log events that were ingested while the key was associated with the log group are still encrypted with that key. Therefore, CloudWatch Logs will need permissions for the key whenever that data is accessed.   Specify the resourceIdentifier parameter with the query-result resource to stop using the KMS key to encrypt the results of all future StartQuery operations in the account. They will instead be encrypted with the default CloudWatch Logs method. The results from queries that ran while the key was associated with the account are still encrypted with that key. Therefore, CloudWatch Logs will need permissions for the key whenever that data is accessed.   It can take up to 5 minutes for this operation to take effect.
   */
  disassociateKmsKey(params: CloudWatchLogs.Types.DisassociateKmsKeyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Disassociates the specified KMS key from the specified log group or from all CloudWatch Logs Insights query results in the account. When you use DisassociateKmsKey, you specify either the logGroupName parameter or the resourceIdentifier parameter. You can't specify both of those parameters in the same operation.   Specify the logGroupName parameter to stop using the KMS key to encrypt future log events ingested and stored in the log group. Instead, they will be encrypted with the default CloudWatch Logs method. The log events that were ingested while the key was associated with the log group are still encrypted with that key. Therefore, CloudWatch Logs will need permissions for the key whenever that data is accessed.   Specify the resourceIdentifier parameter with the query-result resource to stop using the KMS key to encrypt the results of all future StartQuery operations in the account. They will instead be encrypted with the default CloudWatch Logs method. The results from queries that ran while the key was associated with the account are still encrypted with that key. Therefore, CloudWatch Logs will need permissions for the key whenever that data is accessed.   It can take up to 5 minutes for this operation to take effect.
   */
  disassociateKmsKey(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Lists log events from the specified log group. You can list all the log events or filter the results using a filter pattern, a time range, and the name of the log stream. You must have the logs:FilterLogEvents permission to perform this operation. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must include one of these two parameters, but you can't include both.  By default, this operation returns as many log events as can fit in 1 MB (up to 10,000 log events) or all the events found within the specified time range. If the results include a token, that means there are more log events available. You can get additional results by specifying the token in a subsequent call. This operation can return empty results while there are more log events available through the token. The returned log events are sorted by event timestamp, the timestamp when the event was ingested by CloudWatch Logs, and the ID of the PutLogEvents request. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  filterLogEvents(params: CloudWatchLogs.Types.FilterLogEventsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.FilterLogEventsResponse) => void): Request<CloudWatchLogs.Types.FilterLogEventsResponse, AWSError>;
  /**
   * Lists log events from the specified log group. You can list all the log events or filter the results using a filter pattern, a time range, and the name of the log stream. You must have the logs:FilterLogEvents permission to perform this operation. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must include one of these two parameters, but you can't include both.  By default, this operation returns as many log events as can fit in 1 MB (up to 10,000 log events) or all the events found within the specified time range. If the results include a token, that means there are more log events available. You can get additional results by specifying the token in a subsequent call. This operation can return empty results while there are more log events available through the token. The returned log events are sorted by event timestamp, the timestamp when the event was ingested by CloudWatch Logs, and the ID of the PutLogEvents request. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  filterLogEvents(callback?: (err: AWSError, data: CloudWatchLogs.Types.FilterLogEventsResponse) => void): Request<CloudWatchLogs.Types.FilterLogEventsResponse, AWSError>;
  /**
   * Returns information about a log group data protection policy.
   */
  getDataProtectionPolicy(params: CloudWatchLogs.Types.GetDataProtectionPolicyRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDataProtectionPolicyResponse) => void): Request<CloudWatchLogs.Types.GetDataProtectionPolicyResponse, AWSError>;
  /**
   * Returns information about a log group data protection policy.
   */
  getDataProtectionPolicy(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDataProtectionPolicyResponse) => void): Request<CloudWatchLogs.Types.GetDataProtectionPolicyResponse, AWSError>;
  /**
   * Returns complete information about one logical delivery. A delivery is a connection between a  delivery source  and a  delivery destination . A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. Only some Amazon Web Services services support being configured as a delivery source. These services are listed in Enable logging from Amazon Web Services services.  You need to specify the delivery id in this operation. You can find the IDs of the deliveries in your account with the DescribeDeliveries operation.
   */
  getDelivery(params: CloudWatchLogs.Types.GetDeliveryRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliveryResponse) => void): Request<CloudWatchLogs.Types.GetDeliveryResponse, AWSError>;
  /**
   * Returns complete information about one logical delivery. A delivery is a connection between a  delivery source  and a  delivery destination . A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. Only some Amazon Web Services services support being configured as a delivery source. These services are listed in Enable logging from Amazon Web Services services.  You need to specify the delivery id in this operation. You can find the IDs of the deliveries in your account with the DescribeDeliveries operation.
   */
  getDelivery(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliveryResponse) => void): Request<CloudWatchLogs.Types.GetDeliveryResponse, AWSError>;
  /**
   * Retrieves complete information about one delivery destination.
   */
  getDeliveryDestination(params: CloudWatchLogs.Types.GetDeliveryDestinationRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliveryDestinationResponse) => void): Request<CloudWatchLogs.Types.GetDeliveryDestinationResponse, AWSError>;
  /**
   * Retrieves complete information about one delivery destination.
   */
  getDeliveryDestination(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliveryDestinationResponse) => void): Request<CloudWatchLogs.Types.GetDeliveryDestinationResponse, AWSError>;
  /**
   * Retrieves the delivery destination policy assigned to the delivery destination that you specify. For more information about delivery destinations and their policies, see PutDeliveryDestinationPolicy.
   */
  getDeliveryDestinationPolicy(params: CloudWatchLogs.Types.GetDeliveryDestinationPolicyRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliveryDestinationPolicyResponse) => void): Request<CloudWatchLogs.Types.GetDeliveryDestinationPolicyResponse, AWSError>;
  /**
   * Retrieves the delivery destination policy assigned to the delivery destination that you specify. For more information about delivery destinations and their policies, see PutDeliveryDestinationPolicy.
   */
  getDeliveryDestinationPolicy(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliveryDestinationPolicyResponse) => void): Request<CloudWatchLogs.Types.GetDeliveryDestinationPolicyResponse, AWSError>;
  /**
   * Retrieves complete information about one delivery source.
   */
  getDeliverySource(params: CloudWatchLogs.Types.GetDeliverySourceRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliverySourceResponse) => void): Request<CloudWatchLogs.Types.GetDeliverySourceResponse, AWSError>;
  /**
   * Retrieves complete information about one delivery source.
   */
  getDeliverySource(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetDeliverySourceResponse) => void): Request<CloudWatchLogs.Types.GetDeliverySourceResponse, AWSError>;
  /**
   * Retrieves information about the log anomaly detector that you specify.
   */
  getLogAnomalyDetector(params: CloudWatchLogs.Types.GetLogAnomalyDetectorRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogAnomalyDetectorResponse) => void): Request<CloudWatchLogs.Types.GetLogAnomalyDetectorResponse, AWSError>;
  /**
   * Retrieves information about the log anomaly detector that you specify.
   */
  getLogAnomalyDetector(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogAnomalyDetectorResponse) => void): Request<CloudWatchLogs.Types.GetLogAnomalyDetectorResponse, AWSError>;
  /**
   * Lists log events from the specified log stream. You can list all of the log events or filter using a time range. By default, this operation returns as many log events as can fit in a response size of 1MB (up to 10,000 log events). You can get additional log events by specifying one of the tokens in a subsequent call. This operation can return empty results while there are more log events available through the token. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must include one of these two parameters, but you can't include both. 
   */
  getLogEvents(params: CloudWatchLogs.Types.GetLogEventsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogEventsResponse) => void): Request<CloudWatchLogs.Types.GetLogEventsResponse, AWSError>;
  /**
   * Lists log events from the specified log stream. You can list all of the log events or filter using a time range. By default, this operation returns as many log events as can fit in a response size of 1MB (up to 10,000 log events). You can get additional log events by specifying one of the tokens in a subsequent call. This operation can return empty results while there are more log events available through the token. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must include one of these two parameters, but you can't include both. 
   */
  getLogEvents(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogEventsResponse) => void): Request<CloudWatchLogs.Types.GetLogEventsResponse, AWSError>;
  /**
   * Returns a list of the fields that are included in log events in the specified log group. Includes the percentage of log events that contain each field. The search is limited to a time period that you specify. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must specify one of these parameters, but you can't specify both.  In the results, fields that start with @ are fields generated by CloudWatch Logs. For example, @timestamp is the timestamp of each log event. For more information about the fields that are generated by CloudWatch logs, see Supported Logs and Discovered Fields. The response results are sorted by the frequency percentage, starting with the highest percentage. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  getLogGroupFields(params: CloudWatchLogs.Types.GetLogGroupFieldsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogGroupFieldsResponse) => void): Request<CloudWatchLogs.Types.GetLogGroupFieldsResponse, AWSError>;
  /**
   * Returns a list of the fields that are included in log events in the specified log group. Includes the percentage of log events that contain each field. The search is limited to a time period that you specify. You can specify the log group to search by using either logGroupIdentifier or logGroupName. You must specify one of these parameters, but you can't specify both.  In the results, fields that start with @ are fields generated by CloudWatch Logs. For example, @timestamp is the timestamp of each log event. For more information about the fields that are generated by CloudWatch logs, see Supported Logs and Discovered Fields. The response results are sorted by the frequency percentage, starting with the highest percentage. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account and view data from the linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  getLogGroupFields(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogGroupFieldsResponse) => void): Request<CloudWatchLogs.Types.GetLogGroupFieldsResponse, AWSError>;
  /**
   * Retrieves all of the fields and values of a single log event. All fields are retrieved, even if the original query that produced the logRecordPointer retrieved only a subset of fields. Fields are returned as field name/field value pairs. The full unparsed log event is returned within @message.
   */
  getLogRecord(params: CloudWatchLogs.Types.GetLogRecordRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogRecordResponse) => void): Request<CloudWatchLogs.Types.GetLogRecordResponse, AWSError>;
  /**
   * Retrieves all of the fields and values of a single log event. All fields are retrieved, even if the original query that produced the logRecordPointer retrieved only a subset of fields. Fields are returned as field name/field value pairs. The full unparsed log event is returned within @message.
   */
  getLogRecord(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetLogRecordResponse) => void): Request<CloudWatchLogs.Types.GetLogRecordResponse, AWSError>;
  /**
   * Returns the results from the specified query. Only the fields requested in the query are returned, along with a @ptr field, which is the identifier for the log record. You can use the value of @ptr in a GetLogRecord operation to get the full log record.  GetQueryResults does not start running a query. To run a query, use StartQuery. For more information about how long results of previous queries are available, see CloudWatch Logs quotas. If the value of the Status field in the output is Running, this operation returns only partial results. If you see a value of Scheduled or Running for the status, you can retry the operation later to see the final results.  If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account to start queries in linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  getQueryResults(params: CloudWatchLogs.Types.GetQueryResultsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.GetQueryResultsResponse) => void): Request<CloudWatchLogs.Types.GetQueryResultsResponse, AWSError>;
  /**
   * Returns the results from the specified query. Only the fields requested in the query are returned, along with a @ptr field, which is the identifier for the log record. You can use the value of @ptr in a GetLogRecord operation to get the full log record.  GetQueryResults does not start running a query. To run a query, use StartQuery. For more information about how long results of previous queries are available, see CloudWatch Logs quotas. If the value of the Status field in the output is Running, this operation returns only partial results. If you see a value of Scheduled or Running for the status, you can retry the operation later to see the final results.  If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account to start queries in linked source accounts. For more information, see CloudWatch cross-account observability.
   */
  getQueryResults(callback?: (err: AWSError, data: CloudWatchLogs.Types.GetQueryResultsResponse) => void): Request<CloudWatchLogs.Types.GetQueryResultsResponse, AWSError>;
  /**
   * Returns a list of anomalies that log anomaly detectors have found. For details about the structure format of each anomaly object that is returned, see the example in this section.
   */
  listAnomalies(params: CloudWatchLogs.Types.ListAnomaliesRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.ListAnomaliesResponse) => void): Request<CloudWatchLogs.Types.ListAnomaliesResponse, AWSError>;
  /**
   * Returns a list of anomalies that log anomaly detectors have found. For details about the structure format of each anomaly object that is returned, see the example in this section.
   */
  listAnomalies(callback?: (err: AWSError, data: CloudWatchLogs.Types.ListAnomaliesResponse) => void): Request<CloudWatchLogs.Types.ListAnomaliesResponse, AWSError>;
  /**
   * Retrieves a list of the log anomaly detectors in the account.
   */
  listLogAnomalyDetectors(params: CloudWatchLogs.Types.ListLogAnomalyDetectorsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.ListLogAnomalyDetectorsResponse) => void): Request<CloudWatchLogs.Types.ListLogAnomalyDetectorsResponse, AWSError>;
  /**
   * Retrieves a list of the log anomaly detectors in the account.
   */
  listLogAnomalyDetectors(callback?: (err: AWSError, data: CloudWatchLogs.Types.ListLogAnomalyDetectorsResponse) => void): Request<CloudWatchLogs.Types.ListLogAnomalyDetectorsResponse, AWSError>;
  /**
   * Displays the tags associated with a CloudWatch Logs resource. Currently, log groups and destinations support tagging.
   */
  listTagsForResource(params: CloudWatchLogs.Types.ListTagsForResourceRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.ListTagsForResourceResponse) => void): Request<CloudWatchLogs.Types.ListTagsForResourceResponse, AWSError>;
  /**
   * Displays the tags associated with a CloudWatch Logs resource. Currently, log groups and destinations support tagging.
   */
  listTagsForResource(callback?: (err: AWSError, data: CloudWatchLogs.Types.ListTagsForResourceResponse) => void): Request<CloudWatchLogs.Types.ListTagsForResourceResponse, AWSError>;
  /**
   *  The ListTagsLogGroup operation is on the path to deprecation. We recommend that you use ListTagsForResource instead.  Lists the tags for the specified log group.
   */
  listTagsLogGroup(params: CloudWatchLogs.Types.ListTagsLogGroupRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.ListTagsLogGroupResponse) => void): Request<CloudWatchLogs.Types.ListTagsLogGroupResponse, AWSError>;
  /**
   *  The ListTagsLogGroup operation is on the path to deprecation. We recommend that you use ListTagsForResource instead.  Lists the tags for the specified log group.
   */
  listTagsLogGroup(callback?: (err: AWSError, data: CloudWatchLogs.Types.ListTagsLogGroupResponse) => void): Request<CloudWatchLogs.Types.ListTagsLogGroupResponse, AWSError>;
  /**
   * Creates an account-level data protection policy or subscription filter policy that applies to all log groups or a subset of log groups in the account.  Data protection policy  A data protection policy can help safeguard sensitive data that's ingested by your log groups by auditing and masking the sensitive log data. Each account can have only one account-level data protection policy.  Sensitive data is detected and masked when it is ingested into a log group. When you set a data protection policy, log events ingested into the log groups before that time are not masked.  If you use PutAccountPolicy to create a data protection policy for your whole account, it applies to both existing log groups and all log groups that are created later in this account. The account-level policy is applied to existing log groups with eventual consistency. It might take up to 5 minutes before sensitive data in existing log groups begins to be masked. By default, when a user views a log event that includes masked data, the sensitive data is replaced by asterisks. A user who has the logs:Unmask permission can use a GetLogEvents or FilterLogEvents operation with the unmask parameter set to true to view the unmasked log events. Users with the logs:Unmask can also view unmasked data in the CloudWatch Logs console by running a CloudWatch Logs Insights query with the unmask query command. For more information, including a list of types of data that can be audited and masked, see Protect sensitive log data with masking. To use the PutAccountPolicy operation for a data protection policy, you must be signed on with the logs:PutDataProtectionPolicy and logs:PutAccountPolicy permissions. The PutAccountPolicy operation applies to all log groups in the account. You can use PutDataProtectionPolicy to create a data protection policy that applies to just one log group. If a log group has its own data protection policy and the account also has an account-level data protection policy, then the two policies are cumulative. Any sensitive term specified in either policy is masked.  Subscription filter policy  A subscription filter policy sets up a real-time feed of log events from CloudWatch Logs to other Amazon Web Services services. Account-level subscription filter policies apply to both existing log groups and log groups that are created later in this account. Supported destinations are Kinesis Data Streams, Firehose, and Lambda. When log events are sent to the receiving service, they are Base64 encoded and compressed with the GZIP format. The following destinations are supported for subscription filters:   An Kinesis Data Streams data stream in the same account as the subscription policy, for same-account delivery.   An Firehose data stream in the same account as the subscription policy, for same-account delivery.   A Lambda function in the same account as the subscription policy, for same-account delivery.   A logical destination in a different account created with PutDestination, for cross-account delivery. Kinesis Data Streams and Firehose are supported as logical destinations.   Each account can have one account-level subscription filter policy per Region. If you are updating an existing filter, you must specify the correct name in PolicyName. To perform a PutAccountPolicy subscription filter operation for any destination except a Lambda function, you must also have the iam:PassRole permission.
   */
  putAccountPolicy(params: CloudWatchLogs.Types.PutAccountPolicyRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutAccountPolicyResponse) => void): Request<CloudWatchLogs.Types.PutAccountPolicyResponse, AWSError>;
  /**
   * Creates an account-level data protection policy or subscription filter policy that applies to all log groups or a subset of log groups in the account.  Data protection policy  A data protection policy can help safeguard sensitive data that's ingested by your log groups by auditing and masking the sensitive log data. Each account can have only one account-level data protection policy.  Sensitive data is detected and masked when it is ingested into a log group. When you set a data protection policy, log events ingested into the log groups before that time are not masked.  If you use PutAccountPolicy to create a data protection policy for your whole account, it applies to both existing log groups and all log groups that are created later in this account. The account-level policy is applied to existing log groups with eventual consistency. It might take up to 5 minutes before sensitive data in existing log groups begins to be masked. By default, when a user views a log event that includes masked data, the sensitive data is replaced by asterisks. A user who has the logs:Unmask permission can use a GetLogEvents or FilterLogEvents operation with the unmask parameter set to true to view the unmasked log events. Users with the logs:Unmask can also view unmasked data in the CloudWatch Logs console by running a CloudWatch Logs Insights query with the unmask query command. For more information, including a list of types of data that can be audited and masked, see Protect sensitive log data with masking. To use the PutAccountPolicy operation for a data protection policy, you must be signed on with the logs:PutDataProtectionPolicy and logs:PutAccountPolicy permissions. The PutAccountPolicy operation applies to all log groups in the account. You can use PutDataProtectionPolicy to create a data protection policy that applies to just one log group. If a log group has its own data protection policy and the account also has an account-level data protection policy, then the two policies are cumulative. Any sensitive term specified in either policy is masked.  Subscription filter policy  A subscription filter policy sets up a real-time feed of log events from CloudWatch Logs to other Amazon Web Services services. Account-level subscription filter policies apply to both existing log groups and log groups that are created later in this account. Supported destinations are Kinesis Data Streams, Firehose, and Lambda. When log events are sent to the receiving service, they are Base64 encoded and compressed with the GZIP format. The following destinations are supported for subscription filters:   An Kinesis Data Streams data stream in the same account as the subscription policy, for same-account delivery.   An Firehose data stream in the same account as the subscription policy, for same-account delivery.   A Lambda function in the same account as the subscription policy, for same-account delivery.   A logical destination in a different account created with PutDestination, for cross-account delivery. Kinesis Data Streams and Firehose are supported as logical destinations.   Each account can have one account-level subscription filter policy per Region. If you are updating an existing filter, you must specify the correct name in PolicyName. To perform a PutAccountPolicy subscription filter operation for any destination except a Lambda function, you must also have the iam:PassRole permission.
   */
  putAccountPolicy(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutAccountPolicyResponse) => void): Request<CloudWatchLogs.Types.PutAccountPolicyResponse, AWSError>;
  /**
   * Creates a data protection policy for the specified log group. A data protection policy can help safeguard sensitive data that's ingested by the log group by auditing and masking the sensitive log data.  Sensitive data is detected and masked when it is ingested into the log group. When you set a data protection policy, log events ingested into the log group before that time are not masked.  By default, when a user views a log event that includes masked data, the sensitive data is replaced by asterisks. A user who has the logs:Unmask permission can use a GetLogEvents or FilterLogEvents operation with the unmask parameter set to true to view the unmasked log events. Users with the logs:Unmask can also view unmasked data in the CloudWatch Logs console by running a CloudWatch Logs Insights query with the unmask query command. For more information, including a list of types of data that can be audited and masked, see Protect sensitive log data with masking. The PutDataProtectionPolicy operation applies to only the specified log group. You can also use PutAccountPolicy to create an account-level data protection policy that applies to all log groups in the account, including both existing log groups and log groups that are created level. If a log group has its own data protection policy and the account also has an account-level data protection policy, then the two policies are cumulative. Any sensitive term specified in either policy is masked.
   */
  putDataProtectionPolicy(params: CloudWatchLogs.Types.PutDataProtectionPolicyRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDataProtectionPolicyResponse) => void): Request<CloudWatchLogs.Types.PutDataProtectionPolicyResponse, AWSError>;
  /**
   * Creates a data protection policy for the specified log group. A data protection policy can help safeguard sensitive data that's ingested by the log group by auditing and masking the sensitive log data.  Sensitive data is detected and masked when it is ingested into the log group. When you set a data protection policy, log events ingested into the log group before that time are not masked.  By default, when a user views a log event that includes masked data, the sensitive data is replaced by asterisks. A user who has the logs:Unmask permission can use a GetLogEvents or FilterLogEvents operation with the unmask parameter set to true to view the unmasked log events. Users with the logs:Unmask can also view unmasked data in the CloudWatch Logs console by running a CloudWatch Logs Insights query with the unmask query command. For more information, including a list of types of data that can be audited and masked, see Protect sensitive log data with masking. The PutDataProtectionPolicy operation applies to only the specified log group. You can also use PutAccountPolicy to create an account-level data protection policy that applies to all log groups in the account, including both existing log groups and log groups that are created level. If a log group has its own data protection policy and the account also has an account-level data protection policy, then the two policies are cumulative. Any sensitive term specified in either policy is masked.
   */
  putDataProtectionPolicy(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDataProtectionPolicyResponse) => void): Request<CloudWatchLogs.Types.PutDataProtectionPolicyResponse, AWSError>;
  /**
   * Creates or updates a logical delivery destination. A delivery destination is an Amazon Web Services resource that represents an Amazon Web Services service that logs can be sent to. CloudWatch Logs, Amazon S3, and Firehose are supported as logs delivery destinations. To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:   Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see PutDeliverySource.   Use PutDeliveryDestination to create a delivery destination, which is a logical object that represents the actual delivery destination.    If you are delivering logs cross-account, you must use PutDeliveryDestinationPolicy in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Use CreateDelivery to create a delivery by pairing exactly one delivery source and one delivery destination. For more information, see CreateDelivery.    You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination. Only some Amazon Web Services services support being configured as a delivery source. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  If you use this operation to update an existing delivery destination, all the current delivery destination parameters are overwritten with the new parameter values that you specify.
   */
  putDeliveryDestination(params: CloudWatchLogs.Types.PutDeliveryDestinationRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDeliveryDestinationResponse) => void): Request<CloudWatchLogs.Types.PutDeliveryDestinationResponse, AWSError>;
  /**
   * Creates or updates a logical delivery destination. A delivery destination is an Amazon Web Services resource that represents an Amazon Web Services service that logs can be sent to. CloudWatch Logs, Amazon S3, and Firehose are supported as logs delivery destinations. To configure logs delivery between a supported Amazon Web Services service and a destination, you must do the following:   Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see PutDeliverySource.   Use PutDeliveryDestination to create a delivery destination, which is a logical object that represents the actual delivery destination.    If you are delivering logs cross-account, you must use PutDeliveryDestinationPolicy in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Use CreateDelivery to create a delivery by pairing exactly one delivery source and one delivery destination. For more information, see CreateDelivery.    You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination. Only some Amazon Web Services services support being configured as a delivery source. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  If you use this operation to update an existing delivery destination, all the current delivery destination parameters are overwritten with the new parameter values that you specify.
   */
  putDeliveryDestination(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDeliveryDestinationResponse) => void): Request<CloudWatchLogs.Types.PutDeliveryDestinationResponse, AWSError>;
  /**
   * Creates and assigns an IAM policy that grants permissions to CloudWatch Logs to deliver logs cross-account to a specified destination in this account. To configure the delivery of logs from an Amazon Web Services service in another account to a logs delivery destination in the current account, you must do the following:   Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see PutDeliverySource.   Create a delivery destination, which is a logical object that represents the actual delivery destination. For more information, see PutDeliveryDestination.   Use this operation in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Create a delivery by pairing exactly one delivery source and one delivery destination. For more information, see CreateDelivery.   Only some Amazon Web Services services support being configured as a delivery source. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  The contents of the policy must include two statements. One statement enables general logs delivery, and the other allows delivery to the chosen destination. See the examples for the needed policies.
   */
  putDeliveryDestinationPolicy(params: CloudWatchLogs.Types.PutDeliveryDestinationPolicyRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDeliveryDestinationPolicyResponse) => void): Request<CloudWatchLogs.Types.PutDeliveryDestinationPolicyResponse, AWSError>;
  /**
   * Creates and assigns an IAM policy that grants permissions to CloudWatch Logs to deliver logs cross-account to a specified destination in this account. To configure the delivery of logs from an Amazon Web Services service in another account to a logs delivery destination in the current account, you must do the following:   Create a delivery source, which is a logical object that represents the resource that is actually sending the logs. For more information, see PutDeliverySource.   Create a delivery destination, which is a logical object that represents the actual delivery destination. For more information, see PutDeliveryDestination.   Use this operation in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Create a delivery by pairing exactly one delivery source and one delivery destination. For more information, see CreateDelivery.   Only some Amazon Web Services services support being configured as a delivery source. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  The contents of the policy must include two statements. One statement enables general logs delivery, and the other allows delivery to the chosen destination. See the examples for the needed policies.
   */
  putDeliveryDestinationPolicy(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDeliveryDestinationPolicyResponse) => void): Request<CloudWatchLogs.Types.PutDeliveryDestinationPolicyResponse, AWSError>;
  /**
   * Creates or updates a logical delivery source. A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. To configure logs delivery between a delivery destination and an Amazon Web Services service that is supported as a delivery source, you must do the following:   Use PutDeliverySource to create a delivery source, which is a logical object that represents the resource that is actually sending the logs.    Use PutDeliveryDestination to create a delivery destination, which is a logical object that represents the actual delivery destination. For more information, see PutDeliveryDestination.   If you are delivering logs cross-account, you must use PutDeliveryDestinationPolicy in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Use CreateDelivery to create a delivery by pairing exactly one delivery source and one delivery destination. For more information, see CreateDelivery.    You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination. Only some Amazon Web Services services support being configured as a delivery source. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  If you use this operation to update an existing delivery source, all the current delivery source parameters are overwritten with the new parameter values that you specify.
   */
  putDeliverySource(params: CloudWatchLogs.Types.PutDeliverySourceRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDeliverySourceResponse) => void): Request<CloudWatchLogs.Types.PutDeliverySourceResponse, AWSError>;
  /**
   * Creates or updates a logical delivery source. A delivery source represents an Amazon Web Services resource that sends logs to an logs delivery destination. The destination can be CloudWatch Logs, Amazon S3, or Firehose. To configure logs delivery between a delivery destination and an Amazon Web Services service that is supported as a delivery source, you must do the following:   Use PutDeliverySource to create a delivery source, which is a logical object that represents the resource that is actually sending the logs.    Use PutDeliveryDestination to create a delivery destination, which is a logical object that represents the actual delivery destination. For more information, see PutDeliveryDestination.   If you are delivering logs cross-account, you must use PutDeliveryDestinationPolicy in the destination account to assign an IAM policy to the destination. This policy allows delivery to that destination.    Use CreateDelivery to create a delivery by pairing exactly one delivery source and one delivery destination. For more information, see CreateDelivery.    You can configure a single delivery source to send logs to multiple destinations by creating multiple deliveries. You can also create multiple deliveries to configure multiple delivery sources to send logs to the same delivery destination. Only some Amazon Web Services services support being configured as a delivery source. These services are listed as Supported [V2 Permissions] in the table at Enabling logging from Amazon Web Services services.  If you use this operation to update an existing delivery source, all the current delivery source parameters are overwritten with the new parameter values that you specify.
   */
  putDeliverySource(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDeliverySourceResponse) => void): Request<CloudWatchLogs.Types.PutDeliverySourceResponse, AWSError>;
  /**
   * Creates or updates a destination. This operation is used only to create destinations for cross-account subscriptions. A destination encapsulates a physical resource (such as an Amazon Kinesis stream). With a destination, you can subscribe to a real-time stream of log events for a different account, ingested using PutLogEvents. Through an access policy, a destination controls what is written to it. By default, PutDestination does not set any access policy with the destination, which means a cross-account user cannot call PutSubscriptionFilter against this destination. To enable this, the destination owner must call PutDestinationPolicy after PutDestination. To perform a PutDestination operation, you must also have the iam:PassRole permission.
   */
  putDestination(params: CloudWatchLogs.Types.PutDestinationRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDestinationResponse) => void): Request<CloudWatchLogs.Types.PutDestinationResponse, AWSError>;
  /**
   * Creates or updates a destination. This operation is used only to create destinations for cross-account subscriptions. A destination encapsulates a physical resource (such as an Amazon Kinesis stream). With a destination, you can subscribe to a real-time stream of log events for a different account, ingested using PutLogEvents. Through an access policy, a destination controls what is written to it. By default, PutDestination does not set any access policy with the destination, which means a cross-account user cannot call PutSubscriptionFilter against this destination. To enable this, the destination owner must call PutDestinationPolicy after PutDestination. To perform a PutDestination operation, you must also have the iam:PassRole permission.
   */
  putDestination(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutDestinationResponse) => void): Request<CloudWatchLogs.Types.PutDestinationResponse, AWSError>;
  /**
   * Creates or updates an access policy associated with an existing destination. An access policy is an IAM policy document that is used to authorize claims to register a subscription filter against a given destination.
   */
  putDestinationPolicy(params: CloudWatchLogs.Types.PutDestinationPolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates or updates an access policy associated with an existing destination. An access policy is an IAM policy document that is used to authorize claims to register a subscription filter against a given destination.
   */
  putDestinationPolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Uploads a batch of log events to the specified log stream.  The sequence token is now ignored in PutLogEvents actions. PutLogEvents actions are always accepted and never return InvalidSequenceTokenException or DataAlreadyAcceptedException even if the sequence token is not valid. You can use parallel PutLogEvents actions on the same log stream.   The batch of events must satisfy the following constraints:   The maximum batch size is 1,048,576 bytes. This size is calculated as the sum of all event messages in UTF-8, plus 26 bytes for each log event.   None of the log events in the batch can be more than 2 hours in the future.   None of the log events in the batch can be more than 14 days in the past. Also, none of the log events can be from earlier than the retention period of the log group.   The log events in the batch must be in chronological order by their timestamp. The timestamp is the time that the event occurred, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. (In Amazon Web Services Tools for PowerShell and the Amazon Web Services SDK for .NET, the timestamp is specified in .NET format: yyyy-mm-ddThh:mm:ss. For example, 2017-09-15T13:45:30.)    A batch of log events in a single request cannot span more than 24 hours. Otherwise, the operation fails.   Each log event can be no larger than 256 KB.   The maximum number of log events in a batch is 10,000.    The quota of five requests per second per log stream has been removed. Instead, PutLogEvents actions are throttled based on a per-second per-account quota. You can request an increase to the per-second throttling quota by using the Service Quotas service.    If a call to PutLogEvents returns "UnrecognizedClientException" the most likely cause is a non-valid Amazon Web Services access key ID or secret key. 
   */
  putLogEvents(params: CloudWatchLogs.Types.PutLogEventsRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutLogEventsResponse) => void): Request<CloudWatchLogs.Types.PutLogEventsResponse, AWSError>;
  /**
   * Uploads a batch of log events to the specified log stream.  The sequence token is now ignored in PutLogEvents actions. PutLogEvents actions are always accepted and never return InvalidSequenceTokenException or DataAlreadyAcceptedException even if the sequence token is not valid. You can use parallel PutLogEvents actions on the same log stream.   The batch of events must satisfy the following constraints:   The maximum batch size is 1,048,576 bytes. This size is calculated as the sum of all event messages in UTF-8, plus 26 bytes for each log event.   None of the log events in the batch can be more than 2 hours in the future.   None of the log events in the batch can be more than 14 days in the past. Also, none of the log events can be from earlier than the retention period of the log group.   The log events in the batch must be in chronological order by their timestamp. The timestamp is the time that the event occurred, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. (In Amazon Web Services Tools for PowerShell and the Amazon Web Services SDK for .NET, the timestamp is specified in .NET format: yyyy-mm-ddThh:mm:ss. For example, 2017-09-15T13:45:30.)    A batch of log events in a single request cannot span more than 24 hours. Otherwise, the operation fails.   Each log event can be no larger than 256 KB.   The maximum number of log events in a batch is 10,000.    The quota of five requests per second per log stream has been removed. Instead, PutLogEvents actions are throttled based on a per-second per-account quota. You can request an increase to the per-second throttling quota by using the Service Quotas service.    If a call to PutLogEvents returns "UnrecognizedClientException" the most likely cause is a non-valid Amazon Web Services access key ID or secret key. 
   */
  putLogEvents(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutLogEventsResponse) => void): Request<CloudWatchLogs.Types.PutLogEventsResponse, AWSError>;
  /**
   * Creates or updates a metric filter and associates it with the specified log group. With metric filters, you can configure rules to extract metric data from log events ingested through PutLogEvents. The maximum number of metric filters that can be associated with a log group is 100. Using regular expressions to create metric filters is supported. For these filters, there is a quotas of quota of two regular expression patterns within a single filter pattern. There is also a quota of five regular expression patterns per log group. For more information about using regular expressions in metric filters, see  Filter pattern syntax for metric filters, subscription filters, filter log events, and Live Tail. When you create a metric filter, you can also optionally assign a unit and dimensions to the metric that is created.  Metrics extracted from log events are charged as custom metrics. To prevent unexpected high charges, do not specify high-cardinality fields such as IPAddress or requestID as dimensions. Each different value found for a dimension is treated as a separate metric and accrues charges as a separate custom metric.  CloudWatch Logs might disable a metric filter if it generates 1,000 different name/value pairs for your specified dimensions within one hour. You can also set up a billing alarm to alert you if your charges are higher than expected. For more information, see  Creating a Billing Alarm to Monitor Your Estimated Amazon Web Services Charges.  
   */
  putMetricFilter(params: CloudWatchLogs.Types.PutMetricFilterRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates or updates a metric filter and associates it with the specified log group. With metric filters, you can configure rules to extract metric data from log events ingested through PutLogEvents. The maximum number of metric filters that can be associated with a log group is 100. Using regular expressions to create metric filters is supported. For these filters, there is a quotas of quota of two regular expression patterns within a single filter pattern. There is also a quota of five regular expression patterns per log group. For more information about using regular expressions in metric filters, see  Filter pattern syntax for metric filters, subscription filters, filter log events, and Live Tail. When you create a metric filter, you can also optionally assign a unit and dimensions to the metric that is created.  Metrics extracted from log events are charged as custom metrics. To prevent unexpected high charges, do not specify high-cardinality fields such as IPAddress or requestID as dimensions. Each different value found for a dimension is treated as a separate metric and accrues charges as a separate custom metric.  CloudWatch Logs might disable a metric filter if it generates 1,000 different name/value pairs for your specified dimensions within one hour. You can also set up a billing alarm to alert you if your charges are higher than expected. For more information, see  Creating a Billing Alarm to Monitor Your Estimated Amazon Web Services Charges.  
   */
  putMetricFilter(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates or updates a query definition for CloudWatch Logs Insights. For more information, see Analyzing Log Data with CloudWatch Logs Insights. To update a query definition, specify its queryDefinitionId in your request. The values of name, queryString, and logGroupNames are changed to the values that you specify in your update operation. No current values are retained from the current query definition. For example, imagine updating a current query definition that includes log groups. If you don't specify the logGroupNames parameter in your update operation, the query definition changes to contain no log groups. You must have the logs:PutQueryDefinition permission to be able to perform this operation.
   */
  putQueryDefinition(params: CloudWatchLogs.Types.PutQueryDefinitionRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutQueryDefinitionResponse) => void): Request<CloudWatchLogs.Types.PutQueryDefinitionResponse, AWSError>;
  /**
   * Creates or updates a query definition for CloudWatch Logs Insights. For more information, see Analyzing Log Data with CloudWatch Logs Insights. To update a query definition, specify its queryDefinitionId in your request. The values of name, queryString, and logGroupNames are changed to the values that you specify in your update operation. No current values are retained from the current query definition. For example, imagine updating a current query definition that includes log groups. If you don't specify the logGroupNames parameter in your update operation, the query definition changes to contain no log groups. You must have the logs:PutQueryDefinition permission to be able to perform this operation.
   */
  putQueryDefinition(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutQueryDefinitionResponse) => void): Request<CloudWatchLogs.Types.PutQueryDefinitionResponse, AWSError>;
  /**
   * Creates or updates a resource policy allowing other Amazon Web Services services to put log events to this account, such as Amazon Route 53. An account can have up to 10 resource policies per Amazon Web Services Region.
   */
  putResourcePolicy(params: CloudWatchLogs.Types.PutResourcePolicyRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.PutResourcePolicyResponse) => void): Request<CloudWatchLogs.Types.PutResourcePolicyResponse, AWSError>;
  /**
   * Creates or updates a resource policy allowing other Amazon Web Services services to put log events to this account, such as Amazon Route 53. An account can have up to 10 resource policies per Amazon Web Services Region.
   */
  putResourcePolicy(callback?: (err: AWSError, data: CloudWatchLogs.Types.PutResourcePolicyResponse) => void): Request<CloudWatchLogs.Types.PutResourcePolicyResponse, AWSError>;
  /**
   * Sets the retention of the specified log group. With a retention policy, you can configure the number of days for which to retain log events in the specified log group.  CloudWatch Logs doesn’t immediately delete log events when they reach their retention setting. It typically takes up to 72 hours after that before log events are deleted, but in rare situations might take longer. To illustrate, imagine that you change a log group to have a longer retention setting when it contains log events that are past the expiration date, but haven’t been deleted. Those log events will take up to 72 hours to be deleted after the new retention date is reached. To make sure that log data is deleted permanently, keep a log group at its lower retention setting until 72 hours after the previous retention period ends. Alternatively, wait to change the retention setting until you confirm that the earlier log events are deleted.  When log events reach their retention setting they are marked for deletion. After they are marked for deletion, they do not add to your archival storage costs anymore, even if they are not actually deleted until later. These log events marked for deletion are also not included when you use an API to retrieve the storedBytes value to see how many bytes a log group is storing. 
   */
  putRetentionPolicy(params: CloudWatchLogs.Types.PutRetentionPolicyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Sets the retention of the specified log group. With a retention policy, you can configure the number of days for which to retain log events in the specified log group.  CloudWatch Logs doesn’t immediately delete log events when they reach their retention setting. It typically takes up to 72 hours after that before log events are deleted, but in rare situations might take longer. To illustrate, imagine that you change a log group to have a longer retention setting when it contains log events that are past the expiration date, but haven’t been deleted. Those log events will take up to 72 hours to be deleted after the new retention date is reached. To make sure that log data is deleted permanently, keep a log group at its lower retention setting until 72 hours after the previous retention period ends. Alternatively, wait to change the retention setting until you confirm that the earlier log events are deleted.  When log events reach their retention setting they are marked for deletion. After they are marked for deletion, they do not add to your archival storage costs anymore, even if they are not actually deleted until later. These log events marked for deletion are also not included when you use an API to retrieve the storedBytes value to see how many bytes a log group is storing. 
   */
  putRetentionPolicy(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates or updates a subscription filter and associates it with the specified log group. With subscription filters, you can subscribe to a real-time stream of log events ingested through PutLogEvents and have them delivered to a specific destination. When log events are sent to the receiving service, they are Base64 encoded and compressed with the GZIP format. The following destinations are supported for subscription filters:   An Amazon Kinesis data stream belonging to the same account as the subscription filter, for same-account delivery.   A logical destination created with PutDestination that belongs to a different account, for cross-account delivery. We currently support Kinesis Data Streams and Firehose as logical destinations.   An Amazon Kinesis Data Firehose delivery stream that belongs to the same account as the subscription filter, for same-account delivery.   An Lambda function that belongs to the same account as the subscription filter, for same-account delivery.   Each log group can have up to two subscription filters associated with it. If you are updating an existing filter, you must specify the correct name in filterName.  Using regular expressions to create subscription filters is supported. For these filters, there is a quotas of quota of two regular expression patterns within a single filter pattern. There is also a quota of five regular expression patterns per log group. For more information about using regular expressions in subscription filters, see  Filter pattern syntax for metric filters, subscription filters, filter log events, and Live Tail. To perform a PutSubscriptionFilter operation for any destination except a Lambda function, you must also have the iam:PassRole permission.
   */
  putSubscriptionFilter(params: CloudWatchLogs.Types.PutSubscriptionFilterRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Creates or updates a subscription filter and associates it with the specified log group. With subscription filters, you can subscribe to a real-time stream of log events ingested through PutLogEvents and have them delivered to a specific destination. When log events are sent to the receiving service, they are Base64 encoded and compressed with the GZIP format. The following destinations are supported for subscription filters:   An Amazon Kinesis data stream belonging to the same account as the subscription filter, for same-account delivery.   A logical destination created with PutDestination that belongs to a different account, for cross-account delivery. We currently support Kinesis Data Streams and Firehose as logical destinations.   An Amazon Kinesis Data Firehose delivery stream that belongs to the same account as the subscription filter, for same-account delivery.   An Lambda function that belongs to the same account as the subscription filter, for same-account delivery.   Each log group can have up to two subscription filters associated with it. If you are updating an existing filter, you must specify the correct name in filterName.  Using regular expressions to create subscription filters is supported. For these filters, there is a quotas of quota of two regular expression patterns within a single filter pattern. There is also a quota of five regular expression patterns per log group. For more information about using regular expressions in subscription filters, see  Filter pattern syntax for metric filters, subscription filters, filter log events, and Live Tail. To perform a PutSubscriptionFilter operation for any destination except a Lambda function, you must also have the iam:PassRole permission.
   */
  putSubscriptionFilter(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Starts a Live Tail streaming session for one or more log groups. A Live Tail session returns a stream of log events that have been recently ingested in the log groups. For more information, see Use Live Tail to view logs in near real time.  The response to this operation is a response stream, over which the server sends live log events and the client receives them. The following objects are sent over the stream:   A single LiveTailSessionStart object is sent at the start of the session.   Every second, a LiveTailSessionUpdate object is sent. Each of these objects contains an array of the actual log events. If no new log events were ingested in the past second, the LiveTailSessionUpdate object will contain an empty array. The array of log events contained in a LiveTailSessionUpdate can include as many as 500 log events. If the number of log events matching the request exceeds 500 per second, the log events are sampled down to 500 log events to be included in each LiveTailSessionUpdate object. If your client consumes the log events slower than the server produces them, CloudWatch Logs buffers up to 10 LiveTailSessionUpdate events or 5000 log events, after which it starts dropping the oldest events.   A SessionStreamingException object is returned if an unknown error occurs on the server side.   A SessionTimeoutException object is returned when the session times out, after it has been kept open for three hours.    You can end a session before it times out by closing the session stream or by closing the client that is receiving the stream. The session also ends if the established connection between the client and the server breaks.  For examples of using an SDK to start a Live Tail session, see  Start a Live Tail session using an Amazon Web Services SDK.
   */
  startLiveTail(params: CloudWatchLogs.Types.StartLiveTailRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.StartLiveTailResponse) => void): Request<CloudWatchLogs.Types.StartLiveTailResponse, AWSError>;
  /**
   * Starts a Live Tail streaming session for one or more log groups. A Live Tail session returns a stream of log events that have been recently ingested in the log groups. For more information, see Use Live Tail to view logs in near real time.  The response to this operation is a response stream, over which the server sends live log events and the client receives them. The following objects are sent over the stream:   A single LiveTailSessionStart object is sent at the start of the session.   Every second, a LiveTailSessionUpdate object is sent. Each of these objects contains an array of the actual log events. If no new log events were ingested in the past second, the LiveTailSessionUpdate object will contain an empty array. The array of log events contained in a LiveTailSessionUpdate can include as many as 500 log events. If the number of log events matching the request exceeds 500 per second, the log events are sampled down to 500 log events to be included in each LiveTailSessionUpdate object. If your client consumes the log events slower than the server produces them, CloudWatch Logs buffers up to 10 LiveTailSessionUpdate events or 5000 log events, after which it starts dropping the oldest events.   A SessionStreamingException object is returned if an unknown error occurs on the server side.   A SessionTimeoutException object is returned when the session times out, after it has been kept open for three hours.    You can end a session before it times out by closing the session stream or by closing the client that is receiving the stream. The session also ends if the established connection between the client and the server breaks.  For examples of using an SDK to start a Live Tail session, see  Start a Live Tail session using an Amazon Web Services SDK.
   */
  startLiveTail(callback?: (err: AWSError, data: CloudWatchLogs.Types.StartLiveTailResponse) => void): Request<CloudWatchLogs.Types.StartLiveTailResponse, AWSError>;
  /**
   * Schedules a query of a log group using CloudWatch Logs Insights. You specify the log group and time range to query and the query string to use. For more information, see CloudWatch Logs Insights Query Syntax. After you run a query using StartQuery, the query results are stored by CloudWatch Logs. You can use GetQueryResults to retrieve the results of a query, using the queryId that StartQuery returns.  If you have associated a KMS key with the query results in this account, then StartQuery uses that key to encrypt the results when it stores them. If no key is associated with query results, the query results are encrypted with the default CloudWatch Logs encryption method. Queries time out after 60 minutes of runtime. If your queries are timing out, reduce the time range being searched or partition your query into a number of queries. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account to start a query in a linked source account. For more information, see CloudWatch cross-account observability. For a cross-account StartQuery operation, the query definition must be defined in the monitoring account. You can have up to 30 concurrent CloudWatch Logs insights queries, including queries that have been added to dashboards. 
   */
  startQuery(params: CloudWatchLogs.Types.StartQueryRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.StartQueryResponse) => void): Request<CloudWatchLogs.Types.StartQueryResponse, AWSError>;
  /**
   * Schedules a query of a log group using CloudWatch Logs Insights. You specify the log group and time range to query and the query string to use. For more information, see CloudWatch Logs Insights Query Syntax. After you run a query using StartQuery, the query results are stored by CloudWatch Logs. You can use GetQueryResults to retrieve the results of a query, using the queryId that StartQuery returns.  If you have associated a KMS key with the query results in this account, then StartQuery uses that key to encrypt the results when it stores them. If no key is associated with query results, the query results are encrypted with the default CloudWatch Logs encryption method. Queries time out after 60 minutes of runtime. If your queries are timing out, reduce the time range being searched or partition your query into a number of queries. If you are using CloudWatch cross-account observability, you can use this operation in a monitoring account to start a query in a linked source account. For more information, see CloudWatch cross-account observability. For a cross-account StartQuery operation, the query definition must be defined in the monitoring account. You can have up to 30 concurrent CloudWatch Logs insights queries, including queries that have been added to dashboards. 
   */
  startQuery(callback?: (err: AWSError, data: CloudWatchLogs.Types.StartQueryResponse) => void): Request<CloudWatchLogs.Types.StartQueryResponse, AWSError>;
  /**
   * Stops a CloudWatch Logs Insights query that is in progress. If the query has already ended, the operation returns an error indicating that the specified query is not running.
   */
  stopQuery(params: CloudWatchLogs.Types.StopQueryRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.StopQueryResponse) => void): Request<CloudWatchLogs.Types.StopQueryResponse, AWSError>;
  /**
   * Stops a CloudWatch Logs Insights query that is in progress. If the query has already ended, the operation returns an error indicating that the specified query is not running.
   */
  stopQuery(callback?: (err: AWSError, data: CloudWatchLogs.Types.StopQueryResponse) => void): Request<CloudWatchLogs.Types.StopQueryResponse, AWSError>;
  /**
   *  The TagLogGroup operation is on the path to deprecation. We recommend that you use TagResource instead.  Adds or updates the specified tags for the specified log group. To list the tags for a log group, use ListTagsForResource. To remove tags, use UntagResource. For more information about tags, see Tag Log Groups in Amazon CloudWatch Logs in the Amazon CloudWatch Logs User Guide. CloudWatch Logs doesn’t support IAM policies that prevent users from assigning specified tags to log groups using the aws:Resource/key-name  or aws:TagKeys condition keys. For more information about using tags to control access, see Controlling access to Amazon Web Services resources using tags.
   */
  tagLogGroup(params: CloudWatchLogs.Types.TagLogGroupRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   *  The TagLogGroup operation is on the path to deprecation. We recommend that you use TagResource instead.  Adds or updates the specified tags for the specified log group. To list the tags for a log group, use ListTagsForResource. To remove tags, use UntagResource. For more information about tags, see Tag Log Groups in Amazon CloudWatch Logs in the Amazon CloudWatch Logs User Guide. CloudWatch Logs doesn’t support IAM policies that prevent users from assigning specified tags to log groups using the aws:Resource/key-name  or aws:TagKeys condition keys. For more information about using tags to control access, see Controlling access to Amazon Web Services resources using tags.
   */
  tagLogGroup(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Assigns one or more tags (key-value pairs) to the specified CloudWatch Logs resource. Currently, the only CloudWatch Logs resources that can be tagged are log groups and destinations.  Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values. Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters. You can use the TagResource action with a resource that already has tags. If you specify a new tag key for the alarm, this tag is appended to the list of tags associated with the alarm. If you specify a tag key that is already associated with the alarm, the new tag value that you specify replaces the previous value for that tag. You can associate as many as 50 tags with a CloudWatch Logs resource.
   */
  tagResource(params: CloudWatchLogs.Types.TagResourceRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Assigns one or more tags (key-value pairs) to the specified CloudWatch Logs resource. Currently, the only CloudWatch Logs resources that can be tagged are log groups and destinations.  Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values. Tags don't have any semantic meaning to Amazon Web Services and are interpreted strictly as strings of characters. You can use the TagResource action with a resource that already has tags. If you specify a new tag key for the alarm, this tag is appended to the list of tags associated with the alarm. If you specify a tag key that is already associated with the alarm, the new tag value that you specify replaces the previous value for that tag. You can associate as many as 50 tags with a CloudWatch Logs resource.
   */
  tagResource(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Tests the filter pattern of a metric filter against a sample of log event messages. You can use this operation to validate the correctness of a metric filter pattern.
   */
  testMetricFilter(params: CloudWatchLogs.Types.TestMetricFilterRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.TestMetricFilterResponse) => void): Request<CloudWatchLogs.Types.TestMetricFilterResponse, AWSError>;
  /**
   * Tests the filter pattern of a metric filter against a sample of log event messages. You can use this operation to validate the correctness of a metric filter pattern.
   */
  testMetricFilter(callback?: (err: AWSError, data: CloudWatchLogs.Types.TestMetricFilterResponse) => void): Request<CloudWatchLogs.Types.TestMetricFilterResponse, AWSError>;
  /**
   *  The UntagLogGroup operation is on the path to deprecation. We recommend that you use UntagResource instead.  Removes the specified tags from the specified log group. To list the tags for a log group, use ListTagsForResource. To add tags, use TagResource. CloudWatch Logs doesn’t support IAM policies that prevent users from assigning specified tags to log groups using the aws:Resource/key-name  or aws:TagKeys condition keys. 
   */
  untagLogGroup(params: CloudWatchLogs.Types.UntagLogGroupRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   *  The UntagLogGroup operation is on the path to deprecation. We recommend that you use UntagResource instead.  Removes the specified tags from the specified log group. To list the tags for a log group, use ListTagsForResource. To add tags, use TagResource. CloudWatch Logs doesn’t support IAM policies that prevent users from assigning specified tags to log groups using the aws:Resource/key-name  or aws:TagKeys condition keys. 
   */
  untagLogGroup(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Removes one or more tags from the specified resource.
   */
  untagResource(params: CloudWatchLogs.Types.UntagResourceRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Removes one or more tags from the specified resource.
   */
  untagResource(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Use this operation to suppress anomaly detection for a specified anomaly or pattern. If you suppress an anomaly, CloudWatch Logs won’t report new occurrences of that anomaly and won't update that anomaly with new data. If you suppress a pattern, CloudWatch Logs won’t report any anomalies related to that pattern. You must specify either anomalyId or patternId, but you can't specify both parameters in the same operation. If you have previously used this operation to suppress detection of a pattern or anomaly, you can use it again to cause CloudWatch Logs to end the suppression. To do this, use this operation and specify the anomaly or pattern to stop suppressing, and omit the suppressionType and suppressionPeriod parameters.
   */
  updateAnomaly(params: CloudWatchLogs.Types.UpdateAnomalyRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Use this operation to suppress anomaly detection for a specified anomaly or pattern. If you suppress an anomaly, CloudWatch Logs won’t report new occurrences of that anomaly and won't update that anomaly with new data. If you suppress a pattern, CloudWatch Logs won’t report any anomalies related to that pattern. You must specify either anomalyId or patternId, but you can't specify both parameters in the same operation. If you have previously used this operation to suppress detection of a pattern or anomaly, you can use it again to cause CloudWatch Logs to end the suppression. To do this, use this operation and specify the anomaly or pattern to stop suppressing, and omit the suppressionType and suppressionPeriod parameters.
   */
  updateAnomaly(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Use this operation to update the configuration of a delivery to change either the S3 path pattern or the format of the delivered logs. You can't use this operation to change the source or destination of the delivery.
   */
  updateDeliveryConfiguration(params: CloudWatchLogs.Types.UpdateDeliveryConfigurationRequest, callback?: (err: AWSError, data: CloudWatchLogs.Types.UpdateDeliveryConfigurationResponse) => void): Request<CloudWatchLogs.Types.UpdateDeliveryConfigurationResponse, AWSError>;
  /**
   * Use this operation to update the configuration of a delivery to change either the S3 path pattern or the format of the delivered logs. You can't use this operation to change the source or destination of the delivery.
   */
  updateDeliveryConfiguration(callback?: (err: AWSError, data: CloudWatchLogs.Types.UpdateDeliveryConfigurationResponse) => void): Request<CloudWatchLogs.Types.UpdateDeliveryConfigurationResponse, AWSError>;
  /**
   * Updates an existing log anomaly detector.
   */
  updateLogAnomalyDetector(params: CloudWatchLogs.Types.UpdateLogAnomalyDetectorRequest, callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
  /**
   * Updates an existing log anomaly detector.
   */
  updateLogAnomalyDetector(callback?: (err: AWSError, data: {}) => void): Request<{}, AWSError>;
}
declare namespace CloudWatchLogs {
  export type AccessPolicy = string;
  export type AccountId = string;
  export type AccountIds = AccountId[];
  export type AccountPolicies = AccountPolicy[];
  export interface AccountPolicy {
    /**
     * The name of the account policy.
     */
    policyName?: PolicyName;
    /**
     * The policy document for this account policy. The JSON specified in policyDocument can be up to 30,720 characters.
     */
    policyDocument?: AccountPolicyDocument;
    /**
     * The date and time that this policy was most recently updated.
     */
    lastUpdatedTime?: Timestamp;
    /**
     * The type of policy for this account policy.
     */
    policyType?: PolicyType;
    /**
     * The scope of the account policy.
     */
    scope?: Scope;
    /**
     * The log group selection criteria for this subscription filter policy.
     */
    selectionCriteria?: SelectionCriteria;
    /**
     * The Amazon Web Services account ID that the policy applies to.
     */
    accountId?: AccountId;
  }
  export type AccountPolicyDocument = string;
  export type AllowedActionForAllowVendedLogsDeliveryForResource = string;
  export type AllowedFieldDelimiters = FieldDelimiter[];
  export type AllowedFields = RecordField[];
  export type AmazonResourceName = string;
  export type Anomalies = Anomaly[];
  export interface Anomaly {
    /**
     * The unique ID that CloudWatch Logs assigned to this anomaly.
     */
    anomalyId: AnomalyId;
    /**
     * The ID of the pattern used to help identify this anomaly.
     */
    patternId: PatternId;
    /**
     * The ARN of the anomaly detector that identified this anomaly.
     */
    anomalyDetectorArn: AnomalyDetectorArn;
    /**
     * The pattern used to help identify this anomaly, in string format.
     */
    patternString: PatternString;
    /**
     * The pattern used to help identify this anomaly, in regular expression format.
     */
    patternRegex?: PatternRegex;
    /**
     * The priority level of this anomaly, as determined by CloudWatch Logs. Priority is computed based on log severity labels such as FATAL and ERROR and the amount of deviation from the baseline. Possible values are HIGH, MEDIUM, and LOW.
     */
    priority?: Priority;
    /**
     * The date and time when the anomaly detector first saw this anomaly. It is specified as epoch time, which is the number of seconds since January 1, 1970, 00:00:00 UTC.
     */
    firstSeen: EpochMillis;
    /**
     * The date and time when the anomaly detector most recently saw this anomaly. It is specified as epoch time, which is the number of seconds since January 1, 1970, 00:00:00 UTC.
     */
    lastSeen: EpochMillis;
    /**
     * A human-readable description of the anomaly. This description is generated by CloudWatch Logs.
     */
    description: Description;
    /**
     * Specifies whether this anomaly is still ongoing.
     */
    active: Boolean;
    /**
     * Indicates the current state of this anomaly. If it is still being treated as an anomaly, the value is Active. If you have suppressed this anomaly by using the UpdateAnomaly operation, the value is Suppressed. If this behavior is now considered to be normal, the value is Baseline.
     */
    state: State;
    /**
     * A map showing times when the anomaly detector ran, and the number of occurrences of this anomaly that were detected at each of those runs. The times are specified in epoch time, which is the number of seconds since January 1, 1970, 00:00:00 UTC.
     */
    histogram: Histogram;
    /**
     * An array of sample log event messages that are considered to be part of this anomaly.
     */
    logSamples: LogSamples;
    /**
     * An array of structures where each structure contains information about one token that makes up the pattern.
     */
    patternTokens: PatternTokens;
    /**
     * An array of ARNS of the log groups that contained log events considered to be part of this anomaly.
     */
    logGroupArnList: LogGroupArnList;
    /**
     * Indicates whether this anomaly is currently suppressed. To suppress an anomaly, use UpdateAnomaly.
     */
    suppressed?: Boolean;
    /**
     * If the anomaly is suppressed, this indicates when it was suppressed.
     */
    suppressedDate?: EpochMillis;
    /**
     * If the anomaly is suppressed, this indicates when the suppression will end. If this value is 0, the anomaly was suppressed with no expiration, with the INFINITE value.
     */
    suppressedUntil?: EpochMillis;
    /**
     * If this anomaly is suppressed, this field is true if the suppression is because the pattern is suppressed. If false, then only this particular anomaly is suppressed.
     */
    isPatternLevelSuppression?: Boolean;
  }
  export interface AnomalyDetector {
    /**
     * The ARN of the anomaly detector.
     */
    anomalyDetectorArn?: AnomalyDetectorArn;
    /**
     * The name of the anomaly detector.
     */
    detectorName?: DetectorName;
    /**
     * A list of the ARNs of the log groups that this anomaly detector watches.
     */
    logGroupArnList?: LogGroupArnList;
    /**
     * Specifies how often the anomaly detector runs and look for anomalies.
     */
    evaluationFrequency?: EvaluationFrequency;
    filterPattern?: FilterPattern;
    /**
     * Specifies the current status of the anomaly detector. To pause an anomaly detector, use the enabled parameter in the UpdateLogAnomalyDetector operation.
     */
    anomalyDetectorStatus?: AnomalyDetectorStatus;
    /**
     * The ID of the KMS key assigned to this anomaly detector, if any.
     */
    kmsKeyId?: KmsKeyId;
    /**
     * The date and time when this anomaly detector was created.
     */
    creationTimeStamp?: EpochMillis;
    /**
     * The date and time when this anomaly detector was most recently modified.
     */
    lastModifiedTimeStamp?: EpochMillis;
    /**
     * The number of days used as the life cycle of anomalies. After this time, anomalies are automatically baselined and the anomaly detector model will treat new occurrences of similar event as normal. 
     */
    anomalyVisibilityTime?: AnomalyVisibilityTime;
  }
  export type AnomalyDetectorArn = string;
  export type AnomalyDetectorStatus = "INITIALIZING"|"TRAINING"|"ANALYZING"|"FAILED"|"DELETED"|"PAUSED"|string;
  export type AnomalyDetectors = AnomalyDetector[];
  export type AnomalyId = string;
  export type AnomalyVisibilityTime = number;
  export type Arn = string;
  export interface AssociateKmsKeyRequest {
    /**
     * The name of the log group. In your AssociateKmsKey operation, you must specify either the resourceIdentifier parameter or the logGroup parameter, but you can't specify both.
     */
    logGroupName?: LogGroupName;
    /**
     * The Amazon Resource Name (ARN) of the KMS key to use when encrypting log data. This must be a symmetric KMS key. For more information, see Amazon Resource Names and Using Symmetric and Asymmetric Keys.
     */
    kmsKeyId: KmsKeyId;
    /**
     * Specifies the target for this operation. You must specify one of the following:   Specify the following ARN to have future GetQueryResults operations in this account encrypt the results with the specified KMS key. Replace REGION and ACCOUNT_ID with your Region and account ID.  arn:aws:logs:REGION:ACCOUNT_ID:query-result:*    Specify the ARN of a log group to have CloudWatch Logs use the KMS key to encrypt log events that are ingested and stored by that log group. The log group ARN must be in the following format. Replace REGION and ACCOUNT_ID with your Region and account ID.  arn:aws:logs:REGION:ACCOUNT_ID:log-group:LOG_GROUP_NAME     In your AssociateKmsKey operation, you must specify either the resourceIdentifier parameter or the logGroup parameter, but you can't specify both.
     */
    resourceIdentifier?: ResourceIdentifier;
  }
  export type Boolean = boolean;
  export interface CancelExportTaskRequest {
    /**
     * The ID of the export task.
     */
    taskId: ExportTaskId;
  }
  export type ClientToken = string;
  export interface ConfigurationTemplate {
    /**
     * A string specifying which service this configuration template applies to. For more information about supported services see Enable logging from Amazon Web Services services..
     */
    service?: Service;
    /**
     * A string specifying which log type this configuration template applies to.
     */
    logType?: LogType;
    /**
     * A string specifying which resource type this configuration template applies to.
     */
    resourceType?: ResourceType;
    /**
     * A string specifying which destination type this configuration template applies to.
     */
    deliveryDestinationType?: DeliveryDestinationType;
    /**
     * A mapping that displays the default value of each property within a delivery’s configuration, if it is not specified in the request.
     */
    defaultDeliveryConfigValues?: ConfigurationTemplateDeliveryConfigValues;
    /**
     * The allowed fields that a caller can use in the recordFields parameter of a CreateDelivery or UpdateDeliveryConfiguration operation.
     */
    allowedFields?: AllowedFields;
    /**
     * The list of delivery destination output formats that are supported by this log source.
     */
    allowedOutputFormats?: OutputFormats;
    /**
     * The action permissions that a caller needs to have to be able to successfully create a delivery source on the desired resource type when calling PutDeliverySource.
     */
    allowedActionForAllowVendedLogsDeliveryForResource?: AllowedActionForAllowVendedLogsDeliveryForResource;
    /**
     * The valid values that a caller can use as field delimiters when calling CreateDelivery or UpdateDeliveryConfiguration on a delivery that delivers in Plain, W3C, or Raw format.
     */
    allowedFieldDelimiters?: AllowedFieldDelimiters;
    /**
     * The list of variable fields that can be used in the suffix path of a delivery that delivers to an S3 bucket.
     */
    allowedSuffixPathFields?: RecordFields;
  }
  export interface ConfigurationTemplateDeliveryConfigValues {
    /**
     * The default record fields that will be delivered when a list of record fields is not provided in a CreateDelivery operation.
     */
    recordFields?: RecordFields;
    /**
     * The default field delimiter that is used in a CreateDelivery operation when the field delimiter is not specified in that operation. The field delimiter is used only when the final output delivery is in Plain, W3C, or Raw format.
     */
    fieldDelimiter?: FieldDelimiter;
    /**
     * The delivery parameters that are used when you create a delivery to a delivery destination that is an S3 Bucket.
     */
    s3DeliveryConfiguration?: S3DeliveryConfiguration;
  }
  export type ConfigurationTemplates = ConfigurationTemplate[];
  export type Count = number;
  export interface CreateDeliveryRequest {
    /**
     * The name of the delivery source to use for this delivery.
     */
    deliverySourceName: DeliverySourceName;
    /**
     * The ARN of the delivery destination to use for this delivery.
     */
    deliveryDestinationArn: Arn;
    /**
     * The list of record fields to be delivered to the destination, in order. If the delivery’s log source has mandatory fields, they must be included in this list.
     */
    recordFields?: RecordFields;
    /**
     * The field delimiter to use between record fields when the final output format of a delivery is in Plain, W3C, or Raw format.
     */
    fieldDelimiter?: FieldDelimiter;
    /**
     * This structure contains parameters that are valid only when the delivery’s delivery destination is an S3 bucket.
     */
    s3DeliveryConfiguration?: S3DeliveryConfiguration;
    /**
     * An optional list of key-value pairs to associate with the resource. For more information about tagging, see Tagging Amazon Web Services resources 
     */
    tags?: Tags;
  }
  export interface CreateDeliveryResponse {
    /**
     * A structure that contains information about the delivery that you just created.
     */
    delivery?: Delivery;
  }
  export interface CreateExportTaskRequest {
    /**
     * The name of the export task.
     */
    taskName?: ExportTaskName;
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * Export only log streams that match the provided prefix. If you don't specify a value, no prefix filter is applied.
     */
    logStreamNamePrefix?: LogStreamName;
    /**
     * The start time of the range for the request, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp earlier than this time are not exported.
     */
    from: Timestamp;
    /**
     * The end time of the range for the request, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp later than this time are not exported. You must specify a time that is not earlier than when this log group was created.
     */
    to: Timestamp;
    /**
     * The name of S3 bucket for the exported log data. The bucket must be in the same Amazon Web Services Region.
     */
    destination: ExportDestinationBucket;
    /**
     * The prefix used as the start of the key for every object exported. If you don't specify a value, the default is exportedlogs.
     */
    destinationPrefix?: ExportDestinationPrefix;
  }
  export interface CreateExportTaskResponse {
    /**
     * The ID of the export task.
     */
    taskId?: ExportTaskId;
  }
  export interface CreateLogAnomalyDetectorRequest {
    /**
     * An array containing the ARN of the log group that this anomaly detector will watch. You can specify only one log group ARN.
     */
    logGroupArnList: LogGroupArnList;
    /**
     * A name for this anomaly detector.
     */
    detectorName?: DetectorName;
    /**
     * Specifies how often the anomaly detector is to run and look for anomalies. Set this value according to the frequency that the log group receives new logs. For example, if the log group receives new log events every 10 minutes, then 15 minutes might be a good setting for evaluationFrequency .
     */
    evaluationFrequency?: EvaluationFrequency;
    /**
     * You can use this parameter to limit the anomaly detection model to examine only log events that match the pattern you specify here. For more information, see Filter and Pattern Syntax.
     */
    filterPattern?: FilterPattern;
    /**
     * Optionally assigns a KMS key to secure this anomaly detector and its findings. If a key is assigned, the anomalies found and the model used by this detector are encrypted at rest with the key. If a key is assigned to an anomaly detector, a user must have permissions for both this key and for the anomaly detector to retrieve information about the anomalies that it finds. For more information about using a KMS key and to see the required IAM policy, see Use a KMS key with an anomaly detector.
     */
    kmsKeyId?: KmsKeyId;
    /**
     * The number of days to have visibility on an anomaly. After this time period has elapsed for an anomaly, it will be automatically baselined and the anomaly detector will treat new occurrences of a similar anomaly as normal. Therefore, if you do not correct the cause of an anomaly during the time period specified in anomalyVisibilityTime, it will be considered normal going forward and will not be detected as an anomaly.
     */
    anomalyVisibilityTime?: AnomalyVisibilityTime;
    /**
     * An optional list of key-value pairs to associate with the resource. For more information about tagging, see Tagging Amazon Web Services resources 
     */
    tags?: Tags;
  }
  export interface CreateLogAnomalyDetectorResponse {
    /**
     * The ARN of the log anomaly detector that you just created.
     */
    anomalyDetectorArn?: AnomalyDetectorArn;
  }
  export interface CreateLogGroupRequest {
    /**
     * A name for the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The Amazon Resource Name (ARN) of the KMS key to use when encrypting log data. For more information, see Amazon Resource Names.
     */
    kmsKeyId?: KmsKeyId;
    /**
     * The key-value pairs to use for the tags. You can grant users access to certain log groups while preventing them from accessing other log groups. To do so, tag your groups and use IAM policies that refer to those tags. To assign tags when you create a log group, you must have either the logs:TagResource or logs:TagLogGroup permission. For more information about tagging, see Tagging Amazon Web Services resources. For more information about using tags to control access, see Controlling access to Amazon Web Services resources using tags.
     */
    tags?: Tags;
    /**
     * Use this parameter to specify the log group class for this log group. There are two classes:   The Standard log class supports all CloudWatch Logs features.   The Infrequent Access log class supports a subset of CloudWatch Logs features and incurs lower costs.   If you omit this parameter, the default of STANDARD is used.  The value of logGroupClass can't be changed after a log group is created.  For details about the features supported by each class, see Log classes 
     */
    logGroupClass?: LogGroupClass;
  }
  export interface CreateLogStreamRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The name of the log stream.
     */
    logStreamName: LogStreamName;
  }
  export type DataProtectionPolicyDocument = string;
  export type DataProtectionStatus = "ACTIVATED"|"DELETED"|"ARCHIVED"|"DISABLED"|string;
  export type Days = number;
  export type DefaultValue = number;
  export interface DeleteAccountPolicyRequest {
    /**
     * The name of the policy to delete.
     */
    policyName: PolicyName;
    /**
     * The type of policy to delete.
     */
    policyType: PolicyType;
  }
  export interface DeleteDataProtectionPolicyRequest {
    /**
     * The name or ARN of the log group that you want to delete the data protection policy for.
     */
    logGroupIdentifier: LogGroupIdentifier;
  }
  export interface DeleteDeliveryDestinationPolicyRequest {
    /**
     * The name of the delivery destination that you want to delete the policy for.
     */
    deliveryDestinationName: DeliveryDestinationName;
  }
  export interface DeleteDeliveryDestinationRequest {
    /**
     * The name of the delivery destination that you want to delete. You can find a list of delivery destionation names by using the DescribeDeliveryDestinations operation.
     */
    name: DeliveryDestinationName;
  }
  export interface DeleteDeliveryRequest {
    /**
     * The unique ID of the delivery to delete. You can find the ID of a delivery with the DescribeDeliveries operation.
     */
    id: DeliveryId;
  }
  export interface DeleteDeliverySourceRequest {
    /**
     * The name of the delivery source that you want to delete.
     */
    name: DeliverySourceName;
  }
  export interface DeleteDestinationRequest {
    /**
     * The name of the destination.
     */
    destinationName: DestinationName;
  }
  export interface DeleteLogAnomalyDetectorRequest {
    /**
     * The ARN of the anomaly detector to delete. You can find the ARNs of log anomaly detectors in your account by using the ListLogAnomalyDetectors operation.
     */
    anomalyDetectorArn: AnomalyDetectorArn;
  }
  export interface DeleteLogGroupRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
  }
  export interface DeleteLogStreamRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The name of the log stream.
     */
    logStreamName: LogStreamName;
  }
  export interface DeleteMetricFilterRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The name of the metric filter.
     */
    filterName: FilterName;
  }
  export interface DeleteQueryDefinitionRequest {
    /**
     * The ID of the query definition that you want to delete. You can use DescribeQueryDefinitions to retrieve the IDs of your saved query definitions.
     */
    queryDefinitionId: QueryId;
  }
  export interface DeleteQueryDefinitionResponse {
    /**
     * A value of TRUE indicates that the operation succeeded. FALSE indicates that the operation failed.
     */
    success?: Success;
  }
  export interface DeleteResourcePolicyRequest {
    /**
     * The name of the policy to be revoked. This parameter is required.
     */
    policyName?: PolicyName;
  }
  export interface DeleteRetentionPolicyRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
  }
  export interface DeleteSubscriptionFilterRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The name of the subscription filter.
     */
    filterName: FilterName;
  }
  export type Deliveries = Delivery[];
  export interface Delivery {
    /**
     * The unique ID that identifies this delivery in your account.
     */
    id?: DeliveryId;
    /**
     * The Amazon Resource Name (ARN) that uniquely identifies this delivery.
     */
    arn?: Arn;
    /**
     * The name of the delivery source that is associated with this delivery.
     */
    deliverySourceName?: DeliverySourceName;
    /**
     * The ARN of the delivery destination that is associated with this delivery.
     */
    deliveryDestinationArn?: Arn;
    /**
     * Displays whether the delivery destination associated with this delivery is CloudWatch Logs, Amazon S3, or Firehose.
     */
    deliveryDestinationType?: DeliveryDestinationType;
    /**
     * The record fields used in this delivery.
     */
    recordFields?: RecordFields;
    /**
     * The field delimiter that is used between record fields when the final output format of a delivery is in Plain, W3C, or Raw format.
     */
    fieldDelimiter?: FieldDelimiter;
    /**
     * This structure contains delivery configurations that apply only when the delivery destination resource is an S3 bucket.
     */
    s3DeliveryConfiguration?: S3DeliveryConfiguration;
    /**
     * The tags that have been assigned to this delivery.
     */
    tags?: Tags;
  }
  export interface DeliveryDestination {
    /**
     * The name of this delivery destination.
     */
    name?: DeliveryDestinationName;
    /**
     * The Amazon Resource Name (ARN) that uniquely identifies this delivery destination.
     */
    arn?: Arn;
    /**
     * Displays whether this delivery destination is CloudWatch Logs, Amazon S3, or Firehose.
     */
    deliveryDestinationType?: DeliveryDestinationType;
    /**
     * The format of the logs that are sent to this delivery destination. 
     */
    outputFormat?: OutputFormat;
    /**
     * A structure that contains the ARN of the Amazon Web Services resource that will receive the logs.
     */
    deliveryDestinationConfiguration?: DeliveryDestinationConfiguration;
    /**
     * The tags that have been assigned to this delivery destination.
     */
    tags?: Tags;
  }
  export interface DeliveryDestinationConfiguration {
    /**
     * The ARN of the Amazon Web Services destination that this delivery destination represents. That Amazon Web Services destination can be a log group in CloudWatch Logs, an Amazon S3 bucket, or a delivery stream in Firehose.
     */
    destinationResourceArn: Arn;
  }
  export type DeliveryDestinationName = string;
  export type DeliveryDestinationPolicy = string;
  export type DeliveryDestinationType = "S3"|"CWL"|"FH"|string;
  export type DeliveryDestinationTypes = DeliveryDestinationType[];
  export type DeliveryDestinations = DeliveryDestination[];
  export type DeliveryId = string;
  export interface DeliverySource {
    /**
     * The unique name of the delivery source.
     */
    name?: DeliverySourceName;
    /**
     * The Amazon Resource Name (ARN) that uniquely identifies this delivery source.
     */
    arn?: Arn;
    /**
     * This array contains the ARN of the Amazon Web Services resource that sends logs and is represented by this delivery source. Currently, only one ARN can be in the array.
     */
    resourceArns?: ResourceArns;
    /**
     * The Amazon Web Services service that is sending logs.
     */
    service?: Service;
    /**
     * The type of log that the source is sending. For valid values for this parameter, see the documentation for the source service.
     */
    logType?: LogType;
    /**
     * The tags that have been assigned to this delivery source.
     */
    tags?: Tags;
  }
  export type DeliverySourceName = string;
  export type DeliverySources = DeliverySource[];
  export type DeliverySuffixPath = string;
  export type Descending = boolean;
  export interface DescribeAccountPoliciesRequest {
    /**
     * Use this parameter to limit the returned policies to only the policies that match the policy type that you specify.
     */
    policyType: PolicyType;
    /**
     * Use this parameter to limit the returned policies to only the policy with the name that you specify.
     */
    policyName?: PolicyName;
    /**
     * If you are using an account that is set up as a monitoring account for CloudWatch unified cross-account observability, you can use this to specify the account ID of a source account. If you do, the operation returns the account policy for the specified account. Currently, you can specify only one account ID in this parameter. If you omit this parameter, only the policy in the current account is returned.
     */
    accountIdentifiers?: AccountIds;
  }
  export interface DescribeAccountPoliciesResponse {
    /**
     * An array of structures that contain information about the CloudWatch Logs account policies that match the specified filters.
     */
    accountPolicies?: AccountPolicies;
  }
  export interface DescribeConfigurationTemplatesRequest {
    /**
     * Use this parameter to filter the response to include only the configuration templates that apply to the Amazon Web Services service that you specify here.
     */
    service?: Service;
    /**
     * Use this parameter to filter the response to include only the configuration templates that apply to the log types that you specify here.
     */
    logTypes?: LogTypes;
    /**
     * Use this parameter to filter the response to include only the configuration templates that apply to the resource types that you specify here.
     */
    resourceTypes?: ResourceTypes;
    /**
     * Use this parameter to filter the response to include only the configuration templates that apply to the delivery destination types that you specify here.
     */
    deliveryDestinationTypes?: DeliveryDestinationTypes;
    nextToken?: NextToken;
    /**
     * Use this parameter to limit the number of configuration templates that are returned in the response.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeConfigurationTemplatesResponse {
    /**
     * An array of objects, where each object describes one configuration template that matches the filters that you specified in the request.
     */
    configurationTemplates?: ConfigurationTemplates;
    nextToken?: NextToken;
  }
  export interface DescribeDeliveriesRequest {
    nextToken?: NextToken;
    /**
     * Optionally specify the maximum number of deliveries to return in the response.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeDeliveriesResponse {
    /**
     * An array of structures. Each structure contains information about one delivery in the account.
     */
    deliveries?: Deliveries;
    nextToken?: NextToken;
  }
  export interface DescribeDeliveryDestinationsRequest {
    nextToken?: NextToken;
    /**
     * Optionally specify the maximum number of delivery destinations to return in the response.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeDeliveryDestinationsResponse {
    /**
     * An array of structures. Each structure contains information about one delivery destination in the account.
     */
    deliveryDestinations?: DeliveryDestinations;
    nextToken?: NextToken;
  }
  export interface DescribeDeliverySourcesRequest {
    nextToken?: NextToken;
    /**
     * Optionally specify the maximum number of delivery sources to return in the response.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeDeliverySourcesResponse {
    /**
     * An array of structures. Each structure contains information about one delivery source in the account.
     */
    deliverySources?: DeliverySources;
    nextToken?: NextToken;
  }
  export interface DescribeDestinationsRequest {
    /**
     * The prefix to match. If you don't specify a value, no prefix filter is applied.
     */
    DestinationNamePrefix?: DestinationName;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of items returned. If you don't specify a value, the default maximum value of 50 items is used.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeDestinationsResponse {
    /**
     * The destinations.
     */
    destinations?: Destinations;
    nextToken?: NextToken;
  }
  export interface DescribeExportTasksRequest {
    /**
     * The ID of the export task. Specifying a task ID filters the results to one or zero export tasks.
     */
    taskId?: ExportTaskId;
    /**
     * The status code of the export task. Specifying a status code filters the results to zero or more export tasks.
     */
    statusCode?: ExportTaskStatusCode;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of items returned. If you don't specify a value, the default is up to 50 items.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeExportTasksResponse {
    /**
     * The export tasks.
     */
    exportTasks?: ExportTasks;
    nextToken?: NextToken;
  }
  export type DescribeLimit = number;
  export interface DescribeLogGroupsRequest {
    /**
     * When includeLinkedAccounts is set to True, use this parameter to specify the list of accounts to search. You can specify as many as 20 account IDs in the array. 
     */
    accountIdentifiers?: AccountIds;
    /**
     * The prefix to match.   logGroupNamePrefix and logGroupNamePattern are mutually exclusive. Only one of these parameters can be passed.  
     */
    logGroupNamePrefix?: LogGroupName;
    /**
     * If you specify a string for this parameter, the operation returns only log groups that have names that match the string based on a case-sensitive substring search. For example, if you specify Foo, log groups named FooBar, aws/Foo, and GroupFoo would match, but foo, F/o/o and Froo would not match. If you specify logGroupNamePattern in your request, then only arn, creationTime, and logGroupName are included in the response.    logGroupNamePattern and logGroupNamePrefix are mutually exclusive. Only one of these parameters can be passed.  
     */
    logGroupNamePattern?: LogGroupNamePattern;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of items returned. If you don't specify a value, the default is up to 50 items.
     */
    limit?: DescribeLimit;
    /**
     * If you are using a monitoring account, set this to True to have the operation return log groups in the accounts listed in accountIdentifiers. If this parameter is set to true and accountIdentifiers contains a null value, the operation returns all log groups in the monitoring account and all log groups in all source accounts that are linked to the monitoring account. 
     */
    includeLinkedAccounts?: IncludeLinkedAccounts;
    /**
     * Specifies the log group class for this log group. There are two classes:   The Standard log class supports all CloudWatch Logs features.   The Infrequent Access log class supports a subset of CloudWatch Logs features and incurs lower costs.   For details about the features supported by each class, see Log classes 
     */
    logGroupClass?: LogGroupClass;
  }
  export interface DescribeLogGroupsResponse {
    /**
     * The log groups. If the retentionInDays value is not included for a log group, then that log group's events do not expire.
     */
    logGroups?: LogGroups;
    nextToken?: NextToken;
  }
  export interface DescribeLogStreamsRequest {
    /**
     * The name of the log group.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupName?: LogGroupName;
    /**
     * Specify either the name or ARN of the log group to view. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupIdentifier?: LogGroupIdentifier;
    /**
     * The prefix to match. If orderBy is LastEventTime, you cannot specify this parameter.
     */
    logStreamNamePrefix?: LogStreamName;
    /**
     * If the value is LogStreamName, the results are ordered by log stream name. If the value is LastEventTime, the results are ordered by the event time. The default value is LogStreamName. If you order the results by event time, you cannot specify the logStreamNamePrefix parameter.  lastEventTimestamp represents the time of the most recent log event in the log stream in CloudWatch Logs. This number is expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. lastEventTimestamp updates on an eventual consistency basis. It typically updates in less than an hour from ingestion, but in rare situations might take longer.
     */
    orderBy?: OrderBy;
    /**
     * If the value is true, results are returned in descending order. If the value is to false, results are returned in ascending order. The default value is false.
     */
    descending?: Descending;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of items returned. If you don't specify a value, the default is up to 50 items.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeLogStreamsResponse {
    /**
     * The log streams.
     */
    logStreams?: LogStreams;
    nextToken?: NextToken;
  }
  export interface DescribeMetricFiltersRequest {
    /**
     * The name of the log group.
     */
    logGroupName?: LogGroupName;
    /**
     * The prefix to match. CloudWatch Logs uses the value that you set here only if you also include the logGroupName parameter in your request.
     */
    filterNamePrefix?: FilterName;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of items returned. If you don't specify a value, the default is up to 50 items.
     */
    limit?: DescribeLimit;
    /**
     * Filters results to include only those with the specified metric name. If you include this parameter in your request, you must also include the metricNamespace parameter.
     */
    metricName?: MetricName;
    /**
     * Filters results to include only those in the specified namespace. If you include this parameter in your request, you must also include the metricName parameter.
     */
    metricNamespace?: MetricNamespace;
  }
  export interface DescribeMetricFiltersResponse {
    /**
     * The metric filters.
     */
    metricFilters?: MetricFilters;
    nextToken?: NextToken;
  }
  export type DescribeQueriesMaxResults = number;
  export interface DescribeQueriesRequest {
    /**
     * Limits the returned queries to only those for the specified log group.
     */
    logGroupName?: LogGroupName;
    /**
     * Limits the returned queries to only those that have the specified status. Valid values are Cancelled, Complete, Failed, Running, and Scheduled.
     */
    status?: QueryStatus;
    /**
     * Limits the number of returned queries to the specified number.
     */
    maxResults?: DescribeQueriesMaxResults;
    nextToken?: NextToken;
  }
  export interface DescribeQueriesResponse {
    /**
     * The list of queries that match the request.
     */
    queries?: QueryInfoList;
    nextToken?: NextToken;
  }
  export interface DescribeQueryDefinitionsRequest {
    /**
     * Use this parameter to filter your results to only the query definitions that have names that start with the prefix you specify.
     */
    queryDefinitionNamePrefix?: QueryDefinitionName;
    /**
     * Limits the number of returned query definitions to the specified number.
     */
    maxResults?: QueryListMaxResults;
    nextToken?: NextToken;
  }
  export interface DescribeQueryDefinitionsResponse {
    /**
     * The list of query definitions that match your request.
     */
    queryDefinitions?: QueryDefinitionList;
    nextToken?: NextToken;
  }
  export interface DescribeResourcePoliciesRequest {
    nextToken?: NextToken;
    /**
     * The maximum number of resource policies to be displayed with one call of this API.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeResourcePoliciesResponse {
    /**
     * The resource policies that exist in this account.
     */
    resourcePolicies?: ResourcePolicies;
    nextToken?: NextToken;
  }
  export interface DescribeSubscriptionFiltersRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The prefix to match. If you don't specify a value, no prefix filter is applied.
     */
    filterNamePrefix?: FilterName;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of items returned. If you don't specify a value, the default is up to 50 items.
     */
    limit?: DescribeLimit;
  }
  export interface DescribeSubscriptionFiltersResponse {
    /**
     * The subscription filters.
     */
    subscriptionFilters?: SubscriptionFilters;
    nextToken?: NextToken;
  }
  export type Description = string;
  export interface Destination {
    /**
     * The name of the destination.
     */
    destinationName?: DestinationName;
    /**
     * The Amazon Resource Name (ARN) of the physical target where the log events are delivered (for example, a Kinesis stream).
     */
    targetArn?: TargetArn;
    /**
     * A role for impersonation, used when delivering log events to the target.
     */
    roleArn?: RoleArn;
    /**
     * An IAM policy document that governs which Amazon Web Services accounts can create subscription filters against this destination.
     */
    accessPolicy?: AccessPolicy;
    /**
     * The ARN of this destination.
     */
    arn?: Arn;
    /**
     * The creation time of the destination, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    creationTime?: Timestamp;
  }
  export type DestinationArn = string;
  export type DestinationName = string;
  export type Destinations = Destination[];
  export type DetectorName = string;
  export type Dimensions = {[key: string]: DimensionsValue};
  export type DimensionsKey = string;
  export type DimensionsValue = string;
  export interface DisassociateKmsKeyRequest {
    /**
     * The name of the log group. In your DisassociateKmsKey operation, you must specify either the resourceIdentifier parameter or the logGroup parameter, but you can't specify both.
     */
    logGroupName?: LogGroupName;
    /**
     * Specifies the target for this operation. You must specify one of the following:   Specify the ARN of a log group to stop having CloudWatch Logs use the KMS key to encrypt log events that are ingested and stored by that log group. After you run this operation, CloudWatch Logs encrypts ingested log events with the default CloudWatch Logs method. The log group ARN must be in the following format. Replace REGION and ACCOUNT_ID with your Region and account ID.  arn:aws:logs:REGION:ACCOUNT_ID:log-group:LOG_GROUP_NAME     Specify the following ARN to stop using this key to encrypt the results of future StartQuery operations in this account. Replace REGION and ACCOUNT_ID with your Region and account ID.  arn:aws:logs:REGION:ACCOUNT_ID:query-result:*    In your DisssociateKmsKey operation, you must specify either the resourceIdentifier parameter or the logGroup parameter, but you can't specify both.
     */
    resourceIdentifier?: ResourceIdentifier;
  }
  export type Distribution = "Random"|"ByLogStream"|string;
  export type DynamicTokenPosition = number;
  export type EncryptionKey = string;
  export interface Entity {
    /**
     * Reserved for internal use.
     */
    keyAttributes?: EntityKeyAttributes;
    /**
     * Reserved for internal use.
     */
    attributes?: EntityAttributes;
  }
  export type EntityAttributes = {[key: string]: EntityAttributesValue};
  export type EntityAttributesKey = string;
  export type EntityAttributesValue = string;
  export type EntityKeyAttributes = {[key: string]: EntityKeyAttributesValue};
  export type EntityKeyAttributesKey = string;
  export type EntityKeyAttributesValue = string;
  export type EntityRejectionErrorType = "InvalidEntity"|"InvalidTypeValue"|"InvalidKeyAttributes"|"InvalidAttributes"|"EntitySizeTooLarge"|"UnsupportedLogGroupType"|"MissingRequiredFields"|string;
  export type Enumerations = {[key: string]: TokenValue};
  export type EpochMillis = number;
  export type EvaluationFrequency = "ONE_MIN"|"FIVE_MIN"|"TEN_MIN"|"FIFTEEN_MIN"|"THIRTY_MIN"|"ONE_HOUR"|string;
  export type EventId = string;
  export type EventMessage = string;
  export type EventNumber = number;
  export type EventsLimit = number;
  export type ExportDestinationBucket = string;
  export type ExportDestinationPrefix = string;
  export interface ExportTask {
    /**
     * The ID of the export task.
     */
    taskId?: ExportTaskId;
    /**
     * The name of the export task.
     */
    taskName?: ExportTaskName;
    /**
     * The name of the log group from which logs data was exported.
     */
    logGroupName?: LogGroupName;
    /**
     * The start time, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp before this time are not exported.
     */
    from?: Timestamp;
    /**
     * The end time, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp later than this time are not exported.
     */
    to?: Timestamp;
    /**
     * The name of the S3 bucket to which the log data was exported.
     */
    destination?: ExportDestinationBucket;
    /**
     * The prefix that was used as the start of Amazon S3 key for every object exported.
     */
    destinationPrefix?: ExportDestinationPrefix;
    /**
     * The status of the export task.
     */
    status?: ExportTaskStatus;
    /**
     * Execution information about the export task.
     */
    executionInfo?: ExportTaskExecutionInfo;
  }
  export interface ExportTaskExecutionInfo {
    /**
     * The creation time of the export task, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    creationTime?: Timestamp;
    /**
     * The completion time of the export task, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    completionTime?: Timestamp;
  }
  export type ExportTaskId = string;
  export type ExportTaskName = string;
  export interface ExportTaskStatus {
    /**
     * The status code of the export task.
     */
    code?: ExportTaskStatusCode;
    /**
     * The status message related to the status code.
     */
    message?: ExportTaskStatusMessage;
  }
  export type ExportTaskStatusCode = "CANCELLED"|"COMPLETED"|"FAILED"|"PENDING"|"PENDING_CANCEL"|"RUNNING"|string;
  export type ExportTaskStatusMessage = string;
  export type ExportTasks = ExportTask[];
  export type ExtractedValues = {[key: string]: Value};
  export type Field = string;
  export type FieldDelimiter = string;
  export type FieldHeader = string;
  export type FilterCount = number;
  export interface FilterLogEventsRequest {
    /**
     * The name of the log group to search.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupName?: LogGroupName;
    /**
     * Specify either the name or ARN of the log group to view log events from. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupIdentifier?: LogGroupIdentifier;
    /**
     * Filters the results to only logs from the log streams in this list. If you specify a value for both logStreamNames and logStreamNamePrefix, the action returns an InvalidParameterException error.
     */
    logStreamNames?: InputLogStreamNames;
    /**
     * Filters the results to include only events from log streams that have names starting with this prefix. If you specify a value for both logStreamNamePrefix and logStreamNames, the action returns an InvalidParameterException error.
     */
    logStreamNamePrefix?: LogStreamName;
    /**
     * The start of the time range, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp before this time are not returned.
     */
    startTime?: Timestamp;
    /**
     * The end of the time range, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp later than this time are not returned.
     */
    endTime?: Timestamp;
    /**
     * The filter pattern to use. For more information, see Filter and Pattern Syntax. If not provided, all the events are matched.
     */
    filterPattern?: FilterPattern;
    /**
     * The token for the next set of events to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of events to return. The default is 10,000 events.
     */
    limit?: EventsLimit;
    /**
     * If the value is true, the operation attempts to provide responses that contain events from multiple log streams within the log group, interleaved in a single response. If the value is false, all the matched log events in the first log stream are searched first, then those in the next log stream, and so on.  Important As of June 17, 2019, this parameter is ignored and the value is assumed to be true. The response from this operation always interleaves events from multiple log streams within a log group.
     */
    interleaved?: Interleaved;
    /**
     * Specify true to display the log event fields with all sensitive data unmasked and visible. The default is false. To use this operation with this parameter, you must be signed into an account with the logs:Unmask permission.
     */
    unmask?: Unmask;
  }
  export interface FilterLogEventsResponse {
    /**
     * The matched events.
     */
    events?: FilteredLogEvents;
    /**
     *  Important As of May 15, 2020, this parameter is no longer supported. This parameter returns an empty list. Indicates which log streams have been searched and whether each has been searched completely.
     */
    searchedLogStreams?: SearchedLogStreams;
    /**
     * The token to use when requesting the next set of items. The token expires after 24 hours.
     */
    nextToken?: NextToken;
  }
  export type FilterName = string;
  export type FilterPattern = string;
  export interface FilteredLogEvent {
    /**
     * The name of the log stream to which this event belongs.
     */
    logStreamName?: LogStreamName;
    /**
     * The time the event occurred, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    timestamp?: Timestamp;
    /**
     * The data contained in the log event.
     */
    message?: EventMessage;
    /**
     * The time the event was ingested, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    ingestionTime?: Timestamp;
    /**
     * The ID of the event.
     */
    eventId?: EventId;
  }
  export type FilteredLogEvents = FilteredLogEvent[];
  export type ForceUpdate = boolean;
  export interface GetDataProtectionPolicyRequest {
    /**
     * The name or ARN of the log group that contains the data protection policy that you want to see.
     */
    logGroupIdentifier: LogGroupIdentifier;
  }
  export interface GetDataProtectionPolicyResponse {
    /**
     * The log group name or ARN that you specified in your request.
     */
    logGroupIdentifier?: LogGroupIdentifier;
    /**
     * The data protection policy document for this log group.
     */
    policyDocument?: DataProtectionPolicyDocument;
    /**
     * The date and time that this policy was most recently updated.
     */
    lastUpdatedTime?: Timestamp;
  }
  export interface GetDeliveryDestinationPolicyRequest {
    /**
     * The name of the delivery destination that you want to retrieve the policy of.
     */
    deliveryDestinationName: DeliveryDestinationName;
  }
  export interface GetDeliveryDestinationPolicyResponse {
    /**
     * The IAM policy for this delivery destination.
     */
    policy?: Policy;
  }
  export interface GetDeliveryDestinationRequest {
    /**
     * The name of the delivery destination that you want to retrieve.
     */
    name: DeliveryDestinationName;
  }
  export interface GetDeliveryDestinationResponse {
    /**
     * A structure containing information about the delivery destination.
     */
    deliveryDestination?: DeliveryDestination;
  }
  export interface GetDeliveryRequest {
    /**
     * The ID of the delivery that you want to retrieve.
     */
    id: DeliveryId;
  }
  export interface GetDeliveryResponse {
    /**
     * A structure that contains information about the delivery.
     */
    delivery?: Delivery;
  }
  export interface GetDeliverySourceRequest {
    /**
     * The name of the delivery source that you want to retrieve.
     */
    name: DeliverySourceName;
  }
  export interface GetDeliverySourceResponse {
    /**
     * A structure containing information about the delivery source.
     */
    deliverySource?: DeliverySource;
  }
  export interface GetLogAnomalyDetectorRequest {
    /**
     * The ARN of the anomaly detector to retrieve information about. You can find the ARNs of log anomaly detectors in your account by using the ListLogAnomalyDetectors operation.
     */
    anomalyDetectorArn: AnomalyDetectorArn;
  }
  export interface GetLogAnomalyDetectorResponse {
    /**
     * The name of the log anomaly detector
     */
    detectorName?: DetectorName;
    /**
     * An array of structures, where each structure contains the ARN of a log group associated with this anomaly detector.
     */
    logGroupArnList?: LogGroupArnList;
    /**
     * Specifies how often the anomaly detector runs and look for anomalies. Set this value according to the frequency that the log group receives new logs. For example, if the log group receives new log events every 10 minutes, then setting evaluationFrequency to FIFTEEN_MIN might be appropriate.
     */
    evaluationFrequency?: EvaluationFrequency;
    filterPattern?: FilterPattern;
    /**
     * Specifies whether the anomaly detector is currently active. To change its status, use the enabled parameter in the UpdateLogAnomalyDetector operation.
     */
    anomalyDetectorStatus?: AnomalyDetectorStatus;
    /**
     * The ID of the KMS key assigned to this anomaly detector, if any.
     */
    kmsKeyId?: KmsKeyId;
    /**
     * The date and time when this anomaly detector was created.
     */
    creationTimeStamp?: EpochMillis;
    /**
     * The date and time when this anomaly detector was most recently modified.
     */
    lastModifiedTimeStamp?: EpochMillis;
    /**
     * The number of days used as the life cycle of anomalies. After this time, anomalies are automatically baselined and the anomaly detector model will treat new occurrences of similar event as normal. 
     */
    anomalyVisibilityTime?: AnomalyVisibilityTime;
  }
  export interface GetLogEventsRequest {
    /**
     * The name of the log group.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupName?: LogGroupName;
    /**
     * Specify either the name or ARN of the log group to view events from. If the log group is in a source account and you are using a monitoring account, you must use the log group ARN.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupIdentifier?: LogGroupIdentifier;
    /**
     * The name of the log stream.
     */
    logStreamName: LogStreamName;
    /**
     * The start of the time range, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp equal to this time or later than this time are included. Events with a timestamp earlier than this time are not included.
     */
    startTime?: Timestamp;
    /**
     * The end of the time range, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. Events with a timestamp equal to or later than this time are not included.
     */
    endTime?: Timestamp;
    /**
     * The token for the next set of items to return. (You received this token from a previous call.)
     */
    nextToken?: NextToken;
    /**
     * The maximum number of log events returned. If you don't specify a limit, the default is as many log events as can fit in a response size of 1 MB (up to 10,000 log events).
     */
    limit?: EventsLimit;
    /**
     * If the value is true, the earliest log events are returned first. If the value is false, the latest log events are returned first. The default value is false. If you are using a previous nextForwardToken value as the nextToken in this operation, you must specify true for startFromHead.
     */
    startFromHead?: StartFromHead;
    /**
     * Specify true to display the log event fields with all sensitive data unmasked and visible. The default is false. To use this operation with this parameter, you must be signed into an account with the logs:Unmask permission.
     */
    unmask?: Unmask;
  }
  export interface GetLogEventsResponse {
    /**
     * The events.
     */
    events?: OutputLogEvents;
    /**
     * The token for the next set of items in the forward direction. The token expires after 24 hours. If you have reached the end of the stream, it returns the same token you passed in.
     */
    nextForwardToken?: NextToken;
    /**
     * The token for the next set of items in the backward direction. The token expires after 24 hours. This token is not null. If you have reached the end of the stream, it returns the same token you passed in.
     */
    nextBackwardToken?: NextToken;
  }
  export interface GetLogGroupFieldsRequest {
    /**
     * The name of the log group to search.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupName?: LogGroupName;
    /**
     * The time to set as the center of the query. If you specify time, the 8 minutes before and 8 minutes after this time are searched. If you omit time, the most recent 15 minutes up to the current time are searched. The time value is specified as epoch time, which is the number of seconds since January 1, 1970, 00:00:00 UTC.
     */
    time?: Timestamp;
    /**
     * Specify either the name or ARN of the log group to view. If the log group is in a source account and you are using a monitoring account, you must specify the ARN.   You must include either logGroupIdentifier or logGroupName, but not both.  
     */
    logGroupIdentifier?: LogGroupIdentifier;
  }
  export interface GetLogGroupFieldsResponse {
    /**
     * The array of fields found in the query. Each object in the array contains the name of the field, along with the percentage of time it appeared in the log events that were queried.
     */
    logGroupFields?: LogGroupFieldList;
  }
  export interface GetLogRecordRequest {
    /**
     * The pointer corresponding to the log event record you want to retrieve. You get this from the response of a GetQueryResults operation. In that response, the value of the @ptr field for a log event is the value to use as logRecordPointer to retrieve that complete log event record.
     */
    logRecordPointer: LogRecordPointer;
    /**
     * Specify true to display the log event fields with all sensitive data unmasked and visible. The default is false. To use this operation with this parameter, you must be signed into an account with the logs:Unmask permission.
     */
    unmask?: Unmask;
  }
  export interface GetLogRecordResponse {
    /**
     * The requested log event, as a JSON string.
     */
    logRecord?: LogRecord;
  }
  export interface GetQueryResultsRequest {
    /**
     * The ID number of the query.
     */
    queryId: QueryId;
  }
  export interface GetQueryResultsResponse {
    /**
     * The log events that matched the query criteria during the most recent time it ran. The results value is an array of arrays. Each log event is one object in the top-level array. Each of these log event objects is an array of field/value pairs.
     */
    results?: QueryResults;
    /**
     * Includes the number of log events scanned by the query, the number of log events that matched the query criteria, and the total number of bytes in the scanned log events. These values reflect the full raw results of the query.
     */
    statistics?: QueryStatistics;
    /**
     * The status of the most recent running of the query. Possible values are Cancelled, Complete, Failed, Running, Scheduled, Timeout, and Unknown. Queries time out after 60 minutes of runtime. To avoid having your queries time out, reduce the time range being searched or partition your query into a number of queries.
     */
    status?: QueryStatus;
    /**
     * If you associated an KMS key with the CloudWatch Logs Insights query results in this account, this field displays the ARN of the key that's used to encrypt the query results when StartQuery stores them.
     */
    encryptionKey?: EncryptionKey;
  }
  export type Histogram = {[key: string]: Count};
  export type IncludeLinkedAccounts = boolean;
  export type InheritedProperties = InheritedProperty[];
  export type InheritedProperty = "ACCOUNT_DATA_PROTECTION"|string;
  export interface InputLogEvent {
    /**
     * The time the event occurred, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    timestamp: Timestamp;
    /**
     * The raw event message. Each log event can be no larger than 256 KB.
     */
    message: EventMessage;
  }
  export type InputLogEvents = InputLogEvent[];
  export type InputLogStreamNames = LogStreamName[];
  export type Integer = number;
  export type Interleaved = boolean;
  export type IsSampled = boolean;
  export type KmsKeyId = string;
  export type ListAnomaliesLimit = number;
  export interface ListAnomaliesRequest {
    /**
     * Use this to optionally limit the results to only the anomalies found by a certain anomaly detector.
     */
    anomalyDetectorArn?: AnomalyDetectorArn;
    /**
     * You can specify this parameter if you want to the operation to return only anomalies that are currently either suppressed or unsuppressed.
     */
    suppressionState?: SuppressionState;
    /**
     * The maximum number of items to return. If you don't specify a value, the default maximum value of 50 items is used.
     */
    limit?: ListAnomaliesLimit;
    nextToken?: NextToken;
  }
  export interface ListAnomaliesResponse {
    /**
     * An array of structures, where each structure contains information about one anomaly that a log anomaly detector has found.
     */
    anomalies?: Anomalies;
    nextToken?: NextToken;
  }
  export type ListLogAnomalyDetectorsLimit = number;
  export interface ListLogAnomalyDetectorsRequest {
    /**
     * Use this to optionally filter the results to only include anomaly detectors that are associated with the specified log group.
     */
    filterLogGroupArn?: LogGroupArn;
    /**
     * The maximum number of items to return. If you don't specify a value, the default maximum value of 50 items is used.
     */
    limit?: ListLogAnomalyDetectorsLimit;
    nextToken?: NextToken;
  }
  export interface ListLogAnomalyDetectorsResponse {
    /**
     * An array of structures, where each structure in the array contains information about one anomaly detector.
     */
    anomalyDetectors?: AnomalyDetectors;
    nextToken?: NextToken;
  }
  export interface ListTagsForResourceRequest {
    /**
     * The ARN of the resource that you want to view tags for. The ARN format of a log group is arn:aws:logs:Region:account-id:log-group:log-group-name   The ARN format of a destination is arn:aws:logs:Region:account-id:destination:destination-name   For more information about ARN format, see CloudWatch Logs resources and operations.
     */
    resourceArn: AmazonResourceName;
  }
  export interface ListTagsForResourceResponse {
    /**
     * The list of tags associated with the requested resource.&gt;
     */
    tags?: Tags;
  }
  export interface ListTagsLogGroupRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
  }
  export interface ListTagsLogGroupResponse {
    /**
     * The tags for the log group.
     */
    tags?: Tags;
  }
  export interface LiveTailSessionLogEvent {
    /**
     * The name of the log stream that ingested this log event.
     */
    logStreamName?: LogStreamName;
    /**
     * The name or ARN of the log group that ingested this log event.
     */
    logGroupIdentifier?: LogGroupIdentifier;
    /**
     * The log event message text.
     */
    message?: EventMessage;
    /**
     * The timestamp specifying when this log event was created.
     */
    timestamp?: Timestamp;
    /**
     * The timestamp specifying when this log event was ingested into the log group.
     */
    ingestionTime?: Timestamp;
  }
  export interface LiveTailSessionMetadata {
    /**
     * If this is true, then more than 500 log events matched the request for this update, and the sessionResults includes a sample of 500 of those events. If this is false, then 500 or fewer log events matched the request for this update, so no sampling was necessary. In this case, the sessionResults array includes all log events that matched your request during this time.
     */
    sampled?: IsSampled;
  }
  export type LiveTailSessionResults = LiveTailSessionLogEvent[];
  export interface LiveTailSessionStart {
    /**
     * The unique ID generated by CloudWatch Logs to identify this Live Tail session request.
     */
    requestId?: RequestId;
    /**
     * The unique ID generated by CloudWatch Logs to identify this Live Tail session.
     */
    sessionId?: SessionId;
    /**
     * An array of the names and ARNs of the log groups included in this Live Tail session.
     */
    logGroupIdentifiers?: StartLiveTailLogGroupIdentifiers;
    /**
     * If your StartLiveTail operation request included a logStreamNames parameter that filtered the session to only include certain log streams, these streams are listed here.
     */
    logStreamNames?: InputLogStreamNames;
    /**
     * If your StartLiveTail operation request included a logStreamNamePrefixes parameter that filtered the session to only include log streams that have names that start with certain prefixes, these prefixes are listed here.
     */
    logStreamNamePrefixes?: InputLogStreamNames;
    /**
     * An optional pattern to filter the results to include only log events that match the pattern. For example, a filter pattern of error 404 displays only log events that include both error and 404. For more information about filter pattern syntax, see Filter and Pattern Syntax.
     */
    logEventFilterPattern?: FilterPattern;
  }
  export interface LiveTailSessionUpdate {
    /**
     * This object contains the session metadata for a Live Tail session.
     */
    sessionMetadata?: LiveTailSessionMetadata;
    /**
     * An array, where each member of the array includes the information for one log event in the Live Tail session. A sessionResults array can include as many as 500 log events. If the number of log events matching the request exceeds 500 per second, the log events are sampled down to 500 log events to be included in each sessionUpdate structure.
     */
    sessionResults?: LiveTailSessionResults;
  }
  export interface LogEvent {
    /**
     * The time stamp of the log event.
     */
    timestamp?: Timestamp;
    /**
     * The message content of the log event.
     */
    message?: EventMessage;
  }
  export type LogEventIndex = number;
  export interface LogGroup {
    /**
     * The name of the log group.
     */
    logGroupName?: LogGroupName;
    /**
     * The creation time of the log group, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    creationTime?: Timestamp;
    retentionInDays?: Days;
    /**
     * The number of metric filters.
     */
    metricFilterCount?: FilterCount;
    /**
     * The Amazon Resource Name (ARN) of the log group. This version of the ARN includes a trailing :* after the log group name.  Use this version to refer to the ARN in IAM policies when specifying permissions for most API actions. The exception is when specifying permissions for TagResource, UntagResource, and ListTagsForResource. The permissions for those three actions require the ARN version that doesn't include a trailing :*.
     */
    arn?: Arn;
    /**
     * The number of bytes stored.
     */
    storedBytes?: StoredBytes;
    /**
     * The Amazon Resource Name (ARN) of the KMS key to use when encrypting log data.
     */
    kmsKeyId?: KmsKeyId;
    /**
     * Displays whether this log group has a protection policy, or whether it had one in the past. For more information, see PutDataProtectionPolicy.
     */
    dataProtectionStatus?: DataProtectionStatus;
    /**
     * Displays all the properties that this log group has inherited from account-level settings.
     */
    inheritedProperties?: InheritedProperties;
    /**
     * This specifies the log group class for this log group. There are two classes:   The Standard log class supports all CloudWatch Logs features.   The Infrequent Access log class supports a subset of CloudWatch Logs features and incurs lower costs.   For details about the features supported by each class, see Log classes 
     */
    logGroupClass?: LogGroupClass;
    /**
     * The Amazon Resource Name (ARN) of the log group. This version of the ARN doesn't include a trailing :* after the log group name.  Use this version to refer to the ARN in the following situations:   In the logGroupIdentifier input field in many CloudWatch Logs APIs.   In the resourceArn field in tagging APIs   In IAM policies, when specifying permissions for TagResource, UntagResource, and ListTagsForResource.  
     */
    logGroupArn?: Arn;
  }
  export type LogGroupArn = string;
  export type LogGroupArnList = LogGroupArn[];
  export type LogGroupClass = "STANDARD"|"INFREQUENT_ACCESS"|string;
  export interface LogGroupField {
    /**
     * The name of a log field.
     */
    name?: Field;
    /**
     * The percentage of log events queried that contained the field.
     */
    percent?: Percentage;
  }
  export type LogGroupFieldList = LogGroupField[];
  export type LogGroupIdentifier = string;
  export type LogGroupIdentifiers = LogGroupIdentifier[];
  export type LogGroupName = string;
  export type LogGroupNamePattern = string;
  export type LogGroupNames = LogGroupName[];
  export type LogGroups = LogGroup[];
  export type LogRecord = {[key: string]: Value};
  export type LogRecordPointer = string;
  export type LogSamples = LogEvent[];
  export interface LogStream {
    /**
     * The name of the log stream.
     */
    logStreamName?: LogStreamName;
    /**
     * The creation time of the stream, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    creationTime?: Timestamp;
    /**
     * The time of the first event, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    firstEventTimestamp?: Timestamp;
    /**
     * The time of the most recent log event in the log stream in CloudWatch Logs. This number is expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC. The lastEventTime value updates on an eventual consistency basis. It typically updates in less than an hour from ingestion, but in rare situations might take longer.
     */
    lastEventTimestamp?: Timestamp;
    /**
     * The ingestion time, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC The lastIngestionTime value updates on an eventual consistency basis. It typically updates in less than an hour after ingestion, but in rare situations might take longer.
     */
    lastIngestionTime?: Timestamp;
    /**
     * The sequence token.  The sequence token is now ignored in PutLogEvents actions. PutLogEvents actions are always accepted regardless of receiving an invalid sequence token. You don't need to obtain uploadSequenceToken to use a PutLogEvents action. 
     */
    uploadSequenceToken?: SequenceToken;
    /**
     * The Amazon Resource Name (ARN) of the log stream.
     */
    arn?: Arn;
    /**
     * The number of bytes stored.  Important: As of June 17, 2019, this parameter is no longer supported for log streams, and is always reported as zero. This change applies only to log streams. The storedBytes parameter for log groups is not affected.
     */
    storedBytes?: StoredBytes;
  }
  export type LogStreamName = string;
  export type LogStreamSearchedCompletely = boolean;
  export type LogStreams = LogStream[];
  export type LogType = string;
  export type LogTypes = LogType[];
  export type Message = string;
  export interface MetricFilter {
    /**
     * The name of the metric filter.
     */
    filterName?: FilterName;
    filterPattern?: FilterPattern;
    /**
     * The metric transformations.
     */
    metricTransformations?: MetricTransformations;
    /**
     * The creation time of the metric filter, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    creationTime?: Timestamp;
    /**
     * The name of the log group.
     */
    logGroupName?: LogGroupName;
  }
  export interface MetricFilterMatchRecord {
    /**
     * The event number.
     */
    eventNumber?: EventNumber;
    /**
     * The raw event data.
     */
    eventMessage?: EventMessage;
    /**
     * The values extracted from the event data by the filter.
     */
    extractedValues?: ExtractedValues;
  }
  export type MetricFilterMatches = MetricFilterMatchRecord[];
  export type MetricFilters = MetricFilter[];
  export type MetricName = string;
  export type MetricNamespace = string;
  export interface MetricTransformation {
    /**
     * The name of the CloudWatch metric.
     */
    metricName: MetricName;
    /**
     * A custom namespace to contain your metric in CloudWatch. Use namespaces to group together metrics that are similar. For more information, see Namespaces.
     */
    metricNamespace: MetricNamespace;
    /**
     * The value to publish to the CloudWatch metric when a filter pattern matches a log event.
     */
    metricValue: MetricValue;
    /**
     * (Optional) The value to emit when a filter pattern does not match a log event. This value can be null.
     */
    defaultValue?: DefaultValue;
    /**
     * The fields to use as dimensions for the metric. One metric filter can include as many as three dimensions.  Metrics extracted from log events are charged as custom metrics. To prevent unexpected high charges, do not specify high-cardinality fields such as IPAddress or requestID as dimensions. Each different value found for a dimension is treated as a separate metric and accrues charges as a separate custom metric.  CloudWatch Logs disables a metric filter if it generates 1000 different name/value pairs for your specified dimensions within a certain amount of time. This helps to prevent accidental high charges. You can also set up a billing alarm to alert you if your charges are higher than expected. For more information, see  Creating a Billing Alarm to Monitor Your Estimated Amazon Web Services Charges.  
     */
    dimensions?: Dimensions;
    /**
     * The unit to assign to the metric. If you omit this, the unit is set as None.
     */
    unit?: StandardUnit;
  }
  export type MetricTransformations = MetricTransformation[];
  export type MetricValue = string;
  export type NextToken = string;
  export type OrderBy = "LogStreamName"|"LastEventTime"|string;
  export type OutputFormat = "json"|"plain"|"w3c"|"raw"|"parquet"|string;
  export type OutputFormats = OutputFormat[];
  export interface OutputLogEvent {
    /**
     * The time the event occurred, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    timestamp?: Timestamp;
    /**
     * The data contained in the log event.
     */
    message?: EventMessage;
    /**
     * The time the event was ingested, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    ingestionTime?: Timestamp;
  }
  export type OutputLogEvents = OutputLogEvent[];
  export type PatternId = string;
  export type PatternRegex = string;
  export type PatternString = string;
  export interface PatternToken {
    /**
     * For a dynamic token, this indicates where in the pattern that this token appears, related to other dynamic tokens. The dynamic token that appears first has a value of 1, the one that appears second is 2, and so on.
     */
    dynamicTokenPosition?: DynamicTokenPosition;
    /**
     * Specifies whether this is a dynamic token.
     */
    isDynamic?: Boolean;
    /**
     * The string represented by this token. If this is a dynamic token, the value will be &lt;*&gt; 
     */
    tokenString?: TokenString;
    /**
     * Contains the values found for a dynamic token, and the number of times each value was found.
     */
    enumerations?: Enumerations;
  }
  export type PatternTokens = PatternToken[];
  export type Percentage = number;
  export interface Policy {
    /**
     * The contents of the delivery destination policy.
     */
    deliveryDestinationPolicy?: DeliveryDestinationPolicy;
  }
  export type PolicyDocument = string;
  export type PolicyName = string;
  export type PolicyType = "DATA_PROTECTION_POLICY"|"SUBSCRIPTION_FILTER_POLICY"|string;
  export type Priority = string;
  export interface PutAccountPolicyRequest {
    /**
     * A name for the policy. This must be unique within the account.
     */
    policyName: PolicyName;
    /**
     * Specify the policy, in JSON.  Data protection policy  A data protection policy must include two JSON blocks:   The first block must include both a DataIdentifer array and an Operation property with an Audit action. The DataIdentifer array lists the types of sensitive data that you want to mask. For more information about the available options, see Types of data that you can mask. The Operation property with an Audit action is required to find the sensitive data terms. This Audit action must contain a FindingsDestination object. You can optionally use that FindingsDestination object to list one or more destinations to send audit findings to. If you specify destinations such as log groups, Firehose streams, and S3 buckets, they must already exist.   The second block must include both a DataIdentifer array and an Operation property with an Deidentify action. The DataIdentifer array must exactly match the DataIdentifer array in the first block of the policy. The Operation property with the Deidentify action is what actually masks the data, and it must contain the  "MaskConfig": {} object. The  "MaskConfig": {} object must be empty.   For an example data protection policy, see the Examples section on this page.  The contents of the two DataIdentifer arrays must match exactly.  In addition to the two JSON blocks, the policyDocument can also include Name, Description, and Version fields. The Name is different than the operation's policyName parameter, and is used as a dimension when CloudWatch Logs reports audit findings metrics to CloudWatch. The JSON specified in policyDocument can be up to 30,720 characters long.  Subscription filter policy  A subscription filter policy can include the following attributes in a JSON block:    DestinationArn The ARN of the destination to deliver log events to. Supported destinations are:   An Kinesis Data Streams data stream in the same account as the subscription policy, for same-account delivery.   An Firehose data stream in the same account as the subscription policy, for same-account delivery.   A Lambda function in the same account as the subscription policy, for same-account delivery.   A logical destination in a different account created with PutDestination, for cross-account delivery. Kinesis Data Streams and Firehose are supported as logical destinations.      RoleArn The ARN of an IAM role that grants CloudWatch Logs permissions to deliver ingested log events to the destination stream. You don't need to provide the ARN when you are working with a logical destination for cross-account delivery.    FilterPattern A filter pattern for subscribing to a filtered stream of log events.    Distribution The method used to distribute log data to the destination. By default, log data is grouped by log stream, but the grouping can be set to Random for a more even distribution. This property is only applicable when the destination is an Kinesis Data Streams data stream.  
     */
    policyDocument: AccountPolicyDocument;
    /**
     * The type of policy that you're creating or updating.
     */
    policyType: PolicyType;
    /**
     * Currently the only valid value for this parameter is ALL, which specifies that the data protection policy applies to all log groups in the account. If you omit this parameter, the default of ALL is used.
     */
    scope?: Scope;
    /**
     * Use this parameter to apply the subscription filter policy to a subset of log groups in the account. Currently, the only supported filter is LogGroupName NOT IN []. The selectionCriteria string can be up to 25KB in length. The length is determined by using its UTF-8 bytes. Using the selectionCriteria parameter is useful to help prevent infinite loops. For more information, see Log recursion prevention. Specifing selectionCriteria is valid only when you specify  SUBSCRIPTION_FILTER_POLICY for policyType.
     */
    selectionCriteria?: SelectionCriteria;
  }
  export interface PutAccountPolicyResponse {
    /**
     * The account policy that you created.
     */
    accountPolicy?: AccountPolicy;
  }
  export interface PutDataProtectionPolicyRequest {
    /**
     * Specify either the log group name or log group ARN.
     */
    logGroupIdentifier: LogGroupIdentifier;
    /**
     * Specify the data protection policy, in JSON. This policy must include two JSON blocks:   The first block must include both a DataIdentifer array and an Operation property with an Audit action. The DataIdentifer array lists the types of sensitive data that you want to mask. For more information about the available options, see Types of data that you can mask. The Operation property with an Audit action is required to find the sensitive data terms. This Audit action must contain a FindingsDestination object. You can optionally use that FindingsDestination object to list one or more destinations to send audit findings to. If you specify destinations such as log groups, Firehose streams, and S3 buckets, they must already exist.   The second block must include both a DataIdentifer array and an Operation property with an Deidentify action. The DataIdentifer array must exactly match the DataIdentifer array in the first block of the policy. The Operation property with the Deidentify action is what actually masks the data, and it must contain the  "MaskConfig": {} object. The  "MaskConfig": {} object must be empty.   For an example data protection policy, see the Examples section on this page.  The contents of the two DataIdentifer arrays must match exactly.  In addition to the two JSON blocks, the policyDocument can also include Name, Description, and Version fields. The Name is used as a dimension when CloudWatch Logs reports audit findings metrics to CloudWatch. The JSON specified in policyDocument can be up to 30,720 characters.
     */
    policyDocument: DataProtectionPolicyDocument;
  }
  export interface PutDataProtectionPolicyResponse {
    /**
     * The log group name or ARN that you specified in your request.
     */
    logGroupIdentifier?: LogGroupIdentifier;
    /**
     * The data protection policy used for this log group.
     */
    policyDocument?: DataProtectionPolicyDocument;
    /**
     * The date and time that this policy was most recently updated.
     */
    lastUpdatedTime?: Timestamp;
  }
  export interface PutDeliveryDestinationPolicyRequest {
    /**
     * The name of the delivery destination to assign this policy to.
     */
    deliveryDestinationName: DeliveryDestinationName;
    /**
     * The contents of the policy.
     */
    deliveryDestinationPolicy: DeliveryDestinationPolicy;
  }
  export interface PutDeliveryDestinationPolicyResponse {
    /**
     * The contents of the policy that you just created.
     */
    policy?: Policy;
  }
  export interface PutDeliveryDestinationRequest {
    /**
     * A name for this delivery destination. This name must be unique for all delivery destinations in your account.
     */
    name: DeliveryDestinationName;
    /**
     * The format for the logs that this delivery destination will receive.
     */
    outputFormat?: OutputFormat;
    /**
     * A structure that contains the ARN of the Amazon Web Services resource that will receive the logs.
     */
    deliveryDestinationConfiguration: DeliveryDestinationConfiguration;
    /**
     * An optional list of key-value pairs to associate with the resource. For more information about tagging, see Tagging Amazon Web Services resources 
     */
    tags?: Tags;
  }
  export interface PutDeliveryDestinationResponse {
    /**
     * A structure containing information about the delivery destination that you just created or updated.
     */
    deliveryDestination?: DeliveryDestination;
  }
  export interface PutDeliverySourceRequest {
    /**
     * A name for this delivery source. This name must be unique for all delivery sources in your account.
     */
    name: DeliverySourceName;
    /**
     * The ARN of the Amazon Web Services resource that is generating and sending logs. For example, arn:aws:workmail:us-east-1:************:organization/m-1234EXAMPLEabcd1234abcd1234abcd1234 
     */
    resourceArn: Arn;
    /**
     * Defines the type of log that the source is sending.   For Amazon Bedrock, the valid value is APPLICATION_LOGS.   For Amazon CodeWhisperer, the valid value is EVENT_LOGS.   For IAM Identity Center, the valid value is ERROR_LOGS.   For Amazon WorkMail, the valid values are ACCESS_CONTROL_LOGS, AUTHENTICATION_LOGS, WORKMAIL_AVAILABILITY_PROVIDER_LOGS, and WORKMAIL_MAILBOX_ACCESS_LOGS.  
     */
    logType: LogType;
    /**
     * An optional list of key-value pairs to associate with the resource. For more information about tagging, see Tagging Amazon Web Services resources 
     */
    tags?: Tags;
  }
  export interface PutDeliverySourceResponse {
    /**
     * A structure containing information about the delivery source that was just created or updated.
     */
    deliverySource?: DeliverySource;
  }
  export interface PutDestinationPolicyRequest {
    /**
     * A name for an existing destination.
     */
    destinationName: DestinationName;
    /**
     * An IAM policy document that authorizes cross-account users to deliver their log events to the associated destination. This can be up to 5120 bytes.
     */
    accessPolicy: AccessPolicy;
    /**
     * Specify true if you are updating an existing destination policy to grant permission to an organization ID instead of granting permission to individual Amazon Web Services accounts. Before you update a destination policy this way, you must first update the subscription filters in the accounts that send logs to this destination. If you do not, the subscription filters might stop working. By specifying true for forceUpdate, you are affirming that you have already updated the subscription filters. For more information, see  Updating an existing cross-account subscription  If you omit this parameter, the default of false is used.
     */
    forceUpdate?: ForceUpdate;
  }
  export interface PutDestinationRequest {
    /**
     * A name for the destination.
     */
    destinationName: DestinationName;
    /**
     * The ARN of an Amazon Kinesis stream to which to deliver matching log events.
     */
    targetArn: TargetArn;
    /**
     * The ARN of an IAM role that grants CloudWatch Logs permissions to call the Amazon Kinesis PutRecord operation on the destination stream.
     */
    roleArn: RoleArn;
    /**
     * An optional list of key-value pairs to associate with the resource. For more information about tagging, see Tagging Amazon Web Services resources 
     */
    tags?: Tags;
  }
  export interface PutDestinationResponse {
    /**
     * The destination.
     */
    destination?: Destination;
  }
  export interface PutLogEventsRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The name of the log stream.
     */
    logStreamName: LogStreamName;
    /**
     * The log events.
     */
    logEvents: InputLogEvents;
    /**
     * The sequence token obtained from the response of the previous PutLogEvents call.  The sequenceToken parameter is now ignored in PutLogEvents actions. PutLogEvents actions are now accepted and never return InvalidSequenceTokenException or DataAlreadyAcceptedException even if the sequence token is not valid. 
     */
    sequenceToken?: SequenceToken;
    /**
     * Reserved for internal use.
     */
    entity?: Entity;
  }
  export interface PutLogEventsResponse {
    /**
     * The next sequence token.  This field has been deprecated. The sequence token is now ignored in PutLogEvents actions. PutLogEvents actions are always accepted even if the sequence token is not valid. You can use parallel PutLogEvents actions on the same log stream and you do not need to wait for the response of a previous PutLogEvents action to obtain the nextSequenceToken value. 
     */
    nextSequenceToken?: SequenceToken;
    /**
     * The rejected events.
     */
    rejectedLogEventsInfo?: RejectedLogEventsInfo;
    /**
     * Reserved for internal use.
     */
    rejectedEntityInfo?: RejectedEntityInfo;
  }
  export interface PutMetricFilterRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * A name for the metric filter.
     */
    filterName: FilterName;
    /**
     * A filter pattern for extracting metric data out of ingested log events.
     */
    filterPattern: FilterPattern;
    /**
     * A collection of information that defines how metric data gets emitted.
     */
    metricTransformations: MetricTransformations;
  }
  export interface PutQueryDefinitionRequest {
    /**
     * A name for the query definition. If you are saving numerous query definitions, we recommend that you name them. This way, you can find the ones you want by using the first part of the name as a filter in the queryDefinitionNamePrefix parameter of DescribeQueryDefinitions.
     */
    name: QueryDefinitionName;
    /**
     * If you are updating a query definition, use this parameter to specify the ID of the query definition that you want to update. You can use DescribeQueryDefinitions to retrieve the IDs of your saved query definitions. If you are creating a query definition, do not specify this parameter. CloudWatch generates a unique ID for the new query definition and include it in the response to this operation.
     */
    queryDefinitionId?: QueryId;
    /**
     * Use this parameter to include specific log groups as part of your query definition. If you are updating a query definition and you omit this parameter, then the updated definition will contain no log groups.
     */
    logGroupNames?: LogGroupNames;
    /**
     * The query string to use for this definition. For more information, see CloudWatch Logs Insights Query Syntax.
     */
    queryString: QueryDefinitionString;
    /**
     * Used as an idempotency token, to avoid returning an exception if the service receives the same request twice because of a network error.
     */
    clientToken?: ClientToken;
  }
  export interface PutQueryDefinitionResponse {
    /**
     * The ID of the query definition.
     */
    queryDefinitionId?: QueryId;
  }
  export interface PutResourcePolicyRequest {
    /**
     * Name of the new policy. This parameter is required.
     */
    policyName?: PolicyName;
    /**
     * Details of the new policy, including the identity of the principal that is enabled to put logs to this account. This is formatted as a JSON string. This parameter is required. The following example creates a resource policy enabling the Route 53 service to put DNS query logs in to the specified log group. Replace "logArn" with the ARN of your CloudWatch Logs resource, such as a log group or log stream. CloudWatch Logs also supports aws:SourceArn and aws:SourceAccount condition context keys. In the example resource policy, you would replace the value of SourceArn with the resource making the call from Route 53 to CloudWatch Logs. You would also replace the value of SourceAccount with the Amazon Web Services account ID making that call.   { "Version": "2012-10-17", "Statement": [ { "Sid": "Route53LogsToCloudWatchLogs", "Effect": "Allow", "Principal": { "Service": [ "route53.amazonaws.com" ] }, "Action": "logs:PutLogEvents", "Resource": "logArn", "Condition": { "ArnLike": { "aws:SourceArn": "myRoute53ResourceArn" }, "StringEquals": { "aws:SourceAccount": "myAwsAccountId" } } } ] } 
     */
    policyDocument?: PolicyDocument;
  }
  export interface PutResourcePolicyResponse {
    /**
     * The new policy.
     */
    resourcePolicy?: ResourcePolicy;
  }
  export interface PutRetentionPolicyRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    retentionInDays: Days;
  }
  export interface PutSubscriptionFilterRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * A name for the subscription filter. If you are updating an existing filter, you must specify the correct name in filterName. To find the name of the filter currently associated with a log group, use DescribeSubscriptionFilters.
     */
    filterName: FilterName;
    /**
     * A filter pattern for subscribing to a filtered stream of log events.
     */
    filterPattern: FilterPattern;
    /**
     * The ARN of the destination to deliver matching log events to. Currently, the supported destinations are:   An Amazon Kinesis stream belonging to the same account as the subscription filter, for same-account delivery.   A logical destination (specified using an ARN) belonging to a different account, for cross-account delivery. If you're setting up a cross-account subscription, the destination must have an IAM policy associated with it. The IAM policy must allow the sender to send logs to the destination. For more information, see PutDestinationPolicy.   A Kinesis Data Firehose delivery stream belonging to the same account as the subscription filter, for same-account delivery.   A Lambda function belonging to the same account as the subscription filter, for same-account delivery.  
     */
    destinationArn: DestinationArn;
    /**
     * The ARN of an IAM role that grants CloudWatch Logs permissions to deliver ingested log events to the destination stream. You don't need to provide the ARN when you are working with a logical destination for cross-account delivery.
     */
    roleArn?: RoleArn;
    /**
     * The method used to distribute log data to the destination. By default, log data is grouped by log stream, but the grouping can be set to random for a more even distribution. This property is only applicable when the destination is an Amazon Kinesis data stream. 
     */
    distribution?: Distribution;
  }
  export interface QueryDefinition {
    /**
     * The unique ID of the query definition.
     */
    queryDefinitionId?: QueryId;
    /**
     * The name of the query definition.
     */
    name?: QueryDefinitionName;
    /**
     * The query string to use for this definition. For more information, see CloudWatch Logs Insights Query Syntax.
     */
    queryString?: QueryDefinitionString;
    /**
     * The date that the query definition was most recently modified.
     */
    lastModified?: Timestamp;
    /**
     * If this query definition contains a list of log groups that it is limited to, that list appears here.
     */
    logGroupNames?: LogGroupNames;
  }
  export type QueryDefinitionList = QueryDefinition[];
  export type QueryDefinitionName = string;
  export type QueryDefinitionString = string;
  export type QueryId = string;
  export interface QueryInfo {
    /**
     * The unique ID number of this query.
     */
    queryId?: QueryId;
    /**
     * The query string used in this query.
     */
    queryString?: QueryString;
    /**
     * The status of this query. Possible values are Cancelled, Complete, Failed, Running, Scheduled, and Unknown.
     */
    status?: QueryStatus;
    /**
     * The date and time that this query was created.
     */
    createTime?: Timestamp;
    /**
     * The name of the log group scanned by this query.
     */
    logGroupName?: LogGroupName;
  }
  export type QueryInfoList = QueryInfo[];
  export type QueryListMaxResults = number;
  export type QueryResults = ResultRows[];
  export interface QueryStatistics {
    /**
     * The number of log events that matched the query string.
     */
    recordsMatched?: StatsValue;
    /**
     * The total number of log events scanned during the query.
     */
    recordsScanned?: StatsValue;
    /**
     * The total number of bytes in the log events scanned during the query.
     */
    bytesScanned?: StatsValue;
  }
  export type QueryStatus = "Scheduled"|"Running"|"Complete"|"Failed"|"Cancelled"|"Timeout"|"Unknown"|string;
  export type QueryString = string;
  export interface RecordField {
    /**
     * The name to use when specifying this record field in a CreateDelivery or UpdateDeliveryConfiguration operation. 
     */
    name?: FieldHeader;
    /**
     * If this is true, the record field must be present in the recordFields parameter provided to a CreateDelivery or UpdateDeliveryConfiguration operation.
     */
    mandatory?: Boolean;
  }
  export type RecordFields = FieldHeader[];
  export interface RejectedEntityInfo {
    /**
     * Reserved for internal use.
     */
    errorType: EntityRejectionErrorType;
  }
  export interface RejectedLogEventsInfo {
    /**
     * The index of the first log event that is too new. This field is inclusive.
     */
    tooNewLogEventStartIndex?: LogEventIndex;
    /**
     * The index of the last log event that is too old. This field is exclusive.
     */
    tooOldLogEventEndIndex?: LogEventIndex;
    /**
     * The expired log events.
     */
    expiredLogEventEndIndex?: LogEventIndex;
  }
  export type RequestId = string;
  export type ResourceArns = Arn[];
  export type ResourceIdentifier = string;
  export type ResourcePolicies = ResourcePolicy[];
  export interface ResourcePolicy {
    /**
     * The name of the resource policy.
     */
    policyName?: PolicyName;
    /**
     * The details of the policy.
     */
    policyDocument?: PolicyDocument;
    /**
     * Timestamp showing when this policy was last updated, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    lastUpdatedTime?: Timestamp;
  }
  export type ResourceType = string;
  export type ResourceTypes = ResourceType[];
  export interface ResultField {
    /**
     * The log event field.
     */
    field?: Field;
    /**
     * The value of this field.
     */
    value?: Value;
  }
  export type ResultRows = ResultField[];
  export type RoleArn = string;
  export interface S3DeliveryConfiguration {
    /**
     * This string allows re-configuring the S3 object prefix to contain either static or variable sections. The valid variables to use in the suffix path will vary by each log source. See ConfigurationTemplate$allowedSuffixPathFields for more info on what values are supported in the suffix path for each log source.
     */
    suffixPath?: DeliverySuffixPath;
    /**
     * This parameter causes the S3 objects that contain delivered logs to use a prefix structure that allows for integration with Apache Hive.
     */
    enableHiveCompatiblePath?: Boolean;
  }
  export type Scope = "ALL"|string;
  export interface SearchedLogStream {
    /**
     * The name of the log stream.
     */
    logStreamName?: LogStreamName;
    /**
     * Indicates whether all the events in this log stream were searched.
     */
    searchedCompletely?: LogStreamSearchedCompletely;
  }
  export type SearchedLogStreams = SearchedLogStream[];
  export type SelectionCriteria = string;
  export type SequenceToken = string;
  export type Service = string;
  export type SessionId = string;
  export interface SessionStreamingException {
    message?: Message;
  }
  export interface SessionTimeoutException {
    message?: Message;
  }
  export type StandardUnit = "Seconds"|"Microseconds"|"Milliseconds"|"Bytes"|"Kilobytes"|"Megabytes"|"Gigabytes"|"Terabytes"|"Bits"|"Kilobits"|"Megabits"|"Gigabits"|"Terabits"|"Percent"|"Count"|"Bytes/Second"|"Kilobytes/Second"|"Megabytes/Second"|"Gigabytes/Second"|"Terabytes/Second"|"Bits/Second"|"Kilobits/Second"|"Megabits/Second"|"Gigabits/Second"|"Terabits/Second"|"Count/Second"|"None"|string;
  export type StartFromHead = boolean;
  export type StartLiveTailLogGroupIdentifiers = LogGroupIdentifier[];
  export interface StartLiveTailRequest {
    /**
     * An array where each item in the array is a log group to include in the Live Tail session. Specify each log group by its ARN.  If you specify an ARN, the ARN can't end with an asterisk (*).   You can include up to 10 log groups. 
     */
    logGroupIdentifiers: StartLiveTailLogGroupIdentifiers;
    /**
     * If you specify this parameter, then only log events in the log streams that you specify here are included in the Live Tail session. If you specify this field, you can't also specify the logStreamNamePrefixes field.  You can specify this parameter only if you specify only one log group in logGroupIdentifiers. 
     */
    logStreamNames?: InputLogStreamNames;
    /**
     * If you specify this parameter, then only log events in the log streams that have names that start with the prefixes that you specify here are included in the Live Tail session. If you specify this field, you can't also specify the logStreamNames field.  You can specify this parameter only if you specify only one log group in logGroupIdentifiers. 
     */
    logStreamNamePrefixes?: InputLogStreamNames;
    /**
     * An optional pattern to use to filter the results to include only log events that match the pattern. For example, a filter pattern of error 404 causes only log events that include both error and 404 to be included in the Live Tail stream. Regular expression filter patterns are supported. For more information about filter pattern syntax, see Filter and Pattern Syntax.
     */
    logEventFilterPattern?: FilterPattern;
  }
  export interface StartLiveTailResponse {
    /**
     * An object that includes the stream returned by your request. It can include both log events and exceptions.
     */
    responseStream?: StartLiveTailResponseStream;
  }
  export type StartLiveTailResponseStream = EventStream<{sessionStart?:LiveTailSessionStart,sessionUpdate?:LiveTailSessionUpdate,SessionTimeoutException?:SessionTimeoutException,SessionStreamingException?:SessionStreamingException}>;
  export interface StartQueryRequest {
    /**
     * The log group on which to perform the query.  A StartQuery operation must include exactly one of the following parameters: logGroupName, logGroupNames, or logGroupIdentifiers.  
     */
    logGroupName?: LogGroupName;
    /**
     * The list of log groups to be queried. You can include up to 50 log groups.  A StartQuery operation must include exactly one of the following parameters: logGroupName, logGroupNames, or logGroupIdentifiers.  
     */
    logGroupNames?: LogGroupNames;
    /**
     * The list of log groups to query. You can include up to 50 log groups. You can specify them by the log group name or ARN. If a log group that you're querying is in a source account and you're using a monitoring account, you must specify the ARN of the log group here. The query definition must also be defined in the monitoring account. If you specify an ARN, the ARN can't end with an asterisk (*). A StartQuery operation must include exactly one of the following parameters: logGroupName, logGroupNames, or logGroupIdentifiers. 
     */
    logGroupIdentifiers?: LogGroupIdentifiers;
    /**
     * The beginning of the time range to query. The range is inclusive, so the specified start time is included in the query. Specified as epoch time, the number of seconds since January 1, 1970, 00:00:00 UTC.
     */
    startTime: Timestamp;
    /**
     * The end of the time range to query. The range is inclusive, so the specified end time is included in the query. Specified as epoch time, the number of seconds since January 1, 1970, 00:00:00 UTC.
     */
    endTime: Timestamp;
    /**
     * The query string to use. For more information, see CloudWatch Logs Insights Query Syntax.
     */
    queryString: QueryString;
    /**
     * The maximum number of log events to return in the query. If the query string uses the fields command, only the specified fields and their values are returned. The default is 1000.
     */
    limit?: EventsLimit;
  }
  export interface StartQueryResponse {
    /**
     * The unique ID of the query. 
     */
    queryId?: QueryId;
  }
  export type State = "Active"|"Suppressed"|"Baseline"|string;
  export type StatsValue = number;
  export interface StopQueryRequest {
    /**
     * The ID number of the query to stop. To find this ID number, use DescribeQueries.
     */
    queryId: QueryId;
  }
  export interface StopQueryResponse {
    /**
     * This is true if the query was stopped by the StopQuery operation.
     */
    success?: Success;
  }
  export type StoredBytes = number;
  export interface SubscriptionFilter {
    /**
     * The name of the subscription filter.
     */
    filterName?: FilterName;
    /**
     * The name of the log group.
     */
    logGroupName?: LogGroupName;
    filterPattern?: FilterPattern;
    /**
     * The Amazon Resource Name (ARN) of the destination.
     */
    destinationArn?: DestinationArn;
    /**
     * 
     */
    roleArn?: RoleArn;
    distribution?: Distribution;
    /**
     * The creation time of the subscription filter, expressed as the number of milliseconds after Jan 1, 1970 00:00:00 UTC.
     */
    creationTime?: Timestamp;
  }
  export type SubscriptionFilters = SubscriptionFilter[];
  export type Success = boolean;
  export interface SuppressionPeriod {
    /**
     * Specifies the number of seconds, minutes or hours to suppress this anomaly. There is no maximum.
     */
    value?: Integer;
    /**
     * Specifies whether the value of value is in seconds, minutes, or hours.
     */
    suppressionUnit?: SuppressionUnit;
  }
  export type SuppressionState = "SUPPRESSED"|"UNSUPPRESSED"|string;
  export type SuppressionType = "LIMITED"|"INFINITE"|string;
  export type SuppressionUnit = "SECONDS"|"MINUTES"|"HOURS"|string;
  export type TagKey = string;
  export type TagKeyList = TagKey[];
  export type TagList = TagKey[];
  export interface TagLogGroupRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The key-value pairs to use for the tags.
     */
    tags: Tags;
  }
  export interface TagResourceRequest {
    /**
     * The ARN of the resource that you're adding tags to. The ARN format of a log group is arn:aws:logs:Region:account-id:log-group:log-group-name   The ARN format of a destination is arn:aws:logs:Region:account-id:destination:destination-name   For more information about ARN format, see CloudWatch Logs resources and operations.
     */
    resourceArn: AmazonResourceName;
    /**
     * The list of key-value pairs to associate with the resource.
     */
    tags: Tags;
  }
  export type TagValue = string;
  export type Tags = {[key: string]: TagValue};
  export type TargetArn = string;
  export type TestEventMessages = EventMessage[];
  export interface TestMetricFilterRequest {
    filterPattern: FilterPattern;
    /**
     * The log event messages to test.
     */
    logEventMessages: TestEventMessages;
  }
  export interface TestMetricFilterResponse {
    /**
     * The matched events.
     */
    matches?: MetricFilterMatches;
  }
  export type Time = string;
  export type Timestamp = number;
  export type Token = string;
  export type TokenString = string;
  export type TokenValue = number;
  export type Unmask = boolean;
  export interface UntagLogGroupRequest {
    /**
     * The name of the log group.
     */
    logGroupName: LogGroupName;
    /**
     * The tag keys. The corresponding tags are removed from the log group.
     */
    tags: TagList;
  }
  export interface UntagResourceRequest {
    /**
     * The ARN of the CloudWatch Logs resource that you're removing tags from. The ARN format of a log group is arn:aws:logs:Region:account-id:log-group:log-group-name   The ARN format of a destination is arn:aws:logs:Region:account-id:destination:destination-name   For more information about ARN format, see CloudWatch Logs resources and operations.
     */
    resourceArn: AmazonResourceName;
    /**
     * The list of tag keys to remove from the resource.
     */
    tagKeys: TagKeyList;
  }
  export interface UpdateAnomalyRequest {
    /**
     * If you are suppressing or unsuppressing an anomaly, specify its unique ID here. You can find anomaly IDs by using the ListAnomalies operation.
     */
    anomalyId?: AnomalyId;
    /**
     * If you are suppressing or unsuppressing an pattern, specify its unique ID here. You can find pattern IDs by using the ListAnomalies operation.
     */
    patternId?: PatternId;
    /**
     * The ARN of the anomaly detector that this operation is to act on.
     */
    anomalyDetectorArn: AnomalyDetectorArn;
    /**
     * Use this to specify whether the suppression to be temporary or infinite. If you specify LIMITED, you must also specify a suppressionPeriod. If you specify INFINITE, any value for suppressionPeriod is ignored. 
     */
    suppressionType?: SuppressionType;
    /**
     * If you are temporarily suppressing an anomaly or pattern, use this structure to specify how long the suppression is to last.
     */
    suppressionPeriod?: SuppressionPeriod;
  }
  export interface UpdateDeliveryConfigurationRequest {
    /**
     * The ID of the delivery to be updated by this request.
     */
    id: DeliveryId;
    /**
     * The list of record fields to be delivered to the destination, in order. If the delivery’s log source has mandatory fields, they must be included in this list.
     */
    recordFields?: RecordFields;
    /**
     * The field delimiter to use between record fields when the final output format of a delivery is in Plain, W3C, or Raw format.
     */
    fieldDelimiter?: FieldDelimiter;
    /**
     * This structure contains parameters that are valid only when the delivery’s delivery destination is an S3 bucket.
     */
    s3DeliveryConfiguration?: S3DeliveryConfiguration;
  }
  export interface UpdateDeliveryConfigurationResponse {
  }
  export interface UpdateLogAnomalyDetectorRequest {
    /**
     * The ARN of the anomaly detector that you want to update.
     */
    anomalyDetectorArn: AnomalyDetectorArn;
    /**
     * Specifies how often the anomaly detector runs and look for anomalies. Set this value according to the frequency that the log group receives new logs. For example, if the log group receives new log events every 10 minutes, then setting evaluationFrequency to FIFTEEN_MIN might be appropriate.
     */
    evaluationFrequency?: EvaluationFrequency;
    filterPattern?: FilterPattern;
    /**
     * The number of days to use as the life cycle of anomalies. After this time, anomalies are automatically baselined and the anomaly detector model will treat new occurrences of similar event as normal. Therefore, if you do not correct the cause of an anomaly during this time, it will be considered normal going forward and will not be detected.
     */
    anomalyVisibilityTime?: AnomalyVisibilityTime;
    /**
     * Use this parameter to pause or restart the anomaly detector. 
     */
    enabled: Boolean;
  }
  export type Value = string;
  /**
   * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
   */
  export type apiVersion = "2014-03-28"|"latest"|string;
  export interface ClientApiVersions {
    /**
     * A string in YYYY-MM-DD format that represents the latest possible API version that can be used in this service. Specify 'latest' to use the latest possible version.
     */
    apiVersion?: apiVersion;
  }
  export type ClientConfiguration = ServiceConfigurationOptions & ClientApiVersions;
  /**
   * Contains interfaces for use with the CloudWatchLogs client.
   */
  export import Types = CloudWatchLogs;
}
export = CloudWatchLogs;
