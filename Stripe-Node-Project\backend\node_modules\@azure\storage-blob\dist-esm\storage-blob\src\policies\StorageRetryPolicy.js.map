{"version": 3, "file": "StorageRetryPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AASrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAIpD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CAAC,YAAkC;IACtE,OAAO;QACL,MAAM,EAAE,CAAC,UAAyB,EAAE,OAA6B,EAAsB,EAAE;YACvF,OAAO,IAAI,kBAAkB,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QACnE,CAAC;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,sBASX;AATD,WAAY,sBAAsB;IAChC;;OAEG;IACH,iFAAW,CAAA;IACX;;OAEG;IACH,qEAAK,CAAA;AACP,CAAC,EATW,sBAAsB,KAAtB,sBAAsB,QASjC;AAED,wCAAwC;AACxC,MAAM,qBAAqB,GAAwB;IACjD,iBAAiB,EAAE,GAAG,GAAG,IAAI;IAC7B,QAAQ,EAAE,CAAC;IACX,cAAc,EAAE,CAAC,GAAG,IAAI;IACxB,eAAe,EAAE,sBAAsB,CAAC,WAAW;IACnD,aAAa,EAAE,EAAE;IACjB,cAAc,EAAE,SAAS,EAAE,2CAA2C;CACvE,CAAC;AAEF,MAAM,iBAAiB,GAAG,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;AAEvE;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,iBAAiB;IAMvD;;;;;;OAMG;IACH,YACE,UAAyB,EACzB,OAA6B,EAC7B,eAAoC,qBAAqB;QAEzD,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAE3B,2BAA2B;QAC3B,IAAI,CAAC,YAAY,GAAG;YAClB,eAAe,EAAE,YAAY,CAAC,eAAe;gBAC3C,CAAC,CAAC,YAAY,CAAC,eAAe;gBAC9B,CAAC,CAAC,qBAAqB,CAAC,eAAe;YAEzC,QAAQ,EACN,YAAY,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,IAAI,CAAC;gBACjD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC;gBACnC,CAAC,CAAC,qBAAqB,CAAC,QAAQ;YAEpC,cAAc,EACZ,YAAY,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC;gBAC7D,CAAC,CAAC,YAAY,CAAC,cAAc;gBAC7B,CAAC,CAAC,qBAAqB,CAAC,cAAc;YAE1C,cAAc,EACZ,YAAY,CAAC,cAAc,IAAI,YAAY,CAAC,cAAc,IAAI,CAAC;gBAC7D,CAAC,CAAC,IAAI,CAAC,GAAG,CACN,YAAY,CAAC,cAAc,EAC3B,YAAY,CAAC,iBAAiB;oBAC5B,CAAC,CAAC,YAAY,CAAC,iBAAiB;oBAChC,CAAC,CAAC,qBAAqB,CAAC,iBAAkB,CAC7C;gBACH,CAAC,CAAC,qBAAqB,CAAC,cAAc;YAE1C,iBAAiB,EACf,YAAY,CAAC,iBAAiB,IAAI,YAAY,CAAC,iBAAiB,IAAI,CAAC;gBACnE,CAAC,CAAC,YAAY,CAAC,iBAAiB;gBAChC,CAAC,CAAC,qBAAqB,CAAC,iBAAiB;YAE7C,aAAa,EAAE,YAAY,CAAC,aAAa;gBACvC,CAAC,CAAC,YAAY,CAAC,aAAa;gBAC5B,CAAC,CAAC,qBAAqB,CAAC,aAAa;SACxC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,WAAW,CAAC,OAAoB;QAC3C,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;;;;;;;;OASG;IACO,KAAK,CAAC,kBAAkB,CAChC,OAAoB,EACpB,eAAwB,EACxB,OAAe;QAEf,MAAM,UAAU,GAAgB,OAAO,CAAC,KAAK,EAAE,CAAC;QAEhD,MAAM,cAAc,GAClB,eAAe;YACf,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa;YAChC,CAAC,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC;YACxF,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;QAEpB,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,UAAU,CAAC,GAAG,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,aAAc,CAAC,CAAC;QAChF,CAAC;QAED,kEAAkE;QAClE,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;YACrC,UAAU,CAAC,GAAG,GAAG,eAAe,CAC9B,UAAU,CAAC,GAAG,EACd,YAAY,CAAC,UAAU,CAAC,OAAO,EAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,cAAe,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE,CAChE,CAAC;QACJ,CAAC;QAED,IAAI,QAA2C,CAAC;QAChD,IAAI,CAAC;YACH,MAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,IAAI,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAC9F,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAC1D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACzD,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,eAAe,GAAG,eAAe,IAAI,CAAC,CAAC,cAAc,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;QACpF,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,uCAAuC,GAAG,CAAC,OAAO,WAAW,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;YACtF,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC;gBAC9D,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;QAC/D,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;;OAOG;IACO,WAAW,CACnB,cAAuB,EACvB,OAAe,EACf,QAAgC,EAChC,GAAe;QAEf,IAAI,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,QAAS,EAAE,CAAC;YAC3C,MAAM,CAAC,IAAI,CACT,2BAA2B,OAAO,gBAAgB,IAAI,CAAC,YAAY;iBAChE,QAAS,mBAAmB,CAChC,CAAC;YACF,OAAO,KAAK,CAAC;QACf,CAAC;QAED,iFAAiF;QACjF,uBAAuB;QACvB,MAAM,eAAe,GAAG;YACtB,WAAW;YACX,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,QAAQ;YACR,WAAW;YACX,SAAS;YACT,OAAO;YACP,oBAAoB,EAAE,2DAA2D;SAClF,CAAC;QACF,IAAI,GAAG,EAAE,CAAC;YACR,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC7C,IACE,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;oBAC/C,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;oBAClD,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC,EAClE,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,8BAA8B,cAAc,qBAAqB,CAAC,CAAC;oBAC/E,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,kFAAkF;QAClF,gFAAgF;QAChF,gEAAgE;QAChE,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACnE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,0CAA0C;YAC1C,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,2CAA2C,UAAU,GAAG,CAAC,CAAC;gBACtE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,wGAAwG;QACxG,kBAAkB;QAClB,6CAA6C;QAC7C,mCAAmC;QACnC,8FAA8F;QAC9F,2CAA2C;QAC3C,mCAAmC;QACnC,gCAAgC;QAChC,oCAAoC;QACpC,6BAA6B;QAC7B,yBAAyB;QACzB,UAAU;QACV,QAAQ;QACR,MAAM;QACN,IAAI;QAEJ,IAAI,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,MAAK,aAAa,KAAI,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,OAAO,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAA,EAAE,CAAC;YAC9F,MAAM,CAAC,IAAI,CACT,iFAAiF,CAClF,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,KAAK,CAAC,cAAuB,EAAE,OAAe,EAAE,WAA6B;QACzF,IAAI,aAAa,GAAW,CAAC,CAAC;QAE9B,IAAI,cAAc,EAAE,CAAC;YACnB,QAAQ,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;gBAC1C,KAAK,sBAAsB,CAAC,WAAW;oBACrC,aAAa,GAAG,IAAI,CAAC,GAAG,CACtB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,cAAe,EAClE,IAAI,CAAC,YAAY,CAAC,iBAAkB,CACrC,CAAC;oBACF,MAAM;gBACR,KAAK,sBAAsB,CAAC,KAAK;oBAC/B,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,cAAe,CAAC;oBAClD,MAAM;YACV,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;QACvC,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,0BAA0B,aAAa,IAAI,CAAC,CAAC;QACzD,OAAO,KAAK,CAAC,aAAa,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;IAC9D,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport { AbortError } from \"@azure/abort-controller\";\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n  RequestPolicyFactory,\n  WebResourceLike as WebResource,\n  CompatResponse as HttpOperationResponse,\n} from \"@azure/core-http-compat\";\nimport { BaseRequestPolicy } from \"./RequestPolicy\";\nimport type { RestError } from \"@azure/core-rest-pipeline\";\n\nimport type { StorageRetryOptions } from \"../StorageRetryPolicyFactory\";\nimport { URLConstants } from \"../utils/constants\";\nimport { delay, setURLHost, setURLParameter } from \"../utils/utils.common\";\nimport { logger } from \"../log\";\n\n/**\n * A factory method used to generated a RetryPolicy factory.\n *\n * @param retryOptions -\n */\nexport function NewRetryPolicyFactory(retryOptions?: StorageRetryOptions): RequestPolicyFactory {\n  return {\n    create: (nextPolicy: RequestPolicy, options: RequestPolicyOptions): StorageRetryPolicy => {\n      return new StorageRetryPolicy(nextPolicy, options, retryOptions);\n    },\n  };\n}\n\n/**\n * RetryPolicy types.\n */\nexport enum StorageRetryPolicyType {\n  /**\n   * Exponential retry. Retry time delay grows exponentially.\n   */\n  EXPONENTIAL,\n  /**\n   * Linear retry. Retry time delay grows linearly.\n   */\n  FIXED,\n}\n\n// Default values of StorageRetryOptions\nconst DEFAULT_RETRY_OPTIONS: StorageRetryOptions = {\n  maxRetryDelayInMs: 120 * 1000,\n  maxTries: 4,\n  retryDelayInMs: 4 * 1000,\n  retryPolicyType: StorageRetryPolicyType.EXPONENTIAL,\n  secondaryHost: \"\",\n  tryTimeoutInMs: undefined, // Use server side default timeout strategy\n};\n\nconst RETRY_ABORT_ERROR = new AbortError(\"The operation was aborted.\");\n\n/**\n * Retry policy with exponential retry and linear retry implemented.\n */\nexport class StorageRetryPolicy extends BaseRequestPolicy {\n  /**\n   * RetryOptions.\n   */\n  private readonly retryOptions: StorageRetryOptions;\n\n  /**\n   * Creates an instance of RetryPolicy.\n   *\n   * @param nextPolicy -\n   * @param options -\n   * @param retryOptions -\n   */\n  constructor(\n    nextPolicy: RequestPolicy,\n    options: RequestPolicyOptions,\n    retryOptions: StorageRetryOptions = DEFAULT_RETRY_OPTIONS,\n  ) {\n    super(nextPolicy, options);\n\n    // Initialize retry options\n    this.retryOptions = {\n      retryPolicyType: retryOptions.retryPolicyType\n        ? retryOptions.retryPolicyType\n        : DEFAULT_RETRY_OPTIONS.retryPolicyType,\n\n      maxTries:\n        retryOptions.maxTries && retryOptions.maxTries >= 1\n          ? Math.floor(retryOptions.maxTries)\n          : DEFAULT_RETRY_OPTIONS.maxTries,\n\n      tryTimeoutInMs:\n        retryOptions.tryTimeoutInMs && retryOptions.tryTimeoutInMs >= 0\n          ? retryOptions.tryTimeoutInMs\n          : DEFAULT_RETRY_OPTIONS.tryTimeoutInMs,\n\n      retryDelayInMs:\n        retryOptions.retryDelayInMs && retryOptions.retryDelayInMs >= 0\n          ? Math.min(\n              retryOptions.retryDelayInMs,\n              retryOptions.maxRetryDelayInMs\n                ? retryOptions.maxRetryDelayInMs\n                : DEFAULT_RETRY_OPTIONS.maxRetryDelayInMs!,\n            )\n          : DEFAULT_RETRY_OPTIONS.retryDelayInMs,\n\n      maxRetryDelayInMs:\n        retryOptions.maxRetryDelayInMs && retryOptions.maxRetryDelayInMs >= 0\n          ? retryOptions.maxRetryDelayInMs\n          : DEFAULT_RETRY_OPTIONS.maxRetryDelayInMs,\n\n      secondaryHost: retryOptions.secondaryHost\n        ? retryOptions.secondaryHost\n        : DEFAULT_RETRY_OPTIONS.secondaryHost,\n    };\n  }\n\n  /**\n   * Sends request.\n   *\n   * @param request -\n   */\n  public async sendRequest(request: WebResource): Promise<HttpOperationResponse> {\n    return this.attemptSendRequest(request, false, 1);\n  }\n\n  /**\n   * Decide and perform next retry. Won't mutate request parameter.\n   *\n   * @param request -\n   * @param secondaryHas404 -  If attempt was against the secondary & it returned a StatusNotFound (404), then\n   *                                   the resource was not found. This may be due to replication delay. So, in this\n   *                                   case, we'll never try the secondary again for this operation.\n   * @param attempt -           How many retries has been attempted to performed, starting from 1, which includes\n   *                                   the attempt will be performed by this method call.\n   */\n  protected async attemptSendRequest(\n    request: WebResource,\n    secondaryHas404: boolean,\n    attempt: number,\n  ): Promise<HttpOperationResponse> {\n    const newRequest: WebResource = request.clone();\n\n    const isPrimaryRetry =\n      secondaryHas404 ||\n      !this.retryOptions.secondaryHost ||\n      !(request.method === \"GET\" || request.method === \"HEAD\" || request.method === \"OPTIONS\") ||\n      attempt % 2 === 1;\n\n    if (!isPrimaryRetry) {\n      newRequest.url = setURLHost(newRequest.url, this.retryOptions.secondaryHost!);\n    }\n\n    // Set the server-side timeout query parameter \"timeout=[seconds]\"\n    if (this.retryOptions.tryTimeoutInMs) {\n      newRequest.url = setURLParameter(\n        newRequest.url,\n        URLConstants.Parameters.TIMEOUT,\n        Math.floor(this.retryOptions.tryTimeoutInMs! / 1000).toString(),\n      );\n    }\n\n    let response: HttpOperationResponse | undefined;\n    try {\n      logger.info(`RetryPolicy: =====> Try=${attempt} ${isPrimaryRetry ? \"Primary\" : \"Secondary\"}`);\n      response = await this._nextPolicy.sendRequest(newRequest);\n      if (!this.shouldRetry(isPrimaryRetry, attempt, response)) {\n        return response;\n      }\n\n      secondaryHas404 = secondaryHas404 || (!isPrimaryRetry && response.status === 404);\n    } catch (err: any) {\n      logger.error(`RetryPolicy: Caught error, message: ${err.message}, code: ${err.code}`);\n      if (!this.shouldRetry(isPrimaryRetry, attempt, response, err)) {\n        throw err;\n      }\n    }\n\n    await this.delay(isPrimaryRetry, attempt, request.abortSignal);\n    return this.attemptSendRequest(request, secondaryHas404, ++attempt);\n  }\n\n  /**\n   * Decide whether to retry according to last HTTP response and retry counters.\n   *\n   * @param isPrimaryRetry -\n   * @param attempt -\n   * @param response -\n   * @param err -\n   */\n  protected shouldRetry(\n    isPrimaryRetry: boolean,\n    attempt: number,\n    response?: HttpOperationResponse,\n    err?: RestError,\n  ): boolean {\n    if (attempt >= this.retryOptions.maxTries!) {\n      logger.info(\n        `RetryPolicy: Attempt(s) ${attempt} >= maxTries ${this.retryOptions\n          .maxTries!}, no further try.`,\n      );\n      return false;\n    }\n\n    // Handle network failures, you may need to customize the list when you implement\n    // your own http client\n    const retriableErrors = [\n      \"ETIMEDOUT\",\n      \"ESOCKETTIMEDOUT\",\n      \"ECONNREFUSED\",\n      \"ECONNRESET\",\n      \"ENOENT\",\n      \"ENOTFOUND\",\n      \"TIMEOUT\",\n      \"EPIPE\",\n      \"REQUEST_SEND_ERROR\", // For default xhr based http client provided in ms-rest-js\n    ];\n    if (err) {\n      for (const retriableError of retriableErrors) {\n        if (\n          err.name.toUpperCase().includes(retriableError) ||\n          err.message.toUpperCase().includes(retriableError) ||\n          (err.code && err.code.toString().toUpperCase() === retriableError)\n        ) {\n          logger.info(`RetryPolicy: Network error ${retriableError} found, will retry.`);\n          return true;\n        }\n      }\n    }\n\n    // If attempt was against the secondary & it returned a StatusNotFound (404), then\n    // the resource was not found. This may be due to replication delay. So, in this\n    // case, we'll never try the secondary again for this operation.\n    if (response || err) {\n      const statusCode = response ? response.status : err ? err.statusCode : 0;\n      if (!isPrimaryRetry && statusCode === 404) {\n        logger.info(`RetryPolicy: Secondary access with 404, will retry.`);\n        return true;\n      }\n\n      // Server internal error or server timeout\n      if (statusCode === 503 || statusCode === 500) {\n        logger.info(`RetryPolicy: Will retry for status code ${statusCode}.`);\n        return true;\n      }\n    }\n\n    // [Copy source error code] Feature is pending on service side, skip retry on copy source error for now.\n    // if (response) {\n    //   // Retry select Copy Source Error Codes.\n    //   if (response?.status >= 400) {\n    //     const copySourceError = response.headers.get(HeaderConstants.X_MS_CopySourceErrorCode);\n    //     if (copySourceError !== undefined) {\n    //       switch (copySourceError) {\n    //         case \"InternalError\":\n    //         case \"OperationTimedOut\":\n    //         case \"ServerBusy\":\n    //           return true;\n    //       }\n    //     }\n    //   }\n    // }\n\n    if (err?.code === \"PARSE_ERROR\" && err?.message.startsWith(`Error \"Error: Unclosed root tag`)) {\n      logger.info(\n        \"RetryPolicy: Incomplete XML response likely due to service timeout, will retry.\",\n      );\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Delay a calculated time between retries.\n   *\n   * @param isPrimaryRetry -\n   * @param attempt -\n   * @param abortSignal -\n   */\n  private async delay(isPrimaryRetry: boolean, attempt: number, abortSignal?: AbortSignalLike) {\n    let delayTimeInMs: number = 0;\n\n    if (isPrimaryRetry) {\n      switch (this.retryOptions.retryPolicyType) {\n        case StorageRetryPolicyType.EXPONENTIAL:\n          delayTimeInMs = Math.min(\n            (Math.pow(2, attempt - 1) - 1) * this.retryOptions.retryDelayInMs!,\n            this.retryOptions.maxRetryDelayInMs!,\n          );\n          break;\n        case StorageRetryPolicyType.FIXED:\n          delayTimeInMs = this.retryOptions.retryDelayInMs!;\n          break;\n      }\n    } else {\n      delayTimeInMs = Math.random() * 1000;\n    }\n\n    logger.info(`RetryPolicy: Delay for ${delayTimeInMs}ms`);\n    return delay(delayTimeInMs, abortSignal, RETRY_ABORT_ERROR);\n  }\n}\n"]}