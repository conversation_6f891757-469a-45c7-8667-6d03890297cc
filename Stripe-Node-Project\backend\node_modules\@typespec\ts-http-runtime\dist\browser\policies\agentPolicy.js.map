{"version": 3, "file": "agentPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/agentPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAKlC;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,aAAa,CAAC;AAE7C;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,KAAa;IACvC,OAAO;QACL,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC/B,+EAA+E;YAC/E,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC;YACpB,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { Agent } from \"../interfaces.js\";\n\n/**\n * Name of the Agent Policy\n */\nexport const agentPolicyName = \"agentPolicy\";\n\n/**\n * Gets a pipeline policy that sets http.agent\n */\nexport function agentPolicy(agent?: Agent): PipelinePolicy {\n  return {\n    name: agentPolicyName,\n    sendRequest: async (req, next) => {\n      // Users may define an agent on the request, honor it over the client level one\n      if (!req.agent) {\n        req.agent = agent;\n      }\n      return next(req);\n    },\n  };\n}\n"]}