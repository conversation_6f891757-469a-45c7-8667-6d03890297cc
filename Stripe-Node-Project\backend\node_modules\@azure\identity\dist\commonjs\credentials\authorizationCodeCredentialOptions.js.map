{"version": 3, "file": "authorizationCodeCredentialOptions.js", "sourceRoot": "", "sources": ["../../../src/credentials/authorizationCodeCredentialOptions.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AuthorityValidationOptions } from \"./authorityValidationOptions.js\";\nimport type { MultiTenantTokenCredentialOptions } from \"./multiTenantTokenCredentialOptions.js\";\n\n/**\n * Options for the {@link AuthorizationCodeCredential}\n */\nexport interface AuthorizationCodeCredentialOptions\n  extends MultiTenantTokenCredentialOptions,\n    AuthorityValidationOptions {}\n"]}