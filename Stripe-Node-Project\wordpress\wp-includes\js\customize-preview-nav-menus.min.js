/*! This file is auto-generated */
wp.customize.navMenusPreview=wp.customize.MenusCustomizerPreview=function(a,c,l){"use strict";var t={data:{navMenuInstanceArgs:{}}};return"undefined"!=typeof _wpCustomizePreviewNavMenusExports&&c.extend(t.data,_wpCustomizePreviewNavMenusExports),t.init=function(){var n=this,t=!1;l.preview.bind("sync",function(){t=!0}),l.selectiveRefresh&&(l.each(function(e){n.bindSettingListener(e)}),l.bind("add",function(e){e.get()&&!e.get()._invalid&&n.bindSettingListener(e,{fire:t})}),l.bind("remove",function(e){n.unbindSettingListener(e)}),l.selectiveRefresh.bind("render-partials-response",function(e){e.nav_menu_instance_args&&c.extend(n.data.navMenuInstanceArgs,e.nav_menu_instance_args)})),l.preview.bind("active",function(){n.highlightControls()})},l.selectiveRefresh&&(t.NavMenuInstancePartial=l.selectiveRefresh.Partial.extend({initialize:function(e,n){var t=this,a=e.match(/^nav_menu_instance\[([0-9a-f]{32})]$/);if(!a)throw new Error("Illegal id for nav_menu_instance partial. The key corresponds with the args HMAC.");if(a=a[1],(n=n||{}).params=c.extend({selector:'[data-customize-partial-id="'+e+'"]',navMenuArgs:n.constructingContainerContext||{},containerInclusive:!0},n.params||{}),l.selectiveRefresh.Partial.prototype.initialize.call(t,e,n),!c.isObject(t.params.navMenuArgs))throw new Error("Missing navMenuArgs");if(t.params.navMenuArgs.args_hmac!==a)throw new Error("args_hmac mismatch with id")},isRelatedSetting:function(e,n,t){var a,i,r,s,o,u=this;if(c.isString(e)&&(e=l(e)),(i=/^nav_menu_item\[/.test(e.id))&&c.isObject(n)&&c.isObject(t)&&(r=c.clone(n),s=c.clone(t),delete r.type_label,delete s.type_label,"https"===l.preview.scheme.get()&&((o=document.createElement("a")).href=r.url,o.protocol="https:",r.url=o.href,o.href=s.url,o.protocol="https:",s.url=o.href),n.title&&(delete s.original_title,delete r.original_title),c.isEqual(s,r)))return!1;if(u.params.navMenuArgs.theme_location){if("nav_menu_locations["+u.params.navMenuArgs.theme_location+"]"===e.id)return!0;a=l("nav_menu_locations["+u.params.navMenuArgs.theme_location+"]")}return!!(o=!(o=u.params.navMenuArgs.menu)&&a?a():o)&&("nav_menu["+o+"]"===e.id||i&&(n&&n.nav_menu_term_id===o||t&&t.nav_menu_term_id===o))},refresh:function(){var e,n=this,t=a.Deferred();return c.isNumber(n.params.navMenuArgs.menu)?e=n.params.navMenuArgs.menu:n.params.navMenuArgs.theme_location&&l.has("nav_menu_locations["+n.params.navMenuArgs.theme_location+"]")&&(e=l("nav_menu_locations["+n.params.navMenuArgs.theme_location+"]").get()),e?l.selectiveRefresh.Partial.prototype.refresh.call(n):(n.fallback(),t.reject(),t.promise())},renderContent:function(e){var n=e.container;""===e.addedContent&&e.partial.fallback(),l.selectiveRefresh.Partial.prototype.renderContent.call(this,e)&&a(document).trigger("customize-preview-menu-refreshed",[{instanceNumber:null,wpNavArgs:e.context,wpNavMenuArgs:e.context,oldContainer:n,newContainer:e.container}])}}),l.selectiveRefresh.partialConstructor.nav_menu_instance=t.NavMenuInstancePartial,t.handleUnplacedNavMenuInstances=function(e){var n=c.filter(c.values(t.data.navMenuInstanceArgs),function(e){return!l.selectiveRefresh.partial.has("nav_menu_instance["+e.args_hmac+"]")});return!!c.findWhere(n,e)&&(l.selectiveRefresh.requestFullRefresh(),!0)},t.bindSettingListener=function(e,n){var t;return n=n||{},(t=e.id.match(/^nav_menu\[(-?\d+)]$/))?(e._navMenuId=parseInt(t[1],10),e.bind(this.onChangeNavMenuSetting),n.fire&&this.onChangeNavMenuSetting.call(e,e(),!1),!0):(t=e.id.match(/^nav_menu_item\[(-?\d+)]$/))?(e._navMenuItemId=parseInt(t[1],10),e.bind(this.onChangeNavMenuItemSetting),n.fire&&this.onChangeNavMenuItemSetting.call(e,e(),!1),!0):!!(t=e.id.match(/^nav_menu_locations\[(.+?)]/))&&(e._navMenuThemeLocation=t[1],e.bind(this.onChangeNavMenuLocationsSetting),n.fire&&this.onChangeNavMenuLocationsSetting.call(e,e(),!1),!0)},t.unbindSettingListener=function(e){e.unbind(this.onChangeNavMenuSetting),e.unbind(this.onChangeNavMenuItemSetting),e.unbind(this.onChangeNavMenuLocationsSetting)},t.onChangeNavMenuSetting=function(){var n=this;t.handleUnplacedNavMenuInstances({menu:n._navMenuId}),l.each(function(e){e._navMenuThemeLocation&&n._navMenuId===e()&&t.handleUnplacedNavMenuInstances({theme_location:e._navMenuThemeLocation})})},t.onChangeNavMenuItemSetting=function(e,n){e=l("nav_menu["+String((e||n).nav_menu_term_id)+"]");e&&t.onChangeNavMenuSetting.call(e)},t.onChangeNavMenuLocationsSetting=function(){t.handleUnplacedNavMenuInstances({theme_location:this._navMenuThemeLocation}),!c.findWhere(c.values(t.data.navMenuInstanceArgs),{theme_location:this._navMenuThemeLocation})&&l.selectiveRefresh.requestFullRefresh()}),t.highlightControls=function(){l.settings.channel&&a(document).on("click",".menu-item",function(e){var n;e.shiftKey&&(n=a(this).attr("class").match(/(?:^|\s)menu-item-(-?\d+)(?:\s|$)/))&&(e.preventDefault(),e.stopPropagation(),l.preview.send("focus-nav-menu-item-control",parseInt(n[1],10)))})},l.bind("preview-ready",function(){t.init()}),t}(jQuery,_,wp.customize);