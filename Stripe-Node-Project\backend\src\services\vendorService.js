const { supabase, supabaseAdmin } = require('../config/database');
const { stripe, createExpressAccount, createAccountLink } = require('../config/stripe');
const { ValidationError, NotFoundError } = require('../middleware/errorHandler');

/**
 * Vendor management service
 */
class VendorService {
  /**
   * Create a new vendor
   * @param {Object} vendorData - Vendor information
   * @returns {Promise<Object>} Created vendor
   */
  async createVendor(vendorData) {
    const {
      name,
      email,
      niche,
      country = 'US',
      business_type = 'individual',
      metadata = {}
    } = vendorData;

    // Check if vendor with this email already exists
    const { data: existingVendor, error: checkError } = await supabase
      .from('vendors')
      .select('*')
      .eq('email', email)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw checkError;
    }

    if (existingVendor) {
      throw new ValidationError('Vendor with this email already exists');
    }

    // Create vendor record
    const { data: vendor, error } = await supabase
      .from('vendors')
      .insert({
        name,
        email,
        niche,
        country,
        business_type,
        metadata,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return vendor;
  }

  /**
   * Create Stripe Express account for vendor
   * @param {string} vendorId - Vendor ID
   * @returns {Promise<Object>} Stripe account and onboarding link
   */
  async createStripeAccount(vendorId) {
    // Get vendor information
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('*')
      .eq('id', vendorId)
      .single();

    if (vendorError || !vendor) {
      throw new NotFoundError('Vendor not found');
    }

    if (vendor.stripe_account_id) {
      throw new ValidationError('Vendor already has a Stripe account');
    }

    try {
      // Create Stripe Express account
      const account = await createExpressAccount({
        vendor_id: vendorId,
        email: vendor.email,
        country: vendor.country,
        niche: vendor.niche
      });

      // Update vendor with Stripe account ID
      const { data: updatedVendor, error: updateError } = await supabase
        .from('vendors')
        .update({
          stripe_account_id: account.id,
          stripe_account_data: account,
          updated_at: new Date().toISOString()
        })
        .eq('id', vendorId)
        .select()
        .single();

      if (updateError) {
        throw updateError;
      }

      // Create onboarding link
      const refreshUrl = `${process.env.API_BASE_URL}/vendors/${vendorId}/stripe/refresh`;
      const returnUrl = `${process.env.API_BASE_URL}/vendors/${vendorId}/stripe/return`;
      
      const accountLink = await createAccountLink(account.id, refreshUrl, returnUrl);

      return {
        vendor: updatedVendor,
        stripe_account: account,
        onboarding_url: accountLink.url
      };
    } catch (error) {
      console.error('Error creating Stripe account:', error);
      throw new ValidationError('Failed to create Stripe account: ' + error.message);
    }
  }

  /**
   * Get vendor by ID
   * @param {string} vendorId - Vendor ID
   * @returns {Promise<Object>} Vendor information
   */
  async getVendor(vendorId) {
    const { data: vendor, error } = await supabase
      .from('vendors')
      .select('*')
      .eq('id', vendorId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError('Vendor not found');
      }
      throw error;
    }

    return vendor;
  }

  /**
   * Update vendor information
   * @param {string} vendorId - Vendor ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated vendor
   */
  async updateVendor(vendorId, updateData) {
    const allowedFields = [
      'name', 'email', 'niche', 'country', 'business_type', 
      'is_active', 'metadata', 'commission_adjustment', 'tier'
    ];

    // Filter update data to only allowed fields
    const filteredData = {};
    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        filteredData[key] = updateData[key];
      }
    });

    filteredData.updated_at = new Date().toISOString();

    const { data: vendor, error } = await supabase
      .from('vendors')
      .update(filteredData)
      .eq('id', vendorId)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError('Vendor not found');
      }
      throw error;
    }

    return vendor;
  }

  /**
   * List vendors with filtering and pagination
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Vendors list with pagination
   */
  async listVendors(filters = {}) {
    const {
      niche,
      is_active,
      has_stripe_account,
      limit = 50,
      offset = 0,
      search
    } = filters;

    let query = supabaseAdmin
      .from('vendors')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (niche) {
      query = query.eq('niche', niche);
    }
    if (is_active !== undefined) {
      query = query.eq('is_active', is_active);
    }
    if (has_stripe_account !== undefined) {
      if (has_stripe_account) {
        query = query.not('stripe_account_id', 'is', null);
      } else {
        query = query.is('stripe_account_id', null);
      }
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    const { data: vendors, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      vendors,
      pagination: {
        total: count,
        limit: parseInt(limit),
        offset: parseInt(offset),
        has_more: count > offset + limit
      }
    };
  }

  /**
   * Get vendor Stripe account status
   * @param {string} vendorId - Vendor ID
   * @returns {Promise<Object>} Stripe account status
   */
  async getStripeAccountStatus(vendorId) {
    const vendor = await this.getVendor(vendorId);

    if (!vendor.stripe_account_id) {
      return {
        has_account: false,
        charges_enabled: false,
        payouts_enabled: false,
        requirements: null
      };
    }

    try {
      // Get account details from Stripe
      const account = await stripe.accounts.retrieve(vendor.stripe_account_id);

      return {
        has_account: true,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        details_submitted: account.details_submitted,
        requirements: account.requirements,
        account_type: account.type,
        country: account.country,
        default_currency: account.default_currency
      };
    } catch (error) {
      console.error('Error retrieving Stripe account:', error);
      return {
        has_account: true,
        charges_enabled: false,
        payouts_enabled: false,
        error: error.message
      };
    }
  }

  /**
   * Generate new onboarding link for vendor
   * @param {string} vendorId - Vendor ID
   * @returns {Promise<Object>} New onboarding link
   */
  async generateOnboardingLink(vendorId) {
    const vendor = await this.getVendor(vendorId);

    if (!vendor.stripe_account_id) {
      throw new ValidationError('Vendor does not have a Stripe account');
    }

    try {
      const refreshUrl = `${process.env.API_BASE_URL}/vendors/${vendorId}/stripe/refresh`;
      const returnUrl = `${process.env.API_BASE_URL}/vendors/${vendorId}/stripe/return`;
      
      const accountLink = await createAccountLink(
        vendor.stripe_account_id,
        refreshUrl,
        returnUrl
      );

      return {
        onboarding_url: accountLink.url,
        expires_at: accountLink.expires_at
      };
    } catch (error) {
      console.error('Error generating onboarding link:', error);
      throw new ValidationError('Failed to generate onboarding link: ' + error.message);
    }
  }

  /**
   * Get vendor transaction history
   * @param {string} vendorId - Vendor ID
   * @param {Object} filters - Filter options
   * @returns {Promise<Object>} Transaction history
   */
  async getTransactionHistory(vendorId, filters = {}) {
    const {
      status,
      start_date,
      end_date,
      limit = 50,
      offset = 0
    } = filters;

    let query = supabase
      .from('transactions')
      .select('*', { count: 'exact' })
      .eq('vendor_id', vendorId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (start_date) {
      query = query.gte('created_at', start_date);
    }
    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    const { data: transactions, error, count } = await query;

    if (error) {
      throw error;
    }

    // Calculate summary statistics
    const summary = transactions.reduce((acc, transaction) => {
      if (transaction.status === 'succeeded') {
        acc.total_amount += parseFloat(transaction.amount) || 0;
        acc.total_commission += parseFloat(transaction.commission_amount) || 0;
        acc.net_amount += parseFloat(transaction.final_amount) || 0;
        acc.successful_transactions += 1;
      }
      return acc;
    }, {
      total_amount: 0,
      total_commission: 0,
      net_amount: 0,
      successful_transactions: 0,
      total_transactions: transactions.length
    });

    return {
      transactions,
      summary,
      pagination: {
        total: count,
        limit: parseInt(limit),
        offset: parseInt(offset),
        has_more: count > offset + limit
      }
    };
  }

  /**
   * Delete vendor (soft delete)
   * @param {string} vendorId - Vendor ID
   * @returns {Promise<Object>} Deleted vendor
   */
  async deleteVendor(vendorId) {
    const { data: vendor, error } = await supabase
      .from('vendors')
      .update({
        is_active: false,
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', vendorId)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        throw new NotFoundError('Vendor not found');
      }
      throw error;
    }

    return vendor;
  }
}

module.exports = new VendorService();
