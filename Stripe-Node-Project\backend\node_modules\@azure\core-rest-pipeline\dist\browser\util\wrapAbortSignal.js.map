{"version": 3, "file": "wrapAbortSignal.js", "sourceRoot": "", "sources": ["../../../src/util/wrapAbortSignal.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC;;;;;GAKG;AACH,MAAM,UAAU,mBAAmB,CAAC,eAAgC;IAIlE,IAAI,eAAe,YAAY,WAAW,EAAE,CAAC;QAC3C,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,CAAC;IAC1C,CAAC;IAED,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;QAC5B,OAAO,EAAE,WAAW,EAAE,WAAW,CAAC,KAAK,CAAE,eAAuB,CAAC,MAAM,CAAC,EAAE,CAAC;IAC7E,CAAC;IAED,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;IACzC,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,SAAS,OAAO;QACd,IAAI,YAAY,EAAE,CAAC;YACjB,eAAe,CAAC,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACvD,YAAY,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IACD,SAAS,QAAQ;QACf,UAAU,CAAC,KAAK,CAAE,eAAuB,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,eAAe,CAAC,gBAAgB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IACpD,OAAO,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;AACrD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\n\n/**\n * Creates a native AbortSignal which reflects the state of the provided AbortSignalLike.\n * If the AbortSignalLike is already a native AbortSignal, it is returned as is.\n * @param abortSignalLike - The AbortSignalLike to wrap.\n * @returns - An object containing the native AbortSignal and an optional cleanup function. The cleanup function should be called when the AbortSignal is no longer needed.\n */\nexport function wrapAbortSignalLike(abortSignalLike: AbortSignalLike): {\n  abortSignal: AbortSignal;\n  cleanup?: () => void;\n} {\n  if (abortSignalLike instanceof AbortSignal) {\n    return { abortSignal: abortSignalLike };\n  }\n\n  if (abortSignalLike.aborted) {\n    return { abortSignal: AbortSignal.abort((abortSignalLike as any).reason) };\n  }\n\n  const controller = new AbortController();\n  let needsCleanup = true;\n  function cleanup(): void {\n    if (needsCleanup) {\n      abortSignalLike.removeEventListener(\"abort\", listener);\n      needsCleanup = false;\n    }\n  }\n  function listener(): void {\n    controller.abort((abortSignalLike as any).reason);\n    cleanup();\n  }\n\n  abortSignalLike.addEventListener(\"abort\", listener);\n  return { abortSignal: controller.signal, cleanup };\n}\n"]}