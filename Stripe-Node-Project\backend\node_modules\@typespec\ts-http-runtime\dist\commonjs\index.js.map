{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;;AAUlC,kEAA8D;AAArD,2GAAA,UAAU,OAAA;AACnB,gDAQ4B;AAP1B,+GAAA,kBAAkB,OAAA;AAClB,wGAAA,WAAW,OAAA;AACX,wGAAA,WAAW,OAAA;AACX,kHAAA,qBAAqB,OAAA;AA4BvB,mDAAqD;AAA5C,mHAAA,iBAAiB,OAAA;AAC1B,4DAAkC;AAClC,gEAAsC;AAUtC,2DAA0F;AAAjF,2HAAA,qBAAqB,OAAA;AAC9B,6CAMuB;AADrB,kHAAA,mBAAmB,OAAA;AAErB,+CAA+E;AAAtE,yGAAA,SAAS,OAAA;AAAE,2GAAA,WAAW,OAAA;AAC/B,4DAAoG;AAA3F,sHAAA,kBAAkB,OAAA;AAAE,sHAAA,kBAAkB,OAAA;AAC/C,+DAAiE;AAAxD,+HAAA,uBAAuB,OAAA;AAChC,sDAAkD;AAAzC,yGAAA,SAAS,OAAA;AAClB,gFAAyF;AAAhF,gJAAA,mCAAmC,OAAA;AAC5C,sDAAwD;AAA/C,+GAAA,eAAe,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ndeclare global {\n  interface FormData {}\n  interface Blob {}\n  interface File {}\n  interface ReadableStream<R = any> {}\n  interface TransformStream<I = any, O = any> {}\n}\n\nexport { AbortError } from \"./abort-controller/AbortError.js\";\nexport {\n  createClientLogger,\n  getLogLevel,\n  setLogLevel,\n  TypeSpecRuntimeLogger,\n  type Debugger,\n  type TypeSpecRuntimeClientLogger,\n  type TypeSpecRuntimeLogLevel,\n} from \"./logger/logger.js\";\n\nexport type {\n  BodyPart,\n  FormDataValue,\n  RawHttpHeaders,\n  KeyObject,\n  PxfObject,\n  HttpClient,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n  TlsSettings,\n  Agent,\n  RequestBodyType,\n  FormDataMap,\n  HttpHeaders,\n  HttpMethods,\n  MultipartRequestBody,\n  TransferProgressEvent,\n  ProxySettings,\n  RawHttpHeadersInput,\n  PipelineRetryOptions,\n} from \"./interfaces.js\";\nexport { createHttpHeaders } from \"./httpHeaders.js\";\nexport * from \"./auth/schemes.js\";\nexport * from \"./auth/oauth2Flows.js\";\nexport {\n  type BasicCredential,\n  type BearerTokenCredential,\n  type OAuth2TokenCredential,\n  type GetOAuth2TokenOptions,\n  type GetBearerTokenOptions,\n  type ApiKeyCredential,\n  type ClientCredential,\n} from \"./auth/credentials.js\";\nexport { createPipelineRequest, type PipelineRequestOptions } from \"./pipelineRequest.js\";\nexport {\n  type Pipeline,\n  type PipelinePolicy,\n  type AddPolicyOptions,\n  type PipelinePhase,\n  createEmptyPipeline,\n} from \"./pipeline.js\";\nexport { RestError, isRestError, type RestErrorOptions } from \"./restError.js\";\nexport { stringToUint8Array, uint8ArrayToString, type EncodingType } from \"./util/bytesEncoding.js\";\nexport { createDefaultHttpClient } from \"./defaultHttpClient.js\";\nexport { getClient } from \"./client/getClient.js\";\nexport { operationOptionsToRequestParameters } from \"./client/operationOptionHelpers.js\";\nexport { createRestError } from \"./client/restError.js\";\nexport type {\n  Client,\n  ClientOptions,\n  OperationOptions,\n  AdditionalPolicyConfig,\n  PathUnchecked,\n  PathUncheckedResponse,\n  HttpResponse,\n  RawResponseCallback,\n  OperationRequestOptions,\n  PathParameters,\n  ResourceMethods,\n  PathParameterWithOptions,\n  StreamableMethod,\n  RequestParameters,\n  HttpNodeStreamResponse,\n  HttpBrowserStreamResponse,\n  FullOperationResponse,\n} from \"./client/common.js\";\nexport type { PipelineOptions, TelemetryOptions } from \"./createPipelineFromOptions.js\";\nexport type { LogPolicyOptions } from \"./policies/logPolicy.js\";\nexport type { RedirectPolicyOptions } from \"./policies/redirectPolicy.js\";\nexport type { UserAgentPolicyOptions } from \"./policies/userAgentPolicy.js\";\n"]}