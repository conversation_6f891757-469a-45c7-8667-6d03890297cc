{"version": 3, "file": "service.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/service.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  QueueServiceProperties,\n  ServiceSetPropertiesOptionalParams,\n  ServiceSetPropertiesResponse,\n  ServiceGetPropertiesOptionalParams,\n  ServiceGetPropertiesResponse,\n  ServiceGetStatisticsOptionalParams,\n  ServiceGetStatisticsResponse,\n  ServiceListQueuesSegmentOptionalParams,\n  ServiceListQueuesSegmentResponse\n} from \"../models\";\n\n/** Interface representing a Service. */\nexport interface Service {\n  /**\n   * Sets properties for a storage account's Queue service endpoint, including properties for Storage\n   * Analytics and CORS (Cross-Origin Resource Sharing) rules\n   * @param properties The StorageService properties.\n   * @param options The options parameters.\n   */\n  setProperties(\n    properties: QueueServiceProperties,\n    options?: ServiceSetPropertiesOptionalParams\n  ): Promise<ServiceSetPropertiesResponse>;\n  /**\n   * gets the properties of a storage account's Queue service, including properties for Storage Analytics\n   * and CORS (Cross-Origin Resource Sharing) rules.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: ServiceGetPropertiesOptionalParams\n  ): Promise<ServiceGetPropertiesResponse>;\n  /**\n   * Retrieves statistics related to replication for the Queue service. It is only available on the\n   * secondary location endpoint when read-access geo-redundant replication is enabled for the storage\n   * account.\n   * @param options The options parameters.\n   */\n  getStatistics(\n    options?: ServiceGetStatisticsOptionalParams\n  ): Promise<ServiceGetStatisticsResponse>;\n  /**\n   * The List Queues Segment operation returns a list of the queues under the specified account\n   * @param options The options parameters.\n   */\n  listQueuesSegment(\n    options?: ServiceListQueuesSegmentOptionalParams\n  ): Promise<ServiceListQueuesSegmentResponse>;\n}\n"]}