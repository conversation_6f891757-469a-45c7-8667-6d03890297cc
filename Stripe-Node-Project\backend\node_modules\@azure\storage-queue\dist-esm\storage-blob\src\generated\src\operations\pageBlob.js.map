{"version": 3, "file": "pageBlob.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/operations/pageBlob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AAEjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAwBnD,4CAA4C;AAC5C,MAAM,OAAO,YAAY;IAGvB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CACJ,aAAqB,EACrB,iBAAyB,EACzB,OAAsC;QAEtC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,iBAAiB,EAAE,OAAO,EAAE,EAC7C,mBAAmB,CACpB,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,WAAW,CACT,aAAqB,EACrB,IAAsC,EACtC,OAA2C;QAE3C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,EAChC,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,UAAU,CACR,aAAqB,EACrB,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,aAAa,EAAE,OAAO,EAAE,EAC1B,uBAAuB,CACxB,CAAC;IACJ,CAAC;IAED;;;;;;;;;;OAUG;IACH,kBAAkB,CAChB,SAAiB,EACjB,WAAmB,EACnB,aAAqB,EACrB,KAAa,EACb,OAAkD;QAElD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,EACzD,+BAA+B,CAChC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,OAA6C;QAE7C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CACf,OAAiD;QAEjD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,8BAA8B,CAC/B,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,MAAM,CACJ,iBAAyB,EACzB,OAAsC;QAEtC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,iBAAiB,EAAE,OAAO,EAAE,EAC9B,mBAAmB,CACpB,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,oBAAoB,CAClB,oBAA8C,EAC9C,OAAoD;QAEpD,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,oBAAoB,EAAE,OAAO,EAAE,EACjC,iCAAiC,CAClC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACH,eAAe,CACb,UAAkB,EAClB,OAA+C;QAE/C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,UAAU,EAAE,OAAO,EAAE,EACvB,4BAA4B,CAC7B,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,qBAAqB;SAC7C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,sBAAsB;QACjC,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,IAAI;QACf,UAAU,CAAC,cAAc;QACzB,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,kBAAkB;KAC9B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,0BAA0B;SAClD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,KAAK;IAC7B,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,yBAAyB;QACpC,UAAU,CAAC,YAAY;QACvB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,iCAAiC;QAC5C,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,uBAAuB;KACnC;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,QAAQ;IACnB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,uBAAuB,GAA6B;IACxD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iCAAiC;QAC5C,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,UAAU;KACtB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,+BAA+B,GAA6B;IAChE,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,iCAAiC;SACzD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,0CAA0C;SAClE;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,qBAAqB;QAChC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,iCAAiC;QAC5C,UAAU,CAAC,wBAAwB;QACnC,UAAU,CAAC,uBAAuB;QAClC,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,kBAAkB;QAC7B,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,QAAQ;YAC5B,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,qCAAqC;SAC7D;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;KAClB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;KAClB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,8BAA8B,GAA6B;IAC/D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE,OAAO,CAAC,QAAQ;YAC5B,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,yCAAyC;SACjE;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,QAAQ;QACnB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,YAAY;KACxB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,KAAK;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;KAC3B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,qBAAqB;SAC7C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,gBAAgB,CAAC;IAC/D,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,aAAa;QACxB,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,mBAAmB;QAC9B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;KAC7B;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,iCAAiC,GAA6B;IAClE,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,mCAAmC;SAC3D;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4CAA4C;SACpE;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,gBAAgB,CAAC;IAC/D,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,kBAAkB;QAC7B,UAAU,CAAC,oBAAoB;KAChC;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,4BAA4B,GAA6B;IAC7D,IAAI,EAAE,yBAAyB;IAC/B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,8BAA8B;SACtD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,uCAAuC;SAC/D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,MAAM,CAAC;IACjE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,eAAe;QAC1B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,UAAU;KACtB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { PageBlob } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  PageBlobCreateOptionalParams,\n  PageBlobCreateResponse,\n  PageBlobUploadPagesOptionalParams,\n  PageBlobUploadPagesResponse,\n  PageBlobClearPagesOptionalParams,\n  PageBlobClearPagesResponse,\n  PageBlobUploadPagesFromURLOptionalParams,\n  PageBlobUploadPagesFromURLResponse,\n  PageBlobGetPageRangesOptionalParams,\n  PageBlobGetPageRangesResponse,\n  PageBlobGetPageRangesDiffOptionalParams,\n  PageBlobGetPageRangesDiffResponse,\n  PageBlobResizeOptionalParams,\n  PageBlobResizeResponse,\n  SequenceNumberActionType,\n  PageBlobUpdateSequenceNumberOptionalParams,\n  PageBlobUpdateSequenceNumberResponse,\n  PageBlobCopyIncrementalOptionalParams,\n  PageBlobCopyIncrementalResponse,\n} from \"../models\";\n\n/** Class containing PageBlob operations. */\nexport class PageBlobImpl implements PageBlob {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class PageBlob class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * The Create operation creates a new page blob.\n   * @param contentLength The length of the request.\n   * @param blobContentLength This header specifies the maximum size for the page blob, up to 1 TB. The\n   *                          page blob size must be aligned to a 512-byte boundary.\n   * @param options The options parameters.\n   */\n  create(\n    contentLength: number,\n    blobContentLength: number,\n    options?: PageBlobCreateOptionalParams,\n  ): Promise<PageBlobCreateResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, blobContentLength, options },\n      createOperationSpec,\n    );\n  }\n\n  /**\n   * The Upload Pages operation writes a range of pages to a page blob\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  uploadPages(\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: PageBlobUploadPagesOptionalParams,\n  ): Promise<PageBlobUploadPagesResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, body, options },\n      uploadPagesOperationSpec,\n    );\n  }\n\n  /**\n   * The Clear Pages operation clears a set of pages from a page blob\n   * @param contentLength The length of the request.\n   * @param options The options parameters.\n   */\n  clearPages(\n    contentLength: number,\n    options?: PageBlobClearPagesOptionalParams,\n  ): Promise<PageBlobClearPagesResponse> {\n    return this.client.sendOperationRequest(\n      { contentLength, options },\n      clearPagesOperationSpec,\n    );\n  }\n\n  /**\n   * The Upload Pages operation writes a range of pages to a page blob where the contents are read from a\n   * URL\n   * @param sourceUrl Specify a URL to the copy source.\n   * @param sourceRange Bytes of source data in the specified range. The length of this range should\n   *                    match the ContentLength header and x-ms-range/Range destination range header.\n   * @param contentLength The length of the request.\n   * @param range The range of bytes to which the source range would be written. The range should be 512\n   *              aligned and range-end is required.\n   * @param options The options parameters.\n   */\n  uploadPagesFromURL(\n    sourceUrl: string,\n    sourceRange: string,\n    contentLength: number,\n    range: string,\n    options?: PageBlobUploadPagesFromURLOptionalParams,\n  ): Promise<PageBlobUploadPagesFromURLResponse> {\n    return this.client.sendOperationRequest(\n      { sourceUrl, sourceRange, contentLength, range, options },\n      uploadPagesFromURLOperationSpec,\n    );\n  }\n\n  /**\n   * The Get Page Ranges operation returns the list of valid page ranges for a page blob or snapshot of a\n   * page blob\n   * @param options The options parameters.\n   */\n  getPageRanges(\n    options?: PageBlobGetPageRangesOptionalParams,\n  ): Promise<PageBlobGetPageRangesResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPageRangesOperationSpec,\n    );\n  }\n\n  /**\n   * The Get Page Ranges Diff operation returns the list of valid page ranges for a page blob that were\n   * changed between target blob and previous snapshot.\n   * @param options The options parameters.\n   */\n  getPageRangesDiff(\n    options?: PageBlobGetPageRangesDiffOptionalParams,\n  ): Promise<PageBlobGetPageRangesDiffResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPageRangesDiffOperationSpec,\n    );\n  }\n\n  /**\n   * Resize the Blob\n   * @param blobContentLength This header specifies the maximum size for the page blob, up to 1 TB. The\n   *                          page blob size must be aligned to a 512-byte boundary.\n   * @param options The options parameters.\n   */\n  resize(\n    blobContentLength: number,\n    options?: PageBlobResizeOptionalParams,\n  ): Promise<PageBlobResizeResponse> {\n    return this.client.sendOperationRequest(\n      { blobContentLength, options },\n      resizeOperationSpec,\n    );\n  }\n\n  /**\n   * Update the sequence number of the blob\n   * @param sequenceNumberAction Required if the x-ms-blob-sequence-number header is set for the request.\n   *                             This property applies to page blobs only. This property indicates how the service should modify the\n   *                             blob's sequence number\n   * @param options The options parameters.\n   */\n  updateSequenceNumber(\n    sequenceNumberAction: SequenceNumberActionType,\n    options?: PageBlobUpdateSequenceNumberOptionalParams,\n  ): Promise<PageBlobUpdateSequenceNumberResponse> {\n    return this.client.sendOperationRequest(\n      { sequenceNumberAction, options },\n      updateSequenceNumberOperationSpec,\n    );\n  }\n\n  /**\n   * The Copy Incremental operation copies a snapshot of the source page blob to a destination page blob.\n   * The snapshot is copied such that only the differential changes between the previously copied\n   * snapshot are transferred to the destination. The copied snapshots are complete copies of the\n   * original snapshot and can be read or copied from as usual. This API is supported since REST version\n   * 2016-05-31.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  copyIncremental(\n    copySource: string,\n    options?: PageBlobCopyIncrementalOptionalParams,\n  ): Promise<PageBlobCopyIncrementalResponse> {\n    return this.client.sendOperationRequest(\n      { copySource, options },\n      copyIncrementalOperationSpec,\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst createOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.PageBlobCreateHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobCreateExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.metadata,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobCacheControl,\n    Parameters.blobContentType,\n    Parameters.blobContentMD5,\n    Parameters.blobContentEncoding,\n    Parameters.blobContentLanguage,\n    Parameters.blobContentDisposition,\n    Parameters.immutabilityPolicyExpiry,\n    Parameters.immutabilityPolicyMode,\n    Parameters.encryptionScope,\n    Parameters.tier,\n    Parameters.blobTagsString,\n    Parameters.legalHold1,\n    Parameters.blobType,\n    Parameters.blobContentLength,\n    Parameters.blobSequenceNumber,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst uploadPagesOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.PageBlobUploadPagesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobUploadPagesExceptionHeaders,\n    },\n  },\n  requestBody: Parameters.body1,\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp19],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.range,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n    Parameters.transactionalContentMD5,\n    Parameters.transactionalContentCrc64,\n    Parameters.contentType1,\n    Parameters.accept2,\n    Parameters.pageWrite,\n    Parameters.ifSequenceNumberLessThanOrEqualTo,\n    Parameters.ifSequenceNumberLessThan,\n    Parameters.ifSequenceNumberEqualTo,\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"binary\",\n  serializer: xmlSerializer,\n};\nconst clearPagesOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.PageBlobClearPagesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobClearPagesExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp19],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.range,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n    Parameters.ifSequenceNumberLessThanOrEqualTo,\n    Parameters.ifSequenceNumberLessThan,\n    Parameters.ifSequenceNumberEqualTo,\n    Parameters.pageWrite1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst uploadPagesFromURLOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.PageBlobUploadPagesFromURLHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobUploadPagesFromURLExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp19],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.contentLength,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n    Parameters.sourceIfModifiedSince,\n    Parameters.sourceIfUnmodifiedSince,\n    Parameters.sourceIfMatch,\n    Parameters.sourceIfNoneMatch,\n    Parameters.sourceContentMD5,\n    Parameters.copySourceAuthorization,\n    Parameters.pageWrite,\n    Parameters.ifSequenceNumberLessThanOrEqualTo,\n    Parameters.ifSequenceNumberLessThan,\n    Parameters.ifSequenceNumberEqualTo,\n    Parameters.sourceUrl,\n    Parameters.sourceRange,\n    Parameters.sourceContentCrc64,\n    Parameters.range1,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getPageRangesOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.PageList,\n      headersMapper: Mappers.PageBlobGetPageRangesHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobGetPageRangesExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.snapshot,\n    Parameters.comp20,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.range,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst getPageRangesDiffOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: Mappers.PageList,\n      headersMapper: Mappers.PageBlobGetPageRangesDiffHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobGetPageRangesDiffExceptionHeaders,\n    },\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.marker,\n    Parameters.maxPageSize,\n    Parameters.snapshot,\n    Parameters.comp20,\n    Parameters.prevsnapshot,\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.range,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.prevSnapshotUrl,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst resizeOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.PageBlobResizeHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobResizeExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.comp, Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.encryptionKey,\n    Parameters.encryptionKeySha256,\n    Parameters.encryptionAlgorithm,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.encryptionScope,\n    Parameters.blobContentLength,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst updateSequenceNumberOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    200: {\n      headersMapper: Mappers.PageBlobUpdateSequenceNumberHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobUpdateSequenceNumberExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.comp, Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.leaseId,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.blobSequenceNumber,\n    Parameters.sequenceNumberAction,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\nconst copyIncrementalOperationSpec: coreClient.OperationSpec = {\n  path: \"/{containerName}/{blob}\",\n  httpMethod: \"PUT\",\n  responses: {\n    202: {\n      headersMapper: Mappers.PageBlobCopyIncrementalHeaders,\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.PageBlobCopyIncrementalExceptionHeaders,\n    },\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp21],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.ifModifiedSince,\n    Parameters.ifUnmodifiedSince,\n    Parameters.ifMatch,\n    Parameters.ifNoneMatch,\n    Parameters.ifTags,\n    Parameters.copySource,\n  ],\n  isXML: true,\n  serializer: xmlSerializer,\n};\n"]}