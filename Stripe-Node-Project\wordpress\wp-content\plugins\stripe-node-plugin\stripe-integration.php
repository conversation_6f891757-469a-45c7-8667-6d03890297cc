<?php
/**
 * Plugin Name: Stripe Integration with Commission Management
 * Plugin URI: https://your-website.com/stripe-integration
 * Description: A comprehensive Stripe payment integration with commission management for WordPress. Connects to a secure backend API for payment processing and vendor management.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://your-website.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: stripe-integration
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('STRIPE_INTEGRATION_VERSION', '1.0.0');
define('STRIPE_INTEGRATION_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STRIPE_INTEGRATION_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STRIPE_INTEGRATION_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Stripe Integration Plugin Class
 */
class StripeIntegrationPlugin {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Plugin options
     */
    private $options;
    
    /**
     * API client instance
     */
    private $api_client;
    
    /**
     * Get single instance of the plugin
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the plugin
     */
    private function init() {
        // Load plugin options
        $this->options = get_option('stripe_integration_options', array());
        
        // Register hooks
        add_action('init', array($this, 'load_textdomain'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_public_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_stripe_create_payment_intent', array($this, 'ajax_create_payment_intent'));
        add_action('wp_ajax_nopriv_stripe_create_payment_intent', array($this, 'ajax_create_payment_intent'));
        add_action('wp_ajax_stripe_confirm_payment', array($this, 'ajax_confirm_payment'));
        add_action('wp_ajax_nopriv_stripe_confirm_payment', array($this, 'ajax_confirm_payment'));
        add_action('wp_ajax_stripe_get_payment_form', array($this, 'ajax_get_payment_form'));
        add_action('wp_ajax_nopriv_stripe_get_payment_form', array($this, 'ajax_get_payment_form'));
        add_action('wp_ajax_stripe_integration_get_commission_rates', array($this, 'ajax_get_commission_rates'));
        add_action('wp_ajax_stripe_integration_clear_logs', array($this, 'ajax_clear_logs'));
        
        // Register shortcodes
        add_shortcode('stripe_checkout', array($this, 'stripe_checkout_shortcode'));
        add_shortcode('stripe_payment_form', array($this, 'stripe_payment_form_shortcode'));
        
        // Include required files
        $this->include_files();
        
        // Initialize API client
        $this->init_api_client();
    }
    
    /**
     * Include required files
     */
    private function include_files() {
        $required_files = array(
            'includes/class-api-client.php',
            'includes/class-payment-handler.php',
            'includes/class-admin-settings.php',
            'includes/class-security.php',
            'includes/functions.php'
        );

        foreach ($required_files as $file) {
            $file_path = STRIPE_INTEGRATION_PLUGIN_PATH . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            } else {
                error_log('Stripe Integration Plugin: Missing required file - ' . $file);
            }
        }

        // Load admin files only in admin
        if (is_admin()) {
            $admin_files = array(
                'admin/class-admin-dashboard.php',
                'admin/class-admin-transactions.php'
            );

            foreach ($admin_files as $file) {
                $file_path = STRIPE_INTEGRATION_PLUGIN_PATH . $file;
                if (file_exists($file_path)) {
                    require_once $file_path;
                } else {
                    error_log('Stripe Integration Plugin: Missing admin file - ' . $file);
                }
            }
        }
    }
    
    /**
     * Initialize API client
     */
    private function init_api_client() {
        if (class_exists('Stripe_Integration_API_Client')) {
            try {
                $this->api_client = new Stripe_Integration_API_Client($this->options);
            } catch (Exception $e) {
                error_log('Stripe Integration Plugin: Failed to initialize API client - ' . $e->getMessage());
                add_action('admin_notices', array($this, 'show_api_client_error'));
            }
        } else {
            error_log('Stripe Integration Plugin: API Client class not found');
            add_action('admin_notices', array($this, 'show_missing_class_error'));
        }
    }
    
    /**
     * Load plugin textdomain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain('stripe-integration', false, dirname(STRIPE_INTEGRATION_PLUGIN_BASENAME) . '/languages');
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Stripe Integration', 'stripe-integration'),
            __('Stripe Integration', 'stripe-integration'),
            'manage_options',
            'stripe-integration',
            array($this, 'admin_page'),
            'dashicons-money-alt',
            30
        );
        
        add_submenu_page(
            'stripe-integration',
            __('Settings', 'stripe-integration'),
            __('Settings', 'stripe-integration'),
            'manage_options',
            'stripe-integration-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'stripe-integration',
            __('Transactions', 'stripe-integration'),
            __('Transactions', 'stripe-integration'),
            'manage_options',
            'stripe-integration-transactions',
            array($this, 'transactions_page')
        );
    }
    
    /**
     * Initialize admin settings
     */
    public function admin_init() {
        register_setting('stripe_integration_options', 'stripe_integration_options', array($this, 'validate_options'));
        
        // Add settings sections and fields
        add_settings_section(
            'stripe_integration_api_section',
            __('API Configuration', 'stripe-integration'),
            array($this, 'api_section_callback'),
            'stripe-integration-settings'
        );
        
        add_settings_field(
            'api_base_url',
            __('API Base URL', 'stripe-integration'),
            array($this, 'api_base_url_callback'),
            'stripe-integration-settings',
            'stripe_integration_api_section'
        );
        
        add_settings_field(
            'api_key',
            __('API Key', 'stripe-integration'),
            array($this, 'api_key_callback'),
            'stripe-integration-settings',
            'stripe_integration_api_section'
        );
        
        add_settings_field(
            'vendor_id',
            __('Vendor ID', 'stripe-integration'),
            array($this, 'vendor_id_callback'),
            'stripe-integration-settings',
            'stripe_integration_api_section'
        );
        
        add_settings_field(
            'niche',
            __('Business Niche', 'stripe-integration'),
            array($this, 'niche_callback'),
            'stripe-integration-settings',
            'stripe_integration_api_section'
        );
        
        add_settings_field(
            'test_mode',
            __('Test Mode', 'stripe-integration'),
            array($this, 'test_mode_callback'),
            'stripe-integration-settings',
            'stripe_integration_api_section'
        );
    }
    
    /**
     * Enqueue public scripts and styles
     */
    public function enqueue_public_scripts() {
        wp_enqueue_script(
            'stripe-js',
            'https://js.stripe.com/v3/',
            array(),
            null,
            true
        );
        
        wp_enqueue_script(
            'stripe-integration-public',
            STRIPE_INTEGRATION_PLUGIN_URL . 'public/js/stripe-integration.js',
            array('jquery', 'stripe-js'),
            STRIPE_INTEGRATION_VERSION,
            true
        );
        
        wp_enqueue_style(
            'stripe-integration-public',
            STRIPE_INTEGRATION_PLUGIN_URL . 'public/css/stripe-integration.css',
            array(),
            STRIPE_INTEGRATION_VERSION
        );
        
        // Localize script with AJAX URL and nonce
        wp_localize_script('stripe-integration-public', 'stripe_integration_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('stripe_integration_nonce'),
            'test_mode' => !empty($this->options['test_mode']),
            'currency' => get_option('woocommerce_currency', 'USD')
        ));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'stripe-integration') === false) {
            return;
        }
        
        wp_enqueue_script(
            'stripe-integration-admin',
            STRIPE_INTEGRATION_PLUGIN_URL . 'admin/js/admin.js',
            array('jquery'),
            STRIPE_INTEGRATION_VERSION,
            true
        );
        
        wp_enqueue_style(
            'stripe-integration-admin',
            STRIPE_INTEGRATION_PLUGIN_URL . 'admin/css/admin.css',
            array(),
            STRIPE_INTEGRATION_VERSION
        );
        
        wp_localize_script('stripe-integration-admin', 'stripe_integration_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('stripe_integration_admin_nonce')
        ));
    }
    
    /**
     * Main admin page
     */
    public function admin_page() {
        include STRIPE_INTEGRATION_PLUGIN_PATH . 'admin/views/dashboard.php';
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        include STRIPE_INTEGRATION_PLUGIN_PATH . 'admin/views/settings.php';
    }
    
    /**
     * Transactions page
     */
    public function transactions_page() {
        include STRIPE_INTEGRATION_PLUGIN_PATH . 'admin/views/transactions.php';
    }
    
    /**
     * Get plugin options
     */
    public function get_options() {
        return $this->options;
    }
    
    /**
     * Get API client
     */
    public function get_api_client() {
        return $this->api_client;
    }

    /**
     * Get payment handler
     */
    public function get_payment_handler() {
        return $this->payment_handler;
    }

    /**
     * AJAX handler for creating payment intent
     */
    public function ajax_create_payment_intent() {
        // Verify nonce
        if (!Stripe_Integration_Security::verify_nonce($_POST['nonce'], 'stripe_integration_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }

        // Rate limiting
        $client_ip = Stripe_Integration_Security::get_client_ip();
        if (!Stripe_Integration_Security::check_rate_limit($client_ip, 10, 300)) {
            Stripe_Integration_Security::log_security_event('rate_limit_exceeded', array(
                'ip' => $client_ip,
                'endpoint' => 'create_payment_intent'
            ));
            wp_send_json_error('Too many requests. Please try again later.');
            return;
        }

        // Sanitize and validate input
        $payment_data = array(
            'amount' => floatval($_POST['amount']),
            'order_id' => sanitize_text_field($_POST['order_id']),
            'customer_email' => sanitize_email($_POST['customer_email']),
            'customer_name' => sanitize_text_field($_POST['customer_name'])
        );

        $payment_data = Stripe_Integration_Security::sanitize_input($payment_data);

        // Validate payment data
        $validation_errors = Stripe_Integration_Security::validate_payment_data($payment_data);
        if (!empty($validation_errors)) {
            wp_send_json_error('Validation failed: ' . implode(', ', $validation_errors));
            return;
        }

        // Check for suspicious activity
        $suspicious_patterns = Stripe_Integration_Security::check_suspicious_activity($payment_data);
        if (!empty($suspicious_patterns)) {
            wp_send_json_error('Request blocked for security reasons');
            return;
        }

        $payment_handler = new Stripe_Integration_Payment_Handler($this->api_client);
        $result = $payment_handler->create_payment_intent(
            $payment_data['amount'],
            $payment_data['order_id'],
            $payment_data['customer_email'],
            $payment_data['customer_name']
        );

        if ($result['success']) {
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for confirming payment
     */
    public function ajax_confirm_payment() {
        check_ajax_referer('stripe_integration_nonce', 'nonce');

        $payment_intent_id = sanitize_text_field($_POST['payment_intent_id']);

        if (empty($payment_intent_id)) {
            wp_send_json_error('Missing payment intent ID');
            return;
        }

        $payment_handler = new Stripe_Integration_Payment_Handler($this->api_client);
        $result = $payment_handler->confirm_payment($payment_intent_id);

        if ($result['success']) {
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Stripe checkout shortcode
     */
    public function stripe_checkout_shortcode($atts) {
        $atts = shortcode_atts(array(
            'amount' => '',
            'description' => '',
            'button_text' => __('Pay Now', 'stripe-integration'),
            'success_url' => '',
            'cancel_url' => ''
        ), $atts);

        ob_start();
        include STRIPE_INTEGRATION_PLUGIN_PATH . 'public/views/checkout-button.php';
        return ob_get_clean();
    }

    /**
     * Stripe payment form shortcode
     */
    public function stripe_payment_form_shortcode($atts) {
        $atts = shortcode_atts(array(
            'amount' => '',
            'description' => '',
            'show_amount_field' => 'false'
        ), $atts);

        ob_start();
        include STRIPE_INTEGRATION_PLUGIN_PATH . 'public/views/payment-form.php';
        return ob_get_clean();
    }

    /**
     * Settings field callbacks
     */
    public function api_section_callback() {
        echo '<p>' . __('Configure your API connection settings.', 'stripe-integration') . '</p>';
    }

    public function api_base_url_callback() {
        $value = isset($this->options['api_base_url']) ? $this->options['api_base_url'] : '';
        echo '<input type="url" name="stripe_integration_options[api_base_url]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('The base URL of your backend API (e.g., https://api.yoursite.com)', 'stripe-integration') . '</p>';
    }

    public function api_key_callback() {
        $value = isset($this->options['api_key']) ? $this->options['api_key'] : '';
        echo '<input type="password" name="stripe_integration_options[api_key]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Your API key for authentication with the backend', 'stripe-integration') . '</p>';
    }

    public function vendor_id_callback() {
        $value = isset($this->options['vendor_id']) ? $this->options['vendor_id'] : '';
        echo '<input type="text" name="stripe_integration_options[vendor_id]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Your vendor ID in the system', 'stripe-integration') . '</p>';
    }

    public function niche_callback() {
        $value = isset($this->options['niche']) ? $this->options['niche'] : 'other';
        $niches = array(
            'grocery' => __('Grocery', 'stripe-integration'),
            'catering' => __('Catering', 'stripe-integration'),
            'restaurant' => __('Restaurant', 'stripe-integration'),
            'retail' => __('Retail', 'stripe-integration'),
            'other' => __('Other', 'stripe-integration')
        );

        echo '<select name="stripe_integration_options[niche]">';
        foreach ($niches as $key => $label) {
            echo '<option value="' . esc_attr($key) . '"' . selected($value, $key, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
        echo '<p class="description">' . __('Your business niche for commission calculation', 'stripe-integration') . '</p>';
    }

    public function test_mode_callback() {
        $value = isset($this->options['test_mode']) ? $this->options['test_mode'] : false;
        echo '<input type="checkbox" name="stripe_integration_options[test_mode]" value="1"' . checked($value, true, false) . ' />';
        echo '<label>' . __('Enable test mode', 'stripe-integration') . '</label>';
        echo '<p class="description">' . __('Use Stripe test mode for development and testing', 'stripe-integration') . '</p>';
    }

    /**
     * Validate plugin options
     */
    public function validate_options($input) {
        $output = array();

        if (isset($input['api_base_url'])) {
            $output['api_base_url'] = esc_url_raw($input['api_base_url']);
        }

        if (isset($input['api_key'])) {
            $output['api_key'] = sanitize_text_field($input['api_key']);
        }

        if (isset($input['vendor_id'])) {
            $output['vendor_id'] = sanitize_text_field($input['vendor_id']);
        }

        if (isset($input['niche'])) {
            $allowed_niches = array('grocery', 'catering', 'restaurant', 'retail', 'other');
            if (in_array($input['niche'], $allowed_niches)) {
                $output['niche'] = $input['niche'];
            }
        }

        if (isset($input['test_mode'])) {
            $output['test_mode'] = (bool) $input['test_mode'];
        }

        return $output;
    }

    /**
     * AJAX handler for getting payment form HTML
     */
    public function ajax_get_payment_form() {
        check_ajax_referer('stripe_integration_nonce', 'nonce');

        $amount = sanitize_text_field($_POST['amount'] ?? '');
        $description = sanitize_text_field($_POST['description'] ?? '');

        // Create shortcode attributes
        $atts = array(
            'amount' => $amount,
            'description' => $description
        );

        // Generate payment form HTML
        ob_start();
        include STRIPE_INTEGRATION_PATH . 'public/views/payment-form.php';
        $html = ob_get_clean();

        wp_send_json_success($html);
    }

    /**
     * AJAX handler for getting commission rates
     */
    public function ajax_get_commission_rates() {
        check_ajax_referer('stripe_integration_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $result = $this->api_client->get_commission_rates();

        if ($result['success']) {
            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * AJAX handler for clearing payment logs
     */
    public function ajax_clear_logs() {
        check_ajax_referer('stripe_integration_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $this->payment_handler->clear_payment_logs();

        wp_send_json_success('Payment logs cleared successfully');
    }

    /**
     * Show API client error notice
     */
    public function show_api_client_error() {
        echo '<div class="notice notice-error"><p><strong>Stripe Integration:</strong> Failed to initialize API client. Please check your plugin configuration.</p></div>';
    }

    /**
     * Show missing class error notice
     */
    public function show_missing_class_error() {
        echo '<div class="notice notice-error"><p><strong>Stripe Integration:</strong> Required plugin files are missing. Please reinstall the plugin.</p></div>';
    }
}

// Initialize the plugin
add_action('plugins_loaded', function() {
    StripeIntegrationPlugin::get_instance();
});

// Activation hook
register_activation_hook(__FILE__, function() {
    // Create default options
    $default_options = array(
        'api_base_url' => '',
        'api_key' => '',
        'vendor_id' => '',
        'niche' => 'other',
        'test_mode' => true
    );

    add_option('stripe_integration_options', $default_options);
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    // Clean up if needed
});

// Uninstall hook
register_uninstall_hook(__FILE__, function() {
    // Remove options
    delete_option('stripe_integration_options');
});
