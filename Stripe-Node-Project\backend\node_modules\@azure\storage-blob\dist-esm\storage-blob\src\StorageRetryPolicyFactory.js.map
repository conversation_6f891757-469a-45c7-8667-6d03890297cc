{"version": 3, "file": "StorageRetryPolicyFactory.js", "sourceRoot": "", "sources": ["../../../src/StorageRetryPolicyFactory.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,MAAM,+BAA+B,CAAC;AAE3F,OAAO,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,CAAC;AAmDtD;;GAEG;AACH,MAAM,OAAO,yBAAyB;IAGpC;;;OAGG;IACH,YAAY,YAAkC;QAC5C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;IACnC,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,UAAyB,EAAE,OAA6B;QACpE,OAAO,IAAI,kBAAkB,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACxE,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n  RequestPolicyFactory,\n} from \"@azure/core-http-compat\";\nimport { StorageRetryPolicy, StorageRetryPolicyType } from \"./policies/StorageRetryPolicy\";\n\nexport { StorageRetryPolicyType, StorageRetryPolicy };\n\n/**\n * Storage Blob retry options interface.\n */\nexport interface StorageRetryOptions {\n  /**\n   * Optional. StorageRetryPolicyType, default is exponential retry policy.\n   */\n  readonly retryPolicyType?: StorageRetryPolicyType;\n\n  /**\n   * Optional. Max try number of attempts, default is 4.\n   * A value of 1 means 1 try and no retries.\n   * A value smaller than 1 means default retry number of attempts.\n   */\n  readonly maxTries?: number;\n\n  /**\n   * Optional. Indicates the maximum time in ms allowed for any single try of an HTTP request.\n   * A value of zero or undefined means no default timeout on SDK client, Azure\n   * Storage server's default timeout policy will be used.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-blob-service-operations\n   */\n  readonly tryTimeoutInMs?: number;\n\n  /**\n   * Optional. Specifies the amount of delay to use before retrying an operation (default is 4s or 4 * 1000ms).\n   * The delay increases (exponentially or linearly) with each retry up to a maximum specified by\n   * maxRetryDelayInMs. If you specify 0, then you must also specify 0 for maxRetryDelayInMs.\n   */\n  readonly retryDelayInMs?: number;\n\n  /**\n   * Optional. Specifies the maximum delay allowed before retrying an operation (default is 120s or 120 * 1000ms).\n   * If you specify 0, then you must also specify 0 for retryDelayInMs.\n   */\n  readonly maxRetryDelayInMs?: number;\n\n  /**\n   * If a secondaryHost is specified, retries will be tried against this host. If secondaryHost is undefined\n   * (the default) then operations are not retried against another host.\n   *\n   * NOTE: Before setting this field, make sure you understand the issues around\n   * reading stale and potentially-inconsistent data at\n   * {@link https://learn.microsoft.com/en-us/azure/storage/common/storage-designing-ha-apps-with-ragrs}\n   */\n  readonly secondaryHost?: string;\n}\n\n/**\n * StorageRetryPolicyFactory is a factory class helping generating {@link StorageRetryPolicy} objects.\n */\nexport class StorageRetryPolicyFactory implements RequestPolicyFactory {\n  private retryOptions?: StorageRetryOptions;\n\n  /**\n   * Creates an instance of StorageRetryPolicyFactory.\n   * @param retryOptions -\n   */\n  constructor(retryOptions?: StorageRetryOptions) {\n    this.retryOptions = retryOptions;\n  }\n\n  /**\n   * Creates a StorageRetryPolicy object.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  public create(nextPolicy: RequestPolicy, options: RequestPolicyOptions): StorageRetryPolicy {\n    return new StorageRetryPolicy(nextPolicy, options, this.retryOptions);\n  }\n}\n"]}