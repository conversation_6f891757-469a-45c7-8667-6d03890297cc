{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../../../../src/utils/constants.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,MAAM,CAAC,MAAM,WAAW,GAAW,SAAS,CAAC;AAC7C,MAAM,CAAC,MAAM,eAAe,GAAW,YAAY,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAsB,oCAAoC,CAAC;AAE1F,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,UAAU,EAAE;QACV,sBAAsB,EAAE,GAAG;QAC3B,SAAS,EAAE,KAAK;QAChB,OAAO,EAAE,SAAS;KACnB;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC/B,aAAa,EAAE,GAAG;IAClB,cAAc,EAAE,GAAG;IACnB,kBAAkB,EAAE,GAAG;IACvB,0BAA0B,EAAE,GAAG;CAChC,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAAG;IAC7B,aAAa,EAAE,eAAe;IAC9B,oBAAoB,EAAE,QAAQ;IAC9B,gBAAgB,EAAE,kBAAkB;IACpC,gBAAgB,EAAE,kBAAkB;IACpC,cAAc,EAAE,gBAAgB;IAChC,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,cAAc;IAC5B,MAAM,EAAE,QAAQ;IAChB,IAAI,EAAE,MAAM;IACZ,QAAQ,EAAE,UAAU;IACpB,iBAAiB,EAAE,mBAAmB;IACtC,aAAa,EAAE,eAAe;IAC9B,mBAAmB,EAAE,qBAAqB;IAC1C,kBAAkB,EAAE,OAAO;IAC3B,KAAK,EAAE,OAAO;IACd,UAAU,EAAE,YAAY;IACxB,sBAAsB,EAAE,wBAAwB;IAChD,gBAAgB,EAAE,kBAAkB;IACpC,SAAS,EAAE,WAAW;CACvB,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC;AAC3B,MAAM,CAAC,MAAM,OAAO,GAAG,GAAG,CAAC;AAE3B,MAAM,CAAC,MAAM,2BAA2B,GAAG;;yDAEc,CAAC;AAE1D,MAAM,CAAC,MAAM,qCAAqC,GAAG;IACnD,6BAA6B;IAC7B,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,MAAM;IACN,YAAY;IACZ,aAAa;IACb,mBAAmB;IACnB,YAAY;IACZ,wBAAwB;IACxB,WAAW;IACX,iBAAiB;IACjB,iBAAiB;IACjB,+BAA+B;IAC/B,cAAc;IACd,iCAAiC;IACjC,iBAAiB;IACjB,wBAAwB;CACzB,CAAC;AAEF,MAAM,CAAC,MAAM,yCAAyC,GAAG;IACvD,MAAM;IACN,YAAY;IACZ,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,eAAe;IACf,UAAU;IACV,YAAY;IACZ,mBAAmB;CACpB,CAAC;AAEF,iDAAiD;AACjD,wGAAwG;AACxG,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;CACR,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport const SDK_VERSION: string = \"12.26.0\";\nexport const SERVICE_VERSION: string = \"2025-05-05\";\n\n/**\n * The OAuth scope to use with Azure Storage.\n */\nexport const StorageOAuthScopes: string | string[] = \"https://storage.azure.com/.default\";\n\nexport const URLConstants = {\n  Parameters: {\n    FORCE_BROWSER_NO_CACHE: \"_\",\n    SIGNATURE: \"sig\",\n    TIMEOUT: \"timeout\",\n  },\n};\n\nexport const HttpUrlConnection = {\n  HTTP_CONFLICT: 409,\n  HTTP_NOT_FOUND: 404,\n  HTTP_PRECON_FAILED: 412,\n  HTTP_RANGE_NOT_SATISFIABLE: 416,\n};\n\nexport const HeaderConstants = {\n  AUTHORIZATION: \"authorization\",\n  AUTHORIZATION_SCHEME: \"Bearer\",\n  CONTENT_ENCODING: \"content-encoding\",\n  CONTENT_LANGUAGE: \"content-language\",\n  CONTENT_LENGTH: \"content-length\",\n  CONTENT_MD5: \"content-md5\",\n  CONTENT_TYPE: \"content-type\",\n  COOKIE: \"Cookie\",\n  DATE: \"date\",\n  IF_MATCH: \"if-match\",\n  IF_MODIFIED_SINCE: \"if-modified-since\",\n  IF_NONE_MATCH: \"if-none-match\",\n  IF_UNMODIFIED_SINCE: \"if-unmodified-since\",\n  PREFIX_FOR_STORAGE: \"x-ms-\",\n  RANGE: \"Range\",\n  USER_AGENT: \"User-Agent\",\n  X_MS_CLIENT_REQUEST_ID: \"x-ms-client-request-id\",\n  X_MS_COPY_SOURCE: \"x-ms-copy-source\",\n  X_MS_DATE: \"x-ms-date\",\n};\n\nexport const ETagNone = \"\";\nexport const ETagAny = \"*\";\n\nexport const DevelopmentConnectionString = `DefaultEndpointsProtocol=http;AccountName=devstoreaccount1;\n  AccountKey=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==;\n  QueueEndpoint=http://127.0.0.1:10001/devstoreaccount1;`;\n\nexport const StorageQueueLoggingAllowedHeaderNames = [\n  \"Access-Control-Allow-Origin\",\n  \"Cache-Control\",\n  \"Content-Length\",\n  \"Content-Type\",\n  \"Date\",\n  \"Request-Id\",\n  \"traceparent\",\n  \"Transfer-Encoding\",\n  \"User-Agent\",\n  \"x-ms-client-request-id\",\n  \"x-ms-date\",\n  \"x-ms-error-code\",\n  \"x-ms-request-id\",\n  \"x-ms-return-client-request-id\",\n  \"x-ms-version\",\n  \"x-ms-approximate-messages-count\",\n  \"x-ms-popreceipt\",\n  \"x-ms-time-next-visible\",\n];\n\nexport const StorageQueueLoggingAllowedQueryParameters = [\n  \"comp\",\n  \"maxresults\",\n  \"rscc\",\n  \"rscd\",\n  \"rsce\",\n  \"rscl\",\n  \"rsct\",\n  \"se\",\n  \"si\",\n  \"sip\",\n  \"sp\",\n  \"spr\",\n  \"sr\",\n  \"srt\",\n  \"ss\",\n  \"st\",\n  \"sv\",\n  \"include\",\n  \"marker\",\n  \"prefix\",\n  \"messagettl\",\n  \"numofmessages\",\n  \"peekonly\",\n  \"popreceipt\",\n  \"visibilitytimeout\",\n];\n\n/// List of ports used for path style addressing.\n/// Path style addressing means that storage account is put in URI's Path segment in instead of in host.\nexport const PathStylePorts = [\n  \"10000\",\n  \"10001\",\n  \"10002\",\n  \"10003\",\n  \"10004\",\n  \"10100\",\n  \"10101\",\n  \"10102\",\n  \"10103\",\n  \"10104\",\n  \"11000\",\n  \"11001\",\n  \"11002\",\n  \"11003\",\n  \"11004\",\n  \"11100\",\n  \"11101\",\n  \"11102\",\n  \"11103\",\n  \"11104\",\n];\n"]}