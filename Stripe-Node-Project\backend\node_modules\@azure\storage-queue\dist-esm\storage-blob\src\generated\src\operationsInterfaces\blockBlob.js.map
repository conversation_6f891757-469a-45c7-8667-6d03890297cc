{"version": 3, "file": "blockBlob.js", "sourceRoot": "", "sources": ["../../../../../../../storage-blob/src/generated/src/operationsInterfaces/blockBlob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreRestPipeline from \"@azure/core-rest-pipeline\";\nimport {\n  BlockBlobUploadOptionalParams,\n  BlockBlobUploadResponse,\n  BlockBlobPutBlobFromUrlOptionalParams,\n  BlockBlobPutBlobFromUrlResponse,\n  BlockBlobStageBlockOptionalParams,\n  BlockBlobStageBlockResponse,\n  BlockBlobStageBlockFromURLOptionalParams,\n  BlockBlobStageBlockFromURLResponse,\n  BlockLookupList,\n  BlockBlobCommitBlockListOptionalParams,\n  BlockBlobCommitBlockListResponse,\n  BlockListType,\n  BlockBlobGetBlockListOptionalParams,\n  BlockBlobGetBlockListResponse,\n} from \"../models\";\n\n/** Interface representing a BlockBlob. */\nexport interface BlockBlob {\n  /**\n   * The Upload Block Blob operation updates the content of an existing block blob. Updating an existing\n   * block blob overwrites any existing metadata on the blob. Partial updates are not supported with Put\n   * Blob; the content of the existing blob is overwritten with the content of the new blob. To perform a\n   * partial update of the content of a block blob, use the Put Block List operation.\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  upload(\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: BlockBlobUploadOptionalParams,\n  ): Promise<BlockBlobUploadResponse>;\n  /**\n   * The Put Blob from URL operation creates a new Block Blob where the contents of the blob are read\n   * from a given URL.  This API is supported beginning with the 2020-04-08 version. Partial updates are\n   * not supported with Put Blob from URL; the content of an existing blob is overwritten with the\n   * content of the new blob.  To perform partial updates to a block blob’s contents using a source URL,\n   * use the Put Block from URL API in conjunction with Put Block List.\n   * @param contentLength The length of the request.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  putBlobFromUrl(\n    contentLength: number,\n    copySource: string,\n    options?: BlockBlobPutBlobFromUrlOptionalParams,\n  ): Promise<BlockBlobPutBlobFromUrlResponse>;\n  /**\n   * The Stage Block operation creates a new block to be committed as part of a blob\n   * @param blockId A valid Base64 string value that identifies the block. Prior to encoding, the string\n   *                must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified\n   *                for the blockid parameter must be the same size for each block.\n   * @param contentLength The length of the request.\n   * @param body Initial data\n   * @param options The options parameters.\n   */\n  stageBlock(\n    blockId: string,\n    contentLength: number,\n    body: coreRestPipeline.RequestBodyType,\n    options?: BlockBlobStageBlockOptionalParams,\n  ): Promise<BlockBlobStageBlockResponse>;\n  /**\n   * The Stage Block operation creates a new block to be committed as part of a blob where the contents\n   * are read from a URL.\n   * @param blockId A valid Base64 string value that identifies the block. Prior to encoding, the string\n   *                must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified\n   *                for the blockid parameter must be the same size for each block.\n   * @param contentLength The length of the request.\n   * @param sourceUrl Specify a URL to the copy source.\n   * @param options The options parameters.\n   */\n  stageBlockFromURL(\n    blockId: string,\n    contentLength: number,\n    sourceUrl: string,\n    options?: BlockBlobStageBlockFromURLOptionalParams,\n  ): Promise<BlockBlobStageBlockFromURLResponse>;\n  /**\n   * The Commit Block List operation writes a blob by specifying the list of block IDs that make up the\n   * blob. In order to be written as part of a blob, a block must have been successfully written to the\n   * server in a prior Put Block operation. You can call Put Block List to update a blob by uploading\n   * only those blocks that have changed, then committing the new and existing blocks together. You can\n   * do this by specifying whether to commit a block from the committed block list or from the\n   * uncommitted block list, or to commit the most recently uploaded version of the block, whichever list\n   * it may belong to.\n   * @param blocks Blob Blocks.\n   * @param options The options parameters.\n   */\n  commitBlockList(\n    blocks: BlockLookupList,\n    options?: BlockBlobCommitBlockListOptionalParams,\n  ): Promise<BlockBlobCommitBlockListResponse>;\n  /**\n   * The Get Block List operation retrieves the list of blocks that have been uploaded as part of a block\n   * blob\n   * @param listType Specifies whether to return the list of committed blocks, the list of uncommitted\n   *                 blocks, or both lists together.\n   * @param options The options parameters.\n   */\n  getBlockList(\n    listType: BlockListType,\n    options?: BlockBlobGetBlockListOptionalParams,\n  ): Promise<BlockBlobGetBlockListResponse>;\n}\n"]}