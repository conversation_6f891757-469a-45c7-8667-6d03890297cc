const Joi = require('joi');
const validator = require('validator');

/**
 * Validation middleware factory
 * @param {Object} schema - Joi validation schema
 * @param {string} property - Request property to validate ('body', 'query', 'params')
 * @returns {Function} - Express middleware function
 */
function validate(schema, property = 'body') {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));
      
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Request data is invalid',
        details: errorDetails
      });
    }
    
    // Replace request property with validated and sanitized value
    req[property] = value;
    next();
  };
}

// Common validation schemas
const schemas = {
  // Payment validation schemas
  createPaymentIntent: Joi.object({
    amount: Joi.number().positive().max(999999.99).precision(2).required()
      .messages({
        'number.positive': 'Amount must be a positive number',
        'number.max': 'Amount cannot exceed $999,999.99',
        'number.precision': 'Amount cannot have more than 2 decimal places',
        'any.required': 'Amount is required'
      }),
    currency: Joi.string().length(3).uppercase().default('USD')
      .messages({
        'string.length': 'Currency must be a 3-character ISO code',
        'string.uppercase': 'Currency code must be uppercase'
      }),
    customer_email: Joi.string().email({ tlds: { allow: true } }).max(254).required()
      .custom((value, helpers) => {
        if (!validator.isEmail(value)) {
          return helpers.error('string.email');
        }
        return value;
      })
      .messages({
        'string.email': 'Please provide a valid email address',
        'string.max': 'Email address is too long',
        'any.required': 'Customer email is required'
      }),
    customer_name: Joi.string().min(2).max(100).pattern(/^[a-zA-Z\s'-]+$/).required()
      .messages({
        'string.min': 'Customer name must be at least 2 characters',
        'string.max': 'Customer name cannot exceed 100 characters',
        'string.pattern.base': 'Customer name must contain only letters, spaces, hyphens, and apostrophes',
        'any.required': 'Customer name is required'
      }),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required()
      .messages({
        'any.only': 'Niche must be one of: grocery, catering, restaurant, retail, other',
        'any.required': 'Niche is required'
      }),
    vendor_id: Joi.string().uuid({ version: 'uuidv4' }).required()
      .messages({
        'string.guid': 'Vendor ID must be a valid UUID',
        'any.required': 'Vendor ID is required'
      }),
    order_id: Joi.string().alphanum().min(1).max(100).required()
      .messages({
        'string.alphanum': 'Order ID must contain only alphanumeric characters',
        'string.min': 'Order ID cannot be empty',
        'string.max': 'Order ID cannot exceed 100 characters',
        'any.required': 'Order ID is required'
      }),
    metadata: Joi.object().max(20).default({})
      .messages({
        'object.max': 'Metadata cannot have more than 20 properties'
      })
  }),

  // Process payment validation schema (includes card details)
  processPayment: Joi.object({
    amount: Joi.number().positive().max(999999.99).precision(2).required()
      .messages({
        'number.positive': 'Amount must be a positive number',
        'number.max': 'Amount cannot exceed $999,999.99',
        'number.precision': 'Amount cannot have more than 2 decimal places',
        'any.required': 'Amount is required'
      }),
    currency: Joi.string().length(3).uppercase().default('USD')
      .messages({
        'string.length': 'Currency must be a 3-letter ISO code',
        'string.uppercase': 'Currency must be uppercase'
      }),
    description: Joi.string().min(1).max(500).required()
      .messages({
        'string.min': 'Description cannot be empty',
        'string.max': 'Description cannot exceed 500 characters',
        'any.required': 'Description is required'
      }),
    customer_email: Joi.string().email().max(255).required()
      .messages({
        'string.email': 'Please provide a valid email address',
        'string.max': 'Email address is too long',
        'any.required': 'Customer email is required'
      }),
    customer_name: Joi.string().min(2).max(100).pattern(/^[a-zA-Z\s'-]+$/).required()
      .messages({
        'string.min': 'Customer name must be at least 2 characters',
        'string.max': 'Customer name cannot exceed 100 characters',
        'string.pattern.base': 'Customer name must contain only letters, spaces, hyphens, and apostrophes',
        'any.required': 'Customer name is required'
      }),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required()
      .messages({
        'any.only': 'Niche must be one of: grocery, catering, restaurant, retail, other',
        'any.required': 'Niche is required'
      }),
    vendor_id: Joi.string().uuid({ version: 'uuidv4' }).required()
      .messages({
        'string.guid': 'Vendor ID must be a valid UUID',
        'any.required': 'Vendor ID is required'
      }),
    order_id: Joi.string().alphanum().min(1).max(100).required()
      .messages({
        'string.alphanum': 'Order ID must contain only alphanumeric characters',
        'string.min': 'Order ID cannot be empty',
        'string.max': 'Order ID cannot exceed 100 characters',
        'any.required': 'Order ID is required'
      }),
    card: Joi.object({
      number: Joi.string().creditCard().required()
        .messages({
          'string.creditCard': 'Please provide a valid credit card number',
          'any.required': 'Card number is required'
        }),
      exp_month: Joi.number().integer().min(1).max(12).required()
        .messages({
          'number.min': 'Expiry month must be between 1 and 12',
          'number.max': 'Expiry month must be between 1 and 12',
          'any.required': 'Expiry month is required'
        }),
      exp_year: Joi.number().integer().min(new Date().getFullYear()).max(new Date().getFullYear() + 20).required()
        .messages({
          'number.min': 'Expiry year cannot be in the past',
          'number.max': 'Expiry year is too far in the future',
          'any.required': 'Expiry year is required'
        }),
      cvc: Joi.string().pattern(/^\d{3,4}$/).required()
        .messages({
          'string.pattern.base': 'CVC must be 3 or 4 digits',
          'any.required': 'CVC is required'
        })
    }).required()
      .messages({
        'any.required': 'Card details are required'
      }),
    metadata: Joi.object().max(20).default({})
      .messages({
        'object.max': 'Metadata cannot have more than 20 properties'
      })
  }),

  // Commission validation schemas
  createCommissionRate: Joi.object({
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required(),
    rate: Joi.number().min(0.001).max(0.5).required()
      .messages({
        'number.min': 'Commission rate must be at least 0.1%',
        'number.max': 'Commission rate cannot exceed 50%',
        'any.required': 'Commission rate is required'
      }),
    min_amount: Joi.number().min(0).default(0),
    max_amount: Joi.number().min(0).allow(null).default(null),
    is_active: Joi.boolean().default(true)
  }),
  
  updateCommissionRate: Joi.object({
    rate: Joi.number().min(0.001).max(0.5),
    min_amount: Joi.number().min(0),
    max_amount: Joi.number().min(0).allow(null),
    is_active: Joi.boolean()
  }).min(1),
  
  // Vendor validation schemas
  createVendor: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    email: Joi.string().email().required(),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other').required(),
    country: Joi.string().length(2).default('US'),
    business_type: Joi.string().valid('individual', 'company').default('individual'),
    metadata: Joi.object().default({})
  }),
  
  updateVendor: Joi.object({
    name: Joi.string().min(2).max(100),
    email: Joi.string().email(),
    niche: Joi.string().valid('grocery', 'catering', 'restaurant', 'retail', 'other'),
    country: Joi.string().length(2),
    business_type: Joi.string().valid('individual', 'company'),
    is_active: Joi.boolean(),
    metadata: Joi.object()
  }).min(1),
  
  // API key validation schemas
  createApiKey: Joi.object({
    wordpress_site: Joi.string().uri().required()
      .messages({
        'string.uri': 'WordPress site must be a valid URL',
        'any.required': 'WordPress site URL is required'
      }),
    permissions: Joi.array().items(
      Joi.string().valid('payments', 'commissions', 'vendors', 'admin')
    ).default(['payments', 'commissions']),
    description: Joi.string().max(255).default('')
  }),
  
  // Webhook validation schemas
  stripeWebhook: Joi.object({
    id: Joi.string().required(),
    object: Joi.string().valid('event').required(),
    type: Joi.string().required(),
    data: Joi.object().required(),
    created: Joi.number().required(),
    livemode: Joi.boolean().required()
  })
};

/**
 * Sanitize input to prevent XSS and injection attacks
 * @param {any} input - Input to sanitize
 * @returns {any} - Sanitized input
 */
function sanitizeInput(input) {
  if (typeof input === 'string') {
    // Remove potentially dangerous characters
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object' && input !== null) {
    const sanitized = {};
    for (const [key, value] of Object.entries(input)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return input;
}

/**
 * Middleware to sanitize request data
 */
function sanitizeMiddleware(req, res, next) {
  if (req.body) {
    req.body = sanitizeInput(req.body);
  }
  if (req.query) {
    req.query = sanitizeInput(req.query);
  }
  if (req.params) {
    req.params = sanitizeInput(req.params);
  }
  next();
}

module.exports = {
  validate,
  schemas,
  sanitizeInput,
  sanitizeMiddleware
};
