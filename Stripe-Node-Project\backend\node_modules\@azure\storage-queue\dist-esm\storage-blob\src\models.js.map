{"version": 3, "file": "models.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/models.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAalC,OAAO,EAAE,wBAAwB,EAAE,MAAM,mBAAmB,CAAC;AAyF7D;;;GAGG;AACH,MAAM,CAAN,IAAY,aAkBX;AAlBD,WAAY,aAAa;IACvB;;OAEG;IACH,4BAAW,CAAA;IACX;;OAEG;IACH,8BAAa,CAAA;IACb;;OAEG;IACH,8BAAa,CAAA;IACb;;;OAGG;IACH,oCAAmB,CAAA;AACrB,CAAC,EAlBW,aAAa,KAAb,aAAa,QAkBxB;AAED;;;;GAIG;AACH,MAAM,CAAN,IAAY,mBA6CX;AA7CD,WAAY,mBAAmB;IAC7B;;OAEG;IACH,gCAAS,CAAA;IACT;;OAEG;IACH,gCAAS,CAAA;IACT;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;IACX;;OAEG;IACH,kCAAW,CAAA;AACb,CAAC,EA7CW,mBAAmB,KAAnB,mBAAmB,QA6C9B;AAED,MAAM,UAAU,YAAY,CAC1B,IAA8D;IAE9D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,IAAkB,CAAC,CAAC,qGAAqG;AAClI,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,GAAwB,EAAE,OAAgB;IAC7E,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACpB,MAAM,IAAI,UAAU,CAAC,2DAA2D,CAAC,CAAC;IACpF,CAAC;IAED,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;QACpC,GAAG,CAAC,mBAAmB,GAAG,wBAAwB,CAAC;IACrD,CAAC;AACH,CAAC;AA2HD;;GAEG;AACH,MAAM,CAAN,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC7B;;OAEG;IACH,gFAAyD,CAAA;IACzD;;OAEG;IACH,yFAAkE,CAAA;AACpE,CAAC,EATW,mBAAmB,KAAnB,mBAAmB,QAS9B;AAED;;;GAGG;AACH,MAAM,UAAU,6BAA6B,CAAC,kBAA0B;IACtE,OAAO,WAAW,kBAAkB,iCAAiC,CAAC;AACxE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { CancelOnProgress, PollOperationState } from \"@azure/core-lro\";\nimport type { BlobImmutabilityPolicyMode } from \"./generatedModels\";\nimport type {\n  LeaseAccessConditions,\n  SequenceNumberAccessConditions,\n  AppendPositionAccessConditions,\n  AccessTier,\n  CpkInfo,\n  BlobDownloadResponseModel,\n} from \"./generatedModels\";\nimport { EncryptionAlgorithmAES25 } from \"./utils/constants\";\n\n/**\n * Blob tags.\n */\nexport type Tags = Record<string, string>;\n\n/**\n * A map of name-value pairs to associate with the resource.\n */\nexport interface Metadata {\n  /**\n   * A name-value pair.\n   */\n  [propertyName: string]: string;\n}\n\n/**\n * standard HTTP conditional headers and tags condition.\n */\nexport interface ModifiedAccessConditions\n  extends MatchConditions,\n    ModificationConditions,\n    TagConditions {}\n\n/**\n * standard HTTP conditional headers, tags condition and lease condition\n */\nexport interface BlobRequestConditions extends ModifiedAccessConditions, LeaseAccessConditions {}\n\n/**\n * Conditions to add to the creation of this page blob.\n */\nexport interface PageBlobRequestConditions\n  extends BlobRequestConditions,\n    SequenceNumberAccessConditions {}\n\n/**\n * Conditions to add to the creation of this append blob.\n */\nexport interface AppendBlobRequestConditions\n  extends BlobRequestConditions,\n    AppendPositionAccessConditions {}\n\n/**\n * Specifies HTTP options for conditional requests based on modification time.\n */\nexport interface ModificationConditions {\n  /**\n   * Specify this header value to operate only on a blob if it has been modified since the\n   * specified date/time.\n   */\n  ifModifiedSince?: Date;\n  /**\n   * Specify this header value to operate only on a blob if it has not been modified since the\n   * specified date/time.\n   */\n  ifUnmodifiedSince?: Date;\n}\n\n/**\n * Specifies HTTP options for conditional requests based on ETag matching.\n */\nexport interface MatchConditions {\n  /**\n   * Specify an ETag value to operate only on blobs with a matching value.\n   */\n  ifMatch?: string;\n  /**\n   * Specify an ETag value to operate only on blobs without a matching value.\n   */\n  ifNoneMatch?: string;\n}\n\n/**\n * Specifies HTTP options for conditional requests based on blob tags.\n */\nexport interface TagConditions {\n  /**\n   * Optional SQL statement to apply to the tags of the blob.\n   */\n  tagConditions?: string;\n}\n\n/**\n * Conditions to meet for the container.\n */\nexport interface ContainerRequestConditions extends LeaseAccessConditions, ModificationConditions {}\n\n/**\n * Represents the access tier on a blob.\n * For detailed information about block blob level tiering see {@link https://learn.microsoft.com/azure/storage/blobs/storage-blob-storage-tiers|Hot, cool and archive storage tiers.}\n */\nexport enum BlockBlobTier {\n  /**\n   * Optimized for storing data that is accessed frequently.\n   */\n  Hot = \"Hot\",\n  /**\n   * Optimized for storing data that is infrequently accessed and stored for at least 30 days.\n   */\n  Cool = \"Cool\",\n  /**\n   * Optimized for storing data that is rarely accessed.\n   */\n  Cold = \"Cold\",\n  /**\n   * Optimized for storing data that is rarely accessed and stored for at least 180 days\n   * with flexible latency requirements (on the order of hours).\n   */\n  Archive = \"Archive\",\n}\n\n/**\n * Specifies the page blob tier to set the blob to. This is only applicable to page blobs on premium storage accounts.\n * Please see {@link https://learn.microsoft.com/azure/storage/storage-premium-storage#scalability-and-performance-targets|here}\n * for detailed information on the corresponding IOPS and throughput per PageBlobTier.\n */\nexport enum PremiumPageBlobTier {\n  /**\n   * P4 Tier.\n   */\n  P4 = \"P4\",\n  /**\n   * P6 Tier.\n   */\n  P6 = \"P6\",\n  /**\n   * P10 Tier.\n   */\n  P10 = \"P10\",\n  /**\n   * P15 Tier.\n   */\n  P15 = \"P15\",\n  /**\n   * P20 Tier.\n   */\n  P20 = \"P20\",\n  /**\n   * P30 Tier.\n   */\n  P30 = \"P30\",\n  /**\n   * P40 Tier.\n   */\n  P40 = \"P40\",\n  /**\n   * P50 Tier.\n   */\n  P50 = \"P50\",\n  /**\n   * P60 Tier.\n   */\n  P60 = \"P60\",\n  /**\n   * P70 Tier.\n   */\n  P70 = \"P70\",\n  /**\n   * P80 Tier.\n   */\n  P80 = \"P80\",\n}\n\nexport function toAccessTier(\n  tier: BlockBlobTier | PremiumPageBlobTier | string | undefined,\n): AccessTier | undefined {\n  if (tier === undefined) {\n    return undefined;\n  }\n\n  return tier as AccessTier; // No more check if string is a valid AccessTier, and left this to underlay logic to decide(service).\n}\n\nexport function ensureCpkIfSpecified(cpk: CpkInfo | undefined, isHttps: boolean): void {\n  if (cpk && !isHttps) {\n    throw new RangeError(\"Customer-provided encryption key must be used over HTTPS.\");\n  }\n\n  if (cpk && !cpk.encryptionAlgorithm) {\n    cpk.encryptionAlgorithm = EncryptionAlgorithmAES25;\n  }\n}\n\n/**\n * Specifies the Replication Status of a blob. This is used when a storage account has\n * Object Replication Policy(s) applied. See {@link ObjectReplicationPolicy} and {@link ObjectReplicationRule}.\n */\nexport type ObjectReplicationStatus = \"complete\" | \"failed\";\n\n/**\n * Contains the Object Replication Rule ID and {@link ObjectReplicationStatus} of a blob.\n * There can be more than one {@link ObjectReplicationRule} under a {@link ObjectReplicationPolicy}.\n */\nexport interface ObjectReplicationRule {\n  /**\n   * The Object Replication Rule ID.\n   */\n  ruleId: string;\n\n  /**\n   * The Replication Status\n   */\n  replicationStatus: ObjectReplicationStatus;\n}\n\n/**\n * Contains Object Replication Policy ID and the respective list of {@link ObjectReplicationRule}.\n * This is used when retrieving the Object Replication Properties on the source blob. The policy id for the\n * destination blob is set in ObjectReplicationDestinationPolicyId of the respective method responses\n * (e.g. {@link BlobProperties.ObjectReplicationDestinationPolicyId}.\n */\nexport interface ObjectReplicationPolicy {\n  /**\n   * The Object Replication Policy ID.\n   */\n  policyId: string;\n\n  /**\n   * The Rule ID(s) and respective Replication Status(s) that are under the Policy ID.\n   */\n  rules: ObjectReplicationRule[];\n}\n\n/**\n * Contains response data for the {@link BlobClient.download} operation.\n */\nexport interface BlobDownloadResponseParsed extends BlobDownloadResponseModel {\n  /**\n   * Parsed Object Replication Policy Id, Rule Id(s) and status of the source blob.\n   */\n  objectReplicationSourceProperties?: ObjectReplicationPolicy[];\n\n  /**\n   * Object Replication Policy Id of the destination blob.\n   */\n  objectReplicationDestinationPolicyId?: string;\n}\n\n/**\n * The type of a {@link BlobQueryArrowField}.\n */\nexport type BlobQueryArrowFieldType =\n  | \"int64\"\n  | \"bool\"\n  | \"timestamp[ms]\"\n  | \"string\"\n  | \"double\"\n  | \"decimal\";\n\n/**\n * Describe a field in {@link BlobQueryArrowConfiguration}.\n */\nexport interface BlobQueryArrowField {\n  /**\n   * The type of the field.\n   */\n  type: BlobQueryArrowFieldType;\n\n  /**\n   * The name of the field.\n   */\n  name?: string;\n\n  /**\n   * The precision of the field. Required if type is \"decimal\".\n   */\n  precision?: number;\n\n  /**\n   * The scale of the field.  Required if type is is \"decimal\".\n   */\n  scale?: number;\n}\n\n/**\n * Describe immutable policy for blob.\n */\nexport interface BlobImmutabilityPolicy {\n  /**\n   * Specifies the date time when the blobs immutability policy is set to expire.\n   */\n  expiriesOn?: Date;\n  /**\n   * Specifies the immutability policy mode to set on the blob.\n   */\n  policyMode?: BlobImmutabilityPolicyMode;\n}\n\n/**\n * Represents authentication information in Authorization, ProxyAuthorization,\n * WWW-Authenticate, and Proxy-Authenticate header values.\n */\nexport interface HttpAuthorization {\n  /**\n   * The scheme to use for authorization.\n   */\n  scheme: string;\n\n  /**\n   * the credentials containing the authentication information of the user agent for the resource being requested.\n   */\n  value: string;\n}\n\n/**\n * Defines the known cloud audiences for Storage.\n */\nexport enum StorageBlobAudience {\n  /**\n   * The OAuth scope to use to retrieve an AAD token for Azure Storage.\n   */\n  StorageOAuthScopes = \"https://storage.azure.com/.default\",\n  /**\n   * The OAuth scope to use to retrieve an AAD token for Azure Disk.\n   */\n  DiskComputeOAuthScopes = \"https://disk.compute.azure.com/.default\",\n}\n\n/**\n *\n * To get OAuth audience for a storage account for blob service.\n */\nexport function getBlobServiceAccountAudience(storageAccountName: string): string {\n  return `https://${storageAccountName}.blob.core.windows.net/.default`;\n}\n\n/**\n * Abstract representation of a poller, intended to expose just the minimal API that the user needs to work with.\n */\nexport interface PollerLikeWithCancellation<TState extends PollOperationState<TResult>, TResult> {\n  /**\n   * Returns a promise that will resolve once a single polling request finishes.\n   * It does this by calling the update method of the Poller's operation.\n   */\n  poll(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns a promise that will resolve once the underlying operation is completed.\n   */\n  pollUntilDone(): Promise<TResult>;\n  /**\n   * Invokes the provided callback after each polling is completed,\n   * sending the current state of the poller's operation.\n   *\n   * It returns a method that can be used to stop receiving updates on the given callback function.\n   */\n  onProgress(callback: (state: TState) => void): CancelOnProgress;\n  /**\n   * Returns true if the poller has finished polling.\n   */\n  isDone(): boolean;\n  /**\n   * Stops the poller. After this, no manual or automated requests can be sent.\n   */\n  stopPolling(): void;\n  /**\n   * Returns true if the poller is stopped.\n   */\n  isStopped(): boolean;\n  /**\n   * Attempts to cancel the underlying operation.\n   */\n  cancelOperation(options?: { abortSignal?: AbortSignalLike }): Promise<void>;\n  /**\n   * Returns the state of the operation.\n   * The TState defined in PollerLike can be a subset of the TState defined in\n   * the Poller implementation.\n   */\n  getOperationState(): TState;\n  /**\n   * Returns the result value of the operation,\n   * regardless of the state of the poller.\n   * It can return undefined or an incomplete form of the final TResult value\n   * depending on the implementation.\n   */\n  getResult(): TResult | undefined;\n  /**\n   * Returns a serialized version of the poller's operation\n   * by invoking the operation's toString method.\n   */\n  toString(): string;\n}\n"]}