{"version": 3, "file": "BlobBatch.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/BlobBatch.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAE9C,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AAOrD,OAAO,EACL,+BAA+B,EAC/B,mBAAmB,EACnB,iBAAiB,GAClB,MAAM,2BAA2B,CAAC;AACnC,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAC1C,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAC;AAExE,OAAO,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAEvC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9E,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EACL,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,GACnB,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,0BAA0B,EAAE,MAAM,0CAA0C,CAAC;AACtF,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,iCAAiC,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AAC5F,OAAO,EAAE,gCAAgC,EAAE,MAAM,+CAA+C,CAAC;AAmBjG;;;GAGG;AACH,MAAM,OAAO,SAAS;IAKpB;QAHiB,UAAK,GAAW,OAAO,CAAC;QAIvC,IAAI,CAAC,YAAY,GAAG,IAAI,iBAAiB,EAAE,CAAC;IAC9C,CAAC;IAED;;;;OAIG;IACI,uBAAuB;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,uBAAuB,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACI,kBAAkB;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACI,cAAc;QACnB,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;IAC5C,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,UAA2B,EAC3B,sBAA2C;QAE3C,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7B,IAAI,CAAC;YACH,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;YAC/C,MAAM,sBAAsB,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC;gBAAS,CAAC;YACT,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,SAAqC;QACxD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACjC,MAAM,IAAI,UAAU,CAClB,yFAAyF,IAAI,CAAC,SAAS,cAAc,CACtH,CAAC;QACJ,CAAC;IACH,CAAC;IAqCM,KAAK,CAAC,UAAU,CACrB,eAAoC,EACpC,mBAKa,EACb,OAA2B;QAE3B,IAAI,GAAW,CAAC;QAChB,IAAI,UAA8E,CAAC;QAEnF,IACE,OAAO,eAAe,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,IAAI,mBAAmB,YAAY,0BAA0B,CAAC;gBACpE,mBAAmB,YAAY,mBAAmB;gBAClD,iBAAiB,CAAC,mBAAmB,CAAC,CAAC,EACzC,CAAC;YACD,iBAAiB;YACjB,GAAG,GAAG,eAAe,CAAC;YACtB,UAAU,GAAG,mBAAmB,CAAC;QACnC,CAAC;aAAM,IAAI,eAAe,YAAY,UAAU,EAAE,CAAC;YACjD,kBAAkB;YAClB,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;YAC1B,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;YACxC,OAAO,GAAG,mBAAwC,CAAC;QACrD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,UAAU,CAClB,+EAA+E,CAChF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAC5B,MAAM,IAAI,CAAC,qBAAqB,CAC9B;gBACE,GAAG,EAAE,GAAG;gBACR,UAAU,EAAE,UAAU;aACvB,EACD,KAAK,IAAI,EAAE;gBACT,MAAM,IAAI,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAC5E,cAAc,CACf,CAAC;YACJ,CAAC,CACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAgDM,KAAK,CAAC,iBAAiB,CAC5B,eAAoC,EACpC,gBAIc,EACd,aAA+C,EAC/C,OAA4B;QAE5B,IAAI,GAAW,CAAC;QAChB,IAAI,UAA8E,CAAC;QACnF,IAAI,IAAgB,CAAC;QAErB,IACE,OAAO,eAAe,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,IAAI,gBAAgB,YAAY,0BAA0B,CAAC;gBACjE,gBAAgB,YAAY,mBAAmB;gBAC/C,iBAAiB,CAAC,gBAAgB,CAAC,CAAC,EACtC,CAAC;YACD,iBAAiB;YACjB,GAAG,GAAG,eAAe,CAAC;YACtB,UAAU,GAAG,gBAGM,CAAC;YACpB,IAAI,GAAG,aAA2B,CAAC;QACrC,CAAC;aAAM,IAAI,eAAe,YAAY,UAAU,EAAE,CAAC;YACjD,kBAAkB;YAClB,GAAG,GAAG,eAAe,CAAC,GAAG,CAAC;YAC1B,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC;YACxC,IAAI,GAAG,gBAA8B,CAAC;YACtC,OAAO,GAAG,aAAmC,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,UAAU,CAClB,+EAA+E,CAChF,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,mCAAmC,EACnC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;YACnC,MAAM,IAAI,CAAC,qBAAqB,CAC9B;gBACE,GAAG,EAAE,GAAG;gBACR,UAAU,EAAE,UAAU;aACvB,EACD,KAAK,IAAI,EAAE;gBACT,MAAM,IAAI,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CACnF,IAAI,EACJ,cAAc,CACf,CAAC;YACJ,CAAC,CACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,iBAAiB;IASrB;QACE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QAEf,MAAM,QAAQ,GAAG,UAAU,EAAE,CAAC;QAE9B,kBAAkB;QAClB,IAAI,CAAC,QAAQ,GAAG,SAAS,QAAQ,EAAE,CAAC;QACpC,oBAAoB;QACpB,iCAAiC;QACjC,oCAAoC;QACpC,IAAI,CAAC,gBAAgB,GAAG,KAAK,IAAI,CAAC,QAAQ,GAAG,gBAAgB,GAAG,eAAe,CAAC,YAAY,qBAAqB,gBAAgB,GAAG,eAAe,CAAC,yBAAyB,UAAU,CAAC;QACxL,4CAA4C;QAC5C,IAAI,CAAC,oBAAoB,GAAG,6BAA6B,IAAI,CAAC,QAAQ,EAAE,CAAC;QACzE,sBAAsB;QACtB,IAAI,CAAC,kBAAkB,GAAG,KAAK,IAAI,CAAC,QAAQ,IAAI,CAAC;QAEjD,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;IAC/B,CAAC;IAED;;;;;;OAMG;IACI,cAAc,CACnB,UAA8E;QAE9E,MAAM,YAAY,GAAG,mBAAmB,EAAE,CAAC;QAC3C,YAAY,CAAC,SAAS,CACpB,mBAAmB,CAAC;YAClB,YAAY;YACZ,iBAAiB,EAAE;gBACjB,GAAG,EAAE;oBACH,UAAU,EAAE,GAAG;iBAChB;aACF;SACF,CAAC,EACF,EAAE,KAAK,EAAE,WAAW,EAAE,CACvB,CAAC;QACF,gEAAgE;QAChE,YAAY,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC,CAAC;QAClD,yFAAyF;QACzF,YAAY,CAAC,SAAS,CAAC,0BAA0B,CAAC,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,CAAC;QACjF,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,YAAY,CAAC,SAAS,CACpB,+BAA+B,CAAC;gBAC9B,UAAU;gBACV,MAAM,EAAE,kBAAkB;gBAC1B,kBAAkB,EAAE,EAAE,2BAA2B,EAAE,iCAAiC,EAAE;aACvF,CAAC,EACF,EAAE,KAAK,EAAE,MAAM,EAAE,CAClB,CAAC;QACJ,CAAC;aAAM,IAAI,UAAU,YAAY,0BAA0B,EAAE,CAAC;YAC5D,YAAY,CAAC,SAAS,CACpB,gCAAgC,CAAC;gBAC/B,WAAW,EAAE,UAAU,CAAC,WAAW;gBACnC,UAAU,EAAG,UAAkB,CAAC,UAAU;aAC3C,CAAC,EACF,EAAE,KAAK,EAAE,MAAM,EAAE,CAClB,CAAC;QACJ,CAAC;QACD,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;QAClC,qCAAqC;QACpC,QAAgB,CAAC,WAAW,GAAG,UAAU,CAAC;QAC1C,QAAgB,CAAC,aAAa,GAAG,YAAY,CAAC;QAE/C,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,sBAAsB,CAAC,OAAwB;QACpD,gCAAgC;QAChC,IAAI,CAAC,IAAI,IAAI;YACX,IAAI,CAAC,gBAAgB,EAAE,8BAA8B;YACrD,GAAG,eAAe,CAAC,UAAU,KAAK,IAAI,CAAC,cAAc,EAAE,EAAE,2BAA2B;YACpF,EAAE,EAAE,4CAA4C;YAChD,GAAG,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,kBAAkB,CAChD,OAAO,CAAC,GAAG,CACZ,IAAI,gBAAgB,GAAG,gBAAgB,EAAE,EAAE,qCAAqC;SAClF,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEzB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG,gBAAgB,EAAE,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,gBAAgB,CAAC,CAAC,0DAA0D;QACzF,wDAAwD;QACxD,8BAA8B;IAChC,CAAC;IAEM,gBAAgB,CAAC,UAA2B;QACjD,IAAI,IAAI,CAAC,cAAc,IAAI,iBAAiB,EAAE,CAAC;YAC7C,MAAM,IAAI,UAAU,CAAC,iBAAiB,iBAAiB,iCAAiC,CAAC,CAAC;QAC5F,CAAC;QAED,8CAA8C;QAC9C,MAAM,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YACzB,MAAM,IAAI,UAAU,CAAC,iCAAiC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAEM,iBAAiB,CAAC,UAA2B;QAClD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,wFAAwF;IACjF,kBAAkB;QACvB,OAAO,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,GAAG,gBAAgB,EAAE,CAAC;IACrE,CAAC;IAEM,uBAAuB;QAC5B,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAEM,cAAc;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;CACF;AAED,SAAS,0BAA0B,CAAC,YAA+B;IACjE,OAAO;QACL,IAAI,EAAE,4BAA4B;QAClC,KAAK,CAAC,WAAW,CAAC,OAAwB;YACxC,YAAY,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAE7C,OAAO;gBACL,OAAO;gBACP,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,iBAAiB,EAAE;aAC7B,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,uBAAuB;IAC9B,OAAO;QACL,IAAI,EAAE,yBAAyB;QAC/B,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,IAAI,aAAa,GAAG,EAAE,CAAC;YAEvB,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACrC,IAAI,MAAM,CAAC,IAAI,EAAE,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC/C,aAAa,GAAG,IAAI,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,IAAI,aAAa,KAAK,EAAE,EAAE,CAAC;gBACzB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,2DAA2D;YACpG,CAAC;YAED,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { randomUUID } from \"@azure/core-util\";\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\nimport type {\n  PipelinePolicy,\n  PipelineRequest,\n  PipelineResponse,\n  SendRequest,\n} from \"@azure/core-rest-pipeline\";\nimport {\n  bearerTokenAuthenticationPolicy,\n  createEmptyPipeline,\n  createHttpHeaders,\n} from \"@azure/core-rest-pipeline\";\nimport { isNode } from \"@azure/core-util\";\nimport { AnonymousCredential } from \"./credentials/AnonymousCredential\";\nimport type { BlobDeleteOptions, BlobSetTierOptions } from \"./Clients\";\nimport { BlobClient } from \"./Clients\";\nimport type { AccessTier } from \"./generatedModels\";\nimport { Mutex } from \"./utils/Mutex\";\nimport { Pipeline } from \"./Pipeline\";\nimport { getURLPath, getURLPathAndQuery, iEqual } from \"./utils/utils.common\";\nimport { stringifyXML } from \"@azure/core-xml\";\nimport {\n  HeaderConstants,\n  BATCH_MAX_REQUEST,\n  HTTP_VERSION_1_1,\n  HTTP_LINE_ENDING,\n  StorageOAuthScopes,\n} from \"./utils/constants\";\nimport { StorageSharedKeyCredential } from \"./credentials/StorageSharedKeyCredential\";\nimport { tracingClient } from \"./utils/tracing\";\nimport { authorizeRequestOnTenantChallenge, serializationPolicy } from \"@azure/core-client\";\nimport { storageSharedKeyCredentialPolicy } from \"./policies/StorageSharedKeyCredentialPolicyV2\";\n\n/**\n * A request associated with a batch operation.\n */\nexport interface BatchSubRequest {\n  /**\n   * The URL of the resource to request operation.\n   */\n  url: string;\n\n  /**\n   * The credential used for sub request.\n   * Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service.\n   * You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   */\n  credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential;\n}\n\n/**\n * A BlobBatch represents an aggregated set of operations on blobs.\n * Currently, only `delete` and `setAccessTier` are supported.\n */\nexport class BlobBatch {\n  private batchRequest: InnerBatchRequest;\n  private readonly batch: string = \"batch\";\n  private batchType: \"delete\" | \"setAccessTier\" | undefined;\n\n  constructor() {\n    this.batchRequest = new InnerBatchRequest();\n  }\n\n  /**\n   * Get the value of Content-Type for a batch request.\n   * The value must be multipart/mixed with a batch boundary.\n   * Example: multipart/mixed; boundary=batch_a81786c8-e301-4e42-a729-a32ca24ae252\n   */\n  public getMultiPartContentType(): string {\n    return this.batchRequest.getMultipartContentType();\n  }\n\n  /**\n   * Get assembled HTTP request body for sub requests.\n   */\n  public getHttpRequestBody(): string {\n    return this.batchRequest.getHttpRequestBody();\n  }\n\n  /**\n   * Get sub requests that are added into the batch request.\n   */\n  public getSubRequests(): Map<number, BatchSubRequest> {\n    return this.batchRequest.getSubRequests();\n  }\n\n  private async addSubRequestInternal(\n    subRequest: BatchSubRequest,\n    assembleSubRequestFunc: () => Promise<void>,\n  ): Promise<void> {\n    await Mutex.lock(this.batch);\n\n    try {\n      this.batchRequest.preAddSubRequest(subRequest);\n      await assembleSubRequestFunc();\n      this.batchRequest.postAddSubRequest(subRequest);\n    } finally {\n      await Mutex.unlock(this.batch);\n    }\n  }\n\n  private setBatchType(batchType: \"delete\" | \"setAccessTier\"): void {\n    if (!this.batchType) {\n      this.batchType = batchType;\n    }\n    if (this.batchType !== batchType) {\n      throw new RangeError(\n        `BlobBatch only supports one operation type per batch and it already is being used for ${this.batchType} operations.`,\n      );\n    }\n  }\n\n  /**\n   * The deleteBlob operation marks the specified blob or snapshot for deletion.\n   * The blob is later deleted during garbage collection.\n   * Only one kind of operation is allowed per batch request.\n   *\n   * Note that in order to delete a blob, you must delete all of its snapshots.\n   * You can delete both at the same time. See [delete operation details](https://learn.microsoft.com/en-us/rest/api/storageservices/delete-blob).\n   * The operation will be authenticated and authorized with specified credential.\n   * See [blob batch authorization details](https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param url - The url of the blob resource to delete.\n   * @param credential - Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options -\n   */\n  public async deleteBlob(\n    url: string,\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    options?: BlobDeleteOptions,\n  ): Promise<void>;\n\n  /**\n   * The deleteBlob operation marks the specified blob or snapshot for deletion.\n   * The blob is later deleted during garbage collection.\n   * Only one kind of operation is allowed per batch request.\n   *\n   * Note that in order to delete a blob, you must delete all of its snapshots.\n   * You can delete both at the same time. See [delete operation details](https://learn.microsoft.com/en-us/rest/api/storageservices/delete-blob).\n   * The operation will be authenticated and authorized with specified credential.\n   * See [blob batch authorization details](https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param blobClient - The BlobClient.\n   * @param options -\n   */\n  public async deleteBlob(blobClient: BlobClient, options?: BlobDeleteOptions): Promise<void>;\n\n  public async deleteBlob(\n    urlOrBlobClient: string | BlobClient,\n    credentialOrOptions:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | BlobDeleteOptions\n      | undefined,\n    options?: BlobDeleteOptions,\n  ): Promise<void> {\n    let url: string;\n    let credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential;\n\n    if (\n      typeof urlOrBlobClient === \"string\" &&\n      ((isNode && credentialOrOptions instanceof StorageSharedKeyCredential) ||\n        credentialOrOptions instanceof AnonymousCredential ||\n        isTokenCredential(credentialOrOptions))\n    ) {\n      // First overload\n      url = urlOrBlobClient;\n      credential = credentialOrOptions;\n    } else if (urlOrBlobClient instanceof BlobClient) {\n      // Second overload\n      url = urlOrBlobClient.url;\n      credential = urlOrBlobClient.credential;\n      options = credentialOrOptions as BlobDeleteOptions;\n    } else {\n      throw new RangeError(\n        \"Invalid arguments. Either url and credential, or BlobClient need be provided.\",\n      );\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    return tracingClient.withSpan(\n      \"BatchDeleteRequest-addSubRequest\",\n      options,\n      async (updatedOptions) => {\n        this.setBatchType(\"delete\");\n        await this.addSubRequestInternal(\n          {\n            url: url,\n            credential: credential,\n          },\n          async () => {\n            await new BlobClient(url, this.batchRequest.createPipeline(credential)).delete(\n              updatedOptions,\n            );\n          },\n        );\n      },\n    );\n  }\n\n  /**\n   * The setBlobAccessTier operation sets the tier on a blob.\n   * The operation is allowed on block blobs in a blob storage or general purpose v2 account.\n   * Only one kind of operation is allowed per batch request.\n   *\n   * A block blob's tier determines Hot/Cool/Archive storage type.\n   * This operation does not update the blob's ETag.\n   * For detailed information about block blob level tiering\n   * see [hot, cool, and archive access tiers](https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers).\n   * The operation will be authenticated and authorized\n   * with specified credential. See [blob batch authorization details](https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param url - The url of the blob resource to delete.\n   * @param credential - Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param tier -\n   * @param options -\n   */\n  public async setBlobAccessTier(\n    url: string,\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    tier: AccessTier,\n    options?: BlobSetTierOptions,\n  ): Promise<void>;\n\n  /**\n   * The setBlobAccessTier operation sets the tier on a blob.\n   * The operation is allowed on block blobs in a blob storage or general purpose v2 account.\n   * Only one kind of operation is allowed per batch request.\n   *\n   * A block blob's tier determines Hot/Cool/Archive storage type.\n   * This operation does not update the blob's ETag.\n   * For detailed information about block blob level tiering\n   * see [hot, cool, and archive access tiers](https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blob-storage-tiers).\n   * The operation will be authenticated and authorized\n   * with specified credential. See [blob batch authorization details](https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch#authorization).\n   *\n   * @param blobClient - The BlobClient.\n   * @param tier -\n   * @param options -\n   */\n  public async setBlobAccessTier(\n    blobClient: BlobClient,\n    tier: AccessTier,\n    options?: BlobSetTierOptions,\n  ): Promise<void>;\n\n  public async setBlobAccessTier(\n    urlOrBlobClient: string | BlobClient,\n    credentialOrTier:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | AccessTier,\n    tierOrOptions?: AccessTier | BlobSetTierOptions,\n    options?: BlobSetTierOptions,\n  ): Promise<void> {\n    let url: string;\n    let credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential;\n    let tier: AccessTier;\n\n    if (\n      typeof urlOrBlobClient === \"string\" &&\n      ((isNode && credentialOrTier instanceof StorageSharedKeyCredential) ||\n        credentialOrTier instanceof AnonymousCredential ||\n        isTokenCredential(credentialOrTier))\n    ) {\n      // First overload\n      url = urlOrBlobClient;\n      credential = credentialOrTier as\n        | StorageSharedKeyCredential\n        | AnonymousCredential\n        | TokenCredential;\n      tier = tierOrOptions as AccessTier;\n    } else if (urlOrBlobClient instanceof BlobClient) {\n      // Second overload\n      url = urlOrBlobClient.url;\n      credential = urlOrBlobClient.credential;\n      tier = credentialOrTier as AccessTier;\n      options = tierOrOptions as BlobSetTierOptions;\n    } else {\n      throw new RangeError(\n        \"Invalid arguments. Either url and credential, or BlobClient need be provided.\",\n      );\n    }\n\n    if (!options) {\n      options = {};\n    }\n\n    return tracingClient.withSpan(\n      \"BatchSetTierRequest-addSubRequest\",\n      options,\n      async (updatedOptions) => {\n        this.setBatchType(\"setAccessTier\");\n        await this.addSubRequestInternal(\n          {\n            url: url,\n            credential: credential,\n          },\n          async () => {\n            await new BlobClient(url, this.batchRequest.createPipeline(credential)).setAccessTier(\n              tier,\n              updatedOptions,\n            );\n          },\n        );\n      },\n    );\n  }\n}\n\n/**\n * Inner batch request class which is responsible for assembling and serializing sub requests.\n * See https://learn.microsoft.com/en-us/rest/api/storageservices/blob-batch#request-body for how requests are assembled.\n */\nclass InnerBatchRequest {\n  private operationCount: number;\n  private body: string;\n  private subRequests: Map<number, BatchSubRequest>;\n  private readonly boundary: string;\n  private readonly subRequestPrefix: string;\n  private readonly multipartContentType: string;\n  private readonly batchRequestEnding: string;\n\n  constructor() {\n    this.operationCount = 0;\n    this.body = \"\";\n\n    const tempGuid = randomUUID();\n\n    // batch_{batchid}\n    this.boundary = `batch_${tempGuid}`;\n    // --batch_{batchid}\n    // Content-Type: application/http\n    // Content-Transfer-Encoding: binary\n    this.subRequestPrefix = `--${this.boundary}${HTTP_LINE_ENDING}${HeaderConstants.CONTENT_TYPE}: application/http${HTTP_LINE_ENDING}${HeaderConstants.CONTENT_TRANSFER_ENCODING}: binary`;\n    // multipart/mixed; boundary=batch_{batchid}\n    this.multipartContentType = `multipart/mixed; boundary=${this.boundary}`;\n    // --batch_{batchid}--\n    this.batchRequestEnding = `--${this.boundary}--`;\n\n    this.subRequests = new Map();\n  }\n\n  /**\n   * Create pipeline to assemble sub requests. The idea here is to use existing\n   * credential and serialization/deserialization components, with additional policies to\n   * filter unnecessary headers, assemble sub requests into request's body\n   * and intercept request from going to wire.\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   */\n  public createPipeline(\n    credential: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n  ): Pipeline {\n    const corePipeline = createEmptyPipeline();\n    corePipeline.addPolicy(\n      serializationPolicy({\n        stringifyXML,\n        serializerOptions: {\n          xml: {\n            xmlCharKey: \"#\",\n          },\n        },\n      }),\n      { phase: \"Serialize\" },\n    );\n    // Use batch header filter policy to exclude unnecessary headers\n    corePipeline.addPolicy(batchHeaderFilterPolicy());\n    // Use batch assemble policy to assemble request and intercept request from going to wire\n    corePipeline.addPolicy(batchRequestAssemblePolicy(this), { afterPhase: \"Sign\" });\n    if (isTokenCredential(credential)) {\n      corePipeline.addPolicy(\n        bearerTokenAuthenticationPolicy({\n          credential,\n          scopes: StorageOAuthScopes,\n          challengeCallbacks: { authorizeRequestOnChallenge: authorizeRequestOnTenantChallenge },\n        }),\n        { phase: \"Sign\" },\n      );\n    } else if (credential instanceof StorageSharedKeyCredential) {\n      corePipeline.addPolicy(\n        storageSharedKeyCredentialPolicy({\n          accountName: credential.accountName,\n          accountKey: (credential as any).accountKey,\n        }),\n        { phase: \"Sign\" },\n      );\n    }\n    const pipeline = new Pipeline([]);\n    // attach the v2 pipeline to this one\n    (pipeline as any)._credential = credential;\n    (pipeline as any)._corePipeline = corePipeline;\n\n    return pipeline;\n  }\n\n  public appendSubRequestToBody(request: PipelineRequest) {\n    // Start to assemble sub request\n    this.body += [\n      this.subRequestPrefix, // sub request constant prefix\n      `${HeaderConstants.CONTENT_ID}: ${this.operationCount}`, // sub request's content ID\n      \"\", // empty line after sub request's content ID\n      `${request.method.toString()} ${getURLPathAndQuery(\n        request.url,\n      )} ${HTTP_VERSION_1_1}${HTTP_LINE_ENDING}`, // sub request start line with method\n    ].join(HTTP_LINE_ENDING);\n\n    for (const [name, value] of request.headers) {\n      this.body += `${name}: ${value}${HTTP_LINE_ENDING}`;\n    }\n\n    this.body += HTTP_LINE_ENDING; // sub request's headers need be ending with an empty line\n    // No body to assemble for current batch request support\n    // End to assemble sub request\n  }\n\n  public preAddSubRequest(subRequest: BatchSubRequest) {\n    if (this.operationCount >= BATCH_MAX_REQUEST) {\n      throw new RangeError(`Cannot exceed ${BATCH_MAX_REQUEST} sub requests in a single batch`);\n    }\n\n    // Fast fail if url for sub request is invalid\n    const path = getURLPath(subRequest.url);\n    if (!path || path === \"\") {\n      throw new RangeError(`Invalid url for sub request: '${subRequest.url}'`);\n    }\n  }\n\n  public postAddSubRequest(subRequest: BatchSubRequest) {\n    this.subRequests.set(this.operationCount, subRequest);\n    this.operationCount++;\n  }\n\n  // Return the http request body with assembling the ending line to the sub request body.\n  public getHttpRequestBody(): string {\n    return `${this.body}${this.batchRequestEnding}${HTTP_LINE_ENDING}`;\n  }\n\n  public getMultipartContentType(): string {\n    return this.multipartContentType;\n  }\n\n  public getSubRequests(): Map<number, BatchSubRequest> {\n    return this.subRequests;\n  }\n}\n\nfunction batchRequestAssemblePolicy(batchRequest: InnerBatchRequest): PipelinePolicy {\n  return {\n    name: \"batchRequestAssemblePolicy\",\n    async sendRequest(request: PipelineRequest): Promise<PipelineResponse> {\n      batchRequest.appendSubRequestToBody(request);\n\n      return {\n        request,\n        status: 200,\n        headers: createHttpHeaders(),\n      };\n    },\n  };\n}\n\nfunction batchHeaderFilterPolicy(): PipelinePolicy {\n  return {\n    name: \"batchHeaderFilterPolicy\",\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      let xMsHeaderName = \"\";\n\n      for (const [name] of request.headers) {\n        if (iEqual(name, HeaderConstants.X_MS_VERSION)) {\n          xMsHeaderName = name;\n        }\n      }\n\n      if (xMsHeaderName !== \"\") {\n        request.headers.delete(xMsHeaderName); // The subrequests should not have the x-ms-version header.\n      }\n\n      return next(request);\n    },\n  };\n}\n"]}