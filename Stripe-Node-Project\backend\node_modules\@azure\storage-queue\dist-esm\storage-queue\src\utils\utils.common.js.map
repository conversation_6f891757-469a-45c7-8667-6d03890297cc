{"version": 3, "file": "utils.common.js", "sourceRoot": "", "sources": ["../../../../src/utils/utils.common.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAC9D,OAAO,EACL,eAAe,EACf,YAAY,EACZ,2BAA2B,EAC3B,cAAc,GACf,MAAM,aAAa,CAAC;AAGrB;;;;;;;GAOG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,IAAY;IACvD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;IAC9B,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACjF,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAE1B,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AACD;;;;;;;;GAQG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,IAAY,EAAE,KAAc;IACvE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,WAAW,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACnE,kFAAkF;IAClF,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC;IAEtE,MAAM,YAAY,GAAa,EAAE,CAAC;IAElC,KAAK,MAAM,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QACpD,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACjC,IAAI,GAAG,KAAK,WAAW,EAAE,CAAC;gBACxB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IACD,IAAI,YAAY,EAAE,CAAC;QACjB,YAAY,CAAC,IAAI,CAAC,GAAG,WAAW,IAAI,YAAY,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,SAAS,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE3E,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,eAAe,CAAC,GAAW,EAAE,IAAY;;IACvD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,MAAA,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,mCAAI,SAAS,CAAC;AACvD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW,EAAE,IAAY;IAClD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;IAC1B,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,OAAO,SAAS,CAAC,QAAQ,CAAC;IAC5B,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW;IACvC,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;IACtC,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,WAAW,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC;IACjC,WAAW,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;IAEnF,IAAI,eAAe,GAAa,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACvD,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,KAAa,EAAE,EAAE;QACzD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QACxC,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAChD,OAAO,CACL,YAAY,GAAG,CAAC,IAAI,YAAY,KAAK,gBAAgB,IAAI,gBAAgB,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAC7F,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAA8B,EAAE,CAAC;IAC9C,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/C,MAAM,GAAG,GAAW,YAAY,CAAC,CAAC,CAAC,CAAC;QACpC,MAAM,KAAK,GAAW,YAAY,CAAC,CAAC,CAAC,CAAC;QACtC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACvB,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAWD,SAAS,4BAA4B,CAAC,gBAAwB;IAC5D,gCAAgC;IAChC,uKAAuK;IACvK,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,gBAAgB,CAAC,MAAM,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAClE,4FAA4F;QAC5F,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrD,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;YACvC,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,6BAA6B,CAAC,EAAE,CAAC;gBAC7D,QAAQ,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAE,CAAC,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,oBAAoB,CAClC,gBAAwB,EACxB,QAM2B;IAE3B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACxC,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAE,CAAC,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,4BAA4B,CAAC,gBAAwB;IACnE,IAAI,QAAQ,GAAG,EAAE,CAAC;IAElB,IAAI,gBAAgB,CAAC,UAAU,CAAC,4BAA4B,CAAC,EAAE,CAAC;QAC9D,gCAAgC;QAChC,QAAQ,GAAG,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;QAC1D,gBAAgB,GAAG,2BAA2B,CAAC;IACjD,CAAC;IAED,0DAA0D;IAC1D,IAAI,aAAa,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAC5E,uCAAuC;IACvC,kGAAkG;IAClG,aAAa,GAAG,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC;IAEzF,IACE,gBAAgB,CAAC,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;QAC3D,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAC7C,CAAC;QACD,4BAA4B;QAE5B,IAAI,wBAAwB,GAAG,EAAE,CAAC;QAClC,IAAI,WAAW,GAAG,EAAE,CAAC;QACrB,IAAI,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,cAAc,GAAG,EAAE,CAAC;QAExB,2BAA2B;QAC3B,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QACpE,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,YAAY,CAAC,EAAE,QAAQ,CAAC,CAAC;QAEzF,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,gEAAgE;YAChE,8FAA8F;YAE9F,wBAAwB,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,0BAA0B,CAAC,CAAC;YAC9F,MAAM,QAAQ,GAAG,wBAAyB,CAAC,WAAW,EAAE,CAAC;YACzD,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;gBAChD,MAAM,IAAI,KAAK,CACb,iGAAiG,CAClG,CAAC;YACJ,CAAC;YAED,cAAc,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;YAC1E,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;YAC9E,CAAC;YACD,aAAa,GAAG,GAAG,wBAAwB,MAAM,WAAW,UAAU,cAAc,EAAE,CAAC;QACzF,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;QAC3E,CAAC;aAAM,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;QAC1E,CAAC;QAED,OAAO;YACL,IAAI,EAAE,mBAAmB;YACzB,GAAG,EAAE,aAAa;YAClB,WAAW;YACX,UAAU;YACV,QAAQ;SACT,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,wBAAwB;QAExB,MAAM,UAAU,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;QACnF,IAAI,WAAW,GAAG,oBAAoB,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QACxE,4DAA4D;QAC5D,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,WAAW,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;QACjF,CAAC;aAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;QACzF,CAAC;QAED,OAAO,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC;IAChF,CAAC;AACH,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,oBAAoB,CAAC,IAAU,EAAE,mBAA4B,IAAI;IAC/E,iEAAiE;IACjE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;IAEtC,OAAO,gBAAgB;QACrB,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,GAAG;QAC/D,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;AAC3D,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,KAAK,CACzB,QAAgB,EAChB,OAAyB,EACzB,UAAkB;IAElB,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,0CAA0C;QAC1C,IAAI,OAAY,CAAC;QAEjB,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,YAAY,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC;YACD,MAAM,CAAC,UAAU,CAAC,CAAC;QACrB,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,OAAO,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACrD,CAAC;YACD,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;QAEF,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAC/C,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAClD,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,WAAW,CAAC,GAAW;IACrC,IAAI,OAAO,GAAW,GAAG,CAAC;IAC1B,IAAI,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAChE,OAAO,GAAG,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,eAAe,CAAC,cAA2B;IACzD,MAAM,OAAO,GAAgB,iBAAiB,EAAE,CAAC;IACjD,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,cAAc,EAAE,CAAC;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,aAAa,EAAE,CAAC;YACzD,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACnE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CAAC,GAAW;IAC/C,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAC/B,IAAI,WAAW,CAAC;IAChB,IAAI,CAAC;QACH,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;YACjD,0EAA0E;YAC1E,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;YACxC,iFAAiF;YACjF,2GAA2G;YAC3G,mCAAmC;YACnC,WAAW,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,qEAAqE;YACrE,WAAW,GAAG,EAAE,CAAC;QACnB,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;IAC9E,CAAC;AACH,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,SAAc;IAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;IAE5B,sFAAsF;IACtF,wFAAwF;IACxF,wEAAwE;IACxE,wFAAwF;IACxF,OAAO,CACL,mJAAmJ,CAAC,IAAI,CACtJ,IAAI,CACL;QACD,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CACrE,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,gBAAgB,CAAC,GAAW,EAAE,UAAkB;IAC9D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;IAC7B,IAAI,KAAK,EAAE,CAAC;QACV,KAAK,IAAI,GAAG,GAAG,UAAU,CAAC;IAC5B,CAAC;SAAM,CAAC;QACN,KAAK,GAAG,UAAU,CAAC;IACrB,CAAC;IAED,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;IACzB,OAAO,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC9B,CAAC;AAkFD;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAC5B,QAAW;IAEX,IAAI,WAAW,IAAI,QAAQ,EAAE,CAAC;QAC5B,OAAO,QAA0C,CAAC;IACpD,CAAC;IAED,MAAM,IAAI,SAAS,CAAC,8BAA8B,QAAQ,EAAE,CAAC,CAAC;AAChE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { HttpHeaders } from \"@azure/core-rest-pipeline\";\nimport { createHttpHeaders } from \"@azure/core-rest-pipeline\";\nimport {\n  HeaderConstants,\n  URLConstants,\n  DevelopmentConnectionString,\n  PathStylePorts,\n} from \"./constants\";\nimport type { HttpHeadersLike, WebResourceLike } from \"@azure/core-http-compat\";\n\n/**\n * Append a string to URL path. Will remove duplicated \"/\" in front of the string\n * when URL path ends with a \"/\".\n *\n * @param url - Source URL string\n * @param name - String to be appended to URL\n * @returns An updated URL string\n */\nexport function appendToURLPath(url: string, name: string): string {\n  const urlParsed = new URL(url);\n\n  let path = urlParsed.pathname;\n  path = path ? (path.endsWith(\"/\") ? `${path}${name}` : `${path}/${name}`) : name;\n  urlParsed.pathname = path;\n\n  return urlParsed.toString();\n}\n/**\n * Set URL parameter name and value. If name exists in URL parameters, old value\n * will be replaced by name key. If not provide value, the parameter will be deleted.\n *\n * @param url - Source URL string\n * @param name - Parameter name\n * @param value - Parameter value\n * @returns An updated URL string\n */\nexport function setURLParameter(url: string, name: string, value?: string): string {\n  const urlParsed = new URL(url);\n  const encodedName = encodeURIComponent(name);\n  const encodedValue = value ? encodeURIComponent(value) : undefined;\n  // mutating searchParams will change the encoding, so we have to do this ourselves\n  const searchString = urlParsed.search === \"\" ? \"?\" : urlParsed.search;\n\n  const searchPieces: string[] = [];\n\n  for (const pair of searchString.slice(1).split(\"&\")) {\n    if (pair) {\n      const [key] = pair.split(\"=\", 2);\n      if (key !== encodedName) {\n        searchPieces.push(pair);\n      }\n    }\n  }\n  if (encodedValue) {\n    searchPieces.push(`${encodedName}=${encodedValue}`);\n  }\n\n  urlParsed.search = searchPieces.length ? `?${searchPieces.join(\"&\")}` : \"\";\n\n  return urlParsed.toString();\n}\n\n/**\n * Get URL parameter by name.\n *\n * @param url - URL string\n * @param name - Parameter name\n * @returns Parameter value(s) for the given parameter name.\n */\nexport function getURLParameter(url: string, name: string): string | string[] | undefined {\n  const urlParsed = new URL(url);\n  return urlParsed.searchParams.get(name) ?? undefined;\n}\n\n/**\n * Set URL host.\n *\n * @param url - Source URL string\n * @param host - New host string\n * @returns An updated URL string\n */\nexport function setURLHost(url: string, host: string): string {\n  const urlParsed = new URL(url);\n  urlParsed.hostname = host;\n  return urlParsed.toString();\n}\n\n/**\n * Gets URL path from an URL string.\n *\n * @param url - Source URL string\n * @returns The path part of the given URL string.\n */\nexport function getURLPath(url: string): string | undefined {\n  try {\n    const urlParsed = new URL(url);\n    return urlParsed.pathname;\n  } catch (e) {\n    return undefined;\n  }\n}\n\n/**\n * Gets URL query key value pairs from an URL string.\n *\n * @param url -\n * @returns query key value string pairs from the given URL string.\n */\nexport function getURLQueries(url: string): { [key: string]: string } {\n  let queryString = new URL(url).search;\n  if (!queryString) {\n    return {};\n  }\n\n  queryString = queryString.trim();\n  queryString = queryString.startsWith(\"?\") ? queryString.substring(1) : queryString;\n\n  let querySubStrings: string[] = queryString.split(\"&\");\n  querySubStrings = querySubStrings.filter((value: string) => {\n    const indexOfEqual = value.indexOf(\"=\");\n    const lastIndexOfEqual = value.lastIndexOf(\"=\");\n    return (\n      indexOfEqual > 0 && indexOfEqual === lastIndexOfEqual && lastIndexOfEqual < value.length - 1\n    );\n  });\n\n  const queries: { [key: string]: string } = {};\n  for (const querySubString of querySubStrings) {\n    const splitResults = querySubString.split(\"=\");\n    const key: string = splitResults[0];\n    const value: string = splitResults[1];\n    queries[key] = value;\n  }\n\n  return queries;\n}\n\nexport interface ConnectionString {\n  kind: \"AccountConnString\" | \"SASConnString\";\n  url: string;\n  accountName: string;\n  accountKey?: any;\n  accountSas?: string;\n  proxyUri?: string; // Development Connection String may contain proxyUri\n}\n\nfunction getProxyUriFromDevConnString(connectionString: string): string {\n  // Development Connection String\n  // https://learn.microsoft.com/en-us/azure/storage/common/storage-configure-connection-string#connect-to-the-emulator-account-using-the-well-known-account-name-and-key\n  let proxyUri = \"\";\n  if (connectionString.search(\"DevelopmentStorageProxyUri=\") !== -1) {\n    // CONNECTION_STRING=UseDevelopmentStorage=true;DevelopmentStorageProxyUri=http://myProxyUri\n    const matchCredentials = connectionString.split(\";\");\n    for (const element of matchCredentials) {\n      if (element.trim().startsWith(\"DevelopmentStorageProxyUri=\")) {\n        proxyUri = element.trim().match(\"DevelopmentStorageProxyUri=(.*)\")![1];\n      }\n    }\n  }\n  return proxyUri;\n}\n\n/**\n *\n * @param connectionString - Account connection string.\n * @param argument - property to get value from the connection string.\n * @returns Value of the property specified in argument.\n */\nexport function getValueInConnString(\n  connectionString: string,\n  argument:\n    | \"QueueEndpoint\"\n    | \"AccountName\"\n    | \"AccountKey\"\n    | \"DefaultEndpointsProtocol\"\n    | \"EndpointSuffix\"\n    | \"SharedAccessSignature\",\n): string {\n  const elements = connectionString.split(\";\");\n  for (const element of elements) {\n    if (element.trim().startsWith(argument)) {\n      return element.trim().match(argument + \"=(.*)\")![1];\n    }\n  }\n  return \"\";\n}\n\n/**\n * Extracts the parts of an Azure Storage account connection string.\n *\n * @param connectionString - Connection string.\n * @returns String key value pairs of the storage account's url and credentials.\n */\nexport function extractConnectionStringParts(connectionString: string): ConnectionString {\n  let proxyUri = \"\";\n\n  if (connectionString.startsWith(\"UseDevelopmentStorage=true\")) {\n    // Development connection string\n    proxyUri = getProxyUriFromDevConnString(connectionString);\n    connectionString = DevelopmentConnectionString;\n  }\n\n  // Matching QueueEndpoint in the Account connection string\n  let queueEndpoint = getValueInConnString(connectionString, \"QueueEndpoint\");\n  // Slicing off '/' at the end if exists\n  // (The methods that use `extractConnectionStringParts` expect the url to not have `/` at the end)\n  queueEndpoint = queueEndpoint.endsWith(\"/\") ? queueEndpoint.slice(0, -1) : queueEndpoint;\n\n  if (\n    connectionString.search(\"DefaultEndpointsProtocol=\") !== -1 &&\n    connectionString.search(\"AccountKey=\") !== -1\n  ) {\n    // Account connection string\n\n    let defaultEndpointsProtocol = \"\";\n    let accountName = \"\";\n    let accountKey = Buffer.from(\"accountKey\", \"base64\");\n    let endpointSuffix = \"\";\n\n    // Get account name and key\n    accountName = getValueInConnString(connectionString, \"AccountName\");\n    accountKey = Buffer.from(getValueInConnString(connectionString, \"AccountKey\"), \"base64\");\n\n    if (!queueEndpoint) {\n      // QueueEndpoint is not present in the Account connection string\n      // Can be obtained from `${defaultEndpointsProtocol}://${accountName}.queue.${endpointSuffix}`\n\n      defaultEndpointsProtocol = getValueInConnString(connectionString, \"DefaultEndpointsProtocol\");\n      const protocol = defaultEndpointsProtocol!.toLowerCase();\n      if (protocol !== \"https\" && protocol !== \"http\") {\n        throw new Error(\n          \"Invalid DefaultEndpointsProtocol in the provided Connection String. Expecting 'https' or 'http'\",\n        );\n      }\n\n      endpointSuffix = getValueInConnString(connectionString, \"EndpointSuffix\");\n      if (!endpointSuffix) {\n        throw new Error(\"Invalid EndpointSuffix in the provided Connection String\");\n      }\n      queueEndpoint = `${defaultEndpointsProtocol}://${accountName}.queue.${endpointSuffix}`;\n    }\n\n    if (!accountName) {\n      throw new Error(\"Invalid AccountName in the provided Connection String\");\n    } else if (accountKey.length === 0) {\n      throw new Error(\"Invalid AccountKey in the provided Connection String\");\n    }\n\n    return {\n      kind: \"AccountConnString\",\n      url: queueEndpoint,\n      accountName,\n      accountKey,\n      proxyUri,\n    };\n  } else {\n    // SAS connection string\n\n    const accountSas = getValueInConnString(connectionString, \"SharedAccessSignature\");\n    let accountName = getValueInConnString(connectionString, \"AccountName\");\n    // if accountName is empty, try to read it from BlobEndpoint\n    if (!accountName) {\n      accountName = getAccountNameFromUrl(queueEndpoint);\n    }\n    if (!queueEndpoint) {\n      throw new Error(\"Invalid QueueEndpoint in the provided SAS Connection String\");\n    } else if (!accountSas) {\n      throw new Error(\"Invalid SharedAccessSignature in the provided SAS Connection String\");\n    }\n\n    return { kind: \"SASConnString\", url: queueEndpoint, accountName, accountSas };\n  }\n}\n\n/**\n * Rounds a date off to seconds.\n *\n * @param date -\n * @param withMilliseconds - If true, YYYY-MM-DDThh:mm:ss.fffffffZ will be returned;\n *                                          If false, YYYY-MM-DDThh:mm:ssZ will be returned.\n * @returns Date string in ISO8061 format, with or without 7 milliseconds component\n */\nexport function truncatedISO8061Date(date: Date, withMilliseconds: boolean = true): string {\n  // Date.toISOString() will return like \"2018-10-29T06:34:36.139Z\"\n  const dateString = date.toISOString();\n\n  return withMilliseconds\n    ? dateString.substring(0, dateString.length - 1) + \"0000\" + \"Z\"\n    : dateString.substring(0, dateString.length - 5) + \"Z\";\n}\n\n/**\n * Delay specified time interval.\n *\n * @param timeInMs -\n * @param aborter -\n * @param abortError -\n */\nexport async function delay(\n  timeInMs: number,\n  aborter?: AbortSignalLike,\n  abortError?: Error,\n): Promise<void> {\n  return new Promise<void>((resolve, reject) => {\n    /* eslint-disable-next-line prefer-const*/\n    let timeout: any;\n\n    const abortHandler = () => {\n      if (timeout !== undefined) {\n        clearTimeout(timeout);\n      }\n      reject(abortError);\n    };\n\n    const resolveHandler = () => {\n      if (aborter !== undefined) {\n        aborter.removeEventListener(\"abort\", abortHandler);\n      }\n      resolve();\n    };\n\n    timeout = setTimeout(resolveHandler, timeInMs);\n    if (aborter !== undefined) {\n      aborter.addEventListener(\"abort\", abortHandler);\n    }\n  });\n}\n\n/**\n * Sanitizes a url by removing the Signature parameter\n * @param url - to sanitize\n * @returns sanitized string\n */\nexport function sanitizeURL(url: string): string {\n  let safeURL: string = url;\n  if (getURLParameter(safeURL, URLConstants.Parameters.SIGNATURE)) {\n    safeURL = setURLParameter(safeURL, URLConstants.Parameters.SIGNATURE, \"*****\");\n  }\n\n  return safeURL;\n}\n\n/**\n * Sanitize headers by removing sensitive values such as AUTHORIZATION and X_MS_COPY_SOURCE\n * @param originalHeader - original headers\n * @returns sanitized headers\n */\nexport function sanitizeHeaders(originalHeader: HttpHeaders): HttpHeaders {\n  const headers: HttpHeaders = createHttpHeaders();\n  for (const [name, value] of originalHeader) {\n    if (name.toLowerCase() === HeaderConstants.AUTHORIZATION) {\n      headers.set(name, \"*****\");\n    } else if (name.toLowerCase() === HeaderConstants.X_MS_COPY_SOURCE) {\n      headers.set(name, sanitizeURL(value));\n    } else {\n      headers.set(name, value);\n    }\n  }\n\n  return headers;\n}\n\n/**\n * Extracts account name from the url\n * @param url - url to extract the account name from\n * @returns with the account name\n */\nexport function getAccountNameFromUrl(url: string): string {\n  const parsedUrl = new URL(url);\n  let accountName;\n  try {\n    if (parsedUrl.hostname.split(\".\")[1] === \"queue\") {\n      // `${defaultEndpointsProtocol}://${accountName}.queue.${endpointSuffix}`;\n      accountName = parsedUrl.hostname.split(\".\")[0];\n    } else if (isIpEndpointStyle(parsedUrl)) {\n      // IPv4/IPv6 address hosts... Example - http://**********:10001/devstoreaccount1/\n      // Single word domain without a [dot] in the endpoint... Example - http://localhost:10001/devstoreaccount1/\n      // .getPath() -> /devstoreaccount1/\n      accountName = parsedUrl.pathname.split(\"/\")[1];\n    } else {\n      // Custom domain case: \"https://customdomain.com/containername/blob\".\n      accountName = \"\";\n    }\n    return accountName;\n  } catch (error: any) {\n    throw new Error(\"Unable to extract accountName with provided information.\");\n  }\n}\n\nexport function isIpEndpointStyle(parsedUrl: URL): boolean {\n  const host = parsedUrl.host;\n\n  // Case 1: Ipv6, use a broad regex to find out candidates whose host contains two ':'.\n  // Case 2: localhost(:port) or host.docker.internal, use broad regex to match port part.\n  // Case 3: Ipv4, use broad regex which just check if host contains Ipv4.\n  // For valid host please refer to https://man7.org/linux/man-pages/man7/hostname.7.html.\n  return (\n    /^.*:.*:.*$|^(localhost|host.docker.internal)(:[0-9]+)?$|^(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])(\\.(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])){3}(:[0-9]+)?$/.test(\n      host,\n    ) ||\n    (Boolean(parsedUrl.port) && PathStylePorts.includes(parsedUrl.port))\n  );\n}\n\n/**\n * Append a string to URL query.\n *\n * @param url - Source URL string.\n * @param queryParts - String to be appended to the URL query.\n * @returns An updated URL string.\n */\nexport function appendToURLQuery(url: string, queryParts: string): string {\n  const urlParsed = new URL(url);\n\n  let query = urlParsed.search;\n  if (query) {\n    query += \"&\" + queryParts;\n  } else {\n    query = queryParts;\n  }\n\n  urlParsed.search = query;\n  return urlParsed.toString();\n}\n\n/**\n * A representation of an HTTP response that\n * includes a reference to the request that\n * originated it.\n */\nexport interface HttpResponse {\n  /**\n   * The headers from the response.\n   */\n  headers: HttpHeadersLike;\n  /**\n   * The original request that resulted in this response.\n   */\n  request: WebResourceLike;\n  /**\n   * The HTTP status code returned from the service.\n   */\n  status: number;\n}\n\n/**\n * An object with a _response property that has\n * headers already parsed into a typed object.\n */\nexport interface ResponseWithHeaders<Headers> {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: Headers;\n  };\n}\n\n/**\n * An object with a _response property that has body\n * and headers already parsed into known types.\n */\nexport interface ResponseWithBody<Headers, Body> {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: HttpResponse & {\n    /**\n     * The parsed HTTP response headers.\n     */\n    parsedHeaders: Headers;\n    /**\n     * The response body as text (string format)\n     */\n    bodyAsText: string;\n    /**\n     * The response body as parsed JSON or XML\n     */\n    parsedBody: Body;\n  };\n}\n\n/**\n * An object with a simple _response property.\n */\nexport interface ResponseLike {\n  /**\n   * The underlying HTTP response.\n   */\n  _response: HttpResponse;\n}\n\n/**\n * A type that represents an operation result with a known _response property.\n */\nexport type WithResponse<T, Headers = undefined, Body = undefined> = T &\n  (Body extends object\n    ? ResponseWithBody<Headers, Body>\n    : Headers extends object\n      ? ResponseWithHeaders<Headers>\n      : ResponseLike);\n\n/**\n * A typesafe helper for ensuring that a given response object has\n * the original _response attached.\n * @param response - A response object from calling a client operation\n * @returns The same object, but with known _response property\n */\nexport function assertResponse<T extends object, Headers = undefined, Body = undefined>(\n  response: T,\n): WithResponse<T, Headers, Body> {\n  if (`_response` in response) {\n    return response as WithResponse<T, Headers, Body>;\n  }\n\n  throw new TypeError(`Unexpected response object ${response}`);\n}\n"]}