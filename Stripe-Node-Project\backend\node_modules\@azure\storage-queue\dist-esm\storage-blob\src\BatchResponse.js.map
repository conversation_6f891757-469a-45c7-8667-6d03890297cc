{"version": 3, "file": "BatchResponse.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/BatchResponse.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { BatchSubRequest } from \"./BlobBatch\";\nimport type { HttpHeadersLike } from \"@azure/core-http-compat\";\n\n/**\n * The response data associated with a single request within a batch operation.\n */\nexport interface BatchSubResponse {\n  /**\n   * The status code of the sub operation.\n   */\n  status: number;\n\n  /**\n   * The status message of the sub operation.\n   */\n  statusMessage: string;\n\n  /**\n   * The error code of the sub operation, if the sub operation failed.\n   */\n  errorCode?: string;\n\n  /**\n   * The HTTP response headers.\n   */\n  headers: HttpHeadersLike;\n\n  /**\n   * The body as text.\n   */\n  bodyAsText?: string;\n\n  /**\n   * The batch sub request corresponding to the sub response.\n   */\n  _request: BatchSubRequest;\n}\n\n/**\n * The multipart/mixed response which contains the response for each subrequest.\n */\nexport interface ParsedBatchResponse {\n  /**\n   * The parsed sub responses.\n   */\n  subResponses: BatchSubResponse[];\n\n  /**\n   * The succeeded executed sub responses' count;\n   */\n  subResponsesSucceededCount: number;\n\n  /**\n   * The failed executed sub responses' count;\n   */\n  subResponsesFailedCount: number;\n}\n"]}