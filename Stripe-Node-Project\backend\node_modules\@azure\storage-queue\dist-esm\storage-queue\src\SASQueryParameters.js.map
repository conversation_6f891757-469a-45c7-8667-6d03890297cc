{"version": 3, "file": "SASQueryParameters.js", "sourceRoot": "", "sources": ["../../../src/SASQueryParameters.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAE5D;;GAEG;AACH,MAAM,CAAN,IAAY,WAUX;AAVD,WAAY,WAAW;IACrB;;OAEG;IACH,8BAAe,CAAA;IAEf;;OAEG;IACH,0CAA2B,CAAA;AAC7B,CAAC,EAVW,WAAW,KAAX,WAAW,QAUtB;AAED;;;;;;;;GAQG;AACH,MAAM,OAAO,kBAAkB;IA6D7B;;;;OAIG;IACH,IAAW,OAAO;QAChB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,OAAO;gBACL,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG;gBAC1B,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK;aAC/B,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,YACE,OAAe,EACf,SAAiB,EACjB,WAAoB,EACpB,QAAiB,EACjB,aAAsB,EACtB,QAAsB,EACtB,QAAe,EACf,SAAgB,EAChB,OAAoB,EACpB,UAAmB,EACnB,QAAiB;QAEjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;;OAGG;IACI,QAAQ;QACb,MAAM,MAAM,GAAa,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;QAChG,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;oBACjE,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CACvE,CAAC;oBACF,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CACzE,CAAC;oBACF,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAC1B,OAAO,EACP,KAAK,EACL,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CACzD,CAAC;oBACF,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC9D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5D,MAAM;gBACR,KAAK,IAAI;oBACP,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC/D,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC7D,MAAM;YACV,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;IAED;;;;;;OAMG;IACK,uBAAuB,CAAC,OAAiB,EAAE,GAAW,EAAE,KAAc;QAC5E,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QAED,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;QAC9B,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK,EAAE,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { SasIPRange } from \"./SasIPRange\";\nimport { ipRangeToString } from \"./SasIPRange\";\nimport { truncatedISO8061Date } from \"./utils/utils.common\";\n\n/**\n * Protocols for generated SAS.\n */\nexport enum SASProtocol {\n  /**\n   * Protocol that allows HTTPS only\n   */\n  Https = \"https\",\n\n  /**\n   * Protocol that allows both HTTPS and HTTP\n   */\n  HttpsAndHttp = \"https,http\",\n}\n\n/**\n * Represents the components that make up an Azure Storage SAS' query parameters. This type is not constructed directly\n * by the user; it is only generated by the {@link AccountSASSignatureValues} and {@link QueueSASSignatureValues}\n * types. Once generated, it can be encoded into a {@link String} and appended to a URL directly (though caution should\n * be taken here in case there are existing query parameters, which might affect the appropriate means of appending\n * these query parameters).\n *\n * NOTE: Instances of this class are immutable.\n */\nexport class SASQueryParameters {\n  /**\n   * The storage API version.\n   */\n  public readonly version: string;\n\n  /**\n   * Optional. The allowed HTTP protocol(s).\n   */\n  public readonly protocol?: SASProtocol;\n\n  /**\n   * Optional. The start time for this SAS token.\n   */\n  public readonly startsOn?: Date;\n\n  /**\n   * Optional only when identifier is provided. The expiry time for this SAS token.\n   */\n  public readonly expiresOn?: Date;\n\n  /**\n   * Optional only when identifier is provided.\n   * Please refer to {@link AccountSASPermissions}, {@link QueueSASPermissions} for more details.\n   */\n  public readonly permissions?: string;\n\n  /**\n   * Optional. The storage services being accessed (only for Account SAS). Please refer to {@link AccountSASServices}\n   * for more details.\n   */\n  public readonly services?: string;\n\n  /**\n   * Optional. The storage resource types being accessed (only for Account SAS). Please refer to\n   * {@link AccountSASResourceTypes} for more details.\n   */\n  public readonly resourceTypes?: string;\n\n  /**\n   * Optional. The signed identifier (only for {@link QueueSASSignatureValues}).\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  public readonly identifier?: string;\n\n  /**\n   * Optional. The storage queue (only for {@link QueueSASSignatureValues}).\n   */\n  public readonly resource?: string;\n\n  /**\n   * The signature for the SAS token.\n   */\n  public readonly signature: string;\n\n  /**\n   * Inner value of getter ipRange.\n   */\n  private readonly ipRangeInner?: SasIPRange;\n\n  /**\n   * Optional. IP range allowed for this SAS.\n   *\n   * @readonly\n   */\n  public get ipRange(): SasIPRange | undefined {\n    if (this.ipRangeInner) {\n      return {\n        end: this.ipRangeInner.end,\n        start: this.ipRangeInner.start,\n      };\n    }\n    return undefined;\n  }\n\n  /**\n   * Creates an instance of SASQueryParameters.\n   *\n   * @param version - Representing the storage version\n   * @param signature - Representing the signature for the SAS token\n   * @param permissions - Representing the storage permissions\n   * @param services - Representing the storage services being accessed (only for Account SAS)\n   * @param resourceTypes - Representing the storage resource types being accessed (only for Account SAS)\n   * @param protocol - Representing the allowed HTTP protocol(s)\n   * @param startsOn - Representing the start time for this SAS token\n   * @param expiresOn - Representing the expiry time for this SAS token\n   * @param ipRange - Representing the range of valid IP addresses for this SAS token\n   * @param identifier - Representing the signed identifier (only for Service SAS)\n   * @param resource - Representing the storage queue (only for Service SAS)\n   */\n  constructor(\n    version: string,\n    signature: string,\n    permissions?: string,\n    services?: string,\n    resourceTypes?: string,\n    protocol?: SASProtocol,\n    startsOn?: Date,\n    expiresOn?: Date,\n    ipRange?: SasIPRange,\n    identifier?: string,\n    resource?: string,\n  ) {\n    this.version = version;\n    this.services = services;\n    this.resourceTypes = resourceTypes;\n    this.expiresOn = expiresOn;\n    this.permissions = permissions;\n    this.protocol = protocol;\n    this.startsOn = startsOn;\n    this.ipRangeInner = ipRange;\n    this.identifier = identifier;\n    this.resource = resource;\n    this.signature = signature;\n  }\n\n  /**\n   * Encodes all SAS query parameters into a string that can be appended to a URL.\n   *\n   */\n  public toString(): string {\n    const params: string[] = [\"sv\", \"ss\", \"srt\", \"spr\", \"st\", \"se\", \"sip\", \"si\", \"sr\", \"sp\", \"sig\"];\n    const queries: string[] = [];\n\n    for (const param of params) {\n      switch (param) {\n        case \"sv\":\n          this.tryAppendQueryParameter(queries, param, this.version);\n          break;\n        case \"ss\":\n          this.tryAppendQueryParameter(queries, param, this.services);\n          break;\n        case \"srt\":\n          this.tryAppendQueryParameter(queries, param, this.resourceTypes);\n          break;\n        case \"spr\":\n          this.tryAppendQueryParameter(queries, param, this.protocol);\n          break;\n        case \"st\":\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.startsOn ? truncatedISO8061Date(this.startsOn, false) : undefined,\n          );\n          break;\n        case \"se\":\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.expiresOn ? truncatedISO8061Date(this.expiresOn, false) : undefined,\n          );\n          break;\n        case \"sip\":\n          this.tryAppendQueryParameter(\n            queries,\n            param,\n            this.ipRange ? ipRangeToString(this.ipRange) : undefined,\n          );\n          break;\n        case \"si\":\n          this.tryAppendQueryParameter(queries, param, this.identifier);\n          break;\n        case \"sr\":\n          this.tryAppendQueryParameter(queries, param, this.resource);\n          break;\n        case \"sp\":\n          this.tryAppendQueryParameter(queries, param, this.permissions);\n          break;\n        case \"sig\":\n          this.tryAppendQueryParameter(queries, param, this.signature);\n          break;\n      }\n    }\n    return queries.join(\"&\");\n  }\n\n  /**\n   * A private helper method used to filter and append query key/value pairs into an array.\n   *\n   * @param queries -\n   * @param key -\n   * @param value -\n   */\n  private tryAppendQueryParameter(queries: string[], key: string, value?: string): void {\n    if (!value) {\n      return;\n    }\n\n    key = encodeURIComponent(key);\n    value = encodeURIComponent(value);\n    if (key.length > 0 && value.length > 0) {\n      queries.push(`${key}=${value}`);\n    }\n  }\n}\n"]}