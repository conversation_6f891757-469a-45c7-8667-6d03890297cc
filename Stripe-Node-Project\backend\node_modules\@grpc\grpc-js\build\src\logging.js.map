{"version": 3, "file": "logging.js", "sourceRoot": "", "sources": ["../../src/logging.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;;AA6FH,sBAmBC;AAED,0CAIC;AApHD,2CAA2C;AAC3C,qCAA8B;AAE9B,MAAM,aAAa,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC,OAAO,CAAC;AAE5D,MAAM,cAAc,GAAqB;IACvC,KAAK,EAAE,CAAC,OAAa,EAAE,GAAG,cAAqB,EAAE,EAAE;QACjD,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,EAAE,GAAG,cAAc,CAAC,CAAC;IACnD,CAAC;IACD,IAAI,EAAE,CAAC,OAAa,EAAE,GAAG,cAAqB,EAAE,EAAE;QAChD,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,EAAE,GAAG,cAAc,CAAC,CAAC;IACnD,CAAC;IACD,KAAK,EAAE,CAAC,OAAa,EAAE,GAAG,cAAqB,EAAE,EAAE;QACjD,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,OAAO,EAAE,GAAG,cAAc,CAAC,CAAC;IACnD,CAAC;CACF,CAAC;AAEF,IAAI,OAAO,GAAqB,cAAc,CAAC;AAC/C,IAAI,aAAa,GAAiB,wBAAY,CAAC,KAAK,CAAC;AAErD,MAAM,eAAe,GACnB,MAAA,MAAA,OAAO,CAAC,GAAG,CAAC,mBAAmB,mCAAI,OAAO,CAAC,GAAG,CAAC,cAAc,mCAAI,EAAE,CAAC;AAEtE,QAAQ,eAAe,CAAC,WAAW,EAAE,EAAE,CAAC;IACtC,KAAK,OAAO;QACV,aAAa,GAAG,wBAAY,CAAC,KAAK,CAAC;QACnC,MAAM;IACR,KAAK,MAAM;QACT,aAAa,GAAG,wBAAY,CAAC,IAAI,CAAC;QAClC,MAAM;IACR,KAAK,OAAO;QACV,aAAa,GAAG,wBAAY,CAAC,KAAK,CAAC;QACnC,MAAM;IACR,KAAK,MAAM;QACT,aAAa,GAAG,wBAAY,CAAC,IAAI,CAAC;QAClC,MAAM;IACR,QAAQ;IACR,0BAA0B;AAC5B,CAAC;AAEM,MAAM,SAAS,GAAG,GAAqB,EAAE;IAC9C,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEK,MAAM,SAAS,GAAG,CAAC,MAAwB,EAAQ,EAAE;IAC1D,OAAO,GAAG,MAAM,CAAC;AACnB,CAAC,CAAC;AAFW,QAAA,SAAS,aAEpB;AAEK,MAAM,kBAAkB,GAAG,CAAC,SAAuB,EAAQ,EAAE;IAClE,aAAa,GAAG,SAAS,CAAC;AAC5B,CAAC,CAAC;AAFW,QAAA,kBAAkB,sBAE7B;AAEF,8DAA8D;AACvD,MAAM,GAAG,GAAG,CAAC,QAAsB,EAAE,GAAG,IAAW,EAAQ,EAAE;IAClE,IAAI,WAAwC,CAAC;IAC7C,IAAI,QAAQ,IAAI,aAAa,EAAE,CAAC;QAC9B,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,wBAAY,CAAC,KAAK;gBACrB,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC5B,MAAM;YACR,KAAK,wBAAY,CAAC,IAAI;gBACpB,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;gBAC3B,MAAM;YACR,KAAK,wBAAY,CAAC,KAAK;gBACrB,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;gBAC5B,MAAM;QACV,CAAC;QACD;kFAC0E;QAC1E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;QAC9B,CAAC;QACD,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AAvBW,QAAA,GAAG,OAuBd;AAEF,MAAM,aAAa,GACjB,MAAA,MAAA,OAAO,CAAC,GAAG,CAAC,eAAe,mCAAI,OAAO,CAAC,GAAG,CAAC,UAAU,mCAAI,EAAE,CAAC;AAC9D,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;AACzC,MAAM,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;AAC1C,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;IAClD,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/B,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC;SAAM,CAAC;QACN,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;AACH,CAAC;AACD,MAAM,UAAU,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAE7C,SAAgB,KAAK,CACnB,QAAsB,EACtB,MAAc,EACd,IAAY;IAEZ,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,IAAA,WAAG,EACD,QAAQ,EACR,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACtB,MAAM;YACN,aAAa;YACb,GAAG;YACH,aAAG;YACH,KAAK;YACL,MAAM;YACN,KAAK;YACL,IAAI,CACP,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAgB,eAAe,CAAC,MAAc;IAC5C,OAAO,CACL,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAC3E,CAAC;AACJ,CAAC"}