export import ACM = require('./acm');
export import APIGateway = require('./apigateway');
export import ApplicationAutoScaling = require('./applicationautoscaling');
export import AppStream = require('./appstream');
export import AutoScaling = require('./autoscaling');
export import Batch = require('./batch');
export import Budgets = require('./budgets');
export import CloudDirectory = require('./clouddirectory');
export import CloudFormation = require('./cloudformation');
export import CloudFront = require('./cloudfront');
export import CloudHSM = require('./cloudhsm');
export import CloudSearch = require('./cloudsearch');
export import CloudSearchDomain = require('./cloudsearchdomain');
export import CloudTrail = require('./cloudtrail');
export import CloudWatch = require('./cloudwatch');
export import CloudWatchEvents = require('./cloudwatchevents');
export import CloudWatchLogs = require('./cloudwatchlogs');
export import CodeBuild = require('./codebuild');
export import CodeCommit = require('./codecommit');
export import CodeDeploy = require('./codedeploy');
export import CodePipeline = require('./codepipeline');
export import CognitoIdentity = require('./cognitoidentity');
export import CognitoIdentityServiceProvider = require('./cognitoidentityserviceprovider');
export import CognitoSync = require('./cognitosync');
export import ConfigService = require('./configservice');
export import CUR = require('./cur');
export import DataPipeline = require('./datapipeline');
export import DeviceFarm = require('./devicefarm');
export import DirectConnect = require('./directconnect');
export import DirectoryService = require('./directoryservice');
export import Discovery = require('./discovery');
export import DMS = require('./dms');
export import DynamoDB = require('./dynamodb');
export import DynamoDBStreams = require('./dynamodbstreams');
export import EC2 = require('./ec2');
export import ECR = require('./ecr');
export import ECS = require('./ecs');
export import EFS = require('./efs');
export import ElastiCache = require('./elasticache');
export import ElasticBeanstalk = require('./elasticbeanstalk');
export import ELB = require('./elb');
export import ELBv2 = require('./elbv2');
export import EMR = require('./emr');
export import ES = require('./es');
export import ElasticTranscoder = require('./elastictranscoder');
export import Firehose = require('./firehose');
export import GameLift = require('./gamelift');
export import Glacier = require('./glacier');
export import Health = require('./health');
export import IAM = require('./iam');
export import ImportExport = require('./importexport');
export import Inspector = require('./inspector');
export import Iot = require('./iot');
export import IotData = require('./iotdata');
export import Kinesis = require('./kinesis');
export import KinesisAnalytics = require('./kinesisanalytics');
export import KMS = require('./kms');
export import Lambda = require('./lambda');
export import LexRuntime = require('./lexruntime');
export import Lightsail = require('./lightsail');
export import MachineLearning = require('./machinelearning');
export import MarketplaceCommerceAnalytics = require('./marketplacecommerceanalytics');
export import MarketplaceMetering = require('./marketplacemetering');
export import MTurk = require('./mturk');
export import MobileAnalytics = require('./mobileanalytics');
export import OpsWorks = require('./opsworks');
export import OpsWorksCM = require('./opsworkscm');
export import Organizations = require('./organizations');
export import Pinpoint = require('./pinpoint');
export import Polly = require('./polly');
export import RDS = require('./rds');
export import Redshift = require('./redshift');
export import Rekognition = require('./rekognition');
export import ResourceGroupsTaggingAPI = require('./resourcegroupstaggingapi');
export import Route53 = require('./route53');
export import Route53Domains = require('./route53domains');
export import S3 = require('./s3');
export import S3Control = require('./s3control');
export import ServiceCatalog = require('./servicecatalog');
export import SES = require('./ses');
export import Shield = require('./shield');
export import SimpleDB = require('./simpledb');
export import SMS = require('./sms');
export import Snowball = require('./snowball');
export import SNS = require('./sns');
export import SQS = require('./sqs');
export import SSM = require('./ssm');
export import StorageGateway = require('./storagegateway');
export import StepFunctions = require('./stepfunctions');
export import STS = require('./sts');
export import Support = require('./support');
export import SWF = require('./swf');
export import XRay = require('./xray');
export import WAF = require('./waf');
export import WAFRegional = require('./wafregional');
export import WorkDocs = require('./workdocs');
export import WorkSpaces = require('./workspaces');
export import LexModelBuildingService = require('./lexmodelbuildingservice');
export import MarketplaceEntitlementService = require('./marketplaceentitlementservice');
export import Athena = require('./athena');
export import Greengrass = require('./greengrass');
export import DAX = require('./dax');
export import MigrationHub = require('./migrationhub');
export import CloudHSMV2 = require('./cloudhsmv2');
export import Glue = require('./glue');
export import Pricing = require('./pricing');
export import CostExplorer = require('./costexplorer');
export import MediaConvert = require('./mediaconvert');
export import MediaLive = require('./medialive');
export import MediaPackage = require('./mediapackage');
export import MediaStore = require('./mediastore');
export import MediaStoreData = require('./mediastoredata');
export import AppSync = require('./appsync');
export import GuardDuty = require('./guardduty');
export import MQ = require('./mq');
export import Comprehend = require('./comprehend');
export import IoTJobsDataPlane = require('./iotjobsdataplane');
export import KinesisVideoArchivedMedia = require('./kinesisvideoarchivedmedia');
export import KinesisVideoMedia = require('./kinesisvideomedia');
export import KinesisVideo = require('./kinesisvideo');
export import SageMakerRuntime = require('./sagemakerruntime');
export import SageMaker = require('./sagemaker');
export import Translate = require('./translate');
export import ResourceGroups = require('./resourcegroups');
export import Cloud9 = require('./cloud9');
export import ServerlessApplicationRepository = require('./serverlessapplicationrepository');
export import ServiceDiscovery = require('./servicediscovery');
export import WorkMail = require('./workmail');
export import AutoScalingPlans = require('./autoscalingplans');
export import TranscribeService = require('./transcribeservice');
export import Connect = require('./connect');
export import ACMPCA = require('./acmpca');
export import FMS = require('./fms');
export import SecretsManager = require('./secretsmanager');
export import IoTAnalytics = require('./iotanalytics');
export import IoT1ClickDevicesService = require('./iot1clickdevicesservice');
export import IoT1ClickProjects = require('./iot1clickprojects');
export import PI = require('./pi');
export import Neptune = require('./neptune');
export import MediaTailor = require('./mediatailor');
export import EKS = require('./eks');
export import DLM = require('./dlm');
export import Signer = require('./signer');
export import Chime = require('./chime');
export import PinpointEmail = require('./pinpointemail');
export import RAM = require('./ram');
export import Route53Resolver = require('./route53resolver');
export import PinpointSMSVoice = require('./pinpointsmsvoice');
export import QuickSight = require('./quicksight');
export import RDSDataService = require('./rdsdataservice');
export import Amplify = require('./amplify');
export import DataSync = require('./datasync');
export import RoboMaker = require('./robomaker');
export import Transfer = require('./transfer');
export import GlobalAccelerator = require('./globalaccelerator');
export import ComprehendMedical = require('./comprehendmedical');
export import KinesisAnalyticsV2 = require('./kinesisanalyticsv2');
export import MediaConnect = require('./mediaconnect');
export import FSx = require('./fsx');
export import SecurityHub = require('./securityhub');
export import AppMesh = require('./appmesh');
export import LicenseManager = require('./licensemanager');
export import Kafka = require('./kafka');
export import ApiGatewayManagementApi = require('./apigatewaymanagementapi');
export import ApiGatewayV2 = require('./apigatewayv2');
export import DocDB = require('./docdb');
export import Backup = require('./backup');
export import WorkLink = require('./worklink');
export import Textract = require('./textract');
export import ManagedBlockchain = require('./managedblockchain');
export import MediaPackageVod = require('./mediapackagevod');
export import GroundStation = require('./groundstation');
export import IoTThingsGraph = require('./iotthingsgraph');
export import IoTEvents = require('./iotevents');
export import IoTEventsData = require('./ioteventsdata');
export import Personalize = require('./personalize');
export import PersonalizeEvents = require('./personalizeevents');
export import PersonalizeRuntime = require('./personalizeruntime');
export import ApplicationInsights = require('./applicationinsights');
export import ServiceQuotas = require('./servicequotas');
export import EC2InstanceConnect = require('./ec2instanceconnect');
export import EventBridge = require('./eventbridge');
export import LakeFormation = require('./lakeformation');
export import ForecastService = require('./forecastservice');
export import ForecastQueryService = require('./forecastqueryservice');
export import QLDB = require('./qldb');
export import QLDBSession = require('./qldbsession');
export import WorkMailMessageFlow = require('./workmailmessageflow');
export import CodeStarNotifications = require('./codestarnotifications');
export import SavingsPlans = require('./savingsplans');
export import SSO = require('./sso');
export import SSOOIDC = require('./ssooidc');
export import MarketplaceCatalog = require('./marketplacecatalog');
export import DataExchange = require('./dataexchange');
export import SESV2 = require('./sesv2');
export import MigrationHubConfig = require('./migrationhubconfig');
export import ConnectParticipant = require('./connectparticipant');
export import AppConfig = require('./appconfig');
export import IoTSecureTunneling = require('./iotsecuretunneling');
export import WAFV2 = require('./wafv2');
export import ElasticInference = require('./elasticinference');
export import Imagebuilder = require('./imagebuilder');
export import Schemas = require('./schemas');
export import AccessAnalyzer = require('./accessanalyzer');
export import CodeGuruReviewer = require('./codegurureviewer');
export import CodeGuruProfiler = require('./codeguruprofiler');
export import ComputeOptimizer = require('./computeoptimizer');
export import FraudDetector = require('./frauddetector');
export import Kendra = require('./kendra');
export import NetworkManager = require('./networkmanager');
export import Outposts = require('./outposts');
export import AugmentedAIRuntime = require('./augmentedairuntime');
export import EBS = require('./ebs');
export import KinesisVideoSignalingChannels = require('./kinesisvideosignalingchannels');
export import Detective = require('./detective');
export import CodeStarconnections = require('./codestarconnections');
export import Synthetics = require('./synthetics');
export import IoTSiteWise = require('./iotsitewise');
export import Macie2 = require('./macie2');
export import CodeArtifact = require('./codeartifact');
export import IVS = require('./ivs');
export import Braket = require('./braket');
export import IdentityStore = require('./identitystore');
export import Appflow = require('./appflow');
export import RedshiftData = require('./redshiftdata');
export import SSOAdmin = require('./ssoadmin');
export import TimestreamQuery = require('./timestreamquery');
export import TimestreamWrite = require('./timestreamwrite');
export import S3Outposts = require('./s3outposts');
export import DataBrew = require('./databrew');
export import ServiceCatalogAppRegistry = require('./servicecatalogappregistry');
export import NetworkFirewall = require('./networkfirewall');
export import MWAA = require('./mwaa');
export import AmplifyBackend = require('./amplifybackend');
export import AppIntegrations = require('./appintegrations');
export import ConnectContactLens = require('./connectcontactlens');
export import DevOpsGuru = require('./devopsguru');
export import ECRPUBLIC = require('./ecrpublic');
export import LookoutVision = require('./lookoutvision');
export import SageMakerFeatureStoreRuntime = require('./sagemakerfeaturestoreruntime');
export import CustomerProfiles = require('./customerprofiles');
export import AuditManager = require('./auditmanager');
export import EMRcontainers = require('./emrcontainers');
export import HealthLake = require('./healthlake');
export import SagemakerEdge = require('./sagemakeredge');
export import Amp = require('./amp');
export import GreengrassV2 = require('./greengrassv2');
export import IotDeviceAdvisor = require('./iotdeviceadvisor');
export import IoTFleetHub = require('./iotfleethub');
export import IoTWireless = require('./iotwireless');
export import Location = require('./location');
export import WellArchitected = require('./wellarchitected');
export import LexModelsV2 = require('./lexmodelsv2');
export import LexRuntimeV2 = require('./lexruntimev2');
export import Fis = require('./fis');
export import LookoutMetrics = require('./lookoutmetrics');
export import Mgn = require('./mgn');
export import LookoutEquipment = require('./lookoutequipment');
export import Nimble = require('./nimble');
export import Finspace = require('./finspace');
export import Finspacedata = require('./finspacedata');
export import SSMContacts = require('./ssmcontacts');
export import SSMIncidents = require('./ssmincidents');
export import ApplicationCostProfiler = require('./applicationcostprofiler');
export import AppRunner = require('./apprunner');
export import Proton = require('./proton');
export import Route53RecoveryCluster = require('./route53recoverycluster');
export import Route53RecoveryControlConfig = require('./route53recoverycontrolconfig');
export import Route53RecoveryReadiness = require('./route53recoveryreadiness');
export import ChimeSDKIdentity = require('./chimesdkidentity');
export import ChimeSDKMessaging = require('./chimesdkmessaging');
export import SnowDeviceManagement = require('./snowdevicemanagement');
export import MemoryDB = require('./memorydb');
export import OpenSearch = require('./opensearch');
export import KafkaConnect = require('./kafkaconnect');
export import VoiceID = require('./voiceid');
export import Wisdom = require('./wisdom');
export import Account = require('./account');
export import CloudControl = require('./cloudcontrol');
export import Grafana = require('./grafana');
export import Panorama = require('./panorama');
export import ChimeSDKMeetings = require('./chimesdkmeetings');
export import Resiliencehub = require('./resiliencehub');
export import MigrationHubStrategy = require('./migrationhubstrategy');
export import AppConfigData = require('./appconfigdata');
export import Drs = require('./drs');
export import MigrationHubRefactorSpaces = require('./migrationhubrefactorspaces');
export import Evidently = require('./evidently');
export import Inspector2 = require('./inspector2');
export import Rbin = require('./rbin');
export import RUM = require('./rum');
export import BackupGateway = require('./backupgateway');
export import IoTTwinMaker = require('./iottwinmaker');
export import WorkSpacesWeb = require('./workspacesweb');
export import AmplifyUIBuilder = require('./amplifyuibuilder');
export import Keyspaces = require('./keyspaces');
export import Billingconductor = require('./billingconductor');
export import PinpointSMSVoiceV2 = require('./pinpointsmsvoicev2');
export import Ivschat = require('./ivschat');
export import ChimeSDKMediaPipelines = require('./chimesdkmediapipelines');
export import EMRServerless = require('./emrserverless');
export import M2 = require('./m2');
export import ConnectCampaigns = require('./connectcampaigns');
export import RedshiftServerless = require('./redshiftserverless');
export import RolesAnywhere = require('./rolesanywhere');
export import LicenseManagerUserSubscriptions = require('./licensemanagerusersubscriptions');
export import PrivateNetworks = require('./privatenetworks');
export import SupportApp = require('./supportapp');
export import ControlTower = require('./controltower');
export import IoTFleetWise = require('./iotfleetwise');
export import MigrationHubOrchestrator = require('./migrationhuborchestrator');
export import ConnectCases = require('./connectcases');
export import ResourceExplorer2 = require('./resourceexplorer2');
export import Scheduler = require('./scheduler');
export import ChimeSDKVoice = require('./chimesdkvoice');
export import SsmSap = require('./ssmsap');
export import OAM = require('./oam');
export import ARCZonalShift = require('./arczonalshift');
export import Omics = require('./omics');
export import OpenSearchServerless = require('./opensearchserverless');
export import SecurityLake = require('./securitylake');
export import SimSpaceWeaver = require('./simspaceweaver');
export import DocDBElastic = require('./docdbelastic');
export import SageMakerGeospatial = require('./sagemakergeospatial');
export import CodeCatalyst = require('./codecatalyst');
export import Pipes = require('./pipes');
export import SageMakerMetrics = require('./sagemakermetrics');
export import KinesisVideoWebRTCStorage = require('./kinesisvideowebrtcstorage');
export import LicenseManagerLinuxSubscriptions = require('./licensemanagerlinuxsubscriptions');
export import KendraRanking = require('./kendraranking');
export import CleanRooms = require('./cleanrooms');
export import CloudTrailData = require('./cloudtraildata');
export import Tnb = require('./tnb');
export import InternetMonitor = require('./internetmonitor');
export import IVSRealTime = require('./ivsrealtime');
export import VPCLattice = require('./vpclattice');
export import OSIS = require('./osis');
export import MediaPackageV2 = require('./mediapackagev2');
export import PaymentCryptography = require('./paymentcryptography');
export import PaymentCryptographyData = require('./paymentcryptographydata');
export import CodeGuruSecurity = require('./codegurusecurity');
export import VerifiedPermissions = require('./verifiedpermissions');
export import AppFabric = require('./appfabric');
export import MedicalImaging = require('./medicalimaging');
export import EntityResolution = require('./entityresolution');
export import ManagedBlockchainQuery = require('./managedblockchainquery');
export import Neptunedata = require('./neptunedata');
export import PcaConnectorAd = require('./pcaconnectorad');
export import Bedrock = require('./bedrock');
export import BedrockRuntime = require('./bedrockruntime');
export import DataZone = require('./datazone');
export import LaunchWizard = require('./launchwizard');
export import TrustedAdvisor = require('./trustedadvisor');
export import InspectorScan = require('./inspectorscan');
export import BCMDataExports = require('./bcmdataexports');
export import CostOptimizationHub = require('./costoptimizationhub');
export import EKSAuth = require('./eksauth');
export import FreeTier = require('./freetier');
export import Repostspace = require('./repostspace');
export import WorkSpacesThinClient = require('./workspacesthinclient');
export import B2bi = require('./b2bi');
export import BedrockAgent = require('./bedrockagent');
export import BedrockAgentRuntime = require('./bedrockagentruntime');
export import QBusiness = require('./qbusiness');
export import QConnect = require('./qconnect');
export import CleanRoomsML = require('./cleanroomsml');
export import MarketplaceAgreement = require('./marketplaceagreement');
export import MarketplaceDeployment = require('./marketplacedeployment');
export import NetworkMonitor = require('./networkmonitor');
export import SupplyChain = require('./supplychain');
export import Artifact = require('./artifact');
export import Chatbot = require('./chatbot');
export import TimestreamInfluxDB = require('./timestreaminfluxdb');
export import CodeConnections = require('./codeconnections');
export import Deadline = require('./deadline');
export import ControlCatalog = require('./controlcatalog');
export import Route53Profiles = require('./route53profiles');
export import MailManager = require('./mailmanager');
export import TaxSettings = require('./taxsettings');
export import ApplicationSignals = require('./applicationsignals');
export import PcaConnectorScep = require('./pcaconnectorscep');
export import AppTest = require('./apptest');
export import QApps = require('./qapps');
export import SSMQuickSetup = require('./ssmquicksetup');
export import PCS = require('./pcs');
