{"version": 3, "file": "messageId.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/messageId.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  MessageIdUpdateOptionalParams,\n  MessageIdUpdateResponse,\n  MessageIdDeleteOptionalParams,\n  MessageIdDeleteResponse\n} from \"../models\";\n\n/** Interface representing a MessageId. */\nexport interface MessageId {\n  /**\n   * The Update operation was introduced with version 2011-08-18 of the Queue service API. The Update\n   * Message operation updates the visibility timeout of a message. You can also use this operation to\n   * update the contents of a message. A message must be in a format that can be included in an XML\n   * request with UTF-8 encoding, and the encoded message can be up to 64KB in size.\n   * @param popReceipt Required. Specifies the valid pop receipt value returned from an earlier call to\n   *                   the Get Messages or Update Message operation.\n   * @param visibilityTimeout Optional. Specifies the new visibility timeout value, in seconds, relative\n   *                          to server time. The default value is 30 seconds. A specified value must be larger than or equal to 1\n   *                          second, and cannot be larger than 7 days, or larger than 2 hours on REST protocol versions prior to\n   *                          version 2011-08-18. The visibility timeout of a message can be set to a value later than the expiry\n   *                          time.\n   * @param options The options parameters.\n   */\n  update(\n    popReceipt: string,\n    visibilityTimeout: number,\n    options?: MessageIdUpdateOptionalParams\n  ): Promise<MessageIdUpdateResponse>;\n  /**\n   * The Delete operation deletes the specified message.\n   * @param popReceipt Required. Specifies the valid pop receipt value returned from an earlier call to\n   *                   the Get Messages or Update Message operation.\n   * @param options The options parameters.\n   */\n  delete(\n    popReceipt: string,\n    options?: MessageIdDeleteOptionalParams\n  ): Promise<MessageIdDeleteResponse>;\n}\n"]}