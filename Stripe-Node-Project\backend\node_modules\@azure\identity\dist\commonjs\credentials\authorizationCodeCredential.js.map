{"version": 3, "file": "authorizationCodeCredential.js", "sourceRoot": "", "sources": ["../../../src/credentials/authorizationCodeCredential.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAGlC,+DAGkC;AAElC,+DAAyD;AACzD,mDAAsD;AACtD,yDAAqD;AACrD,mDAAmD;AAEnD,mEAAmE;AAEnE,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,6BAA6B,CAAC,CAAC;AAE/D;;;;;;GAMG;AACH,MAAa,2BAA2B;IAmEtC;;;OAGG;IACH,YACE,QAA2B,EAC3B,QAAgB,EAChB,+BAAuC,EACvC,8BAAsC,EACtC,oBAA6E,EAC7E,OAA4C;QAE5C,IAAA,gCAAa,EAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,+BAA+B,CAAC;QAEpD,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YAC7C,wCAAwC;YACxC,IAAI,CAAC,iBAAiB,GAAG,8BAA8B,CAAC;YACxD,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;YACxC,8CAA8C;QAChD,CAAC;aAAM,CAAC;YACN,gBAAgB;YAChB,IAAI,CAAC,iBAAiB,GAAG,+BAA+B,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,8BAAwC,CAAC;YAC5D,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC;YAC9B,OAAO,GAAG,oBAA0D,CAAC;QACvE,CAAC;QAED,oCAAoC;QACpC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,4BAA4B,GAAG,IAAA,sDAAmC,EACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,IAAA,gCAAgB,EAAC,QAAQ,EAAE,QAAQ,kCAChD,OAAO,KACV,MAAM,EACN,sBAAsB,EAAE,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,IACrC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,0BAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,QAAQ,GAAG,IAAA,4CAAyB,EACxC,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YACF,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE/B,MAAM,WAAW,GAAG,IAAA,4BAAY,EAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAChD,WAAW,EACX,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,YAAY,kCAEZ,UAAU,KACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IAEtE,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AA9ID,kEA8IC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils.js\";\nimport type { AuthorizationCodeCredentialOptions } from \"./authorizationCodeCredentialOptions.js\";\nimport { checkTenantId } from \"../util/tenantIdUtils.js\";\nimport { credentialLogger } from \"../util/logging.js\";\nimport { ensureScopes } from \"../util/scopeUtils.js\";\nimport { tracingClient } from \"../util/tracing.js\";\nimport type { MsalClient } from \"../msal/nodeFlows/msalClient.js\";\nimport { createMsalClient } from \"../msal/nodeFlows/msalClient.js\";\n\nconst logger = credentialLogger(\"AuthorizationCodeCredential\");\n\n/**\n * Enables authentication to Microsoft Entra ID using an authorization code\n * that was obtained through the authorization code flow, described in more detail\n * in the Microsoft Entra ID documentation:\n *\n * https://learn.microsoft.com/entra/identity-platform/v2-oauth2-auth-code-flow\n */\nexport class AuthorizationCodeCredential implements TokenCredential {\n  private msalClient: MsalClient;\n  private disableAutomaticAuthentication?: boolean;\n  private authorizationCode: string;\n  private redirectUri: string;\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private clientSecret?: string;\n\n  /**\n   * Creates an instance of AuthorizationCodeCredential with the details needed\n   * to request an access token using an authentication that was obtained\n   * from Microsoft Entra ID.\n   *\n   * It is currently necessary for the user of this credential to initiate\n   * the authorization code flow to obtain an authorization code to be used\n   * with this credential.  A full example of this flow is provided here:\n   *\n   * https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/identity/identity/samples/v2/manual/authorizationCodeSample.ts\n   *\n   * @param tenantId - The Microsoft Entra tenant (directory) ID or name.\n   *                 'common' may be used when dealing with multi-tenant scenarios.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param clientSecret - A client secret that was generated for the App Registration\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecret: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions,\n  );\n  /**\n   * Creates an instance of AuthorizationCodeCredential with the details needed\n   * to request an access token using an authentication that was obtained\n   * from Microsoft Entra ID.\n   *\n   * It is currently necessary for the user of this credential to initiate\n   * the authorization code flow to obtain an authorization code to be used\n   * with this credential.  A full example of this flow is provided here:\n   *\n   * https://github.com/Azure/azure-sdk-for-js/blob/main/sdk/identity/identity/samples/v2/manual/authorizationCodeSample.ts\n   *\n   * @param tenantId - The Microsoft Entra tenant (directory) ID or name.\n   *                 'common' may be used when dealing with multi-tenant scenarios.\n   * @param clientId - The client (application) ID of an App Registration in the tenant.\n   * @param authorizationCode - An authorization code that was received from following the\n                              authorization code flow.  This authorization code must not\n                              have already been used to obtain an access token.\n   * @param redirectUri - The redirect URI that was used to request the authorization code.\n                        Must be the same URI that is configured for the App Registration.\n   * @param options - Options for configuring the client which makes the access token request.\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    authorizationCode: string,\n    redirectUri: string,\n    options?: AuthorizationCodeCredentialOptions,\n  );\n  /**\n   * @hidden\n   * @internal\n   */\n  constructor(\n    tenantId: string | \"common\",\n    clientId: string,\n    clientSecretOrAuthorizationCode: string,\n    authorizationCodeOrRedirectUri: string,\n    redirectUriOrOptions: string | AuthorizationCodeCredentialOptions | undefined,\n    options?: AuthorizationCodeCredentialOptions,\n  ) {\n    checkTenantId(logger, tenantId);\n    this.clientSecret = clientSecretOrAuthorizationCode;\n\n    if (typeof redirectUriOrOptions === \"string\") {\n      // the clientId+clientSecret constructor\n      this.authorizationCode = authorizationCodeOrRedirectUri;\n      this.redirectUri = redirectUriOrOptions;\n      // in this case, options are good as they come\n    } else {\n      // clientId only\n      this.authorizationCode = clientSecretOrAuthorizationCode;\n      this.redirectUri = authorizationCodeOrRedirectUri as string;\n      this.clientSecret = undefined;\n      options = redirectUriOrOptions as AuthorizationCodeCredentialOptions;\n    }\n\n    // TODO: Validate tenant if provided\n    this.tenantId = tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants,\n    );\n\n    this.msalClient = createMsalClient(clientId, tenantId, {\n      ...options,\n      logger,\n      tokenCredentialOptions: options ?? {},\n    });\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        const tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n        );\n        newOptions.tenantId = tenantId;\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalClient.getTokenByAuthorizationCode(\n          arrayScopes,\n          this.redirectUri,\n          this.authorizationCode,\n          this.clientSecret,\n          {\n            ...newOptions,\n            disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n          },\n        );\n      },\n    );\n  }\n}\n"]}