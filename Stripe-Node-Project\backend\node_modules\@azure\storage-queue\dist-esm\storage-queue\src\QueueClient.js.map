{"version": 3, "file": "QueueClient.js", "sourceRoot": "", "sources": ["../../../src/QueueClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AA8B1C,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAEzD,OAAO,EAAE,aAAa,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAEzE,OAAO,EACL,eAAe,EACf,4BAA4B,EAC5B,iBAAiB,EACjB,oBAAoB,EACpB,gBAAgB,EAChB,cAAc,GACf,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,0BAA0B,EAAE,MAAM,+DAA+D,CAAC;AAC3G,OAAO,EAAE,mBAAmB,EAAE,MAAM,wDAAwD,CAAC;AAC7F,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EACL,+BAA+B,EAC/B,uCAAuC,GACxC,MAAM,2BAA2B,CAAC;AAInC,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AA8WpE;;GAEG;AACH,MAAM,OAAO,WAAY,SAAQ,aAAa;IAY5C;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IA6CD,YACE,qBAA6B,EAC7B,+BAKU;IACV,2FAA2F;IAC3F,iEAAiE;IACjE,OAAgC;QAEhC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,QAAkB,CAAC;QACvB,IAAI,GAAW,CAAC;QAChB,IAAI,cAAc,CAAC,+BAA+B,CAAC,EAAE,CAAC;YACpD,oCAAoC;YACpC,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,+BAA+B,CAAC;QAC7C,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,+BAA+B,YAAY,0BAA0B,CAAC;YACjF,+BAA+B,YAAY,mBAAmB;YAC9D,iBAAiB,CAAC,+BAA+B,CAAC,EAClD,CAAC;YACD,mIAAmI;YACnI,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,WAAW,CAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC;QACnE,CAAC;aAAM,IACL,CAAC,+BAA+B;YAChC,OAAO,+BAA+B,KAAK,QAAQ,EACnD,CAAC;YACD,mIAAmI;YACnI,+DAA+D;YAC/D,GAAG,GAAG,qBAAqB,CAAC;YAC5B,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;aAAM,IACL,+BAA+B;YAC/B,OAAO,+BAA+B,KAAK,QAAQ,EACnD,CAAC;YACD,yGAAyG;YACzG,MAAM,cAAc,GAAG,4BAA4B,CAAC,qBAAqB,CAAC,CAAC;YAC3E,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;gBAChD,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,SAAS,GAAG,+BAA+B,CAAC;oBAClD,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAW,EAC1B,cAAc,CAAC,UAAU,CAC1B,CAAC;oBACF,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;oBAErD,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;wBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;oBAC1E,CAAC;oBAED,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;gBACxF,CAAC;YACH,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBACnD,MAAM,SAAS,GAAG,+BAA+B,CAAC;gBAClD,GAAG,GAAG,eAAe,CAAC,cAAc,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,cAAc,CAAC,UAAU,CAAC;gBACvF,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACxC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAEpD,kBAAkB;QAClB,gCAAgC;QAChC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,CAAC,CAAC;YAC/B,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC;YAClE,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAE/C,IAAI,CAAC,eAAe,GAAG,uBAAuB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;IAC5F,CAAC;IAEO,mBAAmB,CAAC,SAAiB;QAC3C,+BAA+B;QAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,gBAAgB,GAAG,UAAU,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC;YACjE,CAAC,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;QAE9C,OAAO,uBAAuB,CAAC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC;IAC5E,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,KAAK,CAAC,MAAM,CAAC,UAA8B,EAAE;QAClD,OAAO,aAAa,CAAC,QAAQ,CAAC,oBAAoB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACpF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAC/C,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,iBAAiB,CAC5B,UAA8B,EAAE;QAEhC,OAAO,aAAa,CAAC,QAAQ,CAC3B,+BAA+B,EAC/B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBAEnD,iIAAiI;gBACjI,yIAAyI;gBACzI,2GAA2G;gBAC3G,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACtC,uBACE,SAAS,EAAE,KAAK,IACb,QAAQ,EACX;gBACJ,CAAC;gBACD,uBACE,SAAS,EAAE,IAAI,IACZ,QAAQ,EACX;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,oBAAoB,EAAE,CAAC;oBAClD,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;gBAED,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACI,KAAK,CAAC,cAAc,CACzB,UAA8B,EAAE;QAEhC,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC5F,IAAI,CAAC;gBACH,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;gBAC9C,uBACE,SAAS,EAAE,IAAI,IACZ,GAAG,EACN;YACJ,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAA,MAAA,CAAC,CAAC,OAAO,0CAAE,SAAS,MAAK,eAAe,EAAE,CAAC;oBAC7C,qCACE,SAAS,EAAE,KAAK,IACb,MAAA,CAAC,CAAC,QAAQ,0CAAE,aAAa,KAC5B,SAAS,EAAE,CAAC,CAAC,QAAQ,IACrB;gBACJ,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,KAAK,CAAC,MAAM,CAAC,UAA8B,EAAE;QAClD,OAAO,aAAa,CAAC,QAAQ,CAAC,oBAAoB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACpF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;gBAC7B,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,MAAM,CAAC,UAA8B,EAAE;QAClD,OAAO,aAAa,CAAC,QAAQ,CAAC,oBAAoB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACpF,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;gBACzC,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;oBACzB,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,MAAM,CAAC,CAAC;YACV,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,KAAK,CAAC,aAAa,CACxB,UAAqC,EAAE;QAEvC,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,cAAc,CAAC,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,WAAW,CACtB,QAAmB,EACnB,UAAmC,EAAE;QAErC,OAAO,aAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACzF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,iCAC9B,cAAc,KACjB,QAAQ,IACR,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,eAAe,CAC1B,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;gBACtC,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YAEF,MAAM,GAAG,GAAiC;gBACxC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,iBAAiB,EAAE,EAAE;gBACrB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC;YAEF,KAAK,MAAM,UAAU,IAAI,QAAQ,EAAE,CAAC;gBAClC,IAAI,YAAY,GAAQ,SAAS,CAAC;gBAClC,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;oBAC5B,YAAY,GAAG;wBACb,WAAW,EAAE,UAAU,CAAC,YAAY,CAAC,WAAW;qBACjD,CAAC;oBAEF,IAAI,UAAU,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;wBACtC,YAAY,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;oBACvE,CAAC;oBAED,IAAI,UAAU,CAAC,YAAY,CAAC,QAAQ,EAAE,CAAC;wBACrC,YAAY,CAAC,QAAQ,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;gBAED,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC;oBACzB,YAAY;oBACZ,EAAE,EAAE,UAAU,CAAC,EAAE;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,eAAe,CAC1B,QAA6B,EAC7B,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,GAAG,GAA4B,EAAE,CAAC;YACxC,KAAK,MAAM,UAAU,IAAI,QAAQ,IAAI,EAAE,EAAE,CAAC;gBACxC,GAAG,CAAC,IAAI,CAAC;oBACP,YAAY,EAAE;wBACZ,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC,SAAS;4BAC1C,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC;4BACzD,CAAC,CAAC,SAAS;wBACb,WAAW,EAAE,UAAU,CAAC,YAAY,CAAC,WAAW;wBAChD,QAAQ,EAAE,UAAU,CAAC,YAAY,CAAC,QAAQ;4BACxC,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC;4BACxD,CAAC,CAAC,SAAS;qBACd;oBACD,EAAE,EAAE,UAAU,CAAC,EAAE;iBAClB,CAAC,CAAC;YACL,CAAC;YAED,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,iCAClC,cAAc,KACjB,QAAQ,EAAE,GAAG,IACb,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,aAAa,CACxB,UAAqC,EAAE;QAEvC,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,cAAc,CAAC,CACjD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACI,KAAK,CAAC,WAAW,CACtB,WAAmB,EACnB,UAAmC,EAAE;QAErC,OAAO,aAAa,CAAC,QAAQ,CAAC,yBAAyB,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YACzF,MAAM,QAAQ,GAAG,cAAc,CAK7B,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAChC;gBACE,WAAW,EAAE,WAAW;aACzB,EACD,cAAc,CACf,CACF,CAAC;YACF,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,OAAO;gBACL,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,KAAK,CAAC,eAAe,CAC1B,UAAsC,EAAE;QAExC,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,MAAM,QAAQ,GAAG,cAAc,CAI7B,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC;YAEtD,MAAM,GAAG,GAAgC;gBACvC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,oBAAoB,EAAE,EAAE;gBACxB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC5B,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,KAAK,CAAC,YAAY,CACvB,UAAoC,EAAE;QAEtC,OAAO,aAAa,CAAC,QAAQ,CAAC,0BAA0B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC1F,MAAM,QAAQ,GAAG,cAAc,CAI7B,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;YAEnD,MAAM,GAAG,GAA8B;gBACrC,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS;gBAC7B,eAAe,EAAE,QAAQ,CAAC,eAAe;gBACzC,kBAAkB,EAAE,EAAE;gBACtB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;aAC9B,CAAC;YAEF,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;gBAC5B,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CACxB,SAAiB,EACjB,UAAkB,EAClB,UAAqC,EAAE;QAEvC,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,CAC7E,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACI,KAAK,CAAC,aAAa,CACxB,SAAiB,EACjB,UAAkB,EAClB,OAAgB,EAChB,iBAA0B,EAC1B,UAAqC,EAAE;QAEvC,OAAO,aAAa,CAAC,QAAQ,CAAC,2BAA2B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;YAC3F,IAAI,YAAY,GAAG,SAAS,CAAC;YAC7B,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,YAAY,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC;YAC1C,CAAC;YACD,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,iBAAiB,IAAI,CAAC,EAAE;gBACnF,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,cAAc,EAAE,cAAc,CAAC,cAAc;gBAC7C,YAAY;aACb,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB;QACzB,IAAI,SAAS,CAAC;QACd,IAAI,CAAC;YACH,mCAAmC;YACnC,gEAAgE;YAChE,sDAAsD;YACtD,yFAAyF;YACzF,oDAAoD;YAEpD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpC,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE,CAAC;gBACjD,wDAAwD;gBACxD,2BAA2B;gBAC3B,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;iBAAM,IAAI,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAAC;gBACxC,0FAA0F;gBAC1F,oHAAoH;gBACpH,4CAA4C;gBAC5C,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,wCAAwC;gBACxC,2BAA2B;gBAC3B,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/C,CAAC;YAED,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACI,cAAc,CAAC,OAAmC;QACvD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,CACd,uFAAuF,CACxF,CAAC;QACJ,CAAC;QAED,MAAM,GAAG,GAAG,+BAA+B,iBAEvC,SAAS,EAAE,IAAI,CAAC,IAAI,IACjB,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,QAAQ,EAAE,CAAC;QAEb,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;;;OAUG;IACH,gEAAgE;IACzD,uBAAuB,CAAC,OAAmC;QAChE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,CACd,uFAAuF,CACxF,CAAC;QACJ,CAAC;QAED,OAAO,uCAAuC,iBAE1C,SAAS,EAAE,IAAI,CAAC,IAAI,IACjB,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,YAAY,CAAC;IACjB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\nimport { isNode } from \"@azure/core-util\";\nimport type {\n  EnqueuedMessage,\n  DequeuedMessageItem,\n  MessagesDequeueHeaders,\n  MessagesEnqueueHeaders,\n  MessagesPeekHeaders,\n  MessageIdUpdateResponse,\n  MessageIdDeleteResponse,\n  MessagesClearResponse,\n  PeekedMessageItem,\n  QueueCreateHeaders,\n  QueueDeleteResponse,\n  QueueGetAccessPolicyHeaders,\n  QueueGetPropertiesResponse,\n  QueueSetAccessPolicyResponse,\n  QueueSetMetadataResponse,\n  SignedIdentifierModel,\n  QueueCreateResponse,\n  QueueDeleteHeaders,\n  QueueSetMetadataHeaders,\n  QueueGetPropertiesHeaders,\n  QueueSetAccessPolicyHeaders,\n  MessagesClearHeaders,\n  MessageIdDeleteHeaders,\n  MessageIdUpdateHeaders,\n} from \"./generatedModels\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { Messages, MessageId, Queue } from \"./generated/src/operationsInterfaces\";\nimport type { StoragePipelineOptions, Pipeline } from \"./Pipeline\";\nimport { newPipeline, isPipelineLike } from \"./Pipeline\";\nimport type { CommonOptions } from \"./StorageClient\";\nimport { StorageClient, getStorageClientContext } from \"./StorageClient\";\nimport type { WithResponse } from \"./utils/utils.common\";\nimport {\n  appendToURLPath,\n  extractConnectionStringParts,\n  isIpEndpointStyle,\n  truncatedISO8061Date,\n  appendToURLQuery,\n  assertResponse,\n} from \"./utils/utils.common\";\nimport { StorageSharedKeyCredential } from \"../../storage-blob/src/credentials/StorageSharedKeyCredential\";\nimport { AnonymousCredential } from \"../../storage-blob/src/credentials/AnonymousCredential\";\nimport { tracingClient } from \"./utils/tracing\";\nimport type { Metadata } from \"./models\";\nimport {\n  generateQueueSASQueryParameters,\n  generateQueueSASQueryParametersInternal,\n} from \"./QueueSASSignatureValues\";\nimport type { SasIPRange } from \"./SasIPRange\";\nimport type { QueueSASPermissions } from \"./QueueSASPermissions\";\nimport type { SASProtocol } from \"./SASQueryParameters\";\nimport { getDefaultProxySettings } from \"@azure/core-rest-pipeline\";\n\n/**\n * Options to configure {@link QueueClient.create} operation\n */\nexport interface QueueCreateOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * A collection of key-value string pair to associate with the queue object.\n   * The keys need to be lower-case.\n   */\n  metadata?: Metadata;\n}\n\n/**\n * Options to configure {@link QueueClient.exists} operation\n */\nexport interface QueueExistsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueClient.getProperties} operation\n */\nexport interface QueueGetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueClient.delete} operation\n */\nexport interface QueueDeleteOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueClient.getAccessPolicy} operation\n */\nexport interface QueueGetAccessPolicyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueClient.setAccessPolicy} operation\n */\nexport interface QueueSetAccessPolicyOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueClient.setMetadata} operation\n */\nexport interface QueueSetMetadataOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Signed identifier.\n */\nexport interface SignedIdentifier {\n  /**\n   * a unique id\n   */\n  id: string;\n  /**\n   * Access Policy\n   */\n  accessPolicy: {\n    /**\n     * the date-time the policy is active.\n     */\n    startsOn?: Date;\n    /**\n     * the date-time the policy expires.\n     */\n    expiresOn?: Date;\n    /**\n     * the permissions for the acl policy\n     * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-queue-acl\n     */\n    permissions?: string;\n  };\n}\n\n/**\n * Contains response data for the {@link QueueClient.getAccessPolicy} operation.\n */\nexport declare type QueueGetAccessPolicyResponse = WithResponse<\n  {\n    signedIdentifiers: SignedIdentifier[];\n  } & QueueGetAccessPolicyHeaders,\n  QueueGetAccessPolicyHeaders,\n  SignedIdentifierModel[]\n>;\n\n/**\n * Options to configure {@link QueueClient.clearMessages} operation\n */\nexport interface QueueClearMessagesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/** Optional parameters. */\nexport interface MessagesEnqueueOptionalParams extends CommonOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://learn.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations\">Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. If specified, the request must be made using an x-ms-version of 2011-08-18 or later. If not specified, the default value is 0. Specifies the new visibility timeout value, in seconds, relative to server time. The new value must be larger than or equal to 0, and cannot be larger than 7 days. The visibility timeout of a message cannot be set to a value later than the expiry time. visibilitytimeout should be set to a value smaller than the time-to-live value. */\n  visibilityTimeout?: number;\n  /** Optional. Specifies the time-to-live interval for the message, in seconds. Prior to version 2017-07-29, the maximum time-to-live allowed is 7 days. For version 2017-07-29 or later, the maximum time-to-live can be any positive number, as well as -1 indicating that the message does not expire. If this parameter is omitted, the default time-to-live is 7 days. */\n  messageTimeToLive?: number;\n}\n\n/**\n * Options to configure {@link QueueClient.sendMessage} operation\n */\nexport interface QueueSendMessageOptions extends MessagesEnqueueOptionalParams, CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/** Optional parameters. */\nexport interface MessagesDequeueOptionalParams extends CommonOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://learn.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations\">Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. A nonzero integer value that specifies the number of messages to retrieve from the queue, up to a maximum of 32. If fewer are visible, the visible messages are returned. By default, a single message is retrieved from the queue with this operation. */\n  numberOfMessages?: number;\n  /** Optional. Specifies the new visibility timeout value, in seconds, relative to server time. The default value is 30 seconds. A specified value must be larger than or equal to 1 second, and cannot be larger than 7 days, or larger than 2 hours on REST protocol versions prior to version 2011-08-18. The visibility timeout of a message can be set to a value later than the expiry time. */\n  visibilityTimeout?: number;\n}\n\n/**\n * Options to configure {@link QueueClient.receiveMessages} operation\n */\nexport interface QueueReceiveMessageOptions extends MessagesDequeueOptionalParams, CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/** Optional parameters. */\nexport interface MessagesPeekOptionalParams extends CommonOptions {\n  /** The The timeout parameter is expressed in seconds. For more information, see <a href=\"https://learn.microsoft.com/en-us/rest/api/storageservices/setting-timeouts-for-queue-service-operations\">Setting Timeouts for Queue Service Operations.</a> */\n  timeoutInSeconds?: number;\n  /** Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled. */\n  requestId?: string;\n  /** Optional. A nonzero integer value that specifies the number of messages to retrieve from the queue, up to a maximum of 32. If fewer are visible, the visible messages are returned. By default, a single message is retrieved from the queue with this operation. */\n  numberOfMessages?: number;\n}\n\n/**\n * Options to configure {@link QueueClient.peekMessages} operation\n */\nexport interface QueuePeekMessagesOptions extends MessagesPeekOptionalParams, CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Contains the response data for the {@link QueueClient.sendMessage} operation.\n */\nexport declare type QueueSendMessageResponse = WithResponse<\n  {\n    /**\n     * The ID of the sent Message.\n     */\n    messageId: string;\n    /**\n     * This value is required to delete the Message.\n     * If deletion fails using this popreceipt then the message has been received\n     * by another client.\n     */\n    popReceipt: string;\n    /**\n     * The time that the message was inserted into the\n     * Queue.\n     */\n    insertedOn: Date;\n    /**\n     * The time that the message will expire and be\n     * automatically deleted.\n     */\n    expiresOn: Date;\n    /**\n     * The time that the message will again become\n     * visible in the Queue.\n     */\n    nextVisibleOn: Date;\n  } & MessagesEnqueueHeaders,\n  MessagesEnqueueHeaders,\n  EnqueuedMessage[]\n>;\n\n/**\n * The object returned in the `receivedMessageItems` array when calling {@link QueueClient.receiveMessages}.\n *\n * See: {@link QueueReceiveMessageResponse}\n */\nexport declare type ReceivedMessageItem = DequeuedMessageItem;\n\n/**\n * Contains the response data for the {@link QueueClient.receiveMessages} operation.\n */\nexport declare type QueueReceiveMessageResponse = WithResponse<\n  {\n    receivedMessageItems: ReceivedMessageItem[];\n  } & MessagesDequeueHeaders,\n  MessagesDequeueHeaders,\n  ReceivedMessageItem[]\n>;\n\n/**\n * Contains the response data for the {@link QueueClient.peekMessages} operation.\n */\nexport declare type QueuePeekMessagesResponse = WithResponse<\n  {\n    peekedMessageItems: PeekedMessageItem[];\n  } & MessagesPeekHeaders,\n  MessagesPeekHeaders,\n  PeekedMessageItem[]\n>;\n\n/**\n * Options to configure the {@link QueueClient.deleteMessage} operation\n */\nexport interface QueueDeleteMessageOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Contains response data for the {@link QueueClient.updateMessage} operation.\n */\nexport declare type QueueUpdateMessageResponse = MessageIdUpdateResponse;\n\n/**\n * Contains response data for the {@link QueueClient.deleteMessage} operation.\n */\nexport declare type QueueDeleteMessageResponse = MessageIdDeleteResponse;\n\n/**\n * Contains response data for the {@link QueueClient.clearMessages} operation.\n */\nexport declare type QueueClearMessagesResponse = MessagesClearResponse;\n\n/**\n * Options to configure {@link QueueClient.updateMessage} operation\n */\nexport interface QueueUpdateMessageOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Contains response data for the {@link QueueClient.createIfNotExists} operation.\n */\nexport interface QueueCreateIfNotExistsResponse extends QueueCreateResponse {\n  /**\n   * Indicate whether the queue is successfully created. Is false when the queue is not changed as it already exists.\n   */\n  succeeded: boolean;\n}\n\n/**\n * Contains response data for the {@link QueueClient.deleteIfExists} operation.\n */\nexport interface QueueDeleteIfExistsResponse extends QueueDeleteResponse {\n  /**\n   * Indicate whether the queue is successfully deleted. Is false if the queue does not exist in the first place.\n   */\n  succeeded: boolean;\n}\n\n/**\n * Options to configure {@link QueueClient.generateSasUrl} operation.\n */\nexport interface QueueGenerateSasUrlOptions {\n  /**\n   * The version of the service this SAS will target. If not specified, it will default to the version targeted by the\n   * library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols, HTTPS only or HTTPSandHTTP\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n\n  /**\n   * Optional only when identifier is provided. The time after which the SAS will no longer work.\n   */\n  expiresOn?: Date;\n\n  /**\n   * Optional only when identifier is provided.\n   * Please refer to {@link QueueSASPermissions} for help constructing the permissions string.\n   */\n  permissions?: QueueSASPermissions;\n\n  /**\n   * Optional. IP ranges allowed in this SAS.\n   */\n  ipRange?: SasIPRange;\n\n  /**\n   * Optional. The name of the access policy on the queue this SAS references if any.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  identifier?: string;\n}\n\n/**\n * A QueueClient represents a URL to an Azure Storage Queue's messages allowing you to manipulate its messages.\n */\nexport class QueueClient extends StorageClient {\n  /**\n   * messagesContext provided by protocol layer.\n   */\n  private messagesContext: Messages;\n  /**\n   * queueContext provided by protocol layer.\n   */\n  private queueContext: Queue;\n  private _name: string;\n  private _messagesUrl: string;\n\n  /**\n   * The name of the queue.\n   */\n  public get name(): string {\n    return this._name;\n  }\n\n  /**\n   * Creates an instance of QueueClient.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param queueName - Queue name.\n   * @param options - Options to configure the HTTP pipeline.\n   */\n  // Legacy, no way to fix the eslint error without breaking. Disable the rule for this line.\n  /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n  constructor(connectionString: string, queueName: string, options?: StoragePipelineOptions);\n  /**\n   * Creates an instance of QueueClient.\n   *\n   * @param url - A URL string pointing to Azure Storage queue, such as\n   *                     \"https://myaccount.queue.core.windows.net/myqueue\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.queue.core.windows.net/myqueue?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Options to configure the HTTP pipeline.\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no way to fix the eslint error without breaking. Disable the rule for this line.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of QueueClient.\n   *\n   * @param url - A URL string pointing to Azure Storage queue, such as\n   *                     \"https://myaccount.queue.core.windows.net/myqueue\". You can\n   *                     append a SAS if using AnonymousCredential, such as\n   *                     \"https://myaccount.queue.core.windows.net/myqueue?sasString\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: Pipeline);\n  constructor(\n    urlOrConnectionString: string,\n    credentialOrPipelineOrQueueName?:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | Pipeline\n      | string,\n    // Legacy, no way to fix the eslint error without breaking. Disable the rule for this line.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: StoragePipelineOptions,\n  ) {\n    options = options || {};\n    let pipeline: Pipeline;\n    let url: string;\n    if (isPipelineLike(credentialOrPipelineOrQueueName)) {\n      // (url: string, pipeline: Pipeline)\n      url = urlOrConnectionString;\n      pipeline = credentialOrPipelineOrQueueName;\n    } else if (\n      (isNode && credentialOrPipelineOrQueueName instanceof StorageSharedKeyCredential) ||\n      credentialOrPipelineOrQueueName instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipelineOrQueueName)\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      url = urlOrConnectionString;\n      pipeline = newPipeline(credentialOrPipelineOrQueueName, options);\n    } else if (\n      !credentialOrPipelineOrQueueName &&\n      typeof credentialOrPipelineOrQueueName !== \"string\"\n    ) {\n      // (url: string, credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential, options?: StoragePipelineOptions)\n      // The second parameter is undefined. Use anonymous credential.\n      url = urlOrConnectionString;\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    } else if (\n      credentialOrPipelineOrQueueName &&\n      typeof credentialOrPipelineOrQueueName === \"string\"\n    ) {\n      // (connectionString: string, containerName: string, queueName: string, options?: StoragePipelineOptions)\n      const extractedCreds = extractConnectionStringParts(urlOrConnectionString);\n      if (extractedCreds.kind === \"AccountConnString\") {\n        if (isNode) {\n          const queueName = credentialOrPipelineOrQueueName;\n          const sharedKeyCredential = new StorageSharedKeyCredential(\n            extractedCreds.accountName,\n            extractedCreds.accountKey,\n          );\n          url = appendToURLPath(extractedCreds.url, queueName);\n\n          if (!options.proxyOptions) {\n            options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n          }\n\n          pipeline = newPipeline(sharedKeyCredential, options);\n        } else {\n          throw new Error(\"Account connection string is only supported in Node.js environment\");\n        }\n      } else if (extractedCreds.kind === \"SASConnString\") {\n        const queueName = credentialOrPipelineOrQueueName;\n        url = appendToURLPath(extractedCreds.url, queueName) + \"?\" + extractedCreds.accountSas;\n        pipeline = newPipeline(new AnonymousCredential(), options);\n      } else {\n        throw new Error(\n          \"Connection string must be either an Account connection string or a SAS connection string\",\n        );\n      }\n    } else {\n      throw new Error(\"Expecting non-empty strings for queueName parameter\");\n    }\n    super(url, pipeline);\n    this._name = this.getQueueNameFromUrl();\n    this.queueContext = this.storageClientContext.queue;\n\n    // MessagesContext\n    // Build the url with \"messages\"\n    const partsOfUrl = this.url.split(\"?\");\n    this._messagesUrl = partsOfUrl[1]\n      ? appendToURLPath(partsOfUrl[0], \"messages\") + \"?\" + partsOfUrl[1]\n      : appendToURLPath(partsOfUrl[0], \"messages\");\n\n    this.messagesContext = getStorageClientContext(this._messagesUrl, this.pipeline).messages;\n  }\n\n  private getMessageIdContext(messageId: string): MessageId {\n    // Build the url with messageId\n    const partsOfUrl = this._messagesUrl.split(\"?\");\n    const urlWithMessageId = partsOfUrl[1]\n      ? appendToURLPath(partsOfUrl[0], messageId) + \"?\" + partsOfUrl[1]\n      : appendToURLPath(partsOfUrl[0], messageId);\n\n    return getStorageClientContext(urlWithMessageId, this.pipeline).messageId;\n  }\n\n  /**\n   * Creates a new queue under the specified account.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-queue4\n   *\n   * @param options - Options to Queue create operation.\n   * @returns Response data for the Queue create operation.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const queueClient = queueServiceClient.getQueueClient(\"<new queue name>\");\n   * const createQueueResponse = await queueClient.create();\n   * ```\n   */\n  public async create(options: QueueCreateOptions = {}): Promise<QueueCreateResponse> {\n    return tracingClient.withSpan(\"QueueClient-create\", options, async (updatedOptions) => {\n      return assertResponse<QueueCreateHeaders, QueueCreateHeaders>(\n        await this.queueContext.create(updatedOptions),\n      );\n    });\n  }\n\n  /**\n   * Creates a new queue under the specified account if it doesn't already exist.\n   * If the queue already exists, it is not changed.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-queue4\n   *\n   * @param options -\n   */\n  public async createIfNotExists(\n    options: QueueCreateOptions = {},\n  ): Promise<QueueCreateIfNotExistsResponse> {\n    return tracingClient.withSpan(\n      \"QueueClient-createIfNotExists\",\n      options,\n      async (updatedOptions) => {\n        try {\n          const response = await this.create(updatedOptions);\n\n          // When a queue with the specified name already exists, the Queue service checks the metadata associated with the existing queue.\n          // If the existing metadata is identical to the metadata specified on the Create Queue request, status code 204 (No Content) is returned.\n          // If the existing metadata does not match, the operation fails and status code 409 (Conflict) is returned.\n          if (response._response.status === 204) {\n            return {\n              succeeded: false,\n              ...response,\n            };\n          }\n          return {\n            succeeded: true,\n            ...response,\n          };\n        } catch (e: any) {\n          if (e.details?.errorCode === \"QueueAlreadyExists\") {\n            return {\n              succeeded: false,\n              ...e.response?.parsedHeaders,\n              _response: e.response,\n            };\n          }\n\n          throw e;\n        }\n      },\n    );\n  }\n\n  /**\n   * Deletes the specified queue permanently if it exists.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-queue3\n   *\n   * @param options -\n   */\n  public async deleteIfExists(\n    options: QueueDeleteOptions = {},\n  ): Promise<QueueDeleteIfExistsResponse> {\n    return tracingClient.withSpan(\"QueueClient-deleteIfExists\", options, async (updatedOptions) => {\n      try {\n        const res = await this.delete(updatedOptions);\n        return {\n          succeeded: true,\n          ...res,\n        };\n      } catch (e: any) {\n        if (e.details?.errorCode === \"QueueNotFound\") {\n          return {\n            succeeded: false,\n            ...e.response?.parsedHeaders,\n            _response: e.response,\n          };\n        }\n        throw e;\n      }\n    });\n  }\n\n  /**\n   * Deletes the specified queue permanently.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-queue3\n   *\n   * @param options - Options to Queue delete operation.\n   * @returns Response data for the Queue delete operation.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const deleteQueueResponse = await queueClient.delete();\n   * console.log(\n   *   \"Delete queue successfully, service assigned request Id:\", deleteQueueResponse.requestId\n   * );\n   * ```\n   */\n  public async delete(options: QueueDeleteOptions = {}): Promise<QueueDeleteResponse> {\n    return tracingClient.withSpan(\"QueueClient-delete\", options, async (updatedOptions) => {\n      return assertResponse<QueueDeleteHeaders, QueueDeleteHeaders>(\n        await this.queueContext.delete({\n          abortSignal: options.abortSignal,\n          tracingOptions: updatedOptions.tracingOptions,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Returns true if the specified queue exists; false otherwise.\n   *\n   * NOTE: use this function with care since an existing queue might be deleted by other clients or\n   * applications. Vice versa new queues might be added by other clients or applications after this\n   * function completes.\n   *\n   * @param options - options to Exists operation.\n   */\n  public async exists(options: QueueExistsOptions = {}): Promise<boolean> {\n    return tracingClient.withSpan(\"QueueClient-exists\", options, async (updatedOptions) => {\n      try {\n        await this.getProperties(updatedOptions);\n        return true;\n      } catch (e: any) {\n        if (e.statusCode === 404) {\n          return false;\n        }\n        throw e;\n      }\n    });\n  }\n\n  /**\n   * Gets all user-defined metadata and system properties for the specified\n   * queue. Metadata is associated with the queue as name-values pairs.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-queue-metadata\n   *\n   * WARNING: The `metadata` object returned in the response will have its keys in lowercase, even if\n   * they originally contained uppercase characters. This differs from the metadata keys returned by\n   * the `listQueues` method of {@link QueueServiceClient} using the `includeMetadata` option, which\n   * will retain their original casing.\n   *\n   * @param options - Options to Queue get properties operation.\n   * @returns Response data for the Queue get properties operation.\n   */\n  public async getProperties(\n    options: QueueGetPropertiesOptions = {},\n  ): Promise<QueueGetPropertiesResponse> {\n    return tracingClient.withSpan(\"QueueClient-getProperties\", options, async (updatedOptions) => {\n      return assertResponse<QueueGetPropertiesHeaders, QueueGetPropertiesHeaders>(\n        await this.queueContext.getProperties(updatedOptions),\n      );\n    });\n  }\n\n  /**\n   * Sets one or more user-defined name-value pairs for the specified queue.\n   *\n   * If no option provided, or no metadata defined in the option parameter, the queue\n   * metadata will be removed.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-queue-metadata\n   *\n   * @param metadata - If no metadata provided, all existing metadata will be removed.\n   * @param options - Options to Queue set metadata operation.\n   * @returns Response data for the Queue set metadata operation.\n   */\n  public async setMetadata(\n    metadata?: Metadata,\n    options: QueueSetMetadataOptions = {},\n  ): Promise<QueueSetMetadataResponse> {\n    return tracingClient.withSpan(\"QueueClient-setMetadata\", options, async (updatedOptions) => {\n      return assertResponse<QueueSetMetadataHeaders, QueueSetMetadataHeaders>(\n        await this.queueContext.setMetadata({\n          ...updatedOptions,\n          metadata,\n        }),\n      );\n    });\n  }\n\n  /**\n   * Gets details about any stored access policies specified on the queue that may be used with Shared Access Signatures.\n   *\n   * WARNING: JavaScript Date will potential lost precision when parsing start and expiry string.\n   * For example, new Date(\"2018-12-31T03:44:23.8827891Z\").toISOString() will get \"2018-12-31T03:44:23.882Z\".\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-queue-acl\n   *\n   * @param options - Options to Queue get access policy operation.\n   * @returns Response data for the Queue get access policy operation.\n   */\n  public async getAccessPolicy(\n    options: QueueGetAccessPolicyOptions = {},\n  ): Promise<QueueGetAccessPolicyResponse> {\n    return tracingClient.withSpan(\n      \"QueueClient-getAccessPolicy\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          QueueGetAccessPolicyHeaders & SignedIdentifierModel[],\n          QueueGetAccessPolicyHeaders,\n          SignedIdentifierModel[]\n        >(\n          await this.queueContext.getAccessPolicy({\n            abortSignal: options.abortSignal,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n\n        const res: QueueGetAccessPolicyResponse = {\n          _response: response._response,\n          date: response.date,\n          requestId: response.requestId,\n          clientRequestId: response.clientRequestId,\n          signedIdentifiers: [],\n          version: response.version,\n          errorCode: response.errorCode,\n        };\n\n        for (const identifier of response) {\n          let accessPolicy: any = undefined;\n          if (identifier.accessPolicy) {\n            accessPolicy = {\n              permissions: identifier.accessPolicy.permissions,\n            };\n\n            if (identifier.accessPolicy.expiresOn) {\n              accessPolicy.expiresOn = new Date(identifier.accessPolicy.expiresOn);\n            }\n\n            if (identifier.accessPolicy.startsOn) {\n              accessPolicy.startsOn = new Date(identifier.accessPolicy.startsOn);\n            }\n          }\n\n          res.signedIdentifiers.push({\n            accessPolicy,\n            id: identifier.id,\n          });\n        }\n\n        return res;\n      },\n    );\n  }\n\n  /**\n   * Sets stored access policies for the queue that may be used with Shared Access Signatures.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-queue-acl\n   *\n   * @param queueAcl -\n   * @param options - Options to Queue set access policy operation.\n   * @returns Response data for the Queue set access policy operation.\n   */\n  public async setAccessPolicy(\n    queueAcl?: SignedIdentifier[],\n    options: QueueSetAccessPolicyOptions = {},\n  ): Promise<QueueSetAccessPolicyResponse> {\n    return tracingClient.withSpan(\n      \"QueueClient-setAccessPolicy\",\n      options,\n      async (updatedOptions) => {\n        const acl: SignedIdentifierModel[] = [];\n        for (const identifier of queueAcl || []) {\n          acl.push({\n            accessPolicy: {\n              expiresOn: identifier.accessPolicy.expiresOn\n                ? truncatedISO8061Date(identifier.accessPolicy.expiresOn)\n                : undefined,\n              permissions: identifier.accessPolicy.permissions,\n              startsOn: identifier.accessPolicy.startsOn\n                ? truncatedISO8061Date(identifier.accessPolicy.startsOn)\n                : undefined,\n            },\n            id: identifier.id,\n          });\n        }\n\n        return assertResponse<QueueSetAccessPolicyHeaders, QueueSetAccessPolicyHeaders>(\n          await this.queueContext.setAccessPolicy({\n            ...updatedOptions,\n            queueAcl: acl,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Clear deletes all messages from a queue.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/clear-messages\n   *\n   * @param options - Options to clear messages operation.\n   * @returns Response data for the clear messages operation.\n   */\n  public async clearMessages(\n    options: QueueClearMessagesOptions = {},\n  ): Promise<QueueClearMessagesResponse> {\n    return tracingClient.withSpan(\"QueueClient-clearMessages\", options, async (updatedOptions) => {\n      return assertResponse<MessagesClearHeaders, MessagesClearHeaders>(\n        await this.messagesContext.clear(updatedOptions),\n      );\n    });\n  }\n\n  /**\n   * sendMessage adds a new message to the back of a queue. The visibility timeout specifies how long\n   * the message should be invisible to Dequeue and Peek operations.\n   * The message content is up to 64KB in size, and must be in a format that can be included in an XML request with UTF-8 encoding.\n   * To include markup in the message, the contents of the message must either be XML-escaped or Base64-encode.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/put-message\n   *\n   * @param messageText - Text of the message to send\n   * @param options - Options to send messages operation.\n   * @returns Response data for the send messages operation.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const sendMessageResponse = await queueClient.sendMessage(\"Hello World!\");\n   * console.log(\n   *   \"Sent message successfully, service assigned message Id:\", sendMessageResponse.messageId,\n   *   \"service assigned request Id:\", sendMessageResponse.requestId\n   * );\n   * ```\n   */\n  public async sendMessage(\n    messageText: string,\n    options: QueueSendMessageOptions = {},\n  ): Promise<QueueSendMessageResponse> {\n    return tracingClient.withSpan(\"QueueClient-sendMessage\", options, async (updatedOptions) => {\n      const response = assertResponse<\n        MessagesEnqueueHeaders & EnqueuedMessage[],\n        MessagesEnqueueHeaders,\n        EnqueuedMessage[]\n      >(\n        await this.messagesContext.enqueue(\n          {\n            messageText: messageText,\n          },\n          updatedOptions,\n        ),\n      );\n      const item = response[0];\n      return {\n        _response: response._response,\n        date: response.date,\n        requestId: response.requestId,\n        clientRequestId: response.clientRequestId,\n        version: response.version,\n        errorCode: response.errorCode,\n        messageId: item.messageId,\n        popReceipt: item.popReceipt,\n        nextVisibleOn: item.nextVisibleOn,\n        insertedOn: item.insertedOn,\n        expiresOn: item.expiresOn,\n      };\n    });\n  }\n\n  /**\n   * receiveMessages retrieves one or more messages from the front of the queue.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-messages\n   *\n   * @param options - Options to receive messages operation.\n   * @returns Response data for the receive messages operation.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const response = await queueClient.receiveMessages();\n   * if (response.receivedMessageItems.length == 1) {\n   *   const receivedMessageItem = response.receivedMessageItems[0];\n   *   console.log(\"Processing & deleting message with content:\", receivedMessageItem.messageText);\n   *   const deleteMessageResponse = await queueClient.deleteMessage(\n   *     receivedMessageItem.messageId,\n   *     receivedMessageItem.popReceipt\n   *   );\n   *   console.log(\n   *     \"Delete message successfully, service assigned request Id:\",\n   *     deleteMessageResponse.requestId\n   *   );\n   * }\n   * ```\n   */\n  public async receiveMessages(\n    options: QueueReceiveMessageOptions = {},\n  ): Promise<QueueReceiveMessageResponse> {\n    return tracingClient.withSpan(\n      \"QueueClient-receiveMessages\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<\n          MessagesDequeueHeaders & DequeuedMessageItem[],\n          MessagesDequeueHeaders,\n          DequeuedMessageItem[]\n        >(await this.messagesContext.dequeue(updatedOptions));\n\n        const res: QueueReceiveMessageResponse = {\n          _response: response._response,\n          date: response.date,\n          requestId: response.requestId,\n          clientRequestId: response.clientRequestId,\n          receivedMessageItems: [],\n          version: response.version,\n          errorCode: response.errorCode,\n        };\n\n        for (const item of response) {\n          res.receivedMessageItems.push(item);\n        }\n\n        return res;\n      },\n    );\n  }\n\n  /**\n   * peekMessages retrieves one or more messages from the front of the queue but does not alter the visibility of the message.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/peek-messages\n   *\n   * @param options - Options to peek messages operation.\n   * @returns Response data for the peek messages operation.\n   *\n   * Example usage:\n   *\n   * ```js\n   * const peekMessagesResponse = await queueClient.peekMessages();\n   * console.log(\"The peeked message is:\", peekMessagesResponse.peekedMessageItems[0].messageText);\n   * ```\n   */\n  public async peekMessages(\n    options: QueuePeekMessagesOptions = {},\n  ): Promise<QueuePeekMessagesResponse> {\n    return tracingClient.withSpan(\"QueueClient-peekMessages\", options, async (updatedOptions) => {\n      const response = assertResponse<\n        MessagesPeekHeaders & PeekedMessageItem[],\n        MessagesPeekHeaders,\n        PeekedMessageItem[]\n      >(await this.messagesContext.peek(updatedOptions));\n\n      const res: QueuePeekMessagesResponse = {\n        _response: response._response,\n        date: response.date,\n        requestId: response.requestId,\n        clientRequestId: response.clientRequestId,\n        peekedMessageItems: [],\n        version: response.version,\n        errorCode: response.errorCode,\n      };\n\n      for (const item of response) {\n        res.peekedMessageItems.push(item);\n      }\n\n      return res;\n    });\n  }\n\n  /**\n   * deleteMessage permanently removes the specified message from its queue.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-message2\n   *\n   * @param messageId - Id of the message.\n   * @param popReceipt - A valid pop receipt value returned from an earlier call to the receive messages or update message operation.\n   * @param options - Options to delete message operation.\n   * @returns Response data for the delete message operation.\n   */\n  public async deleteMessage(\n    messageId: string,\n    popReceipt: string,\n    options: QueueDeleteMessageOptions = {},\n  ): Promise<QueueDeleteMessageResponse> {\n    return tracingClient.withSpan(\"QueueClient-deleteMessage\", options, async (updatedOptions) => {\n      return assertResponse<MessageIdDeleteHeaders, MessageIdDeleteHeaders>(\n        await this.getMessageIdContext(messageId).delete(popReceipt, updatedOptions),\n      );\n    });\n  }\n\n  /**\n   * Update changes a message's visibility timeout and contents.\n   * The message content is up to 64KB in size, and must be in a format that can be included in an XML request with UTF-8 encoding.\n   * To include markup in the message, the contents of the message must either be XML-escaped or Base64-encode.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/update-message\n   *\n   * @param messageId - Id of the message\n   * @param popReceipt - A valid pop receipt value returned from an earlier call to the receive messages or update message operation.\n   * @param message - Message to update. If this parameter is undefined, then the content of the message won't be updated.\n   * @param visibilityTimeout - Specifies the new visibility timeout value, in seconds,\n   *                                   relative to server time. The new value must be larger than or equal to 0,\n   *                                   and cannot be larger than 7 days. The visibility timeout of a message cannot\n   *                                   be set to a value later than the expiry time.\n   *                                   A message can be updated until it has been deleted or has expired.\n   * @param options - Options to update message operation.\n   * @returns Response data for the update message operation.\n   */\n  public async updateMessage(\n    messageId: string,\n    popReceipt: string,\n    message?: string,\n    visibilityTimeout?: number,\n    options: QueueUpdateMessageOptions = {},\n  ): Promise<QueueUpdateMessageResponse> {\n    return tracingClient.withSpan(\"QueueClient-updateMessage\", options, async (updatedOptions) => {\n      let queueMessage = undefined;\n      if (message !== undefined) {\n        queueMessage = { messageText: message };\n      }\n      return assertResponse<MessageIdUpdateHeaders, MessageIdUpdateHeaders>(\n        await this.getMessageIdContext(messageId).update(popReceipt, visibilityTimeout || 0, {\n          abortSignal: options.abortSignal,\n          tracingOptions: updatedOptions.tracingOptions,\n          queueMessage,\n        }),\n      );\n    });\n  }\n\n  private getQueueNameFromUrl(): string {\n    let queueName;\n    try {\n      //  URL may look like the following\n      // \"https://myaccount.queue.core.windows.net/myqueue?sasString\".\n      // \"https://myaccount.queue.core.windows.net/myqueue\".\n      // IPv4/IPv6 address hosts, Endpoints - `http://127.0.0.1:10001/devstoreaccount1/myqueue`\n      // http://localhost:10001/devstoreaccount1/queuename\n\n      const parsedUrl = new URL(this.url);\n\n      if (parsedUrl.hostname.split(\".\")[1] === \"queue\") {\n        // \"https://myaccount.queue.core.windows.net/queuename\".\n        // .getPath() -> /queuename\n        queueName = parsedUrl.pathname.split(\"/\")[1];\n      } else if (isIpEndpointStyle(parsedUrl)) {\n        // IPv4/IPv6 address hosts... Example - http://**********:10001/devstoreaccount1/queuename\n        // Single word domain without a [dot] in the endpoint... Example - http://localhost:10001/devstoreaccount1/queuename\n        // .getPath() -> /devstoreaccount1/queuename\n        queueName = parsedUrl.pathname.split(\"/\")[2];\n      } else {\n        // \"https://customdomain.com/queuename\".\n        // .getPath() -> /queuename\n        queueName = parsedUrl.pathname.split(\"/\")[1];\n      }\n\n      if (!queueName) {\n        throw new Error(\"Provided queueName is invalid.\");\n      }\n\n      return queueName;\n    } catch (error: any) {\n      throw new Error(\"Unable to extract queueName with provided information.\");\n    }\n  }\n\n  /**\n   * Only available for QueueClient constructed with a shared key credential.\n   *\n   * Generates a Service Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateSasUrl(options: QueueGenerateSasUrlOptions): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw RangeError(\n        \"Can only generate the SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    const sas = generateQueueSASQueryParameters(\n      {\n        queueName: this.name,\n        ...options,\n      },\n      this.credential,\n    ).toString();\n\n    return appendToURLQuery(this.url, sas);\n  }\n\n  /**\n   * Only available for QueueClient constructed with a shared key credential.\n   *\n   * Generates string to sign for a Service Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-a-service-sas\n   *\n   * @param options - Optional parameters.\n   * @returns The SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options*/\n  public generateSasStringToSign(options: QueueGenerateSasUrlOptions): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw RangeError(\n        \"Can only generate the SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    return generateQueueSASQueryParametersInternal(\n      {\n        queueName: this.name,\n        ...options,\n      },\n      this.credential,\n    ).stringToSign;\n  }\n}\n"]}