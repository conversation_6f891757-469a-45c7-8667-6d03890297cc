{"version": 3, "file": "decompressResponsePolicy-browser.mjs", "sourceRoot": "", "sources": ["../../../src/policies/decompressResponsePolicy-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;GAEG;AAEH,MAAM,CAAC,MAAM,4BAA4B,GAAG,0BAA0B,CAAC;AAEvE;;;GAGG;AACH,MAAM,UAAU,wBAAwB;IACtC,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAC;AACtF,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/*\n * NOTE: When moving this file, please update \"browser\" section in package.json\n */\n\nexport const decompressResponsePolicyName = \"decompressResponsePolicy\";\n\n/**\n * decompressResponsePolicy is not supported in the browser and attempting\n * to use it will raise an error.\n */\nexport function decompressResponsePolicy(): never {\n  throw new Error(\"decompressResponsePolicy is not supported in browser environment\");\n}\n"]}