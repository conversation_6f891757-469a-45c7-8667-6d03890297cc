{"version": 3, "file": "retryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/retryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAoB,kBAAkB,EAAE,MAAM,eAAe,CAAC;AACrE,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AAE7D,OAAO,EACL,WAAW,IAAI,cAAc,GAE9B,MAAM,6CAA6C,CAAC;AAIrD,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,gCAAgC,CAAC,CAAC;AA4E/E;;GAEG;AACH,MAAM,UAAU,WAAW,CACzB,UAA2B,EAC3B,UAA8B,EAAE,UAAU,EAAE,0BAA0B,EAAE;IAExE,mFAAmF;IACnF,4EAA4E;IAC5E,sDAAsD;IACtD,OAAO,cAAc,CAAC,UAAgC,kBACpD,MAAM,EAAE,iBAAiB,IACtB,OAAO,EACV,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { type AzureLogger, createClientLogger } from \"@azure/logger\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\nimport {\n  retryPolicy as tspRetryPolicy,\n  type RetryStrategy as TspRetryStrategy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\nimport type { PipelineResponse } from \"../interfaces.js\";\nimport type { RestError } from \"../restError.js\";\n\nconst retryPolicyLogger = createClientLogger(\"core-rest-pipeline retryPolicy\");\n\n/**\n * Information provided to the retry strategy about the current progress of the retry policy.\n */\nexport interface RetryInformation {\n  /**\n   * A {@link PipelineResponse}, if the last retry attempt succeeded.\n   */\n  response?: PipelineResponse;\n  /**\n   * A {@link RestError}, if the last retry attempt failed.\n   */\n  responseError?: RestError;\n  /**\n   * Total number of retries so far.\n   */\n  retryCount: number;\n}\n\n/**\n * Properties that can modify the behavior of the retry policy.\n */\nexport interface RetryModifiers {\n  /**\n   * If true, allows skipping the current strategy from running on the retry policy.\n   */\n  skipStrategy?: boolean;\n  /**\n   * Indicates to retry against this URL.\n   */\n  redirectTo?: string;\n  /**\n   * Controls whether to retry in a given number of milliseconds.\n   * If provided, a new retry will be attempted.\n   */\n  retryAfterInMs?: number;\n  /**\n   * Indicates to throw this error instead of retrying.\n   */\n  errorToThrow?: RestError;\n}\n\n/**\n * A retry strategy is intended to define whether to retry or not, and how to retry.\n */\nexport interface RetryStrategy {\n  /**\n   * Name of the retry strategy. Used for logging.\n   */\n  name: string;\n  /**\n   * Logger. If it's not provided, a default logger for all retry strategies is used.\n   */\n  logger?: AzureLogger;\n  /**\n   * Function that determines how to proceed with the subsequent requests.\n   * @param state - Retry state\n   */\n  retry(state: RetryInformation): RetryModifiers;\n}\n\n/**\n * Options to the {@link retryPolicy}\n */\nexport interface RetryPolicyOptions {\n  /**\n   * Maximum number of retries. If not specified, it will limit to 3 retries.\n   */\n  maxRetries?: number;\n  /**\n   * Logger. If it's not provided, a default logger is used.\n   */\n  logger?: AzureLogger;\n}\n\n/**\n * retryPolicy is a generic policy to enable retrying requests when certain conditions are met\n */\nexport function retryPolicy(\n  strategies: RetryStrategy[],\n  options: RetryPolicyOptions = { maxRetries: DEFAULT_RETRY_POLICY_COUNT },\n): PipelinePolicy {\n  // Cast is required since the TSP runtime retry strategy type is slightly different\n  // very deep down (using real AbortSignal vs. AbortSignalLike in RestError).\n  // In practice the difference doesn't actually matter.\n  return tspRetryPolicy(strategies as TspRetryStrategy[], {\n    logger: retryPolicyLogger,\n    ...options,\n  });\n}\n"]}