{"version": 3, "file": "StorageRetryPolicyV2.js", "sourceRoot": "", "sources": ["../../../../src/policies/StorageRetryPolicyV2.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAOrD,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AACnE,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAEnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAC;AAC3E,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAEhC;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;AAE3D;;GAEG;AACH,MAAM,CAAN,IAAY,sBASX;AATD,WAAY,sBAAsB;IAChC;;OAEG;IACH,iFAAW,CAAA;IACX;;OAEG;IACH,qEAAK,CAAA;AACP,CAAC,EATW,sBAAsB,KAAtB,sBAAsB,QASjC;AAED,wCAAwC;AACxC,MAAM,qBAAqB,GAAG;IAC5B,iBAAiB,EAAE,GAAG,GAAG,IAAI;IAC7B,QAAQ,EAAE,CAAC;IACX,cAAc,EAAE,CAAC,GAAG,IAAI;IACxB,eAAe,EAAE,sBAAsB,CAAC,WAAW;IACnD,aAAa,EAAE,EAAE;IACjB,cAAc,EAAE,SAAS,EAAE,2CAA2C;CAC9D,CAAC;AAEX,MAAM,eAAe,GAAG;IACtB,WAAW;IACX,iBAAiB;IACjB,cAAc;IACd,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,SAAS;IACT,OAAO;IACP,oBAAoB;CACZ,CAAC;AAEX,MAAM,iBAAiB,GAAG,IAAI,UAAU,CAAC,4BAA4B,CAAC,CAAC;AAEvE;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,UAA+B,EAAE;;IAClE,MAAM,eAAe,GAAG,MAAA,OAAO,CAAC,eAAe,mCAAI,qBAAqB,CAAC,eAAe,CAAC;IACzF,MAAM,QAAQ,GAAG,MAAA,OAAO,CAAC,QAAQ,mCAAI,qBAAqB,CAAC,QAAQ,CAAC;IACpE,MAAM,cAAc,GAAG,MAAA,OAAO,CAAC,cAAc,mCAAI,qBAAqB,CAAC,cAAc,CAAC;IACtF,MAAM,iBAAiB,GAAG,MAAA,OAAO,CAAC,iBAAiB,mCAAI,qBAAqB,CAAC,iBAAiB,CAAC;IAC/F,MAAM,aAAa,GAAG,MAAA,OAAO,CAAC,aAAa,mCAAI,qBAAqB,CAAC,aAAa,CAAC;IACnF,MAAM,cAAc,GAAG,MAAA,OAAO,CAAC,cAAc,mCAAI,qBAAqB,CAAC,cAAc,CAAC;IAEtF,SAAS,WAAW,CAAC,EACnB,cAAc,EACd,OAAO,EACP,QAAQ,EACR,KAAK,GAMN;;QACC,IAAI,OAAO,IAAI,QAAQ,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,2BAA2B,OAAO,gBAAgB,QAAQ,mBAAmB,CAAC,CAAC;YAC3F,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC7C,IACE,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACjD,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACpD,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,KAAK,cAAc,CAAC,EACtE,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC,8BAA8B,cAAc,qBAAqB,CAAC,CAAC;oBAC/E,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,IACE,CAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,MAAK,aAAa;iBAC7B,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,OAAO,CAAC,UAAU,CAAC,iCAAiC,CAAC,CAAA,EAC5D,CAAC;gBACD,MAAM,CAAC,IAAI,CACT,iFAAiF,CAClF,CAAC;gBACF,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,kFAAkF;QAClF,gFAAgF;QAChF,gEAAgE;QAChE,IAAI,QAAQ,IAAI,KAAK,EAAE,CAAC;YACtB,MAAM,UAAU,GAAG,MAAA,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,MAAM,mCAAI,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,UAAU,mCAAI,CAAC,CAAC;YAC9D,IAAI,CAAC,cAAc,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;gBACnE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,0CAA0C;YAC1C,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC7C,MAAM,CAAC,IAAI,CAAC,2CAA2C,UAAU,GAAG,CAAC,CAAC;gBACtE,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,wGAAwG;QACxG,kBAAkB;QAClB,6CAA6C;QAC7C,mCAAmC;QACnC,8FAA8F;QAC9F,2CAA2C;QAC3C,mCAAmC;QACnC,gCAAgC;QAChC,oCAAoC;QACpC,6BAA6B;QAC7B,yBAAyB;QACzB,UAAU;QACV,QAAQ;QACR,MAAM;QACN,IAAI;QAEJ,OAAO,KAAK,CAAC;IACf,CAAC;IACD,SAAS,cAAc,CAAC,cAAuB,EAAE,OAAe;QAC9D,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,IAAI,cAAc,EAAE,CAAC;YACnB,QAAQ,eAAe,EAAE,CAAC;gBACxB,KAAK,sBAAsB,CAAC,WAAW;oBACrC,aAAa,GAAG,IAAI,CAAC,GAAG,CACtB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,EAC/C,iBAAiB,CAClB,CAAC;oBACF,MAAM;gBACR,KAAK,sBAAsB,CAAC,KAAK;oBAC/B,aAAa,GAAG,cAAc,CAAC;oBAC/B,MAAM;YACV,CAAC;QACH,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;QACvC,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,0BAA0B,aAAa,IAAI,CAAC,CAAC;QACzD,OAAO,aAAa,CAAC;IACvB,CAAC;IACD,OAAO;QACL,IAAI,EAAE,sBAAsB;QAC5B,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;YAC3D,kEAAkE;YAClE,IAAI,cAAc,EAAE,CAAC;gBACnB,OAAO,CAAC,GAAG,GAAG,eAAe,CAC3B,OAAO,CAAC,GAAG,EACX,YAAY,CAAC,UAAU,CAAC,OAAO,EAC/B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC,CAC1C,CAAC;YACJ,CAAC;YACD,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;YAC/B,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YACxF,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,IAAI,OAAO,GAAG,CAAC,CAAC;YAChB,IAAI,UAAU,GAAG,IAAI,CAAC;YACtB,IAAI,QAAsC,CAAC;YAC3C,IAAI,KAA4B,CAAC;YACjC,OAAO,UAAU,EAAE,CAAC;gBAClB,MAAM,cAAc,GAClB,eAAe;oBACf,CAAC,YAAY;oBACb,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;oBACpD,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC;gBACpB,OAAO,CAAC,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAa,CAAC;gBAC1D,QAAQ,GAAG,SAAS,CAAC;gBACrB,KAAK,GAAG,SAAS,CAAC;gBAClB,IAAI,CAAC;oBACH,MAAM,CAAC,IAAI,CACT,2BAA2B,OAAO,IAAI,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CACjF,CAAC;oBACF,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,eAAe,GAAG,eAAe,IAAI,CAAC,CAAC,cAAc,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;gBACpF,CAAC;gBAAC,OAAO,CAAU,EAAE,CAAC;oBACpB,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;wBACnB,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC,OAAO,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;wBAClF,KAAK,GAAG,CAAC,CAAC;oBACZ,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,uCAAuC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC1E,MAAM,CAAC,CAAC;oBACV,CAAC;gBACH,CAAC;gBACD,UAAU,GAAG,WAAW,CAAC,EAAE,cAAc,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC;gBACvE,IAAI,UAAU,EAAE,CAAC;oBACf,MAAM,KAAK,CACT,cAAc,CAAC,cAAc,EAAE,OAAO,CAAC,EACvC,OAAO,CAAC,WAAW,EACnB,iBAAiB,CAClB,CAAC;gBACJ,CAAC;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC;YAClB,CAAC;YACD,MAAM,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC;QAC1E,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"@azure/abort-controller\";\nimport type {\n  PipelinePolicy,\n  PipelineRequest,\n  SendRequest,\n  PipelineResponse,\n} from \"@azure/core-rest-pipeline\";\nimport { isRestError, RestError } from \"@azure/core-rest-pipeline\";\nimport { getErrorMessage } from \"@azure/core-util\";\nimport type { StorageRetryOptions } from \"../StorageRetryPolicyFactory\";\nimport { URLConstants } from \"../utils/constants\";\nimport { delay, setURLHost, setURLParameter } from \"../utils/utils.common\";\nimport { logger } from \"../log\";\n\n/**\n * Name of the {@link storageRetryPolicy}\n */\nexport const storageRetryPolicyName = \"storageRetryPolicy\";\n\n/**\n * RetryPolicy types.\n */\nexport enum StorageRetryPolicyType {\n  /**\n   * Exponential retry. Retry time delay grows exponentially.\n   */\n  EXPONENTIAL,\n  /**\n   * Linear retry. Retry time delay grows linearly.\n   */\n  FIXED,\n}\n\n// Default values of StorageRetryOptions\nconst DEFAULT_RETRY_OPTIONS = {\n  maxRetryDelayInMs: 120 * 1000,\n  maxTries: 4,\n  retryDelayInMs: 4 * 1000,\n  retryPolicyType: StorageRetryPolicyType.EXPONENTIAL,\n  secondaryHost: \"\",\n  tryTimeoutInMs: undefined, // Use server side default timeout strategy\n} as const;\n\nconst retriableErrors = [\n  \"ETIMEDOUT\",\n  \"ESOCKETTIMEDOUT\",\n  \"ECONNREFUSED\",\n  \"ECONNRESET\",\n  \"ENOENT\",\n  \"ENOTFOUND\",\n  \"TIMEOUT\",\n  \"EPIPE\",\n  \"REQUEST_SEND_ERROR\",\n] as const;\n\nconst RETRY_ABORT_ERROR = new AbortError(\"The operation was aborted.\");\n\n/**\n * Retry policy with exponential retry and linear retry implemented.\n */\nexport function storageRetryPolicy(options: StorageRetryOptions = {}): PipelinePolicy {\n  const retryPolicyType = options.retryPolicyType ?? DEFAULT_RETRY_OPTIONS.retryPolicyType;\n  const maxTries = options.maxTries ?? DEFAULT_RETRY_OPTIONS.maxTries;\n  const retryDelayInMs = options.retryDelayInMs ?? DEFAULT_RETRY_OPTIONS.retryDelayInMs;\n  const maxRetryDelayInMs = options.maxRetryDelayInMs ?? DEFAULT_RETRY_OPTIONS.maxRetryDelayInMs;\n  const secondaryHost = options.secondaryHost ?? DEFAULT_RETRY_OPTIONS.secondaryHost;\n  const tryTimeoutInMs = options.tryTimeoutInMs ?? DEFAULT_RETRY_OPTIONS.tryTimeoutInMs;\n\n  function shouldRetry({\n    isPrimaryRetry,\n    attempt,\n    response,\n    error,\n  }: {\n    isPrimaryRetry: boolean;\n    attempt: number;\n    response?: PipelineResponse;\n    error?: RestError;\n  }): boolean {\n    if (attempt >= maxTries) {\n      logger.info(`RetryPolicy: Attempt(s) ${attempt} >= maxTries ${maxTries}, no further try.`);\n      return false;\n    }\n    if (error) {\n      for (const retriableError of retriableErrors) {\n        if (\n          error.name.toUpperCase().includes(retriableError) ||\n          error.message.toUpperCase().includes(retriableError) ||\n          (error.code && error.code.toString().toUpperCase() === retriableError)\n        ) {\n          logger.info(`RetryPolicy: Network error ${retriableError} found, will retry.`);\n          return true;\n        }\n      }\n      if (\n        error?.code === \"PARSE_ERROR\" &&\n        error?.message.startsWith(`Error \"Error: Unclosed root tag`)\n      ) {\n        logger.info(\n          \"RetryPolicy: Incomplete XML response likely due to service timeout, will retry.\",\n        );\n        return true;\n      }\n    }\n\n    // If attempt was against the secondary & it returned a StatusNotFound (404), then\n    // the resource was not found. This may be due to replication delay. So, in this\n    // case, we'll never try the secondary again for this operation.\n    if (response || error) {\n      const statusCode = response?.status ?? error?.statusCode ?? 0;\n      if (!isPrimaryRetry && statusCode === 404) {\n        logger.info(`RetryPolicy: Secondary access with 404, will retry.`);\n        return true;\n      }\n\n      // Server internal error or server timeout\n      if (statusCode === 503 || statusCode === 500) {\n        logger.info(`RetryPolicy: Will retry for status code ${statusCode}.`);\n        return true;\n      }\n    }\n\n    // [Copy source error code] Feature is pending on service side, skip retry on copy source error for now.\n    // if (response) {\n    //   // Retry select Copy Source Error Codes.\n    //   if (response?.status >= 400) {\n    //     const copySourceError = response.headers.get(HeaderConstants.X_MS_CopySourceErrorCode);\n    //     if (copySourceError !== undefined) {\n    //       switch (copySourceError) {\n    //         case \"InternalError\":\n    //         case \"OperationTimedOut\":\n    //         case \"ServerBusy\":\n    //           return true;\n    //       }\n    //     }\n    //   }\n    // }\n\n    return false;\n  }\n  function calculateDelay(isPrimaryRetry: boolean, attempt: number): number {\n    let delayTimeInMs = 0;\n\n    if (isPrimaryRetry) {\n      switch (retryPolicyType) {\n        case StorageRetryPolicyType.EXPONENTIAL:\n          delayTimeInMs = Math.min(\n            (Math.pow(2, attempt - 1) - 1) * retryDelayInMs,\n            maxRetryDelayInMs,\n          );\n          break;\n        case StorageRetryPolicyType.FIXED:\n          delayTimeInMs = retryDelayInMs;\n          break;\n      }\n    } else {\n      delayTimeInMs = Math.random() * 1000;\n    }\n\n    logger.info(`RetryPolicy: Delay for ${delayTimeInMs}ms`);\n    return delayTimeInMs;\n  }\n  return {\n    name: storageRetryPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Set the server-side timeout query parameter \"timeout=[seconds]\"\n      if (tryTimeoutInMs) {\n        request.url = setURLParameter(\n          request.url,\n          URLConstants.Parameters.TIMEOUT,\n          String(Math.floor(tryTimeoutInMs / 1000)),\n        );\n      }\n      const primaryUrl = request.url;\n      const secondaryUrl = secondaryHost ? setURLHost(request.url, secondaryHost) : undefined;\n      let secondaryHas404 = false;\n      let attempt = 1;\n      let retryAgain = true;\n      let response: PipelineResponse | undefined;\n      let error: RestError | undefined;\n      while (retryAgain) {\n        const isPrimaryRetry: boolean =\n          secondaryHas404 ||\n          !secondaryUrl ||\n          ![\"GET\", \"HEAD\", \"OPTIONS\"].includes(request.method) ||\n          attempt % 2 === 1;\n        request.url = isPrimaryRetry ? primaryUrl : secondaryUrl!;\n        response = undefined;\n        error = undefined;\n        try {\n          logger.info(\n            `RetryPolicy: =====> Try=${attempt} ${isPrimaryRetry ? \"Primary\" : \"Secondary\"}`,\n          );\n          response = await next(request);\n          secondaryHas404 = secondaryHas404 || (!isPrimaryRetry && response.status === 404);\n        } catch (e: unknown) {\n          if (isRestError(e)) {\n            logger.error(`RetryPolicy: Caught error, message: ${e.message}, code: ${e.code}`);\n            error = e;\n          } else {\n            logger.error(`RetryPolicy: Caught error, message: ${getErrorMessage(e)}`);\n            throw e;\n          }\n        }\n        retryAgain = shouldRetry({ isPrimaryRetry, attempt, response, error });\n        if (retryAgain) {\n          await delay(\n            calculateDelay(isPrimaryRetry, attempt),\n            request.abortSignal,\n            RETRY_ABORT_ERROR,\n          );\n        }\n        attempt++;\n      }\n      if (response) {\n        return response;\n      }\n      throw error ?? new RestError(\"RetryPolicy failed without known error.\");\n    },\n  };\n}\n"]}