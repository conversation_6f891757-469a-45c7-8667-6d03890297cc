{"version": 3, "file": "messageId.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/messageId.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AASnD,6CAA6C;AAC7C,MAAM,OAAO,aAAa;IAGxB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;;;;;;;;;;;OAaG;IACH,MAAM,CACJ,UAAkB,EAClB,iBAAyB,EACzB,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,UAAU,EAAE,iBAAiB,EAAE,OAAO,EAAE,EAC1C,mBAAmB,CACpB,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,MAAM,CACJ,UAAkB,EAClB,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,UAAU,EAAE,OAAO,EAAE,EACvB,mBAAmB,CACpB,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,mCAAmC;IACzC,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,WAAW,EAAE,UAAU,CAAC,aAAa;IACrC,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,UAAU;QACrB,UAAU,CAAC,kBAAkB;KAC9B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,mCAAmC;IACzC,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,UAAU,CAAC;IACrE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { MessageId } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  MessageIdUpdateOptionalParams,\n  MessageIdUpdateResponse,\n  MessageIdDeleteOptionalParams,\n  MessageIdDeleteResponse\n} from \"../models\";\n\n/** Class containing MessageId operations. */\nexport class MessageIdImpl implements MessageId {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class MessageId class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * The Update operation was introduced with version 2011-08-18 of the Queue service API. The Update\n   * Message operation updates the visibility timeout of a message. You can also use this operation to\n   * update the contents of a message. A message must be in a format that can be included in an XML\n   * request with UTF-8 encoding, and the encoded message can be up to 64KB in size.\n   * @param popReceipt Required. Specifies the valid pop receipt value returned from an earlier call to\n   *                   the Get Messages or Update Message operation.\n   * @param visibilityTimeout Optional. Specifies the new visibility timeout value, in seconds, relative\n   *                          to server time. The default value is 30 seconds. A specified value must be larger than or equal to 1\n   *                          second, and cannot be larger than 7 days, or larger than 2 hours on REST protocol versions prior to\n   *                          version 2011-08-18. The visibility timeout of a message can be set to a value later than the expiry\n   *                          time.\n   * @param options The options parameters.\n   */\n  update(\n    popReceipt: string,\n    visibilityTimeout: number,\n    options?: MessageIdUpdateOptionalParams\n  ): Promise<MessageIdUpdateResponse> {\n    return this.client.sendOperationRequest(\n      { popReceipt, visibilityTimeout, options },\n      updateOperationSpec\n    );\n  }\n\n  /**\n   * The Delete operation deletes the specified message.\n   * @param popReceipt Required. Specifies the valid pop receipt value returned from an earlier call to\n   *                   the Get Messages or Update Message operation.\n   * @param options The options parameters.\n   */\n  delete(\n    popReceipt: string,\n    options?: MessageIdDeleteOptionalParams\n  ): Promise<MessageIdDeleteResponse> {\n    return this.client.sendOperationRequest(\n      { popReceipt, options },\n      deleteOperationSpec\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst updateOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}/messages/{messageid}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {\n      headersMapper: Mappers.MessageIdUpdateHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.MessageIdUpdateExceptionHeaders\n    }\n  },\n  requestBody: Parameters.queueMessage1,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.popReceipt,\n    Parameters.visibilityTimeout1\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer\n};\nconst deleteOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}/messages/{messageid}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {\n      headersMapper: Mappers.MessageIdDeleteHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.MessageIdDeleteExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.popReceipt],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\n"]}