{"version": 3, "file": "defaultRetryPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/defaultRetryPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,wBAAwB,EAAE,MAAM,gDAAgD,CAAC;AAC1F,OAAO,EAAE,uBAAuB,EAAE,MAAM,+CAA+C,CAAC;AACxF,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,0BAA0B,EAAE,MAAM,iBAAiB,CAAC;AAE7D;;GAEG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;AAO3D;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,UAAqC,EAAE;;IACxE,OAAO;QACL,IAAI,EAAE,sBAAsB;QAC5B,WAAW,EAAE,WAAW,CAAC,CAAC,uBAAuB,EAAE,EAAE,wBAAwB,CAAC,OAAO,CAAC,CAAC,EAAE;YACvF,UAAU,EAAE,MAAA,OAAO,CAAC,UAAU,mCAAI,0BAA0B;SAC7D,CAAC,CAAC,WAAW;KACf,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelineRetryOptions } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { exponentialRetryStrategy } from \"../retryStrategies/exponentialRetryStrategy.js\";\nimport { throttlingRetryStrategy } from \"../retryStrategies/throttlingRetryStrategy.js\";\nimport { retryPolicy } from \"./retryPolicy.js\";\nimport { DEFAULT_RETRY_POLICY_COUNT } from \"../constants.js\";\n\n/**\n * Name of the {@link defaultRetryPolicy}\n */\nexport const defaultRetryPolicyName = \"defaultRetryPolicy\";\n\n/**\n * Options that control how to retry failed requests.\n */\nexport interface DefaultRetryPolicyOptions extends PipelineRetryOptions {}\n\n/**\n * A policy that retries according to three strategies:\n * - When the server sends a 429 response with a Retry-After header.\n * - When there are errors in the underlying transport layer (e.g. DNS lookup failures).\n * - Or otherwise if the outgoing request fails, it will retry with an exponentially increasing delay.\n */\nexport function defaultRetryPolicy(options: DefaultRetryPolicyOptions = {}): PipelinePolicy {\n  return {\n    name: defaultRetryPolicyName,\n    sendRequest: retryPolicy([throttlingRetryStrategy(), exponentialRetryStrategy(options)], {\n      maxRetries: options.maxRetries ?? DEFAULT_RETRY_POLICY_COUNT,\n    }).sendRequest,\n  };\n}\n"]}