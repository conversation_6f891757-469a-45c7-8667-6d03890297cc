// Check what API keys are in the database
require('dotenv').config();
const { supabase } = require('./src/config/database');

async function checkApiKeys() {
    console.log('🔍 Checking API keys in database...\n');
    
    try {
        // Get all API keys
        const { data: apiKeys, error } = await supabase
            .from('api_keys')
            .select('*');
            
        if (error) {
            console.error('❌ Error fetching API keys:', error);
            return;
        }
        
        if (!apiKeys || apiKeys.length === 0) {
            console.log('❌ No API keys found in database');
            return;
        }
        
        console.log(`✅ Found ${apiKeys.length} API key(s):\n`);
        
        apiKeys.forEach((key, index) => {
            console.log(`📋 API Key #${index + 1}:`);
            console.log(`   ID: ${key.id}`);
            console.log(`   Key Hash: ${key.key_hash ? key.key_hash.substring(0, 20) + '...' : 'NULL'}`);
            console.log(`   User ID: ${key.user_id}`);
            console.log(`   WordPress Site: ${key.wordpress_site}`);
            console.log(`   Is Active: ${key.is_active}`);
            console.log(`   Permissions: ${JSON.stringify(key.permissions)}`);
            console.log(`   Created: ${key.created_at}`);
            console.log(`   Last Used: ${key.last_used_at || 'Never'}`);
            console.log('');
        });
        
        // Check if our expected API key exists
        const expectedKey = 'sk_api_7036c6f38cab187f402a7c37e701554be2cd1a246c21bb384ad7ca594759c0ae';
        const matchingKey = apiKeys.find(key => key.key_hash === expectedKey);
        
        if (matchingKey) {
            console.log('✅ Expected API key found in database!');
        } else {
            console.log('❌ Expected API key NOT found in database');
            console.log(`   Looking for: ${expectedKey}`);
            console.log('   This explains why authentication is failing.');
            
            // Show what keys we do have
            console.log('\n🔍 Available key hashes:');
            apiKeys.forEach((key, index) => {
                console.log(`   ${index + 1}. ${key.key_hash}`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

checkApiKeys();
