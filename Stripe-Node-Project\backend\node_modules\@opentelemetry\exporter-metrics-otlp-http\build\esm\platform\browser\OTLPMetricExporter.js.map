{"version": 3, "file": "OTLPMetricExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPMetricExporter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;AAGH,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE3D,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AACtE,OAAO,EACL,uBAAuB,EAEvB,uBAAuB,EACvB,2BAA2B,GAC5B,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EACL,iCAAiC,GAElC,MAAM,iCAAiC,CAAC;AAEzC,IAAM,+BAA+B,GAAG,YAAY,CAAC;AACrD,IAAM,qBAAqB,GAAG,2BAAyB,+BAAiC,CAAC;AAEzF;IAAuC,4CAGtC;IACC,kCAAY,MAA2D;QAAvE,YACE,kBAAM,MAAM,CAAC,SAOd;QANC,KAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,KAAI,CAAC,QAAQ,EACb,YAAY,CAAC,uBAAuB,CAClC,MAAM,EAAE,CAAC,kCAAkC,CAC5C,CACF,CAAC;;IACJ,CAAC;IAED,gDAAa,GAAb,UAAc,MAA8B;QAC1C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,GAAG;YACZ,CAAC,CAAC,MAAM,EAAE,CAAC,mCAAmC,CAAC,MAAM,GAAG,CAAC;gBACzD,CAAC,CAAC,2BAA2B,CACzB,MAAM,EAAE,CAAC,mCAAmC,CAC7C;gBACH,CAAC,CAAC,MAAM,EAAE,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC;oBACjD,CAAC,CAAC,uBAAuB,CACrB,MAAM,EAAE,CAAC,2BAA2B,EACpC,+BAA+B,CAChC;oBACH,CAAC,CAAC,qBAAqB,CAAC;IAC5B,CAAC;IAED,0CAAO,GAAP,UAAQ,OAA0B;QAChC,OAAO,iCAAiC,CAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IACH,+BAAC;AAAD,CAAC,AAhCD,CAAuC,uBAAuB,GAgC7D;AAED;;GAEG;AACH;IAAwC,sCAAgD;IACtF,4BAAY,MAA2D;eACrE,kBAAM,IAAI,wBAAwB,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACrD,CAAC;IACH,yBAAC;AAAD,CAAC,AAJD,CAAwC,sBAAsB,GAI7D", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport { baggageUtils, getEnv } from '@opentelemetry/core';\nimport { OTLPMetricExporterOptions } from '../../OTLPMetricExporterOptions';\nimport { OTLPMetricExporterBase } from '../../OTLPMetricExporterBase';\nimport {\n  OTLPExporterBrowserBase,\n  OTLPExporterConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  createExportMetricsServiceRequest,\n  IExportMetricsServiceRequest,\n} from '@opentelemetry/otlp-transformer';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/metrics';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\n\nclass OTLPExporterBrowserProxy extends OTLPExporterBrowserBase<\n  ResourceMetrics,\n  IExportMetricsServiceRequest\n> {\n  constructor(config?: OTLPMetricExporterOptions & OTLPExporterConfigBase) {\n    super(config);\n    this._headers = Object.assign(\n      this._headers,\n      baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_METRICS_HEADERS\n      )\n    );\n  }\n\n  getDefaultUrl(config: OTLPExporterConfigBase): string {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_METRICS_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(\n          getEnv().OTEL_EXPORTER_OTLP_METRICS_ENDPOINT\n        )\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n\n  convert(metrics: ResourceMetrics[]): IExportMetricsServiceRequest {\n    return createExportMetricsServiceRequest(metrics);\n  }\n}\n\n/**\n * Collector Metric Exporter for Web\n */\nexport class OTLPMetricExporter extends OTLPMetricExporterBase<OTLPExporterBrowserProxy> {\n  constructor(config?: OTLPExporterConfigBase & OTLPMetricExporterOptions) {\n    super(new OTLPExporterBrowserProxy(config), config);\n  }\n}\n"]}