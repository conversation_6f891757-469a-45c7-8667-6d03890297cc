{"version": 3, "file": "BlobDownloadResponse.browser.js", "sourceRoot": "", "sources": ["../../../src/BlobDownloadResponse.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,kFAAkF;AAClF,+CAA+C;AAC/C,oEAAoE;AACpE,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n// This file is used as a shim of \"BlobDownloadResponse\" for some browser bundlers\n// when trying to bundle \"BlobDownloadResponse\"\n// \"BlobDownloadResponse\" class is only available in Node.js runtime\nexport const BlobDownloadResponse = 1;\n"]}