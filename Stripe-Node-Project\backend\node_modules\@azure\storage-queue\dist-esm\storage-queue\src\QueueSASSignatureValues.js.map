{"version": 3, "file": "QueueSASSignatureValues.js", "sourceRoot": "", "sources": ["../../../src/QueueSASSignatureValues.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAG5D,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAE/C,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,sBAAsB,CAAC;AAsD5D;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,+BAA+B,CAC7C,uBAAgD,EAChD,mBAA+C;IAE/C,OAAO,uCAAuC,CAAC,uBAAuB,EAAE,mBAAmB,CAAC;SACzF,kBAAkB,CAAC;AACxB,CAAC;AAED,MAAM,UAAU,uCAAuC,CACrD,uBAAgD,EAChD,mBAA+C;IAE/C,IACE,CAAC,uBAAuB,CAAC,UAAU;QACnC,CAAC,CAAC,uBAAuB,CAAC,WAAW,IAAI,uBAAuB,CAAC,SAAS,CAAC,EAC3E,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,wGAAwG,CACzG,CAAC;IACJ,CAAC;IAED,MAAM,OAAO,GAAG,uBAAuB,CAAC,OAAO;QAC7C,CAAC,CAAC,uBAAuB,CAAC,OAAO;QACjC,CAAC,CAAC,eAAe,CAAC;IACpB,IAAI,mBAAuC,CAAC;IAE5C,8FAA8F;IAC9F,IAAI,uBAAuB,CAAC,WAAW,EAAE,CAAC;QACxC,mBAAmB,GAAG,mBAAmB,CAAC,KAAK,CAC7C,uBAAuB,CAAC,WAAW,CAAC,QAAQ,EAAE,CAC/C,CAAC,QAAQ,EAAE,CAAC;IACf,CAAC;IAED,uDAAuD;IACvD,MAAM,YAAY,GAAG;QACnB,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QAC9C,uBAAuB,CAAC,QAAQ;YAC9B,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,QAAQ,EAAE,KAAK,CAAC;YAC/D,CAAC,CAAC,EAAE;QACN,uBAAuB,CAAC,SAAS;YAC/B,CAAC,CAAC,oBAAoB,CAAC,uBAAuB,CAAC,SAAS,EAAE,KAAK,CAAC;YAChE,CAAC,CAAC,EAAE;QACN,gBAAgB,CAAC,mBAAmB,CAAC,WAAW,EAAE,uBAAuB,CAAC,SAAS,CAAC;QACpF,uBAAuB,CAAC,UAAU;QAClC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QACvF,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;QACxE,OAAO;KACR,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEb,MAAM,SAAS,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;IAEtE,OAAO;QACL,kBAAkB,EAAE,IAAI,kBAAkB,CACxC,OAAO,EACP,SAAS,EACT,mBAAmB,EACnB,SAAS,EACT,SAAS,EACT,uBAAuB,CAAC,QAAQ,EAChC,uBAAuB,CAAC,QAAQ,EAChC,uBAAuB,CAAC,SAAS,EACjC,uBAAuB,CAAC,OAAO,EAC/B,uBAAuB,CAAC,UAAU,CACnC;QACD,YAAY,EAAE,YAAY;KAC3B,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,WAAmB,EAAE,SAAiB;IAC9D,oCAAoC;IACpC,OAAO,UAAU,WAAW,IAAI,SAAS,EAAE,CAAC;AAC9C,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { QueueSASPermissions } from \"./QueueSASPermissions\";\nimport type { StorageSharedKeyCredential } from \"../../storage-blob/src/credentials/StorageSharedKeyCredential\";\nimport type { SasIPRange } from \"./SasIPRange\";\nimport { ipRangeToString } from \"./SasIPRange\";\nimport type { SASProtocol } from \"./SASQueryParameters\";\nimport { SASQueryParameters } from \"./SASQueryParameters\";\nimport { SERVICE_VERSION } from \"./utils/constants\";\nimport { truncatedISO8061Date } from \"./utils/utils.common\";\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * QueueSASSignatureValues is used to help generating Queue service SAS tokens for queues.\n */\nexport interface QueueSASSignatureValues {\n  /**\n   * The version of the service this SAS will target. If not specified, it will default to the version targeted by the\n   * library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols, HTTPS only or HTTPSandHTTP\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n\n  /**\n   * Optional only when identifier is provided. The time after which the SAS will no longer work.\n   */\n  expiresOn?: Date;\n\n  /**\n   * Optional only when identifier is provided.\n   * Please refer to {@link QueueSASPermissions}\n   * being accessed for help constructing the permissions string.\n   */\n  permissions?: QueueSASPermissions;\n\n  /**\n   * Optional. IP ranges allowed in this SAS.\n   */\n  ipRange?: SasIPRange;\n\n  /**\n   * The name of the queue the SAS user may access.\n   */\n  queueName: string;\n\n  /**\n   * Optional. The name of the access policy on the queue this SAS references if any.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/establishing-a-stored-access-policy\n   */\n  identifier?: string;\n}\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * Creates an instance of SASQueryParameters.\n *\n * Only accepts required settings needed to create a SAS. For optional settings please\n * set corresponding properties directly, such as permissions, startsOn and identifier.\n *\n * WARNING: When identifier is not provided, permissions and expiresOn are required.\n * You MUST assign value to identifier or expiresOn & permissions manually if you initial with\n * this constructor.\n *\n * @param queueSASSignatureValues -\n * @param sharedKeyCredential -\n */\nexport function generateQueueSASQueryParameters(\n  queueSASSignatureValues: QueueSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): SASQueryParameters {\n  return generateQueueSASQueryParametersInternal(queueSASSignatureValues, sharedKeyCredential)\n    .sasQueryParameters;\n}\n\nexport function generateQueueSASQueryParametersInternal(\n  queueSASSignatureValues: QueueSASSignatureValues,\n  sharedKeyCredential: StorageSharedKeyCredential,\n): { sasQueryParameters: SASQueryParameters; stringToSign: string } {\n  if (\n    !queueSASSignatureValues.identifier &&\n    !(queueSASSignatureValues.permissions && queueSASSignatureValues.expiresOn)\n  ) {\n    throw new RangeError(\n      \"Must provide 'permissions' and 'expiresOn' for Queue SAS generation when 'identifier' is not provided.\",\n    );\n  }\n\n  const version = queueSASSignatureValues.version\n    ? queueSASSignatureValues.version\n    : SERVICE_VERSION;\n  let verifiedPermissions: string | undefined;\n\n  // Calling parse and toString guarantees the proper ordering and throws on invalid characters.\n  if (queueSASSignatureValues.permissions) {\n    verifiedPermissions = QueueSASPermissions.parse(\n      queueSASSignatureValues.permissions.toString(),\n    ).toString();\n  }\n\n  // Signature is generated on the un-url-encoded values.\n  const stringToSign = [\n    verifiedPermissions ? verifiedPermissions : \"\",\n    queueSASSignatureValues.startsOn\n      ? truncatedISO8061Date(queueSASSignatureValues.startsOn, false)\n      : \"\",\n    queueSASSignatureValues.expiresOn\n      ? truncatedISO8061Date(queueSASSignatureValues.expiresOn, false)\n      : \"\",\n    getCanonicalName(sharedKeyCredential.accountName, queueSASSignatureValues.queueName),\n    queueSASSignatureValues.identifier,\n    queueSASSignatureValues.ipRange ? ipRangeToString(queueSASSignatureValues.ipRange) : \"\",\n    queueSASSignatureValues.protocol ? queueSASSignatureValues.protocol : \"\",\n    version,\n  ].join(\"\\n\");\n\n  const signature = sharedKeyCredential.computeHMACSHA256(stringToSign);\n\n  return {\n    sasQueryParameters: new SASQueryParameters(\n      version,\n      signature,\n      verifiedPermissions,\n      undefined,\n      undefined,\n      queueSASSignatureValues.protocol,\n      queueSASSignatureValues.startsOn,\n      queueSASSignatureValues.expiresOn,\n      queueSASSignatureValues.ipRange,\n      queueSASSignatureValues.identifier,\n    ),\n    stringToSign: stringToSign,\n  };\n}\n\nfunction getCanonicalName(accountName: string, queueName: string): string {\n  // Queue: \"/queue/account/queueName\"\n  return `/queue/${accountName}/${queueName}`;\n}\n"]}