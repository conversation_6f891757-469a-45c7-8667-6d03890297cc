{"version": 3, "file": "index.browser.js", "sourceRoot": "", "sources": ["../../../../storage-internal-avro/src/index.browser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport { AvroReader } from \"./AvroReader\";\nexport { AvroReadable } from \"./AvroReadable\";\nexport { AvroReadableFromBlob } from \"./AvroReadableFromBlob\";\n"]}