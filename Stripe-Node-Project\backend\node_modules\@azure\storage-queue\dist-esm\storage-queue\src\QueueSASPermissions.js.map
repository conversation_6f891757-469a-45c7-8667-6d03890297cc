{"version": 3, "file": "QueueSASPermissions.js", "sourceRoot": "", "sources": ["../../../src/QueueSASPermissions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;GAQG;AACH,MAAM,OAAO,mBAAmB;IAAhC;QAgCE;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;IAwBlC,CAAC;IAzEC;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,WAAmB;QACrC,MAAM,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;QAEtD,KAAK,MAAM,IAAI,IAAI,WAAW,EAAE,CAAC;YAC/B,QAAQ,IAAI,EAAE,CAAC;gBACb,KAAK,GAAG;oBACN,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAChC,MAAM;gBACR,KAAK,GAAG;oBACN,mBAAmB,CAAC,GAAG,GAAG,IAAI,CAAC;oBAC/B,MAAM;gBACR,KAAK,GAAG;oBACN,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;oBAClC,MAAM;gBACR,KAAK,GAAG;oBACN,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACnC,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,uBAAuB,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAsBD;;;;;OAKG;IACI,QAAQ;QACb,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * This is a helper class to construct a string representing the permissions granted by a ServiceSAS to a Queue. Setting\n * a value to true means that any SAS which uses these permissions will grant permissions for that operation. Once all\n * the values are set, this should be serialized with toString and set as the permissions field on a\n * {@link QueueSASSignatureValues} object. It is possible to construct the permissions string without this class, but\n * the order of the permissions is particular and this class guarantees correctness.\n */\nexport class QueueSASPermissions {\n  /**\n   * Creates a {@link QueueSASPermissions} from the specified permissions string. This method will throw an\n   * Error if it encounters a character that does not correspond to a valid permission.\n   *\n   * @param permissions -\n   */\n  public static parse(permissions: string): QueueSASPermissions {\n    const queueSASPermissions = new QueueSASPermissions();\n\n    for (const char of permissions) {\n      switch (char) {\n        case \"r\":\n          queueSASPermissions.read = true;\n          break;\n        case \"a\":\n          queueSASPermissions.add = true;\n          break;\n        case \"u\":\n          queueSASPermissions.update = true;\n          break;\n        case \"p\":\n          queueSASPermissions.process = true;\n          break;\n        default:\n          throw new RangeError(`Invalid permission: ${char}`);\n      }\n    }\n\n    return queueSASPermissions;\n  }\n\n  /**\n   * Specifies Read access granted.\n   */\n  public read: boolean = false;\n\n  /**\n   * Specifies Add access granted.\n   */\n  public add: boolean = false;\n\n  /**\n   * Specifies Update access granted.\n   */\n  public update: boolean = false;\n\n  /**\n   * Specifies Process access granted.\n   */\n  public process: boolean = false;\n\n  /**\n   * Converts the given permissions to a string. Using this method will guarantee the permissions are in an\n   * order accepted by the service.\n   *\n   * @returns A string which represents the QueueSASPermissions\n   */\n  public toString(): string {\n    const permissions: string[] = [];\n    if (this.read) {\n      permissions.push(\"r\");\n    }\n    if (this.add) {\n      permissions.push(\"a\");\n    }\n    if (this.update) {\n      permissions.push(\"u\");\n    }\n    if (this.process) {\n      permissions.push(\"p\");\n    }\n    return permissions.join(\"\");\n  }\n}\n"]}