/// <reference types="node" />
import type { ProcessInfo } from './index.js';
export interface ProcessInfoNodeData {
    hrstart?: [number, number];
    date: string;
    argv: string[];
    execArgv: string[];
    NODE_OPTIONS?: string;
    cwd: string;
    pid: number;
    ppid: number;
    parent: string | null;
    uuid: string;
    files: string[];
    sources: Record<string, string[]>;
    root?: string | null;
    externalID?: string | null;
    code?: number | null;
    signal?: NodeJS.Signals | null;
    runtime?: number;
    globalsAdded?: string[];
}
export declare class ProcessInfoNode {
    #private;
    date: string;
    argv: string[];
    execArgv: string[];
    NODE_OPTIONS?: string;
    cwd: string;
    pid: number;
    ppid: number;
    uuid: string;
    code?: number | null;
    signal?: NodeJS.Signals | null;
    runtime?: number;
    globalsAdded?: string[];
    files: string[];
    sources: Record<string, string[]>;
    parent?: ProcessInfoNode | null;
    root?: ProcessInfoNode | null;
    children?: Set<ProcessInfoNode> | null;
    descendants: Set<ProcessInfoNode> | null;
    externalID: string | null;
    constructor(data: ProcessInfoNodeData);
    toJSON(): {
        [k: string]: any;
    };
    link(db: ProcessInfo): void;
}
//# sourceMappingURL=process-info-node.d.ts.map