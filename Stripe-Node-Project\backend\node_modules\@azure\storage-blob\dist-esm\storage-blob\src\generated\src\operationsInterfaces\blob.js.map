{"version": 3, "file": "blob.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/blob.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  BlobDownloadOptionalParams,\n  BlobDownloadResponse,\n  BlobGetPropertiesOptionalParams,\n  BlobGetPropertiesResponse,\n  BlobDeleteOptionalParams,\n  BlobDeleteResponse,\n  BlobUndeleteOptionalParams,\n  BlobUndeleteResponse,\n  BlobExpiryOptions,\n  BlobSetExpiryOptionalParams,\n  BlobSetExpiryResponse,\n  BlobSetHttpHeadersOptionalParams,\n  BlobSetHttpHeadersResponse,\n  BlobSetImmutabilityPolicyOptionalParams,\n  BlobSetImmutabilityPolicyResponse,\n  BlobDeleteImmutabilityPolicyOptionalParams,\n  BlobDeleteImmutabilityPolicyResponse,\n  BlobSetLegalHoldOptionalParams,\n  BlobSetLegalHoldResponse,\n  BlobSetMetadataOptionalParams,\n  BlobSetMetadataResponse,\n  BlobAcquireLeaseOptionalParams,\n  BlobAcquireLeaseResponse,\n  BlobReleaseLeaseOptionalParams,\n  BlobReleaseLeaseResponse,\n  BlobRenewLeaseOptionalParams,\n  BlobRenewLeaseResponse,\n  BlobChangeLeaseOptionalParams,\n  BlobChangeLeaseResponse,\n  BlobBreakLeaseOptionalParams,\n  BlobBreakLeaseResponse,\n  BlobCreateSnapshotOptionalParams,\n  BlobCreateSnapshotResponse,\n  BlobStartCopyFromURLOptionalParams,\n  BlobStartCopyFromURLResponse,\n  BlobCopyFromURLOptionalParams,\n  BlobCopyFromURLResponse,\n  BlobAbortCopyFromURLOptionalParams,\n  BlobAbortCopyFromURLResponse,\n  AccessTier,\n  BlobSetTierOptionalParams,\n  BlobSetTierResponse,\n  BlobGetAccountInfoOptionalParams,\n  BlobGetAccountInfoResponse,\n  BlobQueryOptionalParams,\n  BlobQueryResponse,\n  BlobGetTagsOptionalParams,\n  BlobGetTagsResponse,\n  BlobSetTagsOptionalParams,\n  BlobSetTagsResponse,\n} from \"../models\";\n\n/** Interface representing a Blob. */\nexport interface Blob {\n  /**\n   * The Download operation reads or downloads a blob from the system, including its metadata and\n   * properties. You can also call Download to read a snapshot.\n   * @param options The options parameters.\n   */\n  download(options?: BlobDownloadOptionalParams): Promise<BlobDownloadResponse>;\n  /**\n   * The Get Properties operation returns all user-defined metadata, standard HTTP properties, and system\n   * properties for the blob. It does not return the content of the blob.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: BlobGetPropertiesOptionalParams,\n  ): Promise<BlobGetPropertiesResponse>;\n  /**\n   * If the storage account's soft delete feature is disabled then, when a blob is deleted, it is\n   * permanently removed from the storage account. If the storage account's soft delete feature is\n   * enabled, then, when a blob is deleted, it is marked for deletion and becomes inaccessible\n   * immediately. However, the blob service retains the blob or snapshot for the number of days specified\n   * by the DeleteRetentionPolicy section of [Storage service properties]\n   * (Set-Blob-Service-Properties.md). After the specified number of days has passed, the blob's data is\n   * permanently removed from the storage account. Note that you continue to be charged for the\n   * soft-deleted blob's storage until it is permanently removed. Use the List Blobs API and specify the\n   * \"include=deleted\" query parameter to discover which blobs and snapshots have been soft deleted. You\n   * can then use the Undelete Blob API to restore a soft-deleted blob. All other operations on a\n   * soft-deleted blob or snapshot causes the service to return an HTTP status code of 404\n   * (ResourceNotFound).\n   * @param options The options parameters.\n   */\n  delete(options?: BlobDeleteOptionalParams): Promise<BlobDeleteResponse>;\n  /**\n   * Undelete a blob that was previously soft deleted\n   * @param options The options parameters.\n   */\n  undelete(options?: BlobUndeleteOptionalParams): Promise<BlobUndeleteResponse>;\n  /**\n   * Sets the time a blob will expire and be deleted.\n   * @param expiryOptions Required. Indicates mode of the expiry time\n   * @param options The options parameters.\n   */\n  setExpiry(\n    expiryOptions: BlobExpiryOptions,\n    options?: BlobSetExpiryOptionalParams,\n  ): Promise<BlobSetExpiryResponse>;\n  /**\n   * The Set HTTP Headers operation sets system properties on the blob\n   * @param options The options parameters.\n   */\n  setHttpHeaders(\n    options?: BlobSetHttpHeadersOptionalParams,\n  ): Promise<BlobSetHttpHeadersResponse>;\n  /**\n   * The Set Immutability Policy operation sets the immutability policy on the blob\n   * @param options The options parameters.\n   */\n  setImmutabilityPolicy(\n    options?: BlobSetImmutabilityPolicyOptionalParams,\n  ): Promise<BlobSetImmutabilityPolicyResponse>;\n  /**\n   * The Delete Immutability Policy operation deletes the immutability policy on the blob\n   * @param options The options parameters.\n   */\n  deleteImmutabilityPolicy(\n    options?: BlobDeleteImmutabilityPolicyOptionalParams,\n  ): Promise<BlobDeleteImmutabilityPolicyResponse>;\n  /**\n   * The Set Legal Hold operation sets a legal hold on the blob.\n   * @param legalHold Specified if a legal hold should be set on the blob.\n   * @param options The options parameters.\n   */\n  setLegalHold(\n    legalHold: boolean,\n    options?: BlobSetLegalHoldOptionalParams,\n  ): Promise<BlobSetLegalHoldResponse>;\n  /**\n   * The Set Blob Metadata operation sets user-defined metadata for the specified blob as one or more\n   * name-value pairs\n   * @param options The options parameters.\n   */\n  setMetadata(\n    options?: BlobSetMetadataOptionalParams,\n  ): Promise<BlobSetMetadataResponse>;\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param options The options parameters.\n   */\n  acquireLease(\n    options?: BlobAcquireLeaseOptionalParams,\n  ): Promise<BlobAcquireLeaseResponse>;\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  releaseLease(\n    leaseId: string,\n    options?: BlobReleaseLeaseOptionalParams,\n  ): Promise<BlobReleaseLeaseResponse>;\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param options The options parameters.\n   */\n  renewLease(\n    leaseId: string,\n    options?: BlobRenewLeaseOptionalParams,\n  ): Promise<BlobRenewLeaseResponse>;\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param leaseId Specifies the current lease ID on the resource.\n   * @param proposedLeaseId Proposed lease ID, in a GUID string format. The Blob service returns 400\n   *                        (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor\n   *                        (String) for a list of valid GUID string formats.\n   * @param options The options parameters.\n   */\n  changeLease(\n    leaseId: string,\n    proposedLeaseId: string,\n    options?: BlobChangeLeaseOptionalParams,\n  ): Promise<BlobChangeLeaseResponse>;\n  /**\n   * [Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete\n   * operations\n   * @param options The options parameters.\n   */\n  breakLease(\n    options?: BlobBreakLeaseOptionalParams,\n  ): Promise<BlobBreakLeaseResponse>;\n  /**\n   * The Create Snapshot operation creates a read-only snapshot of a blob\n   * @param options The options parameters.\n   */\n  createSnapshot(\n    options?: BlobCreateSnapshotOptionalParams,\n  ): Promise<BlobCreateSnapshotResponse>;\n  /**\n   * The Start Copy From URL operation copies a blob or an internet resource to a new blob.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  startCopyFromURL(\n    copySource: string,\n    options?: BlobStartCopyFromURLOptionalParams,\n  ): Promise<BlobStartCopyFromURLResponse>;\n  /**\n   * The Copy From URL operation copies a blob or an internet resource to a new blob. It will not return\n   * a response until the copy is complete.\n   * @param copySource Specifies the name of the source page blob snapshot. This value is a URL of up to\n   *                   2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would\n   *                   appear in a request URI. The source blob must either be public or must be authenticated via a shared\n   *                   access signature.\n   * @param options The options parameters.\n   */\n  copyFromURL(\n    copySource: string,\n    options?: BlobCopyFromURLOptionalParams,\n  ): Promise<BlobCopyFromURLResponse>;\n  /**\n   * The Abort Copy From URL operation aborts a pending Copy From URL operation, and leaves a destination\n   * blob with zero length and full metadata.\n   * @param copyId The copy identifier provided in the x-ms-copy-id header of the original Copy Blob\n   *               operation.\n   * @param options The options parameters.\n   */\n  abortCopyFromURL(\n    copyId: string,\n    options?: BlobAbortCopyFromURLOptionalParams,\n  ): Promise<BlobAbortCopyFromURLResponse>;\n  /**\n   * The Set Tier operation sets the tier on a blob. The operation is allowed on a page blob in a premium\n   * storage account and on a block blob in a blob storage account (locally redundant storage only). A\n   * premium page blob's tier determines the allowed size, IOPS, and bandwidth of the blob. A block\n   * blob's tier determines Hot/Cool/Archive storage type. This operation does not update the blob's\n   * ETag.\n   * @param tier Indicates the tier to be set on the blob.\n   * @param options The options parameters.\n   */\n  setTier(\n    tier: AccessTier,\n    options?: BlobSetTierOptionalParams,\n  ): Promise<BlobSetTierResponse>;\n  /**\n   * Returns the sku name and account kind\n   * @param options The options parameters.\n   */\n  getAccountInfo(\n    options?: BlobGetAccountInfoOptionalParams,\n  ): Promise<BlobGetAccountInfoResponse>;\n  /**\n   * The Query operation enables users to select/project on blob data by providing simple query\n   * expressions.\n   * @param options The options parameters.\n   */\n  query(options?: BlobQueryOptionalParams): Promise<BlobQueryResponse>;\n  /**\n   * The Get Tags operation enables users to get the tags associated with a blob.\n   * @param options The options parameters.\n   */\n  getTags(options?: BlobGetTagsOptionalParams): Promise<BlobGetTagsResponse>;\n  /**\n   * The Set Tags operation enables users to set tags on a blob.\n   * @param options The options parameters.\n   */\n  setTags(options?: BlobSetTagsOptionalParams): Promise<BlobSetTagsResponse>;\n}\n"]}