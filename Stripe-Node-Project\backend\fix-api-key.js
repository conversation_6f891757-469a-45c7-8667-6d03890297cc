// Fix API key by creating it properly using the backend's auth system
require('dotenv').config();
const { generateApiKey } = require('./src/middleware/auth');
const { supabase } = require('./src/config/database');

async function fixApiKey() {
    console.log('🔧 Fixing API key...\n');
    
    try {
        // First, check if we have a user with the expected ID
        const { data: users, error: userError } = await supabase
            .from('users')
            .select('*');
            
        if (userError) {
            console.error('❌ Error fetching users:', userError);
            return;
        }
        
        console.log(`📋 Found ${users.length} user(s) in database:`);
        users.forEach((user, index) => {
            console.log(`   ${index + 1}. ID: ${user.id}, Email: ${user.email}, Site: ${user.wordpress_site}`);
        });
        
        if (users.length === 0) {
            console.log('❌ No users found. Need to create a user first.');
            return;
        }
        
        // Use the first user (should be our WordPress user)
        const user = users[0];
        console.log(`\n🎯 Using user: ${user.email} (ID: ${user.id})`);
        
        // Generate API key using the proper function
        const userData = {
            user_id: user.id,
            wordpress_site: user.wordpress_site || 'http://localhost/Stripe-Node-Project/wordpress',
            permissions: ['payments', 'commissions', 'vendors']
        };
        
        console.log('🔑 Generating API key...');
        const apiKey = await generateApiKey(userData);
        
        console.log('✅ API key generated successfully!');
        console.log(`🔑 API Key: ${apiKey}`);
        
        // Verify it was stored correctly
        const { data: apiKeys, error: apiError } = await supabase
            .from('api_keys')
            .select('*')
            .eq('key_hash', apiKey);
            
        if (apiError) {
            console.error('❌ Error verifying API key:', apiError);
            return;
        }
        
        if (apiKeys.length > 0) {
            console.log('✅ API key verified in database!');
            console.log('\n📋 API Key Details:');
            const key = apiKeys[0];
            console.log(`   ID: ${key.id}`);
            console.log(`   User ID: ${key.user_id}`);
            console.log(`   WordPress Site: ${key.wordpress_site}`);
            console.log(`   Permissions: ${JSON.stringify(key.permissions)}`);
            console.log(`   Is Active: ${key.is_active}`);
        } else {
            console.log('❌ API key not found in database after creation');
        }
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Update your WordPress plugin settings with this new API key');
        console.log('2. Test the payment integration again');
        console.log(`3. Use this API key: ${apiKey}`);
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        console.error(error.stack);
    }
}

fixApiKey();
