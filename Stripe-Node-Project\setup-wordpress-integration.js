#!/usr/bin/env node

/**
 * Setup script for WordPress integration
 * This script will help you generate the API key and vendor ID needed for your WordPress plugin
 */

const http = require('http');

const API_BASE = 'http://localhost:3000/api';

async function setupWordPressIntegration() {
    console.log('🚀 Setting up WordPress Integration...\n');

    try {
        // Step 1: Generate API Key
        console.log('📝 Step 1: Generating API Key for WordPress...');
        
        const apiKeyResponse = await axios.post(`${API_BASE}/auth/generate-api-key`, {
            wordpress_site: 'http://localhost/Stripe-Node-Project/wordpress',
            permissions: ['payments', 'commissions', 'vendors'],
            description: 'WordPress Plugin Integration - Local Development'
        });

        const apiKey = apiKeyResponse.data.api_key;
        console.log('✅ API Key generated successfully!');
        console.log(`🔑 API Key: ${apiKey}\n`);

        // Step 2: Create Vendor Account
        console.log('📝 Step 2: Creating Vendor Account...');
        
        const vendorResponse = await axios.post(`${API_BASE}/vendors`, {
            name: 'dkheterpal',
            email: '<EMAIL>', // You can change this to your real email
            niche: 'other',
            country: 'US',
            business_type: 'individual'
        }, {
            headers: {
                'X-API-Key': apiKey
            }
        });

        const vendor = vendorResponse.data.data;
        console.log('✅ Vendor account created successfully!');
        console.log(`👤 Vendor ID: ${vendor.id}`);
        console.log(`📧 Email: ${vendor.email}`);
        console.log(`🏢 Business Niche: ${vendor.niche}\n`);

        // Step 3: Display WordPress Plugin Settings
        console.log('🎯 WordPress Plugin Configuration:');
        console.log('=====================================');
        console.log(`API Base URL: http://localhost:3000`);
        console.log(`API Key: ${apiKey}`);
        console.log(`Vendor ID: ${vendor.id}`);
        console.log(`Business Niche: Other`);
        console.log(`Test Mode: ✅ Enabled`);
        console.log('=====================================\n');

        console.log('📋 Next Steps:');
        console.log('1. Copy the API Key and Vendor ID above');
        console.log('2. Go to your WordPress admin: http://localhost/Stripe-Node-Project/wordpress/wp-admin/');
        console.log('3. Navigate to the Stripe Integration Settings');
        console.log('4. Paste the API Key and Vendor ID');
        console.log('5. Save the settings');
        console.log('6. Test a payment!\n');

        console.log('🎉 Setup completed successfully!');

    } catch (error) {
        console.error('❌ Setup failed:', error.response?.data || error.message);
        
        if (error.response?.status === 404) {
            console.log('\n💡 Make sure your backend server is running:');
            console.log('   cd backend && npm run dev');
        }
    }
}

// Run the setup
setupWordPressIntegration();
