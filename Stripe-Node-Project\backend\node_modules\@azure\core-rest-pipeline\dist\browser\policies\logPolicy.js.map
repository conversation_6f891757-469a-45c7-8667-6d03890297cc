{"version": 3, "file": "logPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/logPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,MAAM,WAAW,CAAC;AACjD,OAAO,EACL,aAAa,IAAI,gBAAgB,EACjC,SAAS,IAAI,YAAY,GAC1B,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,gBAAgB,CAAC;AA4B9C;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,UAA4B,EAAE;IACtD,OAAO,YAAY,iBACjB,MAAM,EAAE,UAAU,CAAC,IAAI,IACpB,OAAO,EACV,CAAC;AACL,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { Debugger } from \"@azure/logger\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { logger as coreLogger } from \"../log.js\";\nimport {\n  logPolicyName as tspLogPolicyName,\n  logPolicy as tspLogPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the logPolicy.\n */\nexport const logPolicyName = tspLogPolicyName;\n\n/**\n * Options to configure the logPolicy.\n */\nexport interface LogPolicyOptions {\n  /**\n   * Header names whose values will be logged when logging is enabled.\n   * Defaults include a list of well-known safe headers. Any headers\n   * specified in this field will be added to that list.  Any other values will\n   * be written to logs as \"REDACTED\".\n   */\n  additionalAllowedHeaderNames?: string[];\n\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n\n  /**\n   * The log function to use for writing pipeline logs.\n   * Defaults to core-http's built-in logger.\n   * Compatible with the `debug` library.\n   */\n  logger?: Debugger;\n}\n\n/**\n * A policy that logs all requests and responses.\n * @param options - Options to configure logPolicy.\n */\nexport function logPolicy(options: LogPolicyOptions = {}): PipelinePolicy {\n  return tspLogPolicy({\n    logger: coreLogger.info,\n    ...options,\n  });\n}\n"]}