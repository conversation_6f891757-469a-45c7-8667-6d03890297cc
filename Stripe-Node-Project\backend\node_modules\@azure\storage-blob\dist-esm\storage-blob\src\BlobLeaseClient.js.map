{"version": 3, "file": "BlobLeaseClient.js", "sourceRoot": "", "sources": ["../../../src/BlobLeaseClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAM9C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAIhD,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AA8EtD;;GAEG;AACH,MAAM,OAAO,eAAe;IAM1B;;;;OAIG;IACH,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;;;OAIG;IACH,IAAW,GAAG;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,YAAY,MAAoC,EAAE,OAAgB;QAChE,MAAM,aAAa,GAAI,MAAc,CAAC,oBAAoB,CAAC;QAC3D,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC;QAEvB,IAAK,MAAqB,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;YACzB,IAAI,CAAC,yBAAyB,GAAG,aAAa,CAAC,SAAS,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,yBAAyB,GAAG,aAAa,CAAC,IAAI,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,UAAU,EAAE,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED;;;;;;;;;;;OAWG;IACI,KAAK,CAAC,YAAY,CACvB,QAAgB,EAChB,UAAiC,EAAE;;QAEnC,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,MAAK,QAAQ,CAAC;gBACxE,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,MAAK,QAAQ,CAAC;iBACjF,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,CAAA,CAAC,EACpC,CAAC;YACD,MAAM,IAAI,UAAU,CAClB,yJAAyJ,CAC1J,CAAC;QACJ,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC;gBAChD,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,QAAQ;gBACR,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,eAAe,EAAE,IAAI,CAAC,QAAQ;gBAC9B,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,WAAW,CACtB,eAAuB,EACvB,UAAiC,EAAE;;QAEnC,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,MAAK,QAAQ,CAAC;gBACxE,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,MAAK,QAAQ,CAAC;iBACjF,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,CAAA,CAAC,EACpC,CAAC;YACD,MAAM,IAAI,UAAU,CAClB,yJAAyJ,CAC1J,CAAC;QACJ,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,6BAA6B,EAC7B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,MAAM,QAAQ,GAAG,cAAc,CAC7B,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE;gBAC/E,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;YACF,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;YAChC,OAAO,QAAQ,CAAC;QAClB,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,YAAY,CAAC,UAAiC,EAAE;;QAC3D,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,MAAK,QAAQ,CAAC;gBACxE,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,MAAK,QAAQ,CAAC;iBACjF,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,CAAA,CAAC,EACpC,CAAC;YACD,MAAM,IAAI,UAAU,CAClB,yJAAyJ,CAC1J,CAAC;QACJ,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAC3B,8BAA8B,EAC9B,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC/D,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,UAAU,CAAC,UAAiC,EAAE;;QACzD,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,MAAK,QAAQ,CAAC;gBACxE,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,MAAK,QAAQ,CAAC;iBACjF,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,CAAA,CAAC,EACpC,CAAC;YACD,MAAM,IAAI,UAAU,CAClB,yJAAyJ,CAC1J,CAAC;QACJ,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC5F,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC9D,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;;;;OAUG;IACI,KAAK,CAAC,UAAU,CACrB,WAAmB,EACnB,UAAiC,EAAE;;QAEnC,IACE,IAAI,CAAC,YAAY;YACjB,CAAC,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,OAAO,MAAK,QAAQ,CAAC;gBACxE,CAAC,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,KAAI,CAAA,MAAA,OAAO,CAAC,UAAU,0CAAE,WAAW,MAAK,QAAQ,CAAC;iBACjF,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,CAAA,CAAC,EACpC,CAAC;YACD,MAAM,IAAI,UAAU,CAClB,yJAAyJ,CAC1J,CAAC;QACJ,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAAC,4BAA4B,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE;;YAC5F,MAAM,gBAAgB,GAAsC;gBAC1D,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,WAAW;gBACX,wBAAwB,kCACnB,OAAO,CAAC,UAAU,KACrB,MAAM,EAAE,MAAA,OAAO,CAAC,UAAU,0CAAE,aAAa,GAC1C;gBACD,cAAc,EAAE,cAAc,CAAC,cAAc;aAC9C,CAAC;YACF,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAClE,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { randomUUID } from \"@azure/core-util\";\nimport type { ContainerBreakLeaseOptionalParams } from \"./generatedModels\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { Blob as StorageBlob, Container } from \"./generated/src/operationsInterfaces\";\nimport type { ModifiedAccessConditions } from \"./models\";\nimport type { CommonOptions } from \"./StorageClient\";\nimport { ETagNone } from \"./utils/constants\";\nimport { tracingClient } from \"./utils/tracing\";\nimport type { BlobClient } from \"./Clients\";\nimport type { ContainerClient } from \"./ContainerClient\";\nimport type { WithResponse } from \"./utils/utils.common\";\nimport { assertResponse } from \"./utils/utils.common\";\nimport type {\n  ContainerAcquireLeaseHeaders,\n  ContainerBreakLeaseHeaders,\n  ContainerReleaseLeaseHeaders,\n} from \"./generated/src\";\n\n/**\n * The details for a specific lease.\n */\nexport interface Lease {\n  /**\n   * The ETag contains a value that you can use to\n   * perform operations conditionally. If the request version is 2011-08-18 or\n   * newer, the ETag value will be in quotes.\n   */\n  etag?: string;\n  /**\n   * Returns the date and time the container was\n   * last modified. Any operation that modifies the blob, including an update\n   * of the blob's metadata or properties, changes the last-modified time of\n   * the blob.\n   */\n  lastModified?: Date;\n  /**\n   * Uniquely identifies a container's lease\n   */\n  leaseId?: string;\n  /**\n   * Approximate time remaining in the lease\n   * period, in seconds.\n   */\n  leaseTime?: number;\n  /**\n   * This header uniquely identifies the request\n   * that was made and can be used for troubleshooting the request.\n   */\n  requestId?: string;\n  /**\n   * Indicates the version of the Blob service used\n   * to execute the request. This header is returned for requests made against\n   * version 2009-09-19 and above.\n   */\n  version?: string;\n  /**\n   * UTC date/time value generated by the service that\n   * indicates the time at which the response was initiated\n   */\n  date?: Date;\n  /**\n   * Error code if any associated with the response that returned\n   * the Lease information.\n   */\n  errorCode?: string;\n}\n\n/**\n * Contains the response data for operations that create, modify, or delete a lease.\n *\n * See {@link BlobLeaseClient}.\n */\nexport type LeaseOperationResponse = WithResponse<Lease, Lease>;\n\n/**\n * Configures lease operations.\n */\nexport interface LeaseOperationOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Conditions to meet when changing the lease.\n   */\n  conditions?: ModifiedAccessConditions;\n}\n\n/**\n * A client that manages leases for a {@link ContainerClient} or a {@link BlobClient}.\n */\nexport class BlobLeaseClient {\n  private _leaseId: string;\n  private _url: string;\n  private _containerOrBlobOperation: Container | StorageBlob;\n  private _isContainer: boolean;\n\n  /**\n   * Gets the lease Id.\n   *\n   * @readonly\n   */\n  public get leaseId(): string {\n    return this._leaseId;\n  }\n\n  /**\n   * Gets the url.\n   *\n   * @readonly\n   */\n  public get url(): string {\n    return this._url;\n  }\n\n  /**\n   * Creates an instance of BlobLeaseClient.\n   * @param client - The client to make the lease operation requests.\n   * @param leaseId - Initial proposed lease id.\n   */\n  constructor(client: ContainerClient | BlobClient, leaseId?: string) {\n    const clientContext = (client as any).storageClientContext;\n    this._url = client.url;\n\n    if ((client as BlobClient).name === undefined) {\n      this._isContainer = true;\n      this._containerOrBlobOperation = clientContext.container;\n    } else {\n      this._isContainer = false;\n      this._containerOrBlobOperation = clientContext.blob;\n    }\n\n    if (!leaseId) {\n      leaseId = randomUUID();\n    }\n    this._leaseId = leaseId;\n  }\n\n  /**\n   * Establishes and manages a lock on a container for delete operations, or on a blob\n   * for write and delete operations.\n   * The lock duration can be 15 to 60 seconds, or can be infinite.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-container\n   * and\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-blob\n   *\n   * @param duration - Must be between 15 to 60 seconds, or infinite (-1)\n   * @param options - option to configure lease management operations.\n   * @returns Response data for acquire lease operation.\n   */\n  public async acquireLease(\n    duration: number,\n    options: LeaseOperationOptions = {},\n  ): Promise<LeaseOperationResponse> {\n    if (\n      this._isContainer &&\n      ((options.conditions?.ifMatch && options.conditions?.ifMatch !== ETagNone) ||\n        (options.conditions?.ifNoneMatch && options.conditions?.ifNoneMatch !== ETagNone) ||\n        options.conditions?.tagConditions)\n    ) {\n      throw new RangeError(\n        \"The IfMatch, IfNoneMatch and tags access conditions are ignored by the service. Values other than undefined or their default values are not acceptable.\",\n      );\n    }\n    return tracingClient.withSpan(\n      \"BlobLeaseClient-acquireLease\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ContainerAcquireLeaseHeaders, ContainerAcquireLeaseHeaders>(\n          await this._containerOrBlobOperation.acquireLease({\n            abortSignal: options.abortSignal,\n            duration,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            proposedLeaseId: this._leaseId,\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * To change the ID of the lease.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-container\n   * and\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-blob\n   *\n   * @param proposedLeaseId - the proposed new lease Id.\n   * @param options - option to configure lease management operations.\n   * @returns Response data for change lease operation.\n   */\n  public async changeLease(\n    proposedLeaseId: string,\n    options: LeaseOperationOptions = {},\n  ): Promise<LeaseOperationResponse> {\n    if (\n      this._isContainer &&\n      ((options.conditions?.ifMatch && options.conditions?.ifMatch !== ETagNone) ||\n        (options.conditions?.ifNoneMatch && options.conditions?.ifNoneMatch !== ETagNone) ||\n        options.conditions?.tagConditions)\n    ) {\n      throw new RangeError(\n        \"The IfMatch, IfNoneMatch and tags access conditions are ignored by the service. Values other than undefined or their default values are not acceptable.\",\n      );\n    }\n\n    return tracingClient.withSpan(\n      \"BlobLeaseClient-changeLease\",\n      options,\n      async (updatedOptions) => {\n        const response = assertResponse<Lease, Lease>(\n          await this._containerOrBlobOperation.changeLease(this._leaseId, proposedLeaseId, {\n            abortSignal: options.abortSignal,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n        this._leaseId = proposedLeaseId;\n        return response;\n      },\n    );\n  }\n\n  /**\n   * To free the lease if it is no longer needed so that another client may\n   * immediately acquire a lease against the container or the blob.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-container\n   * and\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-blob\n   *\n   * @param options - option to configure lease management operations.\n   * @returns Response data for release lease operation.\n   */\n  public async releaseLease(options: LeaseOperationOptions = {}): Promise<LeaseOperationResponse> {\n    if (\n      this._isContainer &&\n      ((options.conditions?.ifMatch && options.conditions?.ifMatch !== ETagNone) ||\n        (options.conditions?.ifNoneMatch && options.conditions?.ifNoneMatch !== ETagNone) ||\n        options.conditions?.tagConditions)\n    ) {\n      throw new RangeError(\n        \"The IfMatch, IfNoneMatch and tags access conditions are ignored by the service. Values other than undefined or their default values are not acceptable.\",\n      );\n    }\n    return tracingClient.withSpan(\n      \"BlobLeaseClient-releaseLease\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ContainerReleaseLeaseHeaders, ContainerReleaseLeaseHeaders>(\n          await this._containerOrBlobOperation.releaseLease(this._leaseId, {\n            abortSignal: options.abortSignal,\n            modifiedAccessConditions: {\n              ...options.conditions,\n              ifTags: options.conditions?.tagConditions,\n            },\n            tracingOptions: updatedOptions.tracingOptions,\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * To renew the lease.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-container\n   * and\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-blob\n   *\n   * @param options - Optional option to configure lease management operations.\n   * @returns Response data for renew lease operation.\n   */\n  public async renewLease(options: LeaseOperationOptions = {}): Promise<Lease> {\n    if (\n      this._isContainer &&\n      ((options.conditions?.ifMatch && options.conditions?.ifMatch !== ETagNone) ||\n        (options.conditions?.ifNoneMatch && options.conditions?.ifNoneMatch !== ETagNone) ||\n        options.conditions?.tagConditions)\n    ) {\n      throw new RangeError(\n        \"The IfMatch, IfNoneMatch and tags access conditions are ignored by the service. Values other than undefined or their default values are not acceptable.\",\n      );\n    }\n    return tracingClient.withSpan(\"BlobLeaseClient-renewLease\", options, async (updatedOptions) => {\n      return this._containerOrBlobOperation.renewLease(this._leaseId, {\n        abortSignal: options.abortSignal,\n        modifiedAccessConditions: {\n          ...options.conditions,\n          ifTags: options.conditions?.tagConditions,\n        },\n        tracingOptions: updatedOptions.tracingOptions,\n      });\n    });\n  }\n\n  /**\n   * To end the lease but ensure that another client cannot acquire a new lease\n   * until the current lease period has expired.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-container\n   * and\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/lease-blob\n   *\n   * @param breakPeriod - Break period\n   * @param options - Optional options to configure lease management operations.\n   * @returns Response data for break lease operation.\n   */\n  public async breakLease(\n    breakPeriod: number,\n    options: LeaseOperationOptions = {},\n  ): Promise<LeaseOperationResponse> {\n    if (\n      this._isContainer &&\n      ((options.conditions?.ifMatch && options.conditions?.ifMatch !== ETagNone) ||\n        (options.conditions?.ifNoneMatch && options.conditions?.ifNoneMatch !== ETagNone) ||\n        options.conditions?.tagConditions)\n    ) {\n      throw new RangeError(\n        \"The IfMatch, IfNoneMatch and tags access conditions are ignored by the service. Values other than undefined or their default values are not acceptable.\",\n      );\n    }\n\n    return tracingClient.withSpan(\"BlobLeaseClient-breakLease\", options, async (updatedOptions) => {\n      const operationOptions: ContainerBreakLeaseOptionalParams = {\n        abortSignal: options.abortSignal,\n        breakPeriod,\n        modifiedAccessConditions: {\n          ...options.conditions,\n          ifTags: options.conditions?.tagConditions,\n        },\n        tracingOptions: updatedOptions.tracingOptions,\n      };\n      return assertResponse<ContainerBreakLeaseHeaders, ContainerBreakLeaseHeaders>(\n        await this._containerOrBlobOperation.breakLease(operationOptions),\n      );\n    });\n  }\n}\n"]}