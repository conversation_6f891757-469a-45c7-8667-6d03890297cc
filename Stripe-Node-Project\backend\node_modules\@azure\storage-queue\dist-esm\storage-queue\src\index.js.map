{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/index.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,2BAA2B,CAAC;AAEtD,cAAc,yBAAyB,CAAC;AACxC,cAAc,2BAA2B,CAAC;AAC1C,cAAc,sBAAsB,CAAC;AACrC,OAAO,EAEL,iCAAiC,GAClC,MAAM,6BAA6B,CAAC;AACrC,cAAc,wDAAwD,CAAC;AACvE,cAAc,+CAA+C,CAAC;AAC9D,cAAc,+DAA+D,CAAC;AAE9E,OAAO,EAAE,iBAAiB,EAAE,MAAM,+CAA+C,CAAC;AAClF,OAAO,EACL,QAAQ,EAGR,cAAc,EACd,WAAW,EAUX,kBAAkB,GAEnB,MAAM,YAAY,CAAC;AACpB,cAAc,2DAA2D,CAAC;AAC1E,cAAc,kDAAkD,CAAC;AACjE,cAAc,kDAAkD,CAAC;AACjE,cAAc,kEAAkE,CAAC;AACjF,cAAc,oDAAoD,CAAC;AACnE,OAAO,EAAY,oBAAoB,EAAE,8BAA8B,EAAE,MAAM,UAAU,CAAC;AAC1F,cAAc,eAAe,CAAC;AAC9B,cAAc,uBAAuB,CAAC;AACtC,OAAO,EAEL,+BAA+B,GAChC,MAAM,2BAA2B,CAAC;AACnC,cAAc,sBAAsB,CAAC;AACrC,cAAc,sBAAsB,CAAC;AAErC,cAAc,mBAAmB,CAAC;AAQlC,OAAO,EAAE,SAAS,EAAE,CAAC;AACrB,OAAO,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { RestError } from \"@azure/core-rest-pipeline\";\n\nexport * from \"./AccountSASPermissions\";\nexport * from \"./AccountSASResourceTypes\";\nexport * from \"./AccountSASServices\";\nexport {\n  AccountSASSignatureValues,\n  generateAccountSASQueryParameters,\n} from \"./AccountSASSignatureValues\";\nexport * from \"../../storage-blob/src/credentials/AnonymousCredential\";\nexport * from \"../../storage-blob/src/credentials/Credential\";\nexport * from \"../../storage-blob/src/credentials/StorageSharedKeyCredential\";\nexport { SasIPRange } from \"./SasIPRange\";\nexport { BaseRequestPolicy } from \"../../storage-blob/src/policies/RequestPolicy\";\nexport {\n  Pipeline,\n  PipelineLike,\n  PipelineOptions,\n  isPipelineLike,\n  newPipeline,\n  StoragePipelineOptions,\n  RequestPolicyFactory,\n  RequestPolicy,\n  RequestPolicyOptions,\n  WebResource,\n  HttpOperationResponse,\n  HttpHeaders,\n  HttpRequestBody,\n  IHttpClient,\n  StorageOAuthScopes,\n  ServiceClientOptions,\n} from \"./Pipeline\";\nexport * from \"../../storage-blob/src/policies/AnonymousCredentialPolicy\";\nexport * from \"../../storage-blob/src/policies/CredentialPolicy\";\nexport * from \"../../storage-blob/src/StorageRetryPolicyFactory\";\nexport * from \"../../storage-blob/src/policies/StorageSharedKeyCredentialPolicy\";\nexport * from \"../../storage-blob/src/StorageBrowserPolicyFactory\";\nexport { Metadata, StorageQueueAudience, getQueueServiceAccountAudience } from \"./models\";\nexport * from \"./QueueClient\";\nexport * from \"./QueueSASPermissions\";\nexport {\n  QueueSASSignatureValues,\n  generateQueueSASQueryParameters,\n} from \"./QueueSASSignatureValues\";\nexport * from \"./QueueServiceClient\";\nexport * from \"./SASQueryParameters\";\nexport { CommonOptions, ListQueuesIncludeType } from \"./StorageClient\";\nexport * from \"./generatedModels\";\nexport {\n  WithResponse,\n  ResponseLike,\n  ResponseWithBody,\n  ResponseWithHeaders,\n  HttpResponse,\n} from \"./utils/utils.common\";\nexport { RestError };\nexport { logger } from \"./log\";\n"]}