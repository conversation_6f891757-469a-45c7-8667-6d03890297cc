{"version": 3, "file": "decompressResponsePolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/decompressResponsePolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAIlC,OAAO,EACL,4BAA4B,IAAI,+BAA+B,EAC/D,wBAAwB,IAAI,2BAA2B,GACxD,MAAM,6CAA6C,CAAC;AAErD;;GAEG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,+BAA+B,CAAC;AAE5E;;;GAGG;AACH,MAAM,UAAU,wBAAwB;IACtC,OAAO,2BAA2B,EAAE,CAAC;AACvC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  decompressResponsePolicyName as tspDecompressResponsePolicyName,\n  decompressResponsePolicy as tspDecompressResponsePolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the decompressResponsePolicy.\n */\nexport const decompressResponsePolicyName = tspDecompressResponsePolicyName;\n\n/**\n * A policy to enable response decompression according to Accept-Encoding header\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding\n */\nexport function decompressResponsePolicy(): PipelinePolicy {\n  return tspDecompressResponsePolicy();\n}\n"]}