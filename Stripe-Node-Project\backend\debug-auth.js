// Debug the authentication process step by step
require('dotenv').config();
const { supabaseAdmin } = require('./src/config/database');

async function debugAuth() {
    console.log('🔍 Debugging authentication process...\n');
    
    const testApiKey = '1433bf7619d3ceb6f3f3fe579a36bc72af6bd3d678b81cae48471dc8ca92d23a';
    
    try {
        console.log('🔑 Testing API key lookup...');
        console.log(`   API Key: ${testApiKey}`);
        
        // Test the exact query that the auth middleware uses
        console.log('\n📋 Query 1: Looking for exact key_hash match...');
        const { data: apiKeyData1, error: error1 } = await supabaseAdmin
            .from('api_keys')
            .select('*')
            .eq('key_hash', testApiKey)
            .eq('is_active', true);
            
        console.log('   Result:', apiKeyData1);
        console.log('   Error:', error1);
        
        console.log('\n📋 Query 2: Looking for any key_hash containing this value...');
        const { data: apiKeyData2, error: error2 } = await supabaseAdmin
            .from('api_keys')
            .select('*')
            .ilike('key_hash', `%${testApiKey}%`);
            
        console.log('   Result:', apiKeyData2);
        console.log('   Error:', error2);
        
        console.log('\n📋 Query 3: Get all API keys to compare...');
        const { data: allKeys, error: error3 } = await supabaseAdmin
            .from('api_keys')
            .select('*');
            
        if (allKeys) {
            console.log(`   Found ${allKeys.length} total API keys:`);
            allKeys.forEach((key, index) => {
                console.log(`   ${index + 1}. Key Hash: "${key.key_hash}"`);
                console.log(`      Length: ${key.key_hash ? key.key_hash.length : 'NULL'}`);
                console.log(`      Is Active: ${key.is_active}`);
                console.log(`      Exact Match: ${key.key_hash === testApiKey}`);
                console.log('');
            });
        }
        
        console.log('\n🔍 Character-by-character comparison:');
        if (allKeys && allKeys.length > 0) {
            const dbKey = allKeys[0].key_hash;
            console.log(`   DB Key:   "${dbKey}"`);
            console.log(`   Test Key: "${testApiKey}"`);
            console.log(`   DB Length: ${dbKey.length}`);
            console.log(`   Test Length: ${testApiKey.length}`);
            
            if (dbKey.length !== testApiKey.length) {
                console.log('   ❌ Length mismatch!');
            }
            
            for (let i = 0; i < Math.max(dbKey.length, testApiKey.length); i++) {
                if (dbKey[i] !== testApiKey[i]) {
                    console.log(`   ❌ Difference at position ${i}: DB="${dbKey[i]}" vs Test="${testApiKey[i]}"`);
                    break;
                }
            }
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

debugAuth();
