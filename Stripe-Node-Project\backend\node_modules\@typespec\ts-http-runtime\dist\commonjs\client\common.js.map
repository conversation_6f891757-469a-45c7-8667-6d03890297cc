{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../src/client/common.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  HttpClient,\n  PipelineRequest,\n  PipelineResponse,\n  RawHttpHeaders,\n  RequestBodyType,\n  TransferProgressEvent,\n  RawHttpHeadersInput,\n} from \"../interfaces.js\";\nimport type { Pipeline, PipelinePolicy } from \"../pipeline.js\";\nimport type { PipelineOptions } from \"../createPipelineFromOptions.js\";\nimport type { LogPolicyOptions } from \"../policies/logPolicy.js\";\nimport type { AuthScheme } from \"../auth/schemes.js\";\nimport type { ClientCredential } from \"../auth/credentials.js\";\n\n/**\n * Shape of the default request parameters, this may be overridden by the specific\n * request types to provide strong types\n */\nexport type RequestParameters = {\n  /**\n   * Headers to send along with the request\n   */\n  headers?: RawHttpHeadersInput;\n  /**\n   * Sets the accept header to send to the service\n   * defaults to 'application/json'. If also a header \"accept\" is set\n   * this property will take precedence.\n   */\n  accept?: string;\n  /**\n   * Body to send with the request\n   */\n  body?: unknown;\n  /**\n   * Query parameters to send with the request\n   */\n  queryParameters?: Record<string, unknown>;\n  /**\n   * Set an explicit content-type to send with the request. If also a header \"content-type\" is set\n   * this property will take precedence.\n   */\n  contentType?: string;\n  /** Set to true if the request is sent over HTTP instead of HTTPS */\n  allowInsecureConnection?: boolean;\n  /** Set to true if you want to skip encoding the path parameters */\n  skipUrlEncoding?: boolean;\n  /**\n   * Path parameters for custom the base url\n   */\n  pathParameters?: Record<string, any>;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignal;\n\n  /**\n   * A function to be called each time a response is received from the server\n   * while performing the requested operation.\n   * May be called multiple times.\n   */\n  onResponse?: RawResponseCallback;\n};\n\n/**\n * A function to be called each time a response is received from the server\n * while performing the requested operation.\n * May be called multiple times.\n */\n// UNBRANDED DIFFERENCE: onResponse callback does not have a second __legacyError parameter which was provided for backwards compatibility\nexport type RawResponseCallback = (rawResponse: FullOperationResponse, error?: unknown) => void;\n\n/**\n * Wrapper object for http request and response. Deserialized object is stored in\n * the `parsedBody` property when the response body is received in JSON.\n */\nexport interface FullOperationResponse extends PipelineResponse {\n  /**\n   * The raw HTTP response headers.\n   */\n  rawHeaders?: RawHttpHeaders;\n\n  /**\n   * The response body as parsed JSON.\n   */\n  parsedBody?: RequestBodyType;\n\n  /**\n   * The request that generated the response.\n   */\n  request: PipelineRequest;\n}\n\n/**\n * The base options type for all operations.\n */\nexport interface OperationOptions {\n  /**\n   * The signal which can be used to abort requests.\n   */\n  abortSignal?: AbortSignal;\n  /**\n   * Options used when creating and sending HTTP requests for this operation.\n   */\n  requestOptions?: OperationRequestOptions;\n  /**\n   * A function to be called each time a response is received from the server\n   * while performing the requested operation.\n   * May be called multiple times.\n   */\n  onResponse?: RawResponseCallback;\n}\n\n/**\n * Options used when creating and sending HTTP requests for this operation.\n */\nexport interface OperationRequestOptions {\n  /**\n   * User defined custom request headers that\n   * will be applied before the request is sent.\n   */\n  headers?: RawHttpHeadersInput;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   */\n  timeout?: number;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Callback which fires upon download progress.\n   */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /**\n   * Set to true if the request is sent over HTTP instead of HTTPS\n   */\n  allowInsecureConnection?: boolean;\n\n  /**\n   * Set to true if you want to skip encoding the path parameters\n   */\n  skipUrlEncoding?: boolean;\n}\n\n/**\n * Type to use with pathUnchecked, overrides the body type to any to allow flexibility\n */\nexport type PathUncheckedResponse = HttpResponse & { body: any };\n\n/**\n * Shape of a Rest Level Client\n */\nexport interface Client {\n  /**\n   * The pipeline used by this client to make requests\n   */\n  pipeline: Pipeline;\n  /**\n   * This method will be used to send request that would check the path to provide\n   * strong types. When used by the codegen this type gets overridden with the generated\n   * types. For example:\n   * ```typescript snippet:ReadmeSamplePathExample\n   * import { Client } from \"@typespec/ts-http-runtime\";\n   *\n   * type MyClient = Client & {\n   *   path: Routes;\n   * };\n   * ```\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\n  path: Function;\n  /**\n   * This method allows arbitrary paths and doesn't provide strong types\n   */\n  pathUnchecked: PathUnchecked;\n}\n\n/**\n * Http Response which body is a NodeJS stream object\n */\nexport type HttpNodeStreamResponse = HttpResponse & {\n  /**\n   * Streamable body\n   */\n  body?: NodeJS.ReadableStream;\n};\n\n/**\n * Http Response which body is a NodeJS stream object\n */\nexport type HttpBrowserStreamResponse = HttpResponse & {\n  /**\n   * Streamable body\n   */\n  body?: ReadableStream<Uint8Array>;\n};\n\n/**\n * Defines the type for a method that supports getting the response body as\n * a raw stream\n */\nexport type StreamableMethod<TResponse = PathUncheckedResponse> = PromiseLike<TResponse> & {\n  /**\n   * Returns the response body as a NodeJS stream. Only available in Node-like environments.\n   */\n  asNodeStream: () => Promise<HttpNodeStreamResponse>;\n  /**\n   * Returns the response body as a browser (Web) stream. Only available in the browser. If you require a Web Stream of the response in Node, consider using the\n   * `Readable.toWeb` Node API on the result of `asNodeStream`.\n   */\n  asBrowserStream: () => Promise<HttpBrowserStreamResponse>;\n};\n\n/**\n * Defines the signature for pathUnchecked.\n */\nexport type PathUnchecked = <TPath extends string>(\n  path: TPath,\n  ...args: PathParameters<TPath>\n) => ResourceMethods<StreamableMethod>;\n\n/**\n * Defines the methods that can be called on a resource\n */\nexport interface ResourceMethods<TResponse = PromiseLike<PathUncheckedResponse>> {\n  /**\n   * Definition of the GET HTTP method for a resource\n   */\n  get: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the POST HTTP method for a resource\n   */\n  post: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the PUT HTTP method for a resource\n   */\n  put: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the PATCH HTTP method for a resource\n   */\n  patch: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the DELETE HTTP method for a resource\n   */\n  delete: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the HEAD HTTP method for a resource\n   */\n  head: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the OPTIONS HTTP method for a resource\n   */\n  options: (options?: RequestParameters) => TResponse;\n  /**\n   * Definition of the TRACE HTTP method for a resource\n   */\n  trace: (options?: RequestParameters) => TResponse;\n}\n\n/**\n * Used to configure additional policies added to the pipeline at construction.\n */\nexport interface AdditionalPolicyConfig {\n  /**\n   * A policy to be added.\n   */\n  policy: PipelinePolicy;\n  /**\n   * Determines if this policy be applied before or after retry logic.\n   * Only use `perRetry` if you need to modify the request again\n   * each time the operation is retried due to retryable service\n   * issues.\n   */\n  position: \"perCall\" | \"perRetry\";\n}\n\n/**\n * General options that a Rest Level Client can take\n */\nexport type ClientOptions = PipelineOptions & {\n  /**\n   * List of authentication schemes supported by the client.\n   * These schemes define how the client can authenticate requests.\n   */\n  authSchemes?: AuthScheme[];\n\n  /**\n   * The credential used to authenticate requests.\n   * Must be compatible with one of the specified authentication schemes.\n   */\n  credential?: ClientCredential;\n\n  // UNBRANDED DIFFERENCE: The deprecated baseUrl property is removed in favor of the endpoint property in the unbranded Core package\n\n  /**\n   * Endpoint for the client\n   */\n  endpoint?: string;\n  /**\n   * Options for setting a custom apiVersion.\n   */\n  apiVersion?: string;\n  /**\n   * Option to allow calling http (insecure) endpoints\n   */\n  allowInsecureConnection?: boolean;\n  /**\n   * Additional policies to include in the HTTP pipeline.\n   */\n  additionalPolicies?: AdditionalPolicyConfig[];\n  /**\n   * Specify a custom HttpClient when making requests.\n   */\n  httpClient?: HttpClient;\n  /**\n   * Options to configure request/response logging.\n   */\n  loggingOptions?: LogPolicyOptions;\n  /**\n   * Pipeline to use for the client. If not provided, a default pipeline will be created using the options provided.\n   * Use with caution -- when setting this option, all client options that are used in the creation of the default pipeline\n   * will be ignored.\n   */\n  pipeline?: Pipeline;\n};\n\n/**\n * Represents the shape of an HttpResponse\n */\nexport type HttpResponse = {\n  /**\n   * The request that generated this response.\n   */\n  request: PipelineRequest;\n  /**\n   * The HTTP response headers.\n   */\n  headers: RawHttpHeaders;\n  /**\n   * Parsed body\n   */\n  body: unknown;\n  /**\n   * The HTTP status code of the response.\n   */\n  status: string;\n};\n\n/**\n * Helper type used to detect parameters in a path template\n * text surrounded by \\{\\} will be considered a path parameter\n */\nexport type PathParameters<\n  TRoute extends string,\n  // This is trying to match the string in TRoute with a template where HEAD/{PARAM}/TAIL\n  // for example in the followint path: /foo/{fooId}/bar/{barId}/baz the template will infer\n  // HEAD: /foo\n  // Param: fooId\n  // Tail: /bar/{barId}/baz\n  // The above sample path would return [pathParam: string, pathParam: string]\n> = TRoute extends `${infer _Head}/{${infer _Param}}${infer Tail}`\n  ? // In case we have a match for the template above we know for sure\n    // that we have at least one pathParameter, that's why we set the first pathParam\n    // in the tuple. At this point we have only matched up until param, if we want to identify\n    // additional parameters we can call RouteParameters recursively on the Tail to match the remaining parts,\n    // in case the Tail has more parameters, it will return a tuple with the parameters found in tail.\n    // We spread the second path params to end up with a single dimension tuple at the end.\n    [\n      pathParameter: string | number | PathParameterWithOptions,\n      ...pathParameters: PathParameters<Tail>,\n    ]\n  : // When the path doesn't match the template, it means that we have no path parameters so we return\n    // an empty tuple.\n    [];\n\n/** A response containing error details. */\nexport interface ErrorResponse {\n  /** The error object. */\n  error: ErrorModel;\n}\n\n/** The error object. */\nexport interface ErrorModel {\n  /** One of a server-defined set of error codes. */\n  code: string;\n  /** A human-readable representation of the error. */\n  message: string;\n  /** The target of the error. */\n  target?: string;\n  /** An array of details about specific errors that led to this reported error. */\n  details: Array<ErrorModel>;\n  /** An object containing more specific information than the current object about the error. */\n  innererror?: InnerError;\n}\n\n/** An object containing more specific information about the error. As per Microsoft One API guidelines - https://github.com/Microsoft/api-guidelines/blob/vNext/Guidelines.md#7102-error-condition-responses. */\nexport interface InnerError {\n  /** One of a server-defined set of error codes. */\n  code: string;\n  /** Inner error. */\n  innererror?: InnerError;\n}\n\n/**\n * An object that can be passed as a path parameter, allowing for additional options to be set relating to how the parameter is encoded.\n */\nexport interface PathParameterWithOptions {\n  /**\n   * The value of the parameter.\n   */\n  value: string | number;\n\n  /**\n   * Whether to allow for reserved characters in the value. If set to true, special characters such as '/' in the parameter's value will not be URL encoded.\n   * Defaults to false.\n   */\n  allowReserved?: boolean;\n}\n"]}