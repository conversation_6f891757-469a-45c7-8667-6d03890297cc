{"version": 3, "file": "messages.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/messages.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAcnD,4CAA4C;AAC5C,MAAM,OAAO,YAAY;IAGvB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,OAAO,CACL,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,oBAAoB,CAAC,CAAC;IAC7E,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,OAAqC;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,kBAAkB,CAAC,CAAC;IAC3E,CAAC;IAED;;;;;;;OAOG;IACH,OAAO,CACL,YAA0B,EAC1B,OAAuC;QAEvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,YAAY,EAAE,OAAO,EAAE,EACzB,oBAAoB,CACrB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,IAAI,CAAC,OAAoC;QACvC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,iBAAiB,CAAC,CAAC;IAC1E,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,oBAAoB,GAA6B;IACrD,IAAI,EAAE,uBAAuB;IAC7B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,qBAAqB,EAAE;qBAC9D;iBACF;gBACD,cAAc,EAAE,sBAAsB;gBACtC,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,cAAc;aAC/B;YACD,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,iBAAiB;KAC7B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,kBAAkB,GAA6B;IACnD,IAAI,EAAE,uBAAuB;IAC7B,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,oBAAoB;SAC5C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,6BAA6B;SACrD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,oBAAoB,GAA6B;IACrD,IAAI,EAAE,uBAAuB;IAC7B,UAAU,EAAE,MAAM;IAClB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,iBAAiB,EAAE,EAAE;iBACvE;gBACD,cAAc,EAAE,qBAAqB;gBACrC,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,cAAc;aAC/B;YACD,aAAa,EAAE,OAAO,CAAC,sBAAsB;SAC9C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,+BAA+B;SACvD;KACF;IACD,WAAW,EAAE,UAAU,CAAC,YAAY;IACpC,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,iBAAiB;QAC5B,UAAU,CAAC,iBAAiB;KAC7B;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,iBAAiB,GAA6B;IAClD,IAAI,EAAE,uBAAuB;IAC7B,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,mBAAmB,EAAE;qBAC5D;iBACF;gBACD,cAAc,EAAE,oBAAoB;gBACpC,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,cAAc;aAC/B;YACD,aAAa,EAAE,OAAO,CAAC,mBAAmB;SAC3C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,4BAA4B;SACpD;KACF;IACD,eAAe,EAAE;QACf,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,gBAAgB;QAC3B,UAAU,CAAC,QAAQ;KACpB;IACD,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { Messages } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  MessagesDequeueOptionalParams,\n  MessagesDequeueResponse,\n  MessagesClearOptionalParams,\n  MessagesClearResponse,\n  QueueMessage,\n  MessagesEnqueueOptionalParams,\n  MessagesEnqueueResponse,\n  MessagesPeekOptionalParams,\n  MessagesPeekResponse\n} from \"../models\";\n\n/** Class containing Messages operations. */\nexport class MessagesImpl implements Messages {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class Messages class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * The Dequeue operation retrieves one or more messages from the front of the queue.\n   * @param options The options parameters.\n   */\n  dequeue(\n    options?: MessagesDequeueOptionalParams\n  ): Promise<MessagesDequeueResponse> {\n    return this.client.sendOperationRequest({ options }, dequeueOperationSpec);\n  }\n\n  /**\n   * The Clear operation deletes all messages from the specified queue.\n   * @param options The options parameters.\n   */\n  clear(options?: MessagesClearOptionalParams): Promise<MessagesClearResponse> {\n    return this.client.sendOperationRequest({ options }, clearOperationSpec);\n  }\n\n  /**\n   * The Enqueue operation adds a new message to the back of the message queue. A visibility timeout can\n   * also be specified to make the message invisible until the visibility timeout expires. A message must\n   * be in a format that can be included in an XML request with UTF-8 encoding. The encoded message can\n   * be up to 64 KB in size for versions 2011-08-18 and newer, or 8 KB in size for previous versions.\n   * @param queueMessage A Message object which can be stored in a Queue\n   * @param options The options parameters.\n   */\n  enqueue(\n    queueMessage: QueueMessage,\n    options?: MessagesEnqueueOptionalParams\n  ): Promise<MessagesEnqueueResponse> {\n    return this.client.sendOperationRequest(\n      { queueMessage, options },\n      enqueueOperationSpec\n    );\n  }\n\n  /**\n   * The Peek operation retrieves one or more messages from the front of the queue, but does not alter\n   * the visibility of the message.\n   * @param options The options parameters.\n   */\n  peek(options?: MessagesPeekOptionalParams): Promise<MessagesPeekResponse> {\n    return this.client.sendOperationRequest({ options }, peekOperationSpec);\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst dequeueOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}/messages\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"DequeuedMessageItem\" }\n          }\n        },\n        serializedName: \"DequeuedMessagesList\",\n        xmlName: \"QueueMessagesList\",\n        xmlIsWrapped: true,\n        xmlElementName: \"QueueMessage\"\n      },\n      headersMapper: Mappers.MessagesDequeueHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.MessagesDequeueExceptionHeaders\n    }\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.numberOfMessages,\n    Parameters.visibilityTimeout\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst clearOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}/messages\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {\n      headersMapper: Mappers.MessagesClearHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.MessagesClearExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst enqueueOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}/messages\",\n  httpMethod: \"POST\",\n  responses: {\n    201: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: { type: { name: \"Composite\", className: \"EnqueuedMessage\" } }\n        },\n        serializedName: \"EnqueuedMessageList\",\n        xmlName: \"QueueMessagesList\",\n        xmlIsWrapped: true,\n        xmlElementName: \"QueueMessage\"\n      },\n      headersMapper: Mappers.MessagesEnqueueHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.MessagesEnqueueExceptionHeaders\n    }\n  },\n  requestBody: Parameters.queueMessage,\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.visibilityTimeout,\n    Parameters.messageTimeToLive\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer\n};\nconst peekOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}/messages\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"PeekedMessageItem\" }\n          }\n        },\n        serializedName: \"PeekedMessagesList\",\n        xmlName: \"QueueMessagesList\",\n        xmlIsWrapped: true,\n        xmlElementName: \"QueueMessage\"\n      },\n      headersMapper: Mappers.MessagesPeekHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.MessagesPeekExceptionHeaders\n    }\n  },\n  queryParameters: [\n    Parameters.timeoutInSeconds,\n    Parameters.numberOfMessages,\n    Parameters.peekonly\n  ],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\n"]}