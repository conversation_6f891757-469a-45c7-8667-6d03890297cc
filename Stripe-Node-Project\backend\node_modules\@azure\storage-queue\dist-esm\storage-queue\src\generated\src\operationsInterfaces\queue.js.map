{"version": 3, "file": "queue.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/queue.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  QueueCreateOptionalParams,\n  QueueCreateResponse,\n  QueueDeleteOptionalParams,\n  QueueDeleteResponse,\n  QueueGetPropertiesOptionalParams,\n  QueueGetPropertiesResponse,\n  QueueSetMetadataOptionalParams,\n  QueueSetMetadataResponse,\n  QueueGetAccessPolicyOptionalParams,\n  QueueGetAccessPolicyResponse,\n  QueueSetAccessPolicyOptionalParams,\n  QueueSetAccessPolicyResponse\n} from \"../models\";\n\n/** Interface representing a Queue. */\nexport interface Queue {\n  /**\n   * creates a new queue under the given account.\n   * @param options The options parameters.\n   */\n  create(options?: QueueCreateOptionalParams): Promise<QueueCreateResponse>;\n  /**\n   * operation permanently deletes the specified queue\n   * @param options The options parameters.\n   */\n  delete(options?: QueueDeleteOptionalParams): Promise<QueueDeleteResponse>;\n  /**\n   * Retrieves user-defined metadata and queue properties on the specified queue. Metadata is associated\n   * with the queue as name-values pairs.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: QueueGetPropertiesOptionalParams\n  ): Promise<QueueGetPropertiesResponse>;\n  /**\n   * sets user-defined metadata on the specified queue. Metadata is associated with the queue as\n   * name-value pairs.\n   * @param options The options parameters.\n   */\n  setMetadata(\n    options?: QueueSetMetadataOptionalParams\n  ): Promise<QueueSetMetadataResponse>;\n  /**\n   * returns details about any stored access policies specified on the queue that may be used with Shared\n   * Access Signatures.\n   * @param options The options parameters.\n   */\n  getAccessPolicy(\n    options?: QueueGetAccessPolicyOptionalParams\n  ): Promise<QueueGetAccessPolicyResponse>;\n  /**\n   * sets stored access policies for the queue that may be used with Shared Access Signatures\n   * @param options The options parameters.\n   */\n  setAccessPolicy(\n    options?: QueueSetAccessPolicyOptionalParams\n  ): Promise<QueueSetAccessPolicyResponse>;\n}\n"]}