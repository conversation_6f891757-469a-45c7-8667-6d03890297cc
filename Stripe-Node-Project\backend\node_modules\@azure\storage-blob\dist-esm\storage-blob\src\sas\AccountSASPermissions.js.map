{"version": 3, "file": "AccountSASPermissions.js", "sourceRoot": "", "sources": ["../../../../src/sas/AccountSASPermissions.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;GAQG;AACH,MAAM,OAAO,qBAAqB;IAAlC;QA4GE;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,UAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,kBAAa,GAAY,KAAK,CAAC;QAEtC;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,YAAO,GAAY,KAAK,CAAC;QAEhC;;WAEG;QACI,QAAG,GAAY,KAAK,CAAC;QAE5B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;QAE/B;;WAEG;QACI,0BAAqB,GAAY,KAAK,CAAC;QAE9C;;WAEG;QACI,oBAAe,GAAY,KAAK,CAAC;IA0D1C,CAAC;IApOC;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,WAAmB;QACrC,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAE1D,KAAK,MAAM,CAAC,IAAI,WAAW,EAAE,CAAC;YAC5B,QAAQ,CAAC,EAAE,CAAC;gBACV,KAAK,GAAG;oBACN,qBAAqB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAClC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC;oBACnC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,aAAa,GAAG,IAAI,CAAC;oBAC3C,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAClC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,GAAG,GAAG,IAAI,CAAC;oBACjC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC;oBACrC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,GAAG,GAAG,IAAI,CAAC;oBACjC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;oBACpC,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,qBAAqB,GAAG,IAAI,CAAC;oBACnD,MAAM;gBACR,KAAK,GAAG;oBACN,qBAAqB,CAAC,eAAe,GAAG,IAAI,CAAC;oBAC7C,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,iCAAiC,CAAC,EAAE,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,IAAI,CAAC,cAAyC;QAC1D,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;QAC1D,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,qBAAqB,CAAC,IAAI,GAAG,IAAI,CAAC;QACpC,CAAC;QACD,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;YACzB,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC;QACrC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;YACjC,qBAAqB,CAAC,aAAa,GAAG,IAAI,CAAC;QAC7C,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC;YACvB,qBAAqB,CAAC,GAAG,GAAG,IAAI,CAAC;QACnC,CAAC;QACD,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,qBAAqB,CAAC,IAAI,GAAG,IAAI,CAAC;QACpC,CAAC;QACD,IAAI,cAAc,CAAC,GAAG,EAAE,CAAC;YACvB,qBAAqB,CAAC,GAAG,GAAG,IAAI,CAAC;QACnC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;YAC1B,qBAAqB,CAAC,MAAM,GAAG,IAAI,CAAC;QACtC,CAAC;QACD,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,qBAAqB,CAAC,OAAO,GAAG,IAAI,CAAC;QACvC,CAAC;QACD,IAAI,cAAc,CAAC,qBAAqB,EAAE,CAAC;YACzC,qBAAqB,CAAC,qBAAqB,GAAG,IAAI,CAAC;QACrD,CAAC;QACD,IAAI,cAAc,CAAC,eAAe,EAAE,CAAC;YACnC,qBAAqB,CAAC,eAAe,GAAG,IAAI,CAAC;QAC/C,CAAC;QACD,OAAO,qBAAqB,CAAC;IAC/B,CAAC;IAmED;;;;;;;;;OASG;IACI,QAAQ;QACb,iFAAiF;QACjF,yFAAyF;QACzF,iFAAiF;QACjF,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC;QACD,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * This is a helper class to construct a string representing the permissions granted by an AccountSAS. Setting a value\n * to true means that any SAS which uses these permissions will grant permissions for that operation. Once all the\n * values are set, this should be serialized with toString and set as the permissions field on an\n * {@link AccountSASSignatureValues} object. It is possible to construct the permissions string without this class, but\n * the order of the permissions is particular and this class guarantees correctness.\n */\nexport class AccountSASPermissions {\n  /**\n   * Parse initializes the AccountSASPermissions fields from a string.\n   *\n   * @param permissions -\n   */\n  public static parse(permissions: string): AccountSASPermissions {\n    const accountSASPermissions = new AccountSASPermissions();\n\n    for (const c of permissions) {\n      switch (c) {\n        case \"r\":\n          accountSASPermissions.read = true;\n          break;\n        case \"w\":\n          accountSASPermissions.write = true;\n          break;\n        case \"d\":\n          accountSASPermissions.delete = true;\n          break;\n        case \"x\":\n          accountSASPermissions.deleteVersion = true;\n          break;\n        case \"l\":\n          accountSASPermissions.list = true;\n          break;\n        case \"a\":\n          accountSASPermissions.add = true;\n          break;\n        case \"c\":\n          accountSASPermissions.create = true;\n          break;\n        case \"u\":\n          accountSASPermissions.update = true;\n          break;\n        case \"p\":\n          accountSASPermissions.process = true;\n          break;\n        case \"t\":\n          accountSASPermissions.tag = true;\n          break;\n        case \"f\":\n          accountSASPermissions.filter = true;\n          break;\n        case \"i\":\n          accountSASPermissions.setImmutabilityPolicy = true;\n          break;\n        case \"y\":\n          accountSASPermissions.permanentDelete = true;\n          break;\n        default:\n          throw new RangeError(`Invalid permission character: ${c}`);\n      }\n    }\n\n    return accountSASPermissions;\n  }\n\n  /**\n   * Creates a {@link AccountSASPermissions} from a raw object which contains same keys as it\n   * and boolean values for them.\n   *\n   * @param permissionLike -\n   */\n  public static from(permissionLike: AccountSASPermissionsLike): AccountSASPermissions {\n    const accountSASPermissions = new AccountSASPermissions();\n    if (permissionLike.read) {\n      accountSASPermissions.read = true;\n    }\n    if (permissionLike.write) {\n      accountSASPermissions.write = true;\n    }\n    if (permissionLike.delete) {\n      accountSASPermissions.delete = true;\n    }\n    if (permissionLike.deleteVersion) {\n      accountSASPermissions.deleteVersion = true;\n    }\n    if (permissionLike.filter) {\n      accountSASPermissions.filter = true;\n    }\n    if (permissionLike.tag) {\n      accountSASPermissions.tag = true;\n    }\n    if (permissionLike.list) {\n      accountSASPermissions.list = true;\n    }\n    if (permissionLike.add) {\n      accountSASPermissions.add = true;\n    }\n    if (permissionLike.create) {\n      accountSASPermissions.create = true;\n    }\n    if (permissionLike.update) {\n      accountSASPermissions.update = true;\n    }\n    if (permissionLike.process) {\n      accountSASPermissions.process = true;\n    }\n    if (permissionLike.setImmutabilityPolicy) {\n      accountSASPermissions.setImmutabilityPolicy = true;\n    }\n    if (permissionLike.permanentDelete) {\n      accountSASPermissions.permanentDelete = true;\n    }\n    return accountSASPermissions;\n  }\n\n  /**\n   * Permission to read resources and list queues and tables granted.\n   */\n  public read: boolean = false;\n\n  /**\n   * Permission to write resources granted.\n   */\n  public write: boolean = false;\n\n  /**\n   * Permission to delete blobs and files granted.\n   */\n  public delete: boolean = false;\n\n  /**\n   * Permission to delete versions granted.\n   */\n  public deleteVersion: boolean = false;\n\n  /**\n   * Permission to list blob containers, blobs, shares, directories, and files granted.\n   */\n  public list: boolean = false;\n\n  /**\n   * Permission to add messages, table entities, and append to blobs granted.\n   */\n  public add: boolean = false;\n\n  /**\n   * Permission to create blobs and files granted.\n   */\n  public create: boolean = false;\n\n  /**\n   * Permissions to update messages and table entities granted.\n   */\n  public update: boolean = false;\n\n  /**\n   * Permission to get and delete messages granted.\n   */\n  public process: boolean = false;\n\n  /**\n   * Specfies Tag access granted.\n   */\n  public tag: boolean = false;\n\n  /**\n   * Permission to filter blobs.\n   */\n  public filter: boolean = false;\n\n  /**\n   * Permission to set immutability policy.\n   */\n  public setImmutabilityPolicy: boolean = false;\n\n  /**\n   * Specifies that Permanent Delete is permitted.\n   */\n  public permanentDelete: boolean = false;\n\n  /**\n   * Produces the SAS permissions string for an Azure Storage account.\n   * Call this method to set AccountSASSignatureValues Permissions field.\n   *\n   * Using this method will guarantee the resource types are in\n   * an order accepted by the service.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n   *\n   */\n  public toString(): string {\n    // The order of the characters should be as specified here to ensure correctness:\n    // https://learn.microsoft.com/en-us/rest/api/storageservices/constructing-an-account-sas\n    // Use a string array instead of string concatenating += operator for performance\n    const permissions: string[] = [];\n    if (this.read) {\n      permissions.push(\"r\");\n    }\n    if (this.write) {\n      permissions.push(\"w\");\n    }\n    if (this.delete) {\n      permissions.push(\"d\");\n    }\n    if (this.deleteVersion) {\n      permissions.push(\"x\");\n    }\n    if (this.filter) {\n      permissions.push(\"f\");\n    }\n    if (this.tag) {\n      permissions.push(\"t\");\n    }\n    if (this.list) {\n      permissions.push(\"l\");\n    }\n    if (this.add) {\n      permissions.push(\"a\");\n    }\n    if (this.create) {\n      permissions.push(\"c\");\n    }\n    if (this.update) {\n      permissions.push(\"u\");\n    }\n    if (this.process) {\n      permissions.push(\"p\");\n    }\n    if (this.setImmutabilityPolicy) {\n      permissions.push(\"i\");\n    }\n    if (this.permanentDelete) {\n      permissions.push(\"y\");\n    }\n    return permissions.join(\"\");\n  }\n}\n\n/**\n * A type that looks like an account SAS permission.\n * Used in {@link AccountSASPermissions} to parse SAS permissions from raw objects.\n */\nexport interface AccountSASPermissionsLike {\n  /**\n   * Permission to read resources and list queues and tables granted.\n   */\n  read?: boolean;\n\n  /**\n   * Permission to write resources granted.\n   */\n  write?: boolean;\n\n  /**\n   * Permission to delete blobs and files granted.\n   */\n  delete?: boolean;\n\n  /**\n   * Permission to delete versions granted.\n   */\n  deleteVersion?: boolean;\n\n  /**\n   * Permission to list blob containers, blobs, shares, directories, and files granted.\n   */\n  list?: boolean;\n\n  /**\n   * Permission to add messages, table entities, and append to blobs granted.\n   */\n  add?: boolean;\n\n  /**\n   * Permission to create blobs and files granted.\n   */\n  create?: boolean;\n\n  /**\n   * Permissions to update messages and table entities granted.\n   */\n  update?: boolean;\n\n  /**\n   * Permission to get and delete messages granted.\n   */\n  process?: boolean;\n\n  /**\n   * Specfies Tag access granted.\n   */\n  tag?: boolean;\n\n  /**\n   * Permission to filter blobs.\n   */\n  filter?: boolean;\n\n  /**\n   * Permission to set immutability policy.\n   */\n  setImmutabilityPolicy?: boolean;\n\n  /**\n   * Specifies that Permanent Delete is permitted.\n   */\n  permanentDelete?: boolean;\n}\n"]}