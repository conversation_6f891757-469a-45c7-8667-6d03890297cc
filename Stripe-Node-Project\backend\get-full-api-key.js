// Get the full API key from the database
require('dotenv').config();
const { supabaseAdmin } = require('./src/config/database');

async function getFullApiKey() {
    console.log('🔑 Getting full API key from database...\n');
    
    try {
        const { data: apiKeys, error } = await supabaseAdmin
            .from('api_keys')
            .select('*')
            .eq('is_active', true);
            
        if (error) {
            console.error('❌ Error fetching API keys:', error);
            return;
        }
        
        if (apiKeys.length === 0) {
            console.log('❌ No active API keys found');
            return;
        }
        
        const apiKey = apiKeys[0];
        console.log('✅ Found active API key:');
        console.log(`   ID: ${apiKey.id}`);
        console.log(`   User ID: ${apiKey.user_id}`);
        console.log(`   Full Key Hash: ${apiKey.key_hash}`);
        console.log(`   WordPress Site: ${apiKey.wordpress_site}`);
        console.log(`   Permissions: ${JSON.stringify(apiKey.permissions)}`);
        console.log(`   Is Active: ${apiKey.is_active}`);
        console.log(`   Created: ${apiKey.created_at}`);
        console.log(`   Last Used: ${apiKey.last_used_at || 'Never'}`);
        
        console.log('\n🎯 Use this API key for testing:');
        console.log(`API Key: ${apiKey.key_hash}`);
        
        return apiKey.key_hash;
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

getFullApiKey();
