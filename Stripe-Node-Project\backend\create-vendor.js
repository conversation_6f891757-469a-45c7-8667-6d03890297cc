const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function createVendor() {
    try {
        console.log('🏪 Creating vendor...');
        
        // First check if vendor already exists
        const { data: existingVendor, error: checkError } = await supabase
            .from('vendors')
            .select('*')
            .eq('email', '<EMAIL>')
            .single();
        
        if (existingVendor) {
            console.log('✅ Vendor already exists:');
            console.log('   ID:', existingVendor.id);
            console.log('   Name:', existingVendor.name);
            console.log('   Email:', existingVendor.email);
            console.log('   Niche:', existingVendor.niche);
            return existingVendor;
        }
        
        // Create new vendor
        const { data: vendor, error: vendorError } = await supabase
            .from('vendors')
            .insert({
                name: 'Test Vendor',
                email: '<EMAIL>',
                niche: 'other',
                country: 'US',
                business_type: 'individual',
                is_active: true
            })
            .select()
            .single();

        if (vendorError) {
            throw vendorError;
        }

        console.log('✅ Vendor created successfully:');
        console.log('   ID:', vendor.id);
        console.log('   Name:', vendor.name);
        console.log('   Email:', vendor.email);
        console.log('   Niche:', vendor.niche);
        
        return vendor;
        
    } catch (error) {
        console.error('❌ Error creating vendor:', error);
        throw error;
    }
}

// Run the script
createVendor()
    .then(() => {
        console.log('🎉 Vendor setup completed!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('💥 Setup failed:', error);
        process.exit(1);
    });
