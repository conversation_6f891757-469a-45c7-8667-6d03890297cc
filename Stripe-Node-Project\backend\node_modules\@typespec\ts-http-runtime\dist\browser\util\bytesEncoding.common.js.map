{"version": 3, "file": "bytesEncoding.common.js", "sourceRoot": "", "sources": ["../../../src/util/bytesEncoding.common.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAWlC;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAiB,EAAE,MAAoB;IACxE,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,OAAO;YACV,OAAO,sBAAsB,CAAC,KAAK,CAAC,CAAC;QACvC,KAAK,QAAQ;YACX,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACnC,KAAK,WAAW;YACd,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtC,KAAK,KAAK;YACR,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAa,EAAE,MAAoB;IACpE,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,OAAO;YACV,OAAO,sBAAsB,CAAC,KAAK,CAAC,CAAC;QACvC,KAAK,QAAQ;YACX,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;QACnC,KAAK,WAAW;YACd,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtC,KAAK,KAAK;YACR,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;IACxC,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAiB;IAClD,OAAO,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAiB;IACrD,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAC7F,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CAAC,KAAiB;IACtD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACzC,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAiB;IACrD,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACzE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,sBAAsB,CAAC,KAAa;IAClD,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAa;IAC9C,OAAO,IAAI,UAAU,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAa;IACjD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACjE,OAAO,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAC1C,CAAC;AAED,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,wBAAwB,CAAC,CAAC;AAEpD;;;GAGG;AACH,MAAM,UAAU,qBAAqB,CAAC,KAAa;IACjD,sGAAsG;IACtG,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QAChC,MAAM,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5D,oFAAoF;YACpF,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,UAAU,GAAG,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;IACvD,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\ndeclare global {\n  // stub these out for the browser\n  function btoa(input: string): string;\n  function atob(input: string): string;\n}\n\n/** The supported character encoding type */\nexport type EncodingType = \"utf-8\" | \"base64\" | \"base64url\" | \"hex\";\n\n/**\n * The helper that transforms bytes with specific character encoding into string\n * @param bytes - the uint8array bytes\n * @param format - the format we use to encode the byte\n * @returns a string of the encoded string\n */\nexport function uint8ArrayToString(bytes: Uint8Array, format: EncodingType): string {\n  switch (format) {\n    case \"utf-8\":\n      return uint8ArrayToUtf8String(bytes);\n    case \"base64\":\n      return uint8ArrayToBase64(bytes);\n    case \"base64url\":\n      return uint8ArrayToBase64Url(bytes);\n    case \"hex\":\n      return uint8ArrayToHexString(bytes);\n  }\n}\n\n/**\n * The helper that transforms string to specific character encoded bytes array.\n * @param value - the string to be converted\n * @param format - the format we use to decode the value\n * @returns a uint8array\n */\nexport function stringToUint8Array(value: string, format: EncodingType): Uint8Array {\n  switch (format) {\n    case \"utf-8\":\n      return utf8StringToUint8Array(value);\n    case \"base64\":\n      return base64ToUint8Array(value);\n    case \"base64url\":\n      return base64UrlToUint8Array(value);\n    case \"hex\":\n      return hexStringToUint8Array(value);\n  }\n}\n\n/**\n * Decodes a Uint8Array into a Base64 string.\n * @internal\n */\nexport function uint8ArrayToBase64(bytes: Uint8Array): string {\n  return btoa([...bytes].map((x) => String.fromCharCode(x)).join(\"\"));\n}\n\n/**\n * Decodes a Uint8Array into a Base64Url string.\n * @internal\n */\nexport function uint8ArrayToBase64Url(bytes: Uint8Array): string {\n  return uint8ArrayToBase64(bytes).replace(/\\+/g, \"-\").replace(/\\//g, \"_\").replace(/=/g, \"\");\n}\n\n/**\n * Decodes a Uint8Array into a javascript string.\n * @internal\n */\nexport function uint8ArrayToUtf8String(bytes: Uint8Array): string {\n  const decoder = new TextDecoder();\n  const dataString = decoder.decode(bytes);\n  return dataString;\n}\n\n/**\n * Decodes a Uint8Array into a hex string\n * @internal\n */\nexport function uint8ArrayToHexString(bytes: Uint8Array): string {\n  return [...bytes].map((x) => x.toString(16).padStart(2, \"0\")).join(\"\");\n}\n\n/**\n * Encodes a JavaScript string into a Uint8Array.\n * @internal\n */\nexport function utf8StringToUint8Array(value: string): Uint8Array {\n  return new TextEncoder().encode(value);\n}\n\n/**\n * Encodes a Base64 string into a Uint8Array.\n * @internal\n */\nexport function base64ToUint8Array(value: string): Uint8Array {\n  return new Uint8Array([...atob(value)].map((x) => x.charCodeAt(0)));\n}\n\n/**\n * Encodes a Base64Url string into a Uint8Array.\n * @internal\n */\nexport function base64UrlToUint8Array(value: string): Uint8Array {\n  const base64String = value.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  return base64ToUint8Array(base64String);\n}\n\nconst hexDigits = new Set(\"0123456789abcdefABCDEF\");\n\n/**\n * Encodes a hex string into a Uint8Array\n * @internal\n */\nexport function hexStringToUint8Array(value: string): Uint8Array {\n  // If value has odd length, the last character will be ignored, consistent with NodeJS Buffer behavior\n  const bytes = new Uint8Array(value.length / 2);\n  for (let i = 0; i < value.length / 2; ++i) {\n    const highNibble = value[2 * i];\n    const lowNibble = value[2 * i + 1];\n    if (!hexDigits.has(highNibble) || !hexDigits.has(lowNibble)) {\n      // Replicate Node Buffer behavior by exiting early when we encounter an invalid byte\n      return bytes.slice(0, i);\n    }\n\n    bytes[i] = parseInt(`${highNibble}${lowNibble}`, 16);\n  }\n\n  return bytes;\n}\n"]}