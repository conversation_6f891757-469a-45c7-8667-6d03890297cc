{"version": 3, "file": "Mutex.js", "sourceRoot": "", "sources": ["../../../../../storage-blob/src/utils/Mutex.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,IAAK,eAGJ;AAHD,WAAK,eAAe;IAClB,yDAAM,CAAA;IACN,6DAAQ,CAAA;AACV,CAAC,EAHI,eAAe,KAAf,eAAe,QAGnB;AAID;;GAEG;AACH,MAAM,OAAO,KAAK;IAChB;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAW;QAClC,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YACnC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,eAAe,CAAC,QAAQ,EAAE,CAAC;gBAChF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC;gBACxC,OAAO,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE;oBAC3B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC;oBACxC,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAW;QACpC,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;YACnC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;YACD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACtB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;IACL,CAAC;IAKO,MAAM,CAAC,aAAa,CAAC,GAAW,EAAE,OAAiB;QACzD,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,GAAW;QACxC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxE,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;YAC5C,YAAY,CAAC,GAAG,EAAE;gBAChB,OAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;;AAlBc,UAAI,GAAuC,EAAE,CAAC;AAC9C,eAAS,GAAkC,EAAE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nenum MutexLockStatus {\n  LOCKED,\n  UNLOCKED,\n}\n\ntype Callback = (...args: any[]) => any;\n\n/**\n * An async mutex lock.\n */\nexport class Mutex {\n  /**\n   * Lock for a specific key. If the lock has been acquired by another customer, then\n   * will wait until getting the lock.\n   *\n   * @param key - lock key\n   */\n  public static async lock(key: string): Promise<void> {\n    return new Promise<void>((resolve) => {\n      if (this.keys[key] === undefined || this.keys[key] === MutexLockStatus.UNLOCKED) {\n        this.keys[key] = MutexLockStatus.LOCKED;\n        resolve();\n      } else {\n        this.onUnlockEvent(key, () => {\n          this.keys[key] = MutexLockStatus.LOCKED;\n          resolve();\n        });\n      }\n    });\n  }\n\n  /**\n   * Unlock a key.\n   *\n   * @param key -\n   */\n  public static async unlock(key: string): Promise<void> {\n    return new Promise<void>((resolve) => {\n      if (this.keys[key] === MutexLockStatus.LOCKED) {\n        this.emitUnlockEvent(key);\n      }\n      delete this.keys[key];\n      resolve();\n    });\n  }\n\n  private static keys: { [key: string]: MutexLockStatus } = {};\n  private static listeners: { [key: string]: Callback[] } = {};\n\n  private static onUnlockEvent(key: string, handler: Callback) {\n    if (this.listeners[key] === undefined) {\n      this.listeners[key] = [handler];\n    } else {\n      this.listeners[key].push(handler);\n    }\n  }\n\n  private static emitUnlockEvent(key: string) {\n    if (this.listeners[key] !== undefined && this.listeners[key].length > 0) {\n      const handler = this.listeners[key].shift();\n      setImmediate(() => {\n        handler!.call(this);\n      });\n    }\n  }\n}\n"]}