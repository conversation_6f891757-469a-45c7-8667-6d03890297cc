{"version": 3, "file": "OTLPMetricExporterBase.js", "sourceRoot": "", "sources": ["../../src/OTLPMetricExporterBase.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAgB,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EACL,sBAAsB,EAEtB,cAAc,GAGf,MAAM,4BAA4B,CAAC;AACpC,OAAO,EACL,gCAAgC,GAEjC,MAAM,6BAA6B,CAAC;AAGrC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,MAAM,CAAC,IAAM,6BAA6B,GACxC,cAAM,OAAA,sBAAsB,CAAC,UAAU,EAAjC,CAAiC,CAAC;AAE1C,MAAM,CAAC,IAAM,wBAAwB,GAAmC,UACtE,cAA8B;IAE9B,QAAQ,cAAc,EAAE;QACtB,KAAK,cAAc,CAAC,OAAO,CAAC;QAC5B,KAAK,cAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,cAAc,CAAC,SAAS,CAAC;QAC9B,KAAK,cAAc,CAAC,gBAAgB;YAClC,OAAO,sBAAsB,CAAC,KAAK,CAAC;QACtC,KAAK,cAAc,CAAC,eAAe,CAAC;QACpC,KAAK,cAAc,CAAC,0BAA0B;YAC5C,OAAO,sBAAsB,CAAC,UAAU,CAAC;KAC5C;AACH,CAAC,CAAC;AAEF,MAAM,CAAC,IAAM,4BAA4B,GAAmC,UAC1E,cAA8B;IAE9B,QAAQ,cAAc,EAAE;QACtB,KAAK,cAAc,CAAC,OAAO,CAAC;QAC5B,KAAK,cAAc,CAAC,SAAS;YAC3B,OAAO,sBAAsB,CAAC,KAAK,CAAC;QACtC,KAAK,cAAc,CAAC,eAAe,CAAC;QACpC,KAAK,cAAc,CAAC,0BAA0B,CAAC;QAC/C,KAAK,cAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,cAAc,CAAC,gBAAgB;YAClC,OAAO,sBAAsB,CAAC,UAAU,CAAC;KAC5C;AACH,CAAC,CAAC;AAEF,SAAS,wCAAwC;IAC/C,IAAM,GAAG,GAAG,MAAM,EAAE,CAAC;IACrB,IAAM,qBAAqB,GACzB,GAAG,CAAC,iDAAiD,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAE7E,IAAI,qBAAqB,KAAK,YAAY,EAAE;QAC1C,OAAO,6BAA6B,CAAC;KACtC;IACD,IAAI,qBAAqB,KAAK,OAAO,EAAE;QACrC,OAAO,wBAAwB,CAAC;KACjC;IACD,IAAI,qBAAqB,KAAK,WAAW,EAAE;QACzC,OAAO,4BAA4B,CAAC;KACrC;IAED,IAAI,CAAC,IAAI,CACP,kEAAgE,GAAG,CAAC,iDAAiD,4FAAyF,CAC/M,CAAC;IACF,OAAO,6BAA6B,CAAC;AACvC,CAAC;AAED,SAAS,yBAAyB,CAChC,qBAE0B;IAE1B,2CAA2C;IAC3C,IAAI,qBAAqB,IAAI,IAAI,EAAE;QACjC,IAAI,qBAAqB,KAAK,gCAAgC,CAAC,KAAK,EAAE;YACpE,OAAO,wBAAwB,CAAC;SACjC;aAAM,IACL,qBAAqB,KAAK,gCAAgC,CAAC,SAAS,EACpE;YACA,OAAO,4BAA4B,CAAC;SACrC;QACD,OAAO,6BAA6B,CAAC;KACtC;IAED,OAAO,wCAAwC,EAAE,CAAC;AACpD,CAAC;AAED;IAWE,gCAAY,QAAW,EAAE,MAAkC;QACzD,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;QAC9B,IAAI,CAAC,+BAA+B,GAAG,yBAAyB,CAC9D,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,qBAAqB,CAC9B,CAAC;IACJ,CAAC;IAED,uCAAM,GAAN,UACE,OAAwB,EACxB,cAA8C;QAE9C,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,EAAE,cAAc,CAAC,CAAC;IACvD,CAAC;IAEK,yCAAQ,GAAd;;;;4BACE,qBAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAA;;wBAAnC,SAAmC,CAAC;;;;;KACrC;IAED,2CAAU,GAAV;QACE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAED,6DAA4B,GAA5B,UACE,cAA8B;QAE9B,OAAO,IAAI,CAAC,+BAA+B,CAAC,cAAc,CAAC,CAAC;IAC9D,CAAC;IACH,6BAAC;AAAD,CAAC,AAtCD,IAsCC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ExportResult, getEnv } from '@opentelemetry/core';\nimport {\n  AggregationTemporality,\n  AggregationTemporalitySelector,\n  InstrumentType,\n  PushMetricExporter,\n  ResourceMetrics,\n} from '@opentelemetry/sdk-metrics';\nimport {\n  AggregationTemporalityPreference,\n  OTLPMetricExporterOptions,\n} from './OTLPMetricExporterOptions';\nimport { OTLPExporterBase } from '@opentelemetry/otlp-exporter-base';\nimport { IExportMetricsServiceRequest } from '@opentelemetry/otlp-transformer';\nimport { diag } from '@opentelemetry/api';\n\nexport const CumulativeTemporalitySelector: AggregationTemporalitySelector =\n  () => AggregationTemporality.CUMULATIVE;\n\nexport const DeltaTemporalitySelector: AggregationTemporalitySelector = (\n  instrumentType: InstrumentType\n) => {\n  switch (instrumentType) {\n    case InstrumentType.COUNTER:\n    case InstrumentType.OBSERVABLE_COUNTER:\n    case InstrumentType.HISTOGRAM:\n    case InstrumentType.OBSERVABLE_GAUGE:\n      return AggregationTemporality.DELTA;\n    case InstrumentType.UP_DOWN_COUNTER:\n    case InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:\n      return AggregationTemporality.CUMULATIVE;\n  }\n};\n\nexport const LowMemoryTemporalitySelector: AggregationTemporalitySelector = (\n  instrumentType: InstrumentType\n) => {\n  switch (instrumentType) {\n    case InstrumentType.COUNTER:\n    case InstrumentType.HISTOGRAM:\n      return AggregationTemporality.DELTA;\n    case InstrumentType.UP_DOWN_COUNTER:\n    case InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:\n    case InstrumentType.OBSERVABLE_COUNTER:\n    case InstrumentType.OBSERVABLE_GAUGE:\n      return AggregationTemporality.CUMULATIVE;\n  }\n};\n\nfunction chooseTemporalitySelectorFromEnvironment() {\n  const env = getEnv();\n  const configuredTemporality =\n    env.OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE.trim().toLowerCase();\n\n  if (configuredTemporality === 'cumulative') {\n    return CumulativeTemporalitySelector;\n  }\n  if (configuredTemporality === 'delta') {\n    return DeltaTemporalitySelector;\n  }\n  if (configuredTemporality === 'lowmemory') {\n    return LowMemoryTemporalitySelector;\n  }\n\n  diag.warn(\n    `OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE is set to '${env.OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE}', but only 'cumulative' and 'delta' are allowed. Using default ('cumulative') instead.`\n  );\n  return CumulativeTemporalitySelector;\n}\n\nfunction chooseTemporalitySelector(\n  temporalityPreference?:\n    | AggregationTemporalityPreference\n    | AggregationTemporality\n): AggregationTemporalitySelector {\n  // Directly passed preference has priority.\n  if (temporalityPreference != null) {\n    if (temporalityPreference === AggregationTemporalityPreference.DELTA) {\n      return DeltaTemporalitySelector;\n    } else if (\n      temporalityPreference === AggregationTemporalityPreference.LOWMEMORY\n    ) {\n      return LowMemoryTemporalitySelector;\n    }\n    return CumulativeTemporalitySelector;\n  }\n\n  return chooseTemporalitySelectorFromEnvironment();\n}\n\nexport class OTLPMetricExporterBase<\n  T extends OTLPExporterBase<\n    OTLPMetricExporterOptions,\n    ResourceMetrics,\n    IExportMetricsServiceRequest\n  >\n> implements PushMetricExporter\n{\n  public _otlpExporter: T;\n  private _aggregationTemporalitySelector: AggregationTemporalitySelector;\n\n  constructor(exporter: T, config?: OTLPMetricExporterOptions) {\n    this._otlpExporter = exporter;\n    this._aggregationTemporalitySelector = chooseTemporalitySelector(\n      config?.temporalityPreference\n    );\n  }\n\n  export(\n    metrics: ResourceMetrics,\n    resultCallback: (result: ExportResult) => void\n  ): void {\n    this._otlpExporter.export([metrics], resultCallback);\n  }\n\n  async shutdown(): Promise<void> {\n    await this._otlpExporter.shutdown();\n  }\n\n  forceFlush(): Promise<void> {\n    return Promise.resolve();\n  }\n\n  selectAggregationTemporality(\n    instrumentType: InstrumentType\n  ): AggregationTemporality {\n    return this._aggregationTemporalitySelector(instrumentType);\n  }\n}\n"]}