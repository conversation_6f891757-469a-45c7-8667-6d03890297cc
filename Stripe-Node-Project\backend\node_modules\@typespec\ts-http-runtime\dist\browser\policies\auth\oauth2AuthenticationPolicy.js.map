{"version": 3, "file": "oauth2AuthenticationPolicy.js", "sourceRoot": "", "sources": ["../../../../src/policies/auth/oauth2AuthenticationPolicy.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAEtE;;GAEG;AACH,MAAM,CAAC,MAAM,8BAA8B,GAAG,4BAA4B,CAAC;AAqB3E;;GAEG;AACH,MAAM,UAAU,0BAA0B,CACxC,OAAkD;IAElD,OAAO;QACL,IAAI,EAAE,8BAA8B;QACpC,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,0FAA0F;YAC1F,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAEzC,MAAM,MAAM,GAAG,MAAA,CAAC,MAAA,OAAO,CAAC,WAAW,mCAAI,OAAO,CAAC,WAAW,CAAC,0CAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;YAE9F,gFAAgF;YAChF,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YACD,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC,KAAiB,EAAE;gBAC9E,WAAW,EAAE,OAAO,CAAC,WAAW;aACjC,CAAC,CAAC;YACH,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OAuth2Flow } from \"../../auth/oauth2Flows.js\";\nimport type { OAuth2TokenCredential } from \"../../auth/credentials.js\";\nimport type { AuthScheme } from \"../../auth/schemes.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../../interfaces.js\";\nimport type { PipelinePolicy } from \"../../pipeline.js\";\nimport { ensureSecureConnection } from \"./checkInsecureConnection.js\";\n\n/**\n * Name of the OAuth2 Authentication Policy\n */\nexport const oauth2AuthenticationPolicyName = \"oauth2AuthenticationPolicy\";\n\n/**\n * Options for configuring the OAuth2 authentication policy\n */\nexport interface OAuth2AuthenticationPolicyOptions<TFlows extends OAuth2Flow> {\n  /**\n   * The OAuth2TokenCredential implementation that can supply the bearer token.\n   */\n  credential: OAuth2TokenCredential<TFlows>;\n  /**\n   * Optional authentication schemes to use. If not provided, schemes from the request will be used.\n   */\n  authSchemes?: AuthScheme[];\n  /**\n   * Allows for connecting to HTTP endpoints instead of enforcing HTTPS.\n   * CAUTION: Never use this option in production.\n   */\n  allowInsecureConnection?: boolean;\n}\n\n/**\n * Gets a pipeline policy that adds authorization header from OAuth2 schemes\n */\nexport function oauth2AuthenticationPolicy<TFlows extends OAuth2Flow>(\n  options: OAuth2AuthenticationPolicyOptions<TFlows>,\n): PipelinePolicy {\n  return {\n    name: oauth2AuthenticationPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      // Ensure allowInsecureConnection is explicitly set when sending request to non-https URLs\n      ensureSecureConnection(request, options);\n\n      const scheme = (request.authSchemes ?? options.authSchemes)?.find((x) => x.kind === \"oauth2\");\n\n      // Skip adding authentication header if no OAuth2 authentication scheme is found\n      if (!scheme) {\n        return next(request);\n      }\n      const token = await options.credential.getOAuth2Token(scheme.flows as TFlows[], {\n        abortSignal: request.abortSignal,\n      });\n      request.headers.set(\"Authorization\", `Bearer ${token}`);\n      return next(request);\n    },\n  };\n}\n"]}