{"version": 3, "file": "queue.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operations/queue.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAGH,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAC;AAC7C,OAAO,KAAK,UAAU,MAAM,sBAAsB,CAAC;AAiBnD,yCAAyC;AACzC,MAAM,OAAO,SAAS;IAGpB;;;OAGG;IACH,YAAY,MAAqB;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,OAAmC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,OAAmC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,OAAO,EAAE,EAAE,mBAAmB,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,OAA0C;QAE1C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,0BAA0B,CAC3B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,WAAW,CACT,OAAwC;QAExC,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,wBAAwB,CACzB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,eAAe,CACb,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,4BAA4B,CAC7B,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,eAAe,CACb,OAA4C;QAE5C,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,CACrC,EAAE,OAAO,EAAE,EACX,4BAA4B,CAC7B,CAAC;IACJ,CAAC;CACF;AACD,2BAA2B;AAC3B,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAE7E,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;KACpB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,mBAAmB,GAA6B;IACpD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,QAAQ;IACpB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,kBAAkB;SAC1C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC;IAC9C,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,0BAA0B,GAA6B;IAC3D,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,yBAAyB;SACjD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,kCAAkC;SAC1D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,wBAAwB,GAA6B;IACzD,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,uBAAuB;SAC/C;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,gCAAgC;SACxD;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,QAAQ;KACpB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,4BAA4B,GAA6B;IAC7D,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,kBAAkB,EAAE;qBAC3D;iBACF;gBACD,cAAc,EAAE,mBAAmB;gBACnC,OAAO,EAAE,mBAAmB;gBAC5B,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,kBAAkB;aACnC;YACD,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;QACpB,UAAU,CAAC,OAAO;KACnB;IACD,KAAK,EAAE,IAAI;IACX,UAAU,EAAE,aAAa;CAC1B,CAAC;AACF,MAAM,4BAA4B,GAA6B;IAC7D,IAAI,EAAE,cAAc;IACpB,UAAU,EAAE,KAAK;IACjB,SAAS,EAAE;QACT,GAAG,EAAE;YACH,aAAa,EAAE,OAAO,CAAC,2BAA2B;SACnD;QACD,OAAO,EAAE;YACP,UAAU,EAAE,OAAO,CAAC,YAAY;YAChC,aAAa,EAAE,OAAO,CAAC,oCAAoC;SAC5D;KACF;IACD,WAAW,EAAE,UAAU,CAAC,QAAQ;IAChC,eAAe,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC;IAC/B,gBAAgB,EAAE;QAChB,UAAU,CAAC,WAAW;QACtB,UAAU,CAAC,MAAM;QACjB,UAAU,CAAC,OAAO;QAClB,UAAU,CAAC,SAAS;KACrB;IACD,KAAK,EAAE,IAAI;IACX,WAAW,EAAE,gCAAgC;IAC7C,SAAS,EAAE,KAAK;IAChB,UAAU,EAAE,aAAa;CAC1B,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport { Queue } from \"../operationsInterfaces\";\nimport * as coreClient from \"@azure/core-client\";\nimport * as Mappers from \"../models/mappers\";\nimport * as Parameters from \"../models/parameters\";\nimport { StorageClient } from \"../storageClient\";\nimport {\n  QueueCreateOptionalParams,\n  QueueCreateResponse,\n  QueueDeleteOptionalParams,\n  QueueDeleteResponse,\n  QueueGetPropertiesOptionalParams,\n  QueueGetPropertiesResponse,\n  QueueSetMetadataOptionalParams,\n  QueueSetMetadataResponse,\n  QueueGetAccessPolicyOptionalParams,\n  QueueGetAccessPolicyResponse,\n  QueueSetAccessPolicyOptionalParams,\n  QueueSetAccessPolicyResponse\n} from \"../models\";\n\n/** Class containing Queue operations. */\nexport class QueueImpl implements Queue {\n  private readonly client: StorageClient;\n\n  /**\n   * Initialize a new instance of the class Queue class.\n   * @param client Reference to the service client\n   */\n  constructor(client: StorageClient) {\n    this.client = client;\n  }\n\n  /**\n   * creates a new queue under the given account.\n   * @param options The options parameters.\n   */\n  create(options?: QueueCreateOptionalParams): Promise<QueueCreateResponse> {\n    return this.client.sendOperationRequest({ options }, createOperationSpec);\n  }\n\n  /**\n   * operation permanently deletes the specified queue\n   * @param options The options parameters.\n   */\n  delete(options?: QueueDeleteOptionalParams): Promise<QueueDeleteResponse> {\n    return this.client.sendOperationRequest({ options }, deleteOperationSpec);\n  }\n\n  /**\n   * Retrieves user-defined metadata and queue properties on the specified queue. Metadata is associated\n   * with the queue as name-values pairs.\n   * @param options The options parameters.\n   */\n  getProperties(\n    options?: QueueGetPropertiesOptionalParams\n  ): Promise<QueueGetPropertiesResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getPropertiesOperationSpec\n    );\n  }\n\n  /**\n   * sets user-defined metadata on the specified queue. Metadata is associated with the queue as\n   * name-value pairs.\n   * @param options The options parameters.\n   */\n  setMetadata(\n    options?: QueueSetMetadataOptionalParams\n  ): Promise<QueueSetMetadataResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setMetadataOperationSpec\n    );\n  }\n\n  /**\n   * returns details about any stored access policies specified on the queue that may be used with Shared\n   * Access Signatures.\n   * @param options The options parameters.\n   */\n  getAccessPolicy(\n    options?: QueueGetAccessPolicyOptionalParams\n  ): Promise<QueueGetAccessPolicyResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      getAccessPolicyOperationSpec\n    );\n  }\n\n  /**\n   * sets stored access policies for the queue that may be used with Shared Access Signatures\n   * @param options The options parameters.\n   */\n  setAccessPolicy(\n    options?: QueueSetAccessPolicyOptionalParams\n  ): Promise<QueueSetAccessPolicyResponse> {\n    return this.client.sendOperationRequest(\n      { options },\n      setAccessPolicyOperationSpec\n    );\n  }\n}\n// Operation Specifications\nconst xmlSerializer = coreClient.createSerializer(Mappers, /* isXml */ true);\n\nconst createOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    201: {\n      headersMapper: Mappers.QueueCreateHeaders\n    },\n    204: {\n      headersMapper: Mappers.QueueCreateHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.QueueCreateExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst deleteOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}\",\n  httpMethod: \"DELETE\",\n  responses: {\n    204: {\n      headersMapper: Mappers.QueueDeleteHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.QueueDeleteExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst getPropertiesOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      headersMapper: Mappers.QueueGetPropertiesHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.QueueGetPropertiesExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp3],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst setMetadataOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {\n      headersMapper: Mappers.QueueSetMetadataHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.QueueSetMetadataExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp3],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1,\n    Parameters.metadata\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst getAccessPolicyOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}\",\n  httpMethod: \"GET\",\n  responses: {\n    200: {\n      bodyMapper: {\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: { name: \"Composite\", className: \"SignedIdentifier\" }\n          }\n        },\n        serializedName: \"SignedIdentifiers\",\n        xmlName: \"SignedIdentifiers\",\n        xmlIsWrapped: true,\n        xmlElementName: \"SignedIdentifier\"\n      },\n      headersMapper: Mappers.QueueGetAccessPolicyHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.QueueGetAccessPolicyExceptionHeaders\n    }\n  },\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp4],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.version,\n    Parameters.requestId,\n    Parameters.accept1\n  ],\n  isXML: true,\n  serializer: xmlSerializer\n};\nconst setAccessPolicyOperationSpec: coreClient.OperationSpec = {\n  path: \"/{queueName}\",\n  httpMethod: \"PUT\",\n  responses: {\n    204: {\n      headersMapper: Mappers.QueueSetAccessPolicyHeaders\n    },\n    default: {\n      bodyMapper: Mappers.StorageError,\n      headersMapper: Mappers.QueueSetAccessPolicyExceptionHeaders\n    }\n  },\n  requestBody: Parameters.queueAcl,\n  queryParameters: [Parameters.timeoutInSeconds, Parameters.comp4],\n  urlParameters: [Parameters.url],\n  headerParameters: [\n    Parameters.contentType,\n    Parameters.accept,\n    Parameters.version,\n    Parameters.requestId\n  ],\n  isXML: true,\n  contentType: \"application/xml; charset=utf-8\",\n  mediaType: \"xml\",\n  serializer: xmlSerializer\n};\n"]}