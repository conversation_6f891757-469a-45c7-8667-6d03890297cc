{"version": 3, "file": "mappers.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/models/mappers.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAIH,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,wBAAwB;IACxC,OAAO,EAAE,0BAA0B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,qBAAqB,EAAE;gBACrB,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,SAAS;iBACrB;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,SAAS;iBACrB;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,eAAe;gBAC/B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,SAAS;iBACrB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,UAAU;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,UAAU;yBACtB;qBACF;iBACF;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA+B;IACjD,cAAc,EAAE,SAAS;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,KAAK,EAAE;gBACL,cAAc,EAAE,OAAO;gBACvB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,iBAAiB;iBAC7B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAA+B;IACzD,cAAc,EAAE,iBAAiB;IACjC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,IAAI,EAAE;gBACJ,WAAW,EAAE;oBACX,gBAAgB,EAAE,CAAC;iBACpB;gBACD,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,OAAO,GAA+B;IACjD,cAAc,EAAE,SAAS;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,SAAS;QACpB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;iBAChB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,iBAAiB;iBAC7B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,QAAQ,GAA+B;IAClD,cAAc,EAAE,UAAU;IAC1B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,UAAU;QACrB,eAAe,EAAE;YACf,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,WAAW,EAAE;oBACX,gBAAgB,EAAE,CAAC;iBACpB;gBACD,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,cAAc;QACzB,eAAe,EAAE;YACf,OAAO,EAAE;gBACP,cAAc,EAAE,SAAS;gBACzB,OAAO,EAAE,SAAS;gBAClB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,yBAAyB,EAAE;gBACzB,cAAc,EAAE,2BAA2B;gBAC3C,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,wBAAwB;IACxC,OAAO,EAAE,qBAAqB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,cAAc,EAAE;gBACd,cAAc,EAAE,gBAAgB;gBAChC,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,gBAAgB;iBAC5B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,cAAc,GAA+B;IACxD,cAAc,EAAE,gBAAgB;IAChC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,gBAAgB;QAC3B,eAAe,EAAE;YACf,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM;oBACZ,aAAa,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,aAAa,CAAC;iBACpD;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,2BAA2B;IAC3C,OAAO,EAAE,oBAAoB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,eAAe,EAAE;gBACf,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,MAAM,EAAE;gBACN,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,QAAQ;gBACjB,YAAY,EAAE,IAAI;gBAClB,cAAc,EAAE,OAAO;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,UAAU;oBAChB,OAAO,EAAE;wBACP,IAAI,EAAE;4BACJ,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,WAAW;yBACvB;qBACF;iBACF;aACF;YACD,iBAAiB,EAAE;gBACjB,cAAc,EAAE,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,SAAS,GAA+B;IACnD,cAAc,EAAE,WAAW;IAC3B,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,WAAW;QACtB,eAAe,EAAE;YACf,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,UAAU;gBAC1B,OAAO,EAAE,UAAU;gBACnB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAA+B;IAC1D,cAAc,EAAE,kBAAkB;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kBAAkB;QAC7B,eAAe,EAAE;YACf,EAAE,EAAE;gBACF,cAAc,EAAE,IAAI;gBACpB,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,cAAc;iBAC1B;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,cAAc;QACzB,eAAe,EAAE;YACf,QAAQ,EAAE;gBACR,cAAc,EAAE,OAAO;gBACvB,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,QAAQ;gBACxB,OAAO,EAAE,QAAQ;gBACjB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,YAAY;gBAC5B,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAA+B;IAC7D,cAAc,EAAE,qBAAqB;IACrC,OAAO,EAAE,cAAc;IACvB,YAAY,EAAE,IAAI;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qBAAqB;QAChC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAA+B;IACtD,cAAc,EAAE,cAAc;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,cAAc;QACzB,eAAe,EAAE;YACf,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,eAAe,GAA+B;IACzD,cAAc,EAAE,iBAAiB;IACjC,OAAO,EAAE,cAAc;IACvB,YAAY,EAAE,IAAI;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iBAAiB;QAC5B,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,YAAY;gBAC5B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,YAAY;gBACrB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,iBAAiB,GAA+B;IAC3D,cAAc,EAAE,mBAAmB;IACnC,OAAO,EAAE,cAAc;IACvB,YAAY,EAAE,IAAI;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,mBAAmB;QAC9B,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,WAAW;gBAC3B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,eAAe;gBAC/B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,gBAAgB;gBAChC,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,YAAY,EAAE;gBACZ,cAAc,EAAE,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,WAAW,EAAE;gBACX,cAAc,EAAE,aAAa;gBAC7B,QAAQ,EAAE,IAAI;gBACd,OAAO,EAAE,aAAa;gBACtB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAA+B;IAC9E,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAA+B;IAC9E,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAA+B;IAC9E,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,wCAAwC,GAA+B;IAClF,cAAc,EAAE,2CAA2C;IAC3D,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,0CAA0C;QACrD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,qBAAqB;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAA+B;IAC5D,cAAc,EAAE,qBAAqB;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oBAAoB;QAC/B,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,yBAAyB,GAA+B;IACnE,cAAc,EAAE,4BAA4B;IAC5C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,2BAA2B;QACtC,eAAe,EAAE;YACf,QAAQ,EAAE;gBACR,cAAc,EAAE,WAAW;gBAC3B,sBAAsB,EAAE,YAAY;gBACpC,OAAO,EAAE,WAAW;gBACpB,IAAI,EAAE;oBACJ,IAAI,EAAE,YAAY;oBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;iBACpC;aACF;YACD,wBAAwB,EAAE;gBACxB,cAAc,EAAE,iCAAiC;gBACjD,OAAO,EAAE,iCAAiC;gBAC1C,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,kCAAkC,GAA+B;IAC5E,cAAc,EAAE,qCAAqC;IACrD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,oCAAoC;QAC/C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAA+B;IACjE,cAAc,EAAE,0BAA0B;IAC1C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,yBAAyB;QACpC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,gCAAgC,GAA+B;IAC1E,cAAc,EAAE,mCAAmC;IACnD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,kCAAkC;QAC7C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAA+B;IAC9E,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAA+B;IACrE,cAAc,EAAE,8BAA8B;IAC9C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,6BAA6B;QACxC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oCAAoC,GAA+B;IAC9E,cAAc,EAAE,uCAAuC;IACvD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sCAAsC;QACjD,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,oBAAoB,GAA+B;IAC9D,cAAc,EAAE,uBAAuB;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,sBAAsB;QACjC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,6BAA6B,GAA+B;IACvE,cAAc,EAAE,gCAAgC;IAChD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,+BAA+B;QAC1C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,mBAAmB,GAA+B;IAC7D,cAAc,EAAE,sBAAsB;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,qBAAqB;QAChC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,4BAA4B,GAA+B;IACtE,cAAc,EAAE,+BAA+B;IAC/C,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,8BAA8B;QACzC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,UAAU,EAAE;gBACV,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,aAAa,EAAE;gBACb,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,sBAAsB,GAA+B;IAChE,cAAc,EAAE,yBAAyB;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,wBAAwB;QACnC,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,cAAc;gBACvB,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,IAAI,EAAE;gBACJ,cAAc,EAAE,MAAM;gBACtB,OAAO,EAAE,MAAM;gBACf,IAAI,EAAE;oBACJ,IAAI,EAAE,iBAAiB;iBACxB;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC;AAEF,MAAM,CAAC,MAAM,+BAA+B,GAA+B;IACzE,cAAc,EAAE,kCAAkC;IAClD,IAAI,EAAE;QACJ,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE,iCAAiC;QAC5C,eAAe,EAAE;YACf,SAAS,EAAE;gBACT,cAAc,EAAE,iBAAiB;gBACjC,OAAO,EAAE,iBAAiB;gBAC1B,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;YACD,eAAe,EAAE;gBACf,cAAc,EAAE,wBAAwB;gBACxC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;KACF;CACF,CAAC", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreClient from \"@azure/core-client\";\n\nexport const QueueServiceProperties: coreClient.CompositeMapper = {\n  serializedName: \"QueueServiceProperties\",\n  xmlName: \"StorageServiceProperties\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueServiceProperties\",\n    modelProperties: {\n      queueAnalyticsLogging: {\n        serializedName: \"Logging\",\n        xmlName: \"Logging\",\n        type: {\n          name: \"Composite\",\n          className: \"Logging\"\n        }\n      },\n      hourMetrics: {\n        serializedName: \"HourMetrics\",\n        xmlName: \"HourMetrics\",\n        type: {\n          name: \"Composite\",\n          className: \"Metrics\"\n        }\n      },\n      minuteMetrics: {\n        serializedName: \"MinuteMetrics\",\n        xmlName: \"MinuteMetrics\",\n        type: {\n          name: \"Composite\",\n          className: \"Metrics\"\n        }\n      },\n      cors: {\n        serializedName: \"Cors\",\n        xmlName: \"Cors\",\n        xmlIsWrapped: true,\n        xmlElementName: \"CorsRule\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"CorsRule\"\n            }\n          }\n        }\n      }\n    }\n  }\n};\n\nexport const Logging: coreClient.CompositeMapper = {\n  serializedName: \"Logging\",\n  type: {\n    name: \"Composite\",\n    className: \"Logging\",\n    modelProperties: {\n      version: {\n        serializedName: \"Version\",\n        required: true,\n        xmlName: \"Version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      deleteProperty: {\n        serializedName: \"Delete\",\n        required: true,\n        xmlName: \"Delete\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      read: {\n        serializedName: \"Read\",\n        required: true,\n        xmlName: \"Read\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      write: {\n        serializedName: \"Write\",\n        required: true,\n        xmlName: \"Write\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      retentionPolicy: {\n        serializedName: \"RetentionPolicy\",\n        xmlName: \"RetentionPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"RetentionPolicy\"\n        }\n      }\n    }\n  }\n};\n\nexport const RetentionPolicy: coreClient.CompositeMapper = {\n  serializedName: \"RetentionPolicy\",\n  type: {\n    name: \"Composite\",\n    className: \"RetentionPolicy\",\n    modelProperties: {\n      enabled: {\n        serializedName: \"Enabled\",\n        required: true,\n        xmlName: \"Enabled\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      days: {\n        constraints: {\n          InclusiveMinimum: 1\n        },\n        serializedName: \"Days\",\n        xmlName: \"Days\",\n        type: {\n          name: \"Number\"\n        }\n      }\n    }\n  }\n};\n\nexport const Metrics: coreClient.CompositeMapper = {\n  serializedName: \"Metrics\",\n  type: {\n    name: \"Composite\",\n    className: \"Metrics\",\n    modelProperties: {\n      version: {\n        serializedName: \"Version\",\n        xmlName: \"Version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      enabled: {\n        serializedName: \"Enabled\",\n        required: true,\n        xmlName: \"Enabled\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      includeAPIs: {\n        serializedName: \"IncludeAPIs\",\n        xmlName: \"IncludeAPIs\",\n        type: {\n          name: \"Boolean\"\n        }\n      },\n      retentionPolicy: {\n        serializedName: \"RetentionPolicy\",\n        xmlName: \"RetentionPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"RetentionPolicy\"\n        }\n      }\n    }\n  }\n};\n\nexport const CorsRule: coreClient.CompositeMapper = {\n  serializedName: \"CorsRule\",\n  type: {\n    name: \"Composite\",\n    className: \"CorsRule\",\n    modelProperties: {\n      allowedOrigins: {\n        serializedName: \"AllowedOrigins\",\n        required: true,\n        xmlName: \"AllowedOrigins\",\n        type: {\n          name: \"String\"\n        }\n      },\n      allowedMethods: {\n        serializedName: \"AllowedMethods\",\n        required: true,\n        xmlName: \"AllowedMethods\",\n        type: {\n          name: \"String\"\n        }\n      },\n      allowedHeaders: {\n        serializedName: \"AllowedHeaders\",\n        required: true,\n        xmlName: \"AllowedHeaders\",\n        type: {\n          name: \"String\"\n        }\n      },\n      exposedHeaders: {\n        serializedName: \"ExposedHeaders\",\n        required: true,\n        xmlName: \"ExposedHeaders\",\n        type: {\n          name: \"String\"\n        }\n      },\n      maxAgeInSeconds: {\n        constraints: {\n          InclusiveMinimum: 0\n        },\n        serializedName: \"MaxAgeInSeconds\",\n        required: true,\n        xmlName: \"MaxAgeInSeconds\",\n        type: {\n          name: \"Number\"\n        }\n      }\n    }\n  }\n};\n\nexport const StorageError: coreClient.CompositeMapper = {\n  serializedName: \"StorageError\",\n  type: {\n    name: \"Composite\",\n    className: \"StorageError\",\n    modelProperties: {\n      message: {\n        serializedName: \"Message\",\n        xmlName: \"Message\",\n        type: {\n          name: \"String\"\n        }\n      },\n      code: {\n        serializedName: \"Code\",\n        xmlName: \"Code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      authenticationErrorDetail: {\n        serializedName: \"AuthenticationErrorDetail\",\n        xmlName: \"AuthenticationErrorDetail\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueServiceStatistics: coreClient.CompositeMapper = {\n  serializedName: \"QueueServiceStatistics\",\n  xmlName: \"StorageServiceStats\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueServiceStatistics\",\n    modelProperties: {\n      geoReplication: {\n        serializedName: \"GeoReplication\",\n        xmlName: \"GeoReplication\",\n        type: {\n          name: \"Composite\",\n          className: \"GeoReplication\"\n        }\n      }\n    }\n  }\n};\n\nexport const GeoReplication: coreClient.CompositeMapper = {\n  serializedName: \"GeoReplication\",\n  type: {\n    name: \"Composite\",\n    className: \"GeoReplication\",\n    modelProperties: {\n      status: {\n        serializedName: \"Status\",\n        required: true,\n        xmlName: \"Status\",\n        type: {\n          name: \"Enum\",\n          allowedValues: [\"live\", \"bootstrap\", \"unavailable\"]\n        }\n      },\n      lastSyncOn: {\n        serializedName: \"LastSyncTime\",\n        required: true,\n        xmlName: \"LastSyncTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      }\n    }\n  }\n};\n\nexport const ListQueuesSegmentResponse: coreClient.CompositeMapper = {\n  serializedName: \"ListQueuesSegmentResponse\",\n  xmlName: \"EnumerationResults\",\n  type: {\n    name: \"Composite\",\n    className: \"ListQueuesSegmentResponse\",\n    modelProperties: {\n      serviceEndpoint: {\n        serializedName: \"ServiceEndpoint\",\n        required: true,\n        xmlName: \"ServiceEndpoint\",\n        xmlIsAttribute: true,\n        type: {\n          name: \"String\"\n        }\n      },\n      prefix: {\n        serializedName: \"Prefix\",\n        required: true,\n        xmlName: \"Prefix\",\n        type: {\n          name: \"String\"\n        }\n      },\n      marker: {\n        serializedName: \"Marker\",\n        xmlName: \"Marker\",\n        type: {\n          name: \"String\"\n        }\n      },\n      maxPageSize: {\n        serializedName: \"MaxResults\",\n        required: true,\n        xmlName: \"MaxResults\",\n        type: {\n          name: \"Number\"\n        }\n      },\n      queueItems: {\n        serializedName: \"QueueItems\",\n        xmlName: \"Queues\",\n        xmlIsWrapped: true,\n        xmlElementName: \"Queue\",\n        type: {\n          name: \"Sequence\",\n          element: {\n            type: {\n              name: \"Composite\",\n              className: \"QueueItem\"\n            }\n          }\n        }\n      },\n      continuationToken: {\n        serializedName: \"NextMarker\",\n        required: true,\n        xmlName: \"NextMarker\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueItem: coreClient.CompositeMapper = {\n  serializedName: \"QueueItem\",\n  xmlName: \"Queue\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueItem\",\n    modelProperties: {\n      name: {\n        serializedName: \"Name\",\n        required: true,\n        xmlName: \"Name\",\n        type: {\n          name: \"String\"\n        }\n      },\n      metadata: {\n        serializedName: \"Metadata\",\n        xmlName: \"Metadata\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } }\n        }\n      }\n    }\n  }\n};\n\nexport const SignedIdentifier: coreClient.CompositeMapper = {\n  serializedName: \"SignedIdentifier\",\n  type: {\n    name: \"Composite\",\n    className: \"SignedIdentifier\",\n    modelProperties: {\n      id: {\n        serializedName: \"Id\",\n        required: true,\n        xmlName: \"Id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      accessPolicy: {\n        serializedName: \"AccessPolicy\",\n        xmlName: \"AccessPolicy\",\n        type: {\n          name: \"Composite\",\n          className: \"AccessPolicy\"\n        }\n      }\n    }\n  }\n};\n\nexport const AccessPolicy: coreClient.CompositeMapper = {\n  serializedName: \"AccessPolicy\",\n  type: {\n    name: \"Composite\",\n    className: \"AccessPolicy\",\n    modelProperties: {\n      startsOn: {\n        serializedName: \"Start\",\n        xmlName: \"Start\",\n        type: {\n          name: \"String\"\n        }\n      },\n      expiresOn: {\n        serializedName: \"Expiry\",\n        xmlName: \"Expiry\",\n        type: {\n          name: \"String\"\n        }\n      },\n      permissions: {\n        serializedName: \"Permission\",\n        xmlName: \"Permission\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const DequeuedMessageItem: coreClient.CompositeMapper = {\n  serializedName: \"DequeuedMessageItem\",\n  xmlName: \"QueueMessage\",\n  xmlIsWrapped: true,\n  type: {\n    name: \"Composite\",\n    className: \"DequeuedMessageItem\",\n    modelProperties: {\n      messageId: {\n        serializedName: \"MessageId\",\n        required: true,\n        xmlName: \"MessageId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      insertedOn: {\n        serializedName: \"InsertionTime\",\n        required: true,\n        xmlName: \"InsertionTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      expiresOn: {\n        serializedName: \"ExpirationTime\",\n        required: true,\n        xmlName: \"ExpirationTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      popReceipt: {\n        serializedName: \"PopReceipt\",\n        required: true,\n        xmlName: \"PopReceipt\",\n        type: {\n          name: \"String\"\n        }\n      },\n      nextVisibleOn: {\n        serializedName: \"TimeNextVisible\",\n        required: true,\n        xmlName: \"TimeNextVisible\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      dequeueCount: {\n        serializedName: \"DequeueCount\",\n        required: true,\n        xmlName: \"DequeueCount\",\n        type: {\n          name: \"Number\"\n        }\n      },\n      messageText: {\n        serializedName: \"MessageText\",\n        required: true,\n        xmlName: \"MessageText\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueMessage: coreClient.CompositeMapper = {\n  serializedName: \"QueueMessage\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueMessage\",\n    modelProperties: {\n      messageText: {\n        serializedName: \"MessageText\",\n        required: true,\n        xmlName: \"MessageText\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const EnqueuedMessage: coreClient.CompositeMapper = {\n  serializedName: \"EnqueuedMessage\",\n  xmlName: \"QueueMessage\",\n  xmlIsWrapped: true,\n  type: {\n    name: \"Composite\",\n    className: \"EnqueuedMessage\",\n    modelProperties: {\n      messageId: {\n        serializedName: \"MessageId\",\n        required: true,\n        xmlName: \"MessageId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      insertedOn: {\n        serializedName: \"InsertionTime\",\n        required: true,\n        xmlName: \"InsertionTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      expiresOn: {\n        serializedName: \"ExpirationTime\",\n        required: true,\n        xmlName: \"ExpirationTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      popReceipt: {\n        serializedName: \"PopReceipt\",\n        required: true,\n        xmlName: \"PopReceipt\",\n        type: {\n          name: \"String\"\n        }\n      },\n      nextVisibleOn: {\n        serializedName: \"TimeNextVisible\",\n        required: true,\n        xmlName: \"TimeNextVisible\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      }\n    }\n  }\n};\n\nexport const PeekedMessageItem: coreClient.CompositeMapper = {\n  serializedName: \"PeekedMessageItem\",\n  xmlName: \"QueueMessage\",\n  xmlIsWrapped: true,\n  type: {\n    name: \"Composite\",\n    className: \"PeekedMessageItem\",\n    modelProperties: {\n      messageId: {\n        serializedName: \"MessageId\",\n        required: true,\n        xmlName: \"MessageId\",\n        type: {\n          name: \"String\"\n        }\n      },\n      insertedOn: {\n        serializedName: \"InsertionTime\",\n        required: true,\n        xmlName: \"InsertionTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      expiresOn: {\n        serializedName: \"ExpirationTime\",\n        required: true,\n        xmlName: \"ExpirationTime\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      dequeueCount: {\n        serializedName: \"DequeueCount\",\n        required: true,\n        xmlName: \"DequeueCount\",\n        type: {\n          name: \"Number\"\n        }\n      },\n      messageText: {\n        serializedName: \"MessageText\",\n        required: true,\n        xmlName: \"MessageText\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceSetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_setPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceSetPropertiesHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceSetPropertiesExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_setPropertiesExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceSetPropertiesExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceGetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetPropertiesHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceGetPropertiesExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getPropertiesExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetPropertiesExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceGetStatisticsHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getStatisticsHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetStatisticsHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceGetStatisticsExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_getStatisticsExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceGetStatisticsExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceListQueuesSegmentHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_listQueuesSegmentHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceListQueuesSegmentHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const ServiceListQueuesSegmentExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Service_listQueuesSegmentExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"ServiceListQueuesSegmentExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueCreateHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_createHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueCreateHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueCreateExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_createExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueCreateExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueDeleteHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_deleteHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueDeleteHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueDeleteExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_deleteExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueDeleteExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueGetPropertiesHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_getPropertiesHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueGetPropertiesHeaders\",\n    modelProperties: {\n      metadata: {\n        serializedName: \"x-ms-meta\",\n        headerCollectionPrefix: \"x-ms-meta-\",\n        xmlName: \"x-ms-meta\",\n        type: {\n          name: \"Dictionary\",\n          value: { type: { name: \"String\" } }\n        }\n      },\n      approximateMessagesCount: {\n        serializedName: \"x-ms-approximate-messages-count\",\n        xmlName: \"x-ms-approximate-messages-count\",\n        type: {\n          name: \"Number\"\n        }\n      },\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueGetPropertiesExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_getPropertiesExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueGetPropertiesExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueSetMetadataHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_setMetadataHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueSetMetadataHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueSetMetadataExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_setMetadataExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueSetMetadataExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueGetAccessPolicyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_getAccessPolicyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueGetAccessPolicyHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueGetAccessPolicyExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_getAccessPolicyExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueGetAccessPolicyExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueSetAccessPolicyHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_setAccessPolicyHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueSetAccessPolicyHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const QueueSetAccessPolicyExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Queue_setAccessPolicyExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"QueueSetAccessPolicyExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesDequeueHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_dequeueHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesDequeueHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesDequeueExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_dequeueExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesDequeueExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesClearHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_clearHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesClearHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesClearExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_clearExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesClearExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesEnqueueHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_enqueueHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesEnqueueHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesEnqueueExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_enqueueExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesEnqueueExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesPeekHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_peekHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesPeekHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessagesPeekExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"Messages_peekExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessagesPeekExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessageIdUpdateHeaders: coreClient.CompositeMapper = {\n  serializedName: \"MessageId_updateHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessageIdUpdateHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      popReceipt: {\n        serializedName: \"x-ms-popreceipt\",\n        xmlName: \"x-ms-popreceipt\",\n        type: {\n          name: \"String\"\n        }\n      },\n      nextVisibleOn: {\n        serializedName: \"x-ms-time-next-visible\",\n        xmlName: \"x-ms-time-next-visible\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessageIdUpdateExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"MessageId_updateExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessageIdUpdateExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessageIdDeleteHeaders: coreClient.CompositeMapper = {\n  serializedName: \"MessageId_deleteHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessageIdDeleteHeaders\",\n    modelProperties: {\n      requestId: {\n        serializedName: \"x-ms-request-id\",\n        xmlName: \"x-ms-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      version: {\n        serializedName: \"x-ms-version\",\n        xmlName: \"x-ms-version\",\n        type: {\n          name: \"String\"\n        }\n      },\n      date: {\n        serializedName: \"date\",\n        xmlName: \"date\",\n        type: {\n          name: \"DateTimeRfc1123\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      },\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n\nexport const MessageIdDeleteExceptionHeaders: coreClient.CompositeMapper = {\n  serializedName: \"MessageId_deleteExceptionHeaders\",\n  type: {\n    name: \"Composite\",\n    className: \"MessageIdDeleteExceptionHeaders\",\n    modelProperties: {\n      errorCode: {\n        serializedName: \"x-ms-error-code\",\n        xmlName: \"x-ms-error-code\",\n        type: {\n          name: \"String\"\n        }\n      },\n      clientRequestId: {\n        serializedName: \"x-ms-client-request-id\",\n        xmlName: \"x-ms-client-request-id\",\n        type: {\n          name: \"String\"\n        }\n      }\n    }\n  }\n};\n"]}