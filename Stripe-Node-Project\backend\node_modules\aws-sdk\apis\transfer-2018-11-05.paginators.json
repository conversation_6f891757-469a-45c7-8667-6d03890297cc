{"pagination": {"ListAccesses": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Accesses"}, "ListAgreements": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Agreements"}, "ListCertificates": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Certificates"}, "ListConnectors": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Connectors"}, "ListExecutions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Executions"}, "ListProfiles": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Profiles"}, "ListSecurityPolicies": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "SecurityPolicyNames"}, "ListServers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Servers"}, "ListTagsForResource": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Tags"}, "ListUsers": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Users"}, "ListWorkflows": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Workflows"}}}