{"version": 3, "file": "cache.js", "sourceRoot": "", "sources": ["../../../../src/utils/cache.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAEpE,IAAI,kBAA8B,CAAC;AAEnC,MAAM,UAAU,0BAA0B;IACxC,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,kBAAkB,GAAG,uBAAuB,EAAE,CAAC;IACjD,CAAC;IACD,OAAO,kBAAkB,CAAC;AAC5B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"@azure/core-rest-pipeline\";\nimport { createDefaultHttpClient } from \"@azure/core-rest-pipeline\";\n\nlet _defaultHttpClient: HttpClient;\n\nexport function getCachedDefaultHttpClient(): HttpClient {\n  if (!_defaultHttpClient) {\n    _defaultHttpClient = createDefaultHttpClient();\n  }\n  return _defaultHttpClient;\n}\n"]}