# Testing Guide for Stripe Integration Project

## Overview

This guide provides step-by-step instructions for testing the complete Stripe Integration system, including both the Node.js backend API and WordPress plugin.

## Prerequisites

### Required Accounts & Services
1. **Stripe Account** (Test Mode)
   - Sign up at https://stripe.com
   - Get test API keys from Dashboard > Developers > API keys
   - Test Secret Key: `sk_test_...`
   - Test Publishable Key: `pk_test_...`

2. **Supabase Account**
   - Sign up at https://supabase.com
   - Create a new project
   - Get URL and service role key from Settings > API

3. **WordPress Installation**
   - Local WordPress site (XAMPP, WAMP, or Docker)
   - Or staging WordPress site

### Required Software
- Node.js 16+ and npm
- PHP 7.4+ and Composer
- MySQL/PostgreSQL database
- Git

## Testing Environment Setup

### 1. Backend API Testing

#### Step 1: Install Dependencies
```bash
cd backend
npm install
```

#### Step 2: Environment Configuration
Create `.env` file in `backend/` directory:
```env
# Environment
NODE_ENV=development
PORT=3000

# Database
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Stripe
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Security
JWT_SECRET=your_jwt_secret_key_here
API_SECRET_KEY=your_api_secret_key_here
WEBHOOK_SECRET=your_webhook_secret_here

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Optional Security
ENABLE_IP_WHITELIST=false
ENABLE_SIGNATURE_VALIDATION=true
SECURITY_WEBHOOK_URL=
```

#### Step 3: Database Setup
```bash
# Run the database schema
# Copy the SQL from backend/database/schema.sql
# Execute in your Supabase SQL editor
```

#### Step 4: Start Backend Server
```bash
cd backend
npm start
```

Server should start at `http://localhost:3000`

#### Step 5: Test Backend Endpoints

**Health Check:**
```bash
curl http://localhost:3000/health
```

**Create Test Vendor:**
```bash
curl -X POST http://localhost:3000/api/vendors \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Test Vendor",
    "email": "<EMAIL>",
    "niche": "restaurant",
    "country": "US"
  }'
```

### 2. WordPress Plugin Testing

#### Step 1: Install Plugin
1. Copy `wordpress-plugin/` folder to `wp-content/plugins/stripe-integration/`
2. Activate plugin in WordPress admin
3. Go to Settings > Stripe Integration

#### Step 2: Configure Plugin
In WordPress admin:
1. **API Settings:**
   - API Base URL: `http://localhost:3000/api`
   - API Key: Generate from backend
   - Vendor ID: Get from vendor creation

2. **Stripe Settings:**
   - Publishable Key: `pk_test_...`
   - Business Niche: Select appropriate niche

#### Step 3: Test Payment Form
Add shortcode to a page/post:
```
[stripe_payment_form amount="100" order_id="test-order-123"]
```

### 3. Integration Testing Scenarios

#### Scenario 1: Complete Payment Flow
1. **Setup:** Configure both backend and WordPress plugin
2. **Action:** Create payment using WordPress form
3. **Expected:** Payment intent created, commission calculated
4. **Verify:** Check Supabase database for transaction record

#### Scenario 2: Commission Calculation
1. **Setup:** Create vendors with different niches
2. **Action:** Process payments for each vendor
3. **Expected:** Different commission rates applied
4. **Verify:** Commission amounts match niche rates

#### Scenario 3: Security Testing
1. **Rate Limiting:** Make multiple rapid requests
2. **Input Validation:** Send malformed data
3. **Authentication:** Test without proper tokens
4. **Expected:** Appropriate security responses

## Automated Testing

### Backend Unit Tests
```bash
cd backend
npm test
```

### Security Tests
```bash
cd backend
npm run test:security
```

### Integration Tests
```bash
cd backend
npm run test:integration
```

## Manual Testing Checklist

### Backend API Tests
- [ ] Health endpoint responds
- [ ] Authentication works with JWT tokens
- [ ] Rate limiting blocks excessive requests
- [ ] Input validation rejects invalid data
- [ ] Payment intents create successfully
- [ ] Commission calculations are correct
- [ ] Webhook handling works
- [ ] Security headers are present
- [ ] Error handling is appropriate

### WordPress Plugin Tests
- [ ] Plugin activates without errors
- [ ] Admin settings save correctly
- [ ] Payment form renders properly
- [ ] AJAX requests work
- [ ] Nonce verification functions
- [ ] Rate limiting prevents abuse
- [ ] Input sanitization works
- [ ] Error messages display correctly

### Integration Tests
- [ ] WordPress to API communication
- [ ] Payment flow end-to-end
- [ ] Commission calculation accuracy
- [ ] Database record creation
- [ ] Error propagation
- [ ] Security event logging

## Test Data

### Test Credit Cards (Stripe Test Mode)
- **Successful Payment:** ****************
- **Declined Payment:** ****************
- **Insufficient Funds:** ****************
- **Expired Card:** ****************

### Test Scenarios
1. **Small Payment:** $1.00
2. **Large Payment:** $1,000.00
3. **Different Niches:** grocery, restaurant, retail
4. **Multiple Vendors:** Test commission variations

## Troubleshooting

### Common Issues

**Backend Won't Start:**
- Check environment variables
- Verify database connection
- Check port availability

**WordPress Plugin Errors:**
- Check PHP error logs
- Verify API connectivity
- Check WordPress permissions

**Payment Failures:**
- Verify Stripe test keys
- Check webhook configuration
- Review API error logs

**Database Issues:**
- Check Supabase connection
- Verify RLS policies
- Check table permissions

### Debug Mode
Enable debug logging:
```env
LOG_LEVEL=debug
WP_DEBUG=true
WP_DEBUG_LOG=true
```

## Performance Testing

### Load Testing
```bash
# Install artillery
npm install -g artillery

# Run load test
artillery run backend/tests/load-test.yml
```

### Stress Testing
- Test rate limiting thresholds
- Database connection limits
- Memory usage under load

## Security Testing

### Penetration Testing
- SQL injection attempts
- XSS attack vectors
- CSRF token bypass
- Rate limit circumvention
- Authentication bypass

### Security Scan
```bash
# Install security scanner
npm install -g nsp

# Run security audit
npm audit
nsp check
```

## Deployment Testing

### Staging Environment
1. Deploy to staging server
2. Test with production-like data
3. Verify SSL certificates
4. Test webhook endpoints
5. Monitor performance

### Production Checklist
- [ ] Environment variables configured
- [ ] SSL certificates installed
- [ ] Database migrations run
- [ ] Monitoring configured
- [ ] Backup systems tested
- [ ] Security scanning completed

## Monitoring & Logging

### Log Files to Monitor
- Backend: `logs/app.log`
- WordPress: `wp-content/debug.log`
- Security: Database audit log
- Stripe: Dashboard webhook logs

### Key Metrics
- Response times
- Error rates
- Payment success rates
- Security events
- Database performance

## Support & Documentation

### API Documentation
- Endpoint specifications
- Authentication methods
- Error codes
- Rate limiting details

### WordPress Documentation
- Installation guide
- Configuration options
- Shortcode usage
- Troubleshooting

## Next Steps After Testing

1. **Fix any identified issues**
2. **Optimize performance bottlenecks**
3. **Enhance security based on findings**
4. **Update documentation**
5. **Prepare for production deployment**
