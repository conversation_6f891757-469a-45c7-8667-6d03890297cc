{"version": 3, "file": "PageBlobRangeResponse.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/PageBlobRangeResponse.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AA0ClC;;;;;GAKG;AACH,MAAM,UAAU,sBAAsB,CACpC,QAAqF;IAErF,MAAM,SAAS,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5E,MAAM,EAAE,CAAC,CAAC,KAAK;QACf,KAAK,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK;KACvB,CAAC,CAAC,CAAC;IAEJ,MAAM,UAAU,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9E,MAAM,EAAE,CAAC,CAAC,KAAK;QACf,KAAK,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK;KACvB,CAAC,CAAC,CAAC;IAEJ,uCACK,QAAQ,KACX,SAAS;QACT,UAAU,EACV,SAAS,kCACJ,QAAQ,CAAC,SAAS,KACrB,UAAU,EAAE;gBACV,SAAS;gBACT,UAAU;aACX,OAEH;AACJ,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  PageBlobGetPageRangesHeaders,\n  PageBlobGetPageRangesDiffHeaders,\n  PageBlobGetPageRangesResponseModel,\n  PageBlobGetPageRangesDiffResponseModel,\n} from \"./generatedModels\";\nimport type { Range } from \"./Range\";\nimport type { ResponseWithBody } from \"./utils/utils.common\";\n\n/**\n * List of page ranges for a blob.\n */\nexport interface PageList {\n  /**\n   * Valid non-overlapping page ranges.\n   */\n  pageRange?: Range[];\n  /**\n   * Present if the prevSnapshot parameter was specified and there were cleared\n   * pages between the previous snapshot and the target snapshot.\n   */\n  clearRange?: Range[];\n}\n\n/**\n * Contains response data for the {@link BlobClient.getPageRanges} operation.\n */\nexport interface PageBlobGetPageRangesResponse\n  extends PageList,\n    PageBlobGetPageRangesHeaders,\n    ResponseWithBody<PageBlobGetPageRangesHeaders, PageList> {}\n\n/**\n * Contains response data for the {@link BlobClient.getPageRangesDiff} operation.\n */\nexport interface PageBlobGetPageRangesDiffResponse\n  extends PageList,\n    PageBlobGetPageRangesDiffHeaders,\n    ResponseWithBody<PageBlobGetPageRangesDiffHeaders, PageList> {}\n\n/**\n * Function that converts PageRange and ClearRange to a common Range object.\n * PageRange and ClearRange have start and end while Range offset and count\n * this function normalizes to Range.\n * @param response - Model PageBlob Range response\n */\nexport function rangeResponseFromModel(\n  response: PageBlobGetPageRangesResponseModel | PageBlobGetPageRangesDiffResponseModel,\n): PageBlobGetPageRangesResponse | PageBlobGetPageRangesDiffResponse {\n  const pageRange = (response._response.parsedBody.pageRange || []).map((x) => ({\n    offset: x.start,\n    count: x.end - x.start,\n  }));\n\n  const clearRange = (response._response.parsedBody.clearRange || []).map((x) => ({\n    offset: x.start,\n    count: x.end - x.start,\n  }));\n\n  return {\n    ...response,\n    pageRange,\n    clearRange,\n    _response: {\n      ...response._response,\n      parsedBody: {\n        pageRange,\n        clearRange,\n      },\n    },\n  };\n}\n"]}