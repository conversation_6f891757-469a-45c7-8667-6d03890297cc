{"version": 3, "file": "QueueServiceClient.js", "sourceRoot": "", "sources": ["../../../src/QueueServiceClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;;AAGlC,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAoB1C,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAEzD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,4BAA4B,EAC5B,cAAc,GACf,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,0BAA0B,EAAE,MAAM,+DAA+D,CAAC;AAC3G,OAAO,EAAE,mBAAmB,EAAE,MAAM,wDAAwD,CAAC;AAC7F,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAEhD,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAC;AAChE,OAAO,EACL,iCAAiC,EACjC,yCAAyC,GAC1C,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAG1D,OAAO,EAAE,uBAAuB,EAAE,MAAM,2BAA2B,CAAC;AAsHpE;;;GAGG;AACH,MAAM,OAAO,kBAAmB,SAAQ,aAAa;IACnD;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,oBAAoB,CAChC,gBAAwB;IACxB,2FAA2F;IAC3F,iEAAiE;IACjE,OAAgC;QAEhC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,MAAM,cAAc,GAAG,4BAA4B,CAAC,gBAAgB,CAAC,CAAC;QACtE,IAAI,cAAc,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;YAChD,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,CACxD,cAAc,CAAC,WAAY,EAC3B,cAAc,CAAC,UAAU,CAC1B,CAAC;gBACF,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;oBAC1B,OAAO,CAAC,YAAY,GAAG,uBAAuB,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC1E,CAAC;gBACD,MAAM,QAAQ,GAAG,WAAW,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBAC3D,OAAO,IAAI,kBAAkB,CAAC,cAAc,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,oEAAoE,CAAC,CAAC;YACxF,CAAC;QACH,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;YACjE,OAAO,IAAI,kBAAkB,CAAC,cAAc,CAAC,GAAG,GAAG,GAAG,GAAG,cAAc,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAChG,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,0FAA0F,CAC3F,CAAC;QACJ,CAAC;IACH,CAAC;IA+DD,YACE,GAAW,EACX,oBAIY;IACZ,2FAA2F;IAC3F,iEAAiE;IACjE,OAAgC;QAEhC,IAAI,QAAkB,CAAC;QACvB,IAAI,cAAc,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACzC,QAAQ,GAAG,oBAAoB,CAAC;QAClC,CAAC;aAAM,IACL,CAAC,MAAM,IAAI,oBAAoB,YAAY,0BAA0B,CAAC;YACtE,oBAAoB,YAAY,mBAAmB;YACnD,iBAAiB,CAAC,oBAAoB,CAAC,EACvC,CAAC;YACD,QAAQ,GAAG,WAAW,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;QACxD,CAAC;aAAM,CAAC;YACN,+DAA+D;YAC/D,QAAQ,GAAG,WAAW,CAAC,IAAI,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QACD,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACrB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;IAC1D,CAAC;IAED;;;;;;;;;;;;OAYG;IACI,cAAc,CAAC,SAAiB;QACrC,OAAO,IAAI,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC9E,CAAC;IAED;;;;;;;;;;;;;OAaG;IACK,KAAK,CAAC,iBAAiB,CAC7B,MAAe,EACf,UAA2C,EAAE;QAE7C,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,OAAO,aAAa,CAAC,QAAQ,CAC3B,sCAAsC,EACtC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CAKnB,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,iCACtC,cAAc,KACjB,MAAM,EACN,OAAO,EAAE,OAAO,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IACtE,CACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACY,YAAY;0EACzB,MAAe,EACf,UAA2C,EAAE;YAE7C,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;gBAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;YAC7B,CAAC;YAED,IAAI,kBAAkB,CAAC;YACvB,GAAG,CAAC;gBACF,kBAAkB,GAAG,cAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,CAAC;gBACnE,MAAM,GAAG,kBAAkB,CAAC,iBAAiB,CAAC;gBAC9C,oBAAM,cAAM,kBAAkB,CAAA,CAAA,CAAC;YACjC,CAAC,QAAQ,MAAM,EAAE;QACnB,CAAC;KAAA;IAED;;;;OAIG;IACY,SAAS;uEACtB,UAA2C,EAAE;;YAE7C,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;gBAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;YAC7B,CAAC;YAED,IAAI,MAA0B,CAAC;;gBAC/B,KAA4B,eAAA,KAAA,cAAA,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA,IAAA,+DAAE,CAAC;oBAArC,cAAkC;oBAAlC,WAAkC;oBAAnD,MAAM,OAAO,KAAA,CAAA;oBACtB,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;wBACvB,cAAA,KAAK,CAAC,CAAC,iBAAA,cAAA,OAAO,CAAC,UAAU,CAAA,CAAA,CAAA,CAAC;oBAC5B,CAAC;gBACH,CAAC;;;;;;;;;QACH,CAAC;KAAA;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA4EG;IACI,UAAU,CACf,UAAoC,EAAE;QAEtC,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YAC1B,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;QAC7B,CAAC;QAED,MAAM,cAAc,mCACf,OAAO,GACP,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAC5D,CAAC;QAEF,+CAA+C;QAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC5C,OAAO;YACL;;eAEG;YACH,IAAI;gBACF,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;YACD;;eAEG;YACH,CAAC,MAAM,CAAC,aAAa,CAAC;gBACpB,OAAO,IAAI,CAAC;YACd,CAAC;YACD;;eAEG;YACH,MAAM,EAAE,CAAC,WAAyB,EAAE,EAAE,EAAE;gBACtC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,kBACjD,WAAW,EAAE,QAAQ,CAAC,WAAW,IAC9B,cAAc,EACjB,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,aAAa,CACxB,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CAInB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;QAC7D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CACxB,UAAkC,EAClC,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CACnB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,UAAU,EAAE,cAAc,CAAC,CACpE,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CACxB,UAAuC,EAAE;QAEzC,OAAO,aAAa,CAAC,QAAQ,CAC3B,kCAAkC,EAClC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,cAAc,CAInB,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC;QAC7D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,WAAW,CACtB,SAAiB,EACjB,UAA8B,EAAE;QAEhC,OAAO,aAAa,CAAC,QAAQ,CAC3B,gCAAgC,EAChC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC/D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;OAOG;IACI,KAAK,CAAC,WAAW,CACtB,SAAiB,EACjB,UAA8B,EAAE;QAEhC,OAAO,aAAa,CAAC,QAAQ,CAC3B,gCAAgC,EAChC,OAAO,EACP,KAAK,EAAE,cAAc,EAAE,EAAE;YACvB,OAAO,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;QAC/D,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,qBAAqB,CAC1B,SAAgB,EAChB,cAAqC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,EACrE,gBAAwB,KAAK,EAC7B,UAA+C,EAAE;QAEjD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,CACd,+FAA+F,CAChG,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC;QAED,MAAM,GAAG,GAAG,iCAAiC,iBAEzC,WAAW;YACX,SAAS;YACT,aAAa,EACb,QAAQ,EAAE,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAC/C,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,QAAQ,EAAE,CAAC;QAEb,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IACzC,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,uBAAuB,CAC5B,SAAgB,EAChB,cAAqC,qBAAqB,CAAC,KAAK,CAAC,GAAG,CAAC,EACrE,gBAAwB,KAAK,EAC7B,UAA+C,EAAE;QAEjD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,0BAA0B,CAAC,EAAE,CAAC;YAC7D,MAAM,UAAU,CACd,+FAA+F,CAChG,CAAC;QACJ,CAAC;QAED,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,SAAS,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,yCAAyC,iBAE5C,WAAW;YACX,SAAS;YACT,aAAa,EACb,QAAQ,EAAE,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAC/C,OAAO,GAEZ,IAAI,CAAC,UAAU,CAChB,CAAC,YAAY,CAAC;IACjB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { isTokenCredential } from \"@azure/core-auth\";\nimport { isNode } from \"@azure/core-util\";\nimport type {\n  QueueCreateResponse,\n  QueueDeleteResponse,\n  QueueItem,\n  QueueServiceProperties,\n  ServiceGetPropertiesResponse,\n  ServiceGetPropertiesHeaders,\n  ServiceGetStatisticsResponse,\n  ServiceListQueuesSegmentResponse,\n  ServiceSetPropertiesResponse,\n  ServiceListQueuesSegmentHeaders,\n  ListQueuesSegmentResponse,\n  ServiceSetPropertiesHeaders,\n  ServiceGetStatisticsHeaders,\n  QueueServiceStatistics,\n} from \"./generatedModels\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { Service } from \"./generated/src/operationsInterfaces\";\nimport type { StoragePipelineOptions, Pipeline } from \"./Pipeline\";\nimport { newPipeline, isPipelineLike } from \"./Pipeline\";\nimport type { CommonOptions } from \"./StorageClient\";\nimport { StorageClient } from \"./StorageClient\";\nimport type { PageSettings, PagedAsyncIterableIterator } from \"@azure/core-paging\";\nimport {\n  appendToURLPath,\n  appendToURLQuery,\n  extractConnectionStringParts,\n  assertResponse,\n} from \"./utils/utils.common\";\nimport { StorageSharedKeyCredential } from \"../../storage-blob/src/credentials/StorageSharedKeyCredential\";\nimport { AnonymousCredential } from \"../../storage-blob/src/credentials/AnonymousCredential\";\nimport { tracingClient } from \"./utils/tracing\";\nimport type { QueueCreateOptions, QueueDeleteOptions } from \"./QueueClient\";\nimport { QueueClient } from \"./QueueClient\";\nimport { AccountSASPermissions } from \"./AccountSASPermissions\";\nimport {\n  generateAccountSASQueryParameters,\n  generateAccountSASQueryParametersInternal,\n} from \"./AccountSASSignatureValues\";\nimport { AccountSASServices } from \"./AccountSASServices\";\nimport type { SASProtocol } from \"./SASQueryParameters\";\nimport type { SasIPRange } from \"./SasIPRange\";\nimport { getDefaultProxySettings } from \"@azure/core-rest-pipeline\";\n\n/**\n * Options to configure {@link QueueServiceClient.getProperties} operation\n */\nexport interface ServiceGetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueServiceClient.setProperties} operation\n */\nexport interface ServiceSetPropertiesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure {@link QueueServiceClient.getStatistics} operation\n */\nexport interface ServiceGetStatisticsOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\n/**\n * Options to configure Queue Service - List Queues Segment operation\n *\n * See:\n * - {@link QueueServiceClient.listSegments}\n * - {@link QueueServiceClient.listQueuesSegment}\n * - {@link QueueServiceClient.listItems}\n */\ninterface ServiceListQueuesSegmentOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Filters the results to return only queues\n   * whose name begins with the specified prefix.\n   */\n  prefix?: string;\n  /**\n   * Specifies the maximum number of queues\n   * to return. If the request does not specify maxPageSize, or specifies a\n   * value greater than 5000, the server will return up to 5000 items. Note\n   * that if the listing operation crosses a partition boundary, then the\n   * service will return a continuation token for retrieving the remainder of\n   * the results. For this reason, it is possible that the service will return\n   * fewer results than specified by maxPageSize, or than the default of 5000.\n   */\n  maxPageSize?: number;\n  /**\n   * Include this parameter to\n   * specify that the queue's metadata be returned as part of the response\n   * body. Possible values include: 'metadata'\n   */\n  include?: string;\n}\n\n/**\n * Options to configure {@link QueueServiceClient.listQueues} operation\n */\nexport interface ServiceListQueuesOptions extends CommonOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n  /**\n   * Filters the results to return only queues\n   * whose name begins with the specified prefix.\n   */\n  prefix?: string;\n  /**\n   * Specifies whether the queue's metadata be returned as part of the response\n   * body.\n   */\n  includeMetadata?: boolean;\n}\n\n/**\n * Options to configure {@link QueueServiceClient.generateAccountSasUrl} operation.\n */\nexport interface ServiceGenerateAccountSasUrlOptions {\n  /**\n   * The version of the service this SAS will target. If not specified, it will default to the version targeted by the\n   * library.\n   */\n  version?: string;\n\n  /**\n   * Optional. SAS protocols allowed.\n   */\n  protocol?: SASProtocol;\n\n  /**\n   * Optional. When the SAS will take effect.\n   */\n  startsOn?: Date;\n  /**\n   * Optional. IP range allowed.\n   */\n  ipRange?: SasIPRange;\n}\n\n/**\n * A QueueServiceClient represents a URL to the Azure Storage Queue service allowing you\n * to manipulate queues.\n */\nexport class QueueServiceClient extends StorageClient {\n  /**\n   * Creates an instance of QueueServiceClient.\n   *\n   * @param connectionString - Account connection string or a SAS connection string of an Azure storage account.\n   *                                  [ Note - Account connection string can only be used in NODE.JS runtime. ]\n   *                                  Account connection string example -\n   *                                  `DefaultEndpointsProtocol=https;AccountName=myaccount;AccountKey=accountKey;EndpointSuffix=core.windows.net`\n   *                                  SAS connection string example -\n   *                                  `BlobEndpoint=https://myaccount.blob.core.windows.net/;QueueEndpoint=https://myaccount.queue.core.windows.net/;FileEndpoint=https://myaccount.file.core.windows.net/;TableEndpoint=https://myaccount.table.core.windows.net/;SharedAccessSignature=sasString`\n   * @param options - Options to configure the HTTP pipeline.\n   * @returns A new QueueServiceClient object from the given connection string.\n   */\n  public static fromConnectionString(\n    connectionString: string,\n    // Legacy, no way to fix the eslint error without breaking. Disable the rule for this line.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: StoragePipelineOptions,\n  ): QueueServiceClient {\n    options = options || {};\n    const extractedCreds = extractConnectionStringParts(connectionString);\n    if (extractedCreds.kind === \"AccountConnString\") {\n      if (isNode) {\n        const sharedKeyCredential = new StorageSharedKeyCredential(\n          extractedCreds.accountName!,\n          extractedCreds.accountKey,\n        );\n        if (!options.proxyOptions) {\n          options.proxyOptions = getDefaultProxySettings(extractedCreds.proxyUri);\n        }\n        const pipeline = newPipeline(sharedKeyCredential, options);\n        return new QueueServiceClient(extractedCreds.url, pipeline);\n      } else {\n        throw new Error(\"Account connection string is only supported in Node.js environment\");\n      }\n    } else if (extractedCreds.kind === \"SASConnString\") {\n      const pipeline = newPipeline(new AnonymousCredential(), options);\n      return new QueueServiceClient(extractedCreds.url + \"?\" + extractedCreds.accountSas, pipeline);\n    } else {\n      throw new Error(\n        \"Connection string must be either an Account connection string or a SAS connection string\",\n      );\n    }\n  }\n\n  /**\n   * serviceContext provided by protocol layer.\n   */\n  private serviceContext: Service;\n\n  /**\n   * Creates an instance of QueueServiceClient.\n   *\n   * @param url - A URL string pointing to Azure Storage queue service, such as\n   *                     \"https://myaccount.queue.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.queue.core.windows.net?sasString\".\n   * @param credential -  Such as AnonymousCredential, StorageSharedKeyCredential or any credential from the `@azure/identity` package to authenticate requests to the service. You can also provide an object that implements the TokenCredential interface. If not specified, AnonymousCredential is used.\n   * @param options - Options to configure the HTTP pipeline.\n   *\n   * Example using DefaultAzureCredential from `@azure/identity`:\n   *\n   * ```js\n   * const account = \"<account>\";\n   *\n   * const credential = new DefaultAzureCredential();\n   *\n   * const queueServiceClient = new QueueServiceClient(\n   *   `https://${account}.queue.core.windows.net`,\n   *   credential\n   * }\n   * ```\n   *\n   * Example using an account name/key:\n   *\n   * ```js\n   * const account = \"<account>\";\n   *\n   * const sharedKeyCredential = new StorageSharedKeyCredential(account, \"<account key>\");\n   *\n   * const queueServiceClient = new QueueServiceClient(\n   *   `https://${account}.queue.core.windows.net`,\n   *   sharedKeyCredential,\n   *   {\n   *     retryOptions: { maxTries: 4 }, // Retry options\n   *     telemetry: { value: \"BasicSample/V11.0.0\" } // Customized telemetry string\n   *   }\n   * );\n   * ```\n   */\n  constructor(\n    url: string,\n    credential?: StorageSharedKeyCredential | AnonymousCredential | TokenCredential,\n    // Legacy, no way to fix the eslint error without breaking. Disable the rule for this line.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: StoragePipelineOptions,\n  );\n  /**\n   * Creates an instance of QueueServiceClient.\n   *\n   * @param url - A URL string pointing to Azure Storage queue service, such as\n   *                     \"https://myaccount.queue.core.windows.net\". You can append a SAS\n   *                     if using AnonymousCredential, such as \"https://myaccount.queue.core.windows.net?sasString\".\n   * @param pipeline - Call newPipeline() to create a default\n   *                            pipeline, or provide a customized pipeline.\n   */\n  constructor(url: string, pipeline: Pipeline);\n  constructor(\n    url: string,\n    credentialOrPipeline?:\n      | StorageSharedKeyCredential\n      | AnonymousCredential\n      | TokenCredential\n      | Pipeline,\n    // Legacy, no way to fix the eslint error without breaking. Disable the rule for this line.\n    /* eslint-disable-next-line @azure/azure-sdk/ts-naming-options */\n    options?: StoragePipelineOptions,\n  ) {\n    let pipeline: Pipeline;\n    if (isPipelineLike(credentialOrPipeline)) {\n      pipeline = credentialOrPipeline;\n    } else if (\n      (isNode && credentialOrPipeline instanceof StorageSharedKeyCredential) ||\n      credentialOrPipeline instanceof AnonymousCredential ||\n      isTokenCredential(credentialOrPipeline)\n    ) {\n      pipeline = newPipeline(credentialOrPipeline, options);\n    } else {\n      // The second parameter is undefined. Use anonymous credential.\n      pipeline = newPipeline(new AnonymousCredential(), options);\n    }\n    super(url, pipeline);\n    this.serviceContext = this.storageClientContext.service;\n  }\n\n  /**\n   * Creates a {@link QueueClient} object.\n   *\n   * @param queueName -\n   * @returns a new QueueClient\n   *\n   * Example usage:\n   *\n   * ```js\n   * const queueClient = queueServiceClient.getQueueClient(\"<new queue name>\");\n   * const createQueueResponse = await queueClient.create();\n   * ```\n   */\n  public getQueueClient(queueName: string): QueueClient {\n    return new QueueClient(appendToURLPath(this.url, queueName), this.pipeline);\n  }\n\n  /**\n   * Returns a list of the queues under the specified account.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/list-queues1\n   *\n   * @param marker - A string value that identifies the portion of\n   *                        the list of queues to be returned with the next listing operation. The\n   *                        operation returns the continuationToken value within the response body if the\n   *                        listing operation did not return all queues remaining to be listed\n   *                        with the current page. The continuationToken value can be used as the value for\n   *                        the marker parameter in a subsequent call to request the next page of list\n   *                        items. The marker value is opaque to the client.\n   * @param options - Options to list queues operation.\n   * @returns Response data for the list queues segment operation.\n   */\n  private async listQueuesSegment(\n    marker?: string,\n    options: ServiceListQueuesSegmentOptions = {},\n  ): Promise<ServiceListQueuesSegmentResponse> {\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    return tracingClient.withSpan(\n      \"QueueServiceClient-listQueuesSegment\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          ServiceListQueuesSegmentHeaders & ListQueuesSegmentResponse,\n          ServiceListQueuesSegmentHeaders,\n          ListQueuesSegmentResponse\n        >(\n          await this.serviceContext.listQueuesSegment({\n            ...updatedOptions,\n            marker,\n            include: options.include === undefined ? undefined : [options.include],\n          }),\n        );\n      },\n    );\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for {@link ServiceListQueuesSegmentResponse} objects\n   *\n   * @param marker - A string value that identifies the portion of\n   *                        the list of queues to be returned with the next listing operation. The\n   *                        operation returns the continuationToken value within the response body if the\n   *                        listing operation did not return all queues remaining to be listed\n   *                        with the current page. The continuationToken value can be used as the value for\n   *                        the marker parameter in a subsequent call to request the next page of list\n   *                        items. The marker value is opaque to the client.\n   * @param options - Options to list queues operation.\n   */\n  private async *listSegments(\n    marker?: string,\n    options: ServiceListQueuesSegmentOptions = {},\n  ): AsyncIterableIterator<ServiceListQueuesSegmentResponse> {\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    let listQueuesResponse;\n    do {\n      listQueuesResponse = await this.listQueuesSegment(marker, options);\n      marker = listQueuesResponse.continuationToken;\n      yield await listQueuesResponse;\n    } while (marker);\n  }\n\n  /**\n   * Returns an AsyncIterableIterator for {@link QueueItem} objects\n   *\n   * @param options - Options to list queues operation.\n   */\n  private async *listItems(\n    options: ServiceListQueuesSegmentOptions = {},\n  ): AsyncIterableIterator<QueueItem> {\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    let marker: string | undefined;\n    for await (const segment of this.listSegments(marker, options)) {\n      if (segment.queueItems) {\n        yield* segment.queueItems;\n      }\n    }\n  }\n\n  /**\n   * Returns an async iterable iterator to list all the queues\n   * under the specified account.\n   *\n   * .byPage() returns an async iterable iterator to list the queues in pages.\n   *\n   * Example using `for await` syntax:\n   *\n   * ```js\n   * let i = 1;\n   * for await (const item of queueServiceClient.listQueues()) {\n   *   console.log(`Queue${i}: ${item.name}`);\n   *   i++;\n   * }\n   * ```\n   *\n   * Example using `iter.next()`:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = queueServiceClient.listQueues();\n   * let item = await iterator.next();\n   * while (!item.done) {\n   *   console.log(`Queue${i}: ${item.value.name}`);\n   *   i++;\n   *   item = await iterator.next();\n   * }\n   * ```\n   *\n   * Example using `byPage()`:\n   *\n   * ```js\n   * // passing optional maxPageSize in the page settings\n   * let i = 1;\n   * for await (const item2 of queueServiceClient.listQueues().byPage({ maxPageSize: 20 })) {\n   *   if (item2.queueItems) {\n   *     for (const queueItem of item2.queueItems) {\n   *       console.log(`Queue${i}: ${queueItem.name}`);\n   *       i++;\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * Example using paging with a marker:\n   *\n   * ```js\n   * let i = 1;\n   * let iterator = queueServiceClient.listQueues().byPage({ maxPageSize: 2 });\n   * let item = (await iterator.next()).value;\n   *\n   * // Prints 2 queue names\n   * if (item.queueItems) {\n   *   for (const queueItem of item.queueItems) {\n   *     console.log(`Queue${i}: ${queueItem.name}`);\n   *     i++;\n   *   }\n   * }\n   * // Gets next marker\n   * let marker = item.continuationToken;\n   *\n   * // Passing next marker as continuationToken\n   * iterator = queueServiceClient.listQueues().byPage({ continuationToken: marker, maxPageSize: 10 });\n   * item = (await iterator.next()).value;\n   *\n   * // Prints 10 queue names\n   * if (item.queueItems) {\n   *   for (const queueItem of item.queueItems) {\n   *     console.log(`Queue${i}: ${queueItem.name}`);\n   *     i++;\n   *   }\n   * }\n   * ```\n   *\n   * @param options - Options to list queues operation.\n   * @returns An asyncIterableIterator that supports paging.\n   */\n  public listQueues(\n    options: ServiceListQueuesOptions = {},\n  ): PagedAsyncIterableIterator<QueueItem, ServiceListQueuesSegmentResponse> {\n    if (options.prefix === \"\") {\n      options.prefix = undefined;\n    }\n\n    const updatedOptions: ServiceListQueuesSegmentOptions = {\n      ...options,\n      ...(options.includeMetadata ? { include: \"metadata\" } : {}),\n    };\n\n    // AsyncIterableIterator to iterate over queues\n    const iter = this.listItems(updatedOptions);\n    return {\n      /**\n       * The next method, part of the iteration protocol\n       */\n      next() {\n        return iter.next();\n      },\n      /**\n       * The connection to the async iterator, part of the iteration protocol\n       */\n      [Symbol.asyncIterator]() {\n        return this;\n      },\n      /**\n       * Return an AsyncIterableIterator that works a page at a time\n       */\n      byPage: (settings: PageSettings = {}) => {\n        return this.listSegments(settings.continuationToken, {\n          maxPageSize: settings.maxPageSize,\n          ...updatedOptions,\n        });\n      },\n    };\n  }\n\n  /**\n   * Gets the properties of a storage account’s Queue service, including properties\n   * for Storage Analytics and CORS (Cross-Origin Resource Sharing) rules.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-queue-service-properties\n   *\n   * @param options - Options to get properties operation.\n   * @returns Response data including the queue service properties.\n   */\n  public async getProperties(\n    options: ServiceGetPropertiesOptions = {},\n  ): Promise<ServiceGetPropertiesResponse> {\n    return tracingClient.withSpan(\n      \"QueueServiceClient-getProperties\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          ServiceGetPropertiesHeaders & QueueServiceProperties,\n          ServiceGetPropertiesHeaders,\n          QueueServiceProperties\n        >(await this.serviceContext.getProperties(updatedOptions));\n      },\n    );\n  }\n\n  /**\n   * Sets properties for a storage account’s Queue service endpoint, including properties\n   * for Storage Analytics, CORS (Cross-Origin Resource Sharing) rules and soft delete settings.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/set-queue-service-properties\n   *\n   * @param properties -\n   * @param options - Options to set properties operation.\n   * @returns Response data for the Set Properties operation.\n   */\n  public async setProperties(\n    properties: QueueServiceProperties,\n    options: ServiceGetPropertiesOptions = {},\n  ): Promise<ServiceSetPropertiesResponse> {\n    return tracingClient.withSpan(\n      \"QueueServiceClient-setProperties\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<ServiceSetPropertiesHeaders, ServiceSetPropertiesHeaders>(\n          await this.serviceContext.setProperties(properties, updatedOptions),\n        );\n      },\n    );\n  }\n\n  /**\n   * Retrieves statistics related to replication for the Queue service. It is only\n   * available on the secondary location endpoint when read-access geo-redundant\n   * replication is enabled for the storage account.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/get-queue-service-stats\n   *\n   * @param options - Options to get statistics operation.\n   * @returns Response data for get statistics the operation.\n   */\n  public async getStatistics(\n    options: ServiceGetStatisticsOptions = {},\n  ): Promise<ServiceGetStatisticsResponse> {\n    return tracingClient.withSpan(\n      \"QueueServiceClient-getStatistics\",\n      options,\n      async (updatedOptions) => {\n        return assertResponse<\n          ServiceGetStatisticsHeaders & QueueServiceStatistics,\n          ServiceGetStatisticsHeaders,\n          QueueServiceStatistics\n        >(await this.serviceContext.getStatistics(updatedOptions));\n      },\n    );\n  }\n\n  /**\n   * Creates a new queue under the specified account.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-queue4\n   *\n   * @param queueName - name of the queue to create\n   * @param options - Options to Queue create operation.\n   * @returns Response data for the Queue create operation.\n   */\n  public async createQueue(\n    queueName: string,\n    options: QueueCreateOptions = {},\n  ): Promise<QueueCreateResponse> {\n    return tracingClient.withSpan(\n      \"QueueServiceClient-createQueue\",\n      options,\n      async (updatedOptions) => {\n        return this.getQueueClient(queueName).create(updatedOptions);\n      },\n    );\n  }\n\n  /**\n   * Deletes the specified queue permanently.\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/delete-queue3\n   *\n   * @param queueName - name of the queue to delete.\n   * @param options - Options to Queue delete operation.\n   * @returns Response data for the Queue delete operation.\n   */\n  public async deleteQueue(\n    queueName: string,\n    options: QueueDeleteOptions = {},\n  ): Promise<QueueDeleteResponse> {\n    return tracingClient.withSpan(\n      \"QueueServiceClient-deleteQueue\",\n      options,\n      async (updatedOptions) => {\n        return this.getQueueClient(queueName).delete(updatedOptions);\n      },\n    );\n  }\n\n  /**\n   * Only available for QueueServiceClient constructed with a shared key credential.\n   *\n   * Generates an account Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-account-sas\n   *\n   * @param expiresOn - Optional. The time at which the shared access signature becomes invalid. Default to an hour later if not specified.\n   * @param permissions - Specifies the list of permissions to be associated with the SAS.\n   * @param resourceTypes - Specifies the resource types associated with the shared access signature.\n   * @param options - Optional parameters.\n   * @returns An account SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateAccountSasUrl(\n    expiresOn?: Date,\n    permissions: AccountSASPermissions = AccountSASPermissions.parse(\"r\"),\n    resourceTypes: string = \"sco\",\n    options: ServiceGenerateAccountSasUrlOptions = {},\n  ): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw RangeError(\n        \"Can only generate the account SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    if (expiresOn === undefined) {\n      const now = new Date();\n      expiresOn = new Date(now.getTime() + 3600 * 1000);\n    }\n\n    const sas = generateAccountSASQueryParameters(\n      {\n        permissions,\n        expiresOn,\n        resourceTypes,\n        services: AccountSASServices.parse(\"q\").toString(),\n        ...options,\n      },\n      this.credential,\n    ).toString();\n\n    return appendToURLQuery(this.url, sas);\n  }\n\n  /**\n   * Only available for QueueServiceClient constructed with a shared key credential.\n   *\n   * Generates string to sign for an account Shared Access Signature (SAS) URI based on the client properties\n   * and parameters passed in. The SAS is signed by the shared key credential of the client.\n   *\n   * @see https://learn.microsoft.com/en-us/rest/api/storageservices/create-account-sas\n   *\n   * @param expiresOn - Optional. The time at which the shared access signature becomes invalid. Default to an hour later if not specified.\n   * @param permissions - Specifies the list of permissions to be associated with the SAS.\n   * @param resourceTypes - Specifies the resource types associated with the shared access signature.\n   * @param options - Optional parameters.\n   * @returns An account SAS URI consisting of the URI to the resource represented by this client, followed by the generated SAS token.\n   */\n  public generateSasStringToSign(\n    expiresOn?: Date,\n    permissions: AccountSASPermissions = AccountSASPermissions.parse(\"r\"),\n    resourceTypes: string = \"sco\",\n    options: ServiceGenerateAccountSasUrlOptions = {},\n  ): string {\n    if (!(this.credential instanceof StorageSharedKeyCredential)) {\n      throw RangeError(\n        \"Can only generate the account SAS when the client is initialized with a shared key credential\",\n      );\n    }\n\n    if (expiresOn === undefined) {\n      const now = new Date();\n      expiresOn = new Date(now.getTime() + 3600 * 1000);\n    }\n\n    return generateAccountSASQueryParametersInternal(\n      {\n        permissions,\n        expiresOn,\n        resourceTypes,\n        services: AccountSASServices.parse(\"q\").toString(),\n        ...options,\n      },\n      this.credential,\n    ).stringToSign;\n  }\n}\n"]}