{"version": 3, "file": "StorageBrowserPolicyFactory.js", "sourceRoot": "", "sources": ["../../../../storage-blob/src/StorageBrowserPolicyFactory.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAC;AACvE,OAAO,EAAE,oBAAoB,EAAE,CAAC;AAEhC;;GAEG;AACH,MAAM,OAAO,2BAA2B;IACtC;;;;;OAKG;IACI,MAAM,CAAC,UAAyB,EAAE,OAA6B;QACpE,OAAO,IAAI,oBAAoB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  RequestPolicy,\n  RequestPolicyOptionsLike as RequestPolicyOptions,\n  RequestPolicyFactory,\n} from \"@azure/core-http-compat\";\nimport { StorageBrowserPolicy } from \"./policies/StorageBrowserPolicy\";\nexport { StorageBrowserPolicy };\n\n/**\n * StorageBrowserPolicyFactory is a factory class helping generating StorageBrowserPolicy objects.\n */\nexport class StorageBrowserPolicyFactory implements RequestPolicyFactory {\n  /**\n   * Creates a StorageBrowserPolicyFactory object.\n   *\n   * @param nextPolicy -\n   * @param options -\n   */\n  public create(nextPolicy: RequestPolicy, options: RequestPolicyOptions): StorageBrowserPolicy {\n    return new StorageBrowserPolicy(nextPolicy, options);\n  }\n}\n"]}