{"version": 3, "file": "Batch.js", "sourceRoot": "", "sources": ["../../../../src/utils/Batch.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,8FAA8F;AAC9F,mCAAmC;AACnC,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AAOtC;;GAEG;AACH,IAAK,WAGJ;AAHD,WAAK,WAAW;IACd,6CAAI,CAAA;IACJ,+CAAK,CAAA;AACP,CAAC,EAHI,WAAW,KAAX,WAAW,QAGf;AAED;;;;GAIG;AACH,MAAM,OAAO,KAAK;IAqChB;;;OAGG;IACH,YAAmB,cAAsB,CAAC;QAnC1C;;WAEG;QACK,YAAO,GAAW,CAAC,CAAC;QAE5B;;WAEG;QACK,cAAS,GAAW,CAAC,CAAC;QAE9B;;WAEG;QACK,WAAM,GAAW,CAAC,CAAC;QAE3B;;WAEG;QACK,eAAU,GAAgB,EAAE,CAAC;QAErC;;;WAGG;QACK,UAAK,GAAgB,WAAW,CAAC,IAAI,CAAC;QAY5C,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,UAAU,CAAC,mCAAmC,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,YAAY,EAAE,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,YAAY,CAAC,SAAoB;QACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC;gBACH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,MAAM,SAAS,EAAE,CAAC;gBAClB,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,EAAE;QACb,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAEnC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACjC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;gBAC/B,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACK,aAAa;QACnB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACK,eAAe;QACrB,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC,KAAK,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACvC,IAAI,SAAS,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;YACd,CAAC;iBAAM,CAAC;gBACN,OAAO;YACT,CAAC;QACH,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n// In browser, during webpack or browserify bundling, this module will be replaced by 'events'\n// https://github.com/Gozala/events\nimport { EventEmitter } from \"events\";\n\n/**\n * Operation is an async function to be executed and managed by <PERSON><PERSON>.\n */\nexport declare type Operation = () => Promise<any>;\n\n/**\n * States for Batch.\n */\nenum BatchStates {\n  Good,\n  Error,\n}\n\n/**\n * Batch provides basic parallel execution with concurrency limits.\n * Will stop execute left operations when one of the executed operation throws an error.\n * But Bat<PERSON> cannot cancel ongoing operations, you need to cancel them by yourself.\n */\nexport class Batch {\n  /**\n   * Concurrency. Must be lager than 0.\n   */\n  private concurrency: number;\n\n  /**\n   * Number of active operations under execution.\n   */\n  private actives: number = 0;\n\n  /**\n   * Number of completed operations under execution.\n   */\n  private completed: number = 0;\n\n  /**\n   * Offset of next operation to be executed.\n   */\n  private offset: number = 0;\n\n  /**\n   * Operation array to be executed.\n   */\n  private operations: Operation[] = [];\n\n  /**\n   * States of Batch. When an error happens, state will turn into error.\n   * Batch will stop execute left operations.\n   */\n  private state: BatchStates = BatchStates.Good;\n\n  /**\n   * A private emitter used to pass events inside this class.\n   */\n  private emitter: EventEmitter;\n\n  /**\n   * Creates an instance of Batch.\n   * @param concurrency -\n   */\n  public constructor(concurrency: number = 5) {\n    if (concurrency < 1) {\n      throw new RangeError(\"concurrency must be larger than 0\");\n    }\n    this.concurrency = concurrency;\n    this.emitter = new EventEmitter();\n  }\n\n  /**\n   * Add a operation into queue.\n   *\n   * @param operation -\n   */\n  public addOperation(operation: Operation): void {\n    this.operations.push(async () => {\n      try {\n        this.actives++;\n        await operation();\n        this.actives--;\n        this.completed++;\n        this.parallelExecute();\n      } catch (error: any) {\n        this.emitter.emit(\"error\", error);\n      }\n    });\n  }\n\n  /**\n   * Start execute operations in the queue.\n   *\n   */\n  public async do(): Promise<void> {\n    if (this.operations.length === 0) {\n      return Promise.resolve();\n    }\n\n    this.parallelExecute();\n\n    return new Promise<void>((resolve, reject) => {\n      this.emitter.on(\"finish\", resolve);\n\n      this.emitter.on(\"error\", (error) => {\n        this.state = BatchStates.Error;\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Get next operation to be executed. Return null when reaching ends.\n   *\n   */\n  private nextOperation(): Operation | null {\n    if (this.offset < this.operations.length) {\n      return this.operations[this.offset++];\n    }\n    return null;\n  }\n\n  /**\n   * Start execute operations. One one the most important difference between\n   * this method with do() is that do() wraps as an sync method.\n   *\n   */\n  private parallelExecute(): void {\n    if (this.state === BatchStates.Error) {\n      return;\n    }\n\n    if (this.completed >= this.operations.length) {\n      this.emitter.emit(\"finish\");\n      return;\n    }\n\n    while (this.actives < this.concurrency) {\n      const operation = this.nextOperation();\n      if (operation) {\n        operation();\n      } else {\n        return;\n      }\n    }\n  }\n}\n"]}