{"version": 3, "file": "OTLPMetricExporter.js", "sourceRoot": "", "sources": ["../../src/OTLPMetricExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0FAGmD;AAEnD,oFAMgD;AAChD,8CAA2D;AAC3D,2CAAyC;AACzC,sEAGyC;AACzC,uCAAoC;AAEpC,MAAM,UAAU,GAAG;IACjB,YAAY,EAAE,iCAAiC,iBAAO,EAAE;CACzD,CAAC;AAEF,MAAM,uBAAwB,SAAQ,kDAGrC;IACC,YAAY,MAA+D;QACzE,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,MAAM,OAAO,mCACR,UAAU,GACV,mBAAY,CAAC,uBAAuB,CACrC,IAAA,aAAM,GAAE,CAAC,kCAAkC,CAC5C,CACF,CAAC;QAEF,IAAI,CAAC,QAAQ,KAAb,IAAI,CAAC,QAAQ,GAAK,IAAI,kBAAQ,EAAE,EAAC;QACjC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC5C,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;IACH,CAAC;IAED,mBAAmB;QACjB,OAAO,gEAAgE,CAAC;IAC1E,CAAC;IAED,oBAAoB;QAClB,OAAO,2CAAiB,CAAC,OAAO,CAAC;IACnC,CAAC;IAED,aAAa,CAAC,MAAkC;QAC9C,OAAO,IAAA,iDAAuB,EAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,OAAO,CAAC,OAA0B;QAChC,OAAO,IAAA,oDAAiC,EAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAED,gBAAgB,CAAC,MAAkC;QACjD,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ,EAAE;YAClC,OAAO,MAAM,CAAC,GAAG,CAAC;SACnB;QAED,OAAO,CACL,IAAA,aAAM,GAAE,CAAC,mCAAmC;YAC5C,IAAA,aAAM,GAAE,CAAC,2BAA2B;YACpC,+CAAqB,CACtB,CAAC;IACJ,CAAC;CACF;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,mDAA+C;IACrF,YAAY,MAA+D;QACzE,KAAK,CAAC,IAAI,uBAAuB,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;IACrD,CAAC;CACF;AAJD,gDAIC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OTLPMetricExporterBase,\n  OTLPMetricExporterOptions,\n} from '@opentelemetry/exporter-metrics-otlp-http';\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport {\n  OTLPGRPCExporterConfigNode,\n  OTLPGRPCExporterNodeBase,\n  ServiceClientType,\n  validateAndNormalizeUrl,\n  DEFAULT_COLLECTOR_URL,\n} from '@opentelemetry/otlp-grpc-exporter-base';\nimport { baggageUtils, getEnv } from '@opentelemetry/core';\nimport { Metadata } from '@grpc/grpc-js';\nimport {\n  createExportMetricsServiceRequest,\n  IExportMetricsServiceRequest,\n} from '@opentelemetry/otlp-transformer';\nimport { VERSION } from './version';\n\nconst USER_AGENT = {\n  'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n};\n\nclass OTLPMetricExporterProxy extends OTLPGRPCExporterNodeBase<\n  ResourceMetrics,\n  IExportMetricsServiceRequest\n> {\n  constructor(config?: OTLPGRPCExporterConfigNode & OTLPMetricExporterOptions) {\n    super(config);\n    const headers = {\n      ...USER_AGENT,\n      ...baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_METRICS_HEADERS\n      ),\n    };\n\n    this.metadata ||= new Metadata();\n    for (const [k, v] of Object.entries(headers)) {\n      this.metadata.set(k, v);\n    }\n  }\n\n  getServiceProtoPath(): string {\n    return 'opentelemetry/proto/collector/metrics/v1/metrics_service.proto';\n  }\n\n  getServiceClientType(): ServiceClientType {\n    return ServiceClientType.METRICS;\n  }\n\n  getDefaultUrl(config: OTLPGRPCExporterConfigNode): string {\n    return validateAndNormalizeUrl(this.getUrlFromConfig(config));\n  }\n\n  convert(metrics: ResourceMetrics[]): IExportMetricsServiceRequest {\n    return createExportMetricsServiceRequest(metrics);\n  }\n\n  getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string {\n    if (typeof config.url === 'string') {\n      return config.url;\n    }\n\n    return (\n      getEnv().OTEL_EXPORTER_OTLP_METRICS_ENDPOINT ||\n      getEnv().OTEL_EXPORTER_OTLP_ENDPOINT ||\n      DEFAULT_COLLECTOR_URL\n    );\n  }\n}\n\n/**\n * OTLP-gRPC metric exporter\n */\nexport class OTLPMetricExporter extends OTLPMetricExporterBase<OTLPMetricExporterProxy> {\n  constructor(config?: OTLPGRPCExporterConfigNode & OTLPMetricExporterOptions) {\n    super(new OTLPMetricExporterProxy(config), config);\n  }\n}\n"]}