{"version": 3, "file": "helpers.js", "sourceRoot": "", "sources": ["../../../src/util/helpers.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,EAAE,UAAU,EAAE,MAAM,mCAAmC,CAAC;AAG/D,MAAM,oBAAoB,GAAG,4BAA4B,CAAC;AAE1D;;;;;;;;GAQG;AACH,MAAM,UAAU,KAAK,CACnB,SAAiB,EACjB,KAAS,EACT,OAGC;IAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,KAAK,GAA8C,SAAS,CAAC;QACjE,IAAI,SAAS,GAA6B,SAAS,CAAC;QAEpD,MAAM,aAAa,GAAG,GAAS,EAAE;YAC/B,OAAO,MAAM,CACX,IAAI,UAAU,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,EAAC,CAAC,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,aAAa,CAAC,CAAC,CAAC,oBAAoB,CAAC,CACvF,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,GAAS,EAAE;YACjC,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,KAAI,SAAS,EAAE,CAAC;gBACtC,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC,CAAC;QAEF,SAAS,GAAG,GAAS,EAAE;YACrB,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC;YACD,eAAe,EAAE,CAAC;YAClB,OAAO,aAAa,EAAE,CAAC;QACzB,CAAC,CAAC;QAEF,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,KAAI,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACxD,OAAO,aAAa,EAAE,CAAC;QACzB,CAAC;QAED,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;YACtB,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,EAAE,SAAS,CAAC,CAAC;QAEd,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,WAAW,EAAE,CAAC;YACzB,OAAO,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,wBAAwB,CACtC,QAA0B,EAC1B,UAAkB;IAElB,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/C,IAAI,CAAC,KAAK;QAAE,OAAO;IACnB,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACjC,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC;QAAE,OAAO;IACrC,OAAO,UAAU,CAAC;AACpB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport { AbortError } from \"../abort-controller/AbortError.js\";\nimport type { PipelineResponse } from \"../interfaces.js\";\n\nconst StandardAbortMessage = \"The operation was aborted.\";\n\n/**\n * A wrapper for setTimeout that resolves a promise after delayInMs milliseconds.\n * @param delayInMs - The number of milliseconds to be delayed.\n * @param value - The value to be resolved with after a timeout of t milliseconds.\n * @param options - The options for delay - currently abort options\n *                  - abortSignal - The abortSignal associated with containing operation.\n *                  - abortErrorMsg - The abort error message associated with containing operation.\n * @returns Resolved promise\n */\nexport function delay<T>(\n  delayInMs: number,\n  value?: T,\n  options?: {\n    abortSignal?: AbortSignal;\n    abortErrorMsg?: string;\n  },\n): Promise<T | void> {\n  return new Promise((resolve, reject) => {\n    let timer: ReturnType<typeof setTimeout> | undefined = undefined;\n    let onAborted: (() => void) | undefined = undefined;\n\n    const rejectOnAbort = (): void => {\n      return reject(\n        new AbortError(options?.abortErrorMsg ? options?.abortErrorMsg : StandardAbortMessage),\n      );\n    };\n\n    const removeListeners = (): void => {\n      if (options?.abortSignal && onAborted) {\n        options.abortSignal.removeEventListener(\"abort\", onAborted);\n      }\n    };\n\n    onAborted = (): void => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n      removeListeners();\n      return rejectOnAbort();\n    };\n\n    if (options?.abortSignal && options.abortSignal.aborted) {\n      return rejectOnAbort();\n    }\n\n    timer = setTimeout(() => {\n      removeListeners();\n      resolve(value);\n    }, delayInMs);\n\n    if (options?.abortSignal) {\n      options.abortSignal.addEventListener(\"abort\", onAborted);\n    }\n  });\n}\n\n/**\n * @internal\n * @returns the parsed value or undefined if the parsed value is invalid.\n */\nexport function parseHeaderValueAsNumber(\n  response: PipelineResponse,\n  headerName: string,\n): number | undefined {\n  const value = response.headers.get(headerName);\n  if (!value) return;\n  const valueAsNum = Number(value);\n  if (Number.isNaN(valueAsNum)) return;\n  return valueAsNum;\n}\n"]}