{"version": 3, "file": "AvroParser.js", "sourceRoot": "", "sources": ["../../../../storage-internal-avro/src/AvroParser.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAqBlC,MAAM,OAAO,UAAU;IACrB;;;;;;OAMG;IACI,MAAM,CAAC,KAAK,CAAC,cAAc,CAChC,MAAoB,EACpB,MAAc,EACd,UAAiC,EAAE;QAEnC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAC9E,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACrC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;OAKG;IACK,MAAM,CAAC,KAAK,CAAC,QAAQ,CAC3B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QAChE,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IAED,6DAA6D;IAC7D,8EAA8E;IAC9E,oFAAoF;IAC5E,MAAM,CAAC,KAAK,CAAC,cAAc,CACjC,MAAoB,EACpB,UAAiC,EAAE;QAEnC,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAC1B,IAAI,IAAI,EAAE,YAAY,EAAE,mBAAmB,CAAC;QAE5C,GAAG,CAAC;YACF,IAAI,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClD,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC;YAC3B,aAAa,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,iBAAiB,CAAC;YACpD,iBAAiB,IAAI,CAAC,CAAC;QACzB,CAAC,QAAQ,YAAY,IAAI,iBAAiB,GAAG,EAAE,EAAE,CAAC,mDAAmD;QAErG,IAAI,YAAY,EAAE,CAAC;YACjB,6BAA6B;YAC7B,0CAA0C;YAC1C,aAAa,GAAG,aAAa,CAAC;YAC9B,mBAAmB,GAAG,SAAS,CAAC,CAAC,WAAW;YAC5C,GAAG,CAAC;gBACF,IAAI,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAClD,aAAa,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,mBAAmB,CAAC;gBACrD,mBAAmB,IAAI,GAAG,CAAC,CAAC,SAAS;YACvC,CAAC,QAAQ,IAAI,GAAG,IAAI,EAAE;YAEtB,MAAM,GAAG,GAAG,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC3E,IAAI,GAAG,GAAG,MAAM,CAAC,gBAAgB,IAAI,GAAG,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBACnE,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAC1B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,OAAO,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,MAAoB,EACpB,UAAiC,EAAE;QAEnC,OAAO,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,QAAQ;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,WAAW,CAC7B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,CAAC,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACrD,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,SAAS,CAC3B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QAClE,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,sBAAsB;IACzD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,UAAU,CAC5B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;QAClE,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC5E,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,sBAAsB;IACzD,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,SAAS,CAC3B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACxD,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;IACjE,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,UAAU,CAC5B,MAAoB,EACpB,UAAiC,EAAE;QAEnC,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;QACtC,OAAO,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,WAAW,CAC9B,MAAoB,EACpB,cAAgF,EAChF,UAAiC,EAAE;QAEnC,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACzD,0GAA0G;QAC1G,MAAM,KAAK,GAAG,MAAM,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACpD,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;IACxB,CAAC;IAEM,MAAM,CAAC,KAAK,CAAC,OAAO,CACzB,MAAoB,EACpB,cAAgF,EAChF,UAAiC,EAAE;QAEnC,MAAM,cAAc,GAAG,CACrB,CAAe,EACf,OAA8B,EAAE,EACN,EAAE;YAC5B,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC;QAEF,MAAM,KAAK,GAAsB,MAAM,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;QAE7F,MAAM,IAAI,GAAsB,EAAE,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAC9B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,MAAM,CAAC,KAAK,CAAC,SAAS,CAC5B,MAAoB,EACpB,cAAgF,EAChF,UAAiC,EAAE;QAEnC,MAAM,KAAK,GAAQ,EAAE,CAAC;QACtB,KACE,IAAI,KAAK,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,EACtD,KAAK,KAAK,CAAC,EACX,KAAK,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,EAClD,CAAC;YACD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACd,qBAAqB;gBACrB,MAAM,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBAC3C,KAAK,GAAG,CAAC,KAAK,CAAC;YACjB,CAAC;YAED,OAAO,KAAK,EAAE,EAAE,CAAC;gBACf,MAAM,IAAI,GAAM,MAAM,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACtD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAOD,IAAK,WAOJ;AAPD,WAAK,WAAW;IACd,gCAAiB,CAAA;IACjB,4BAAa,CAAA;IACb,8BAAe,CAAA;IACf,0BAAW,CAAA;IACX,8BAAe,CAAA;IACf,8BAAe,CAAA;AACjB,CAAC,EAPI,WAAW,KAAX,WAAW,QAOf;AAYD,IAAK,aASJ;AATD,WAAK,aAAa;IAChB,8BAAa,CAAA;IACb,oCAAmB,CAAA;IACnB,4BAAW,CAAA;IACX,8BAAa,CAAA;IACb,gCAAe,CAAA;IACf,kCAAiB,CAAA;IACjB,gCAAe,CAAA;IACf,kCAAiB,CAAA;AACnB,CAAC,EATI,aAAa,KAAb,aAAa,QASjB;AAED,MAAM,OAAgB,QAAQ;IAS5B;;OAEG;IACH,sEAAsE;IAC/D,MAAM,CAAC,UAAU,CAAC,MAAuB;QAC9C,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,OAAO,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,QAAQ,CAAC,gBAAgB,CAAC,MAAsB,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,MAAc;QAC5C,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,aAAa,CAAC,IAAI,CAAC;YACxB,KAAK,aAAa,CAAC,OAAO,CAAC;YAC3B,KAAK,aAAa,CAAC,GAAG,CAAC;YACvB,KAAK,aAAa,CAAC,IAAI,CAAC;YACxB,KAAK,aAAa,CAAC,KAAK,CAAC;YACzB,KAAK,aAAa,CAAC,MAAM,CAAC;YAC1B,KAAK,aAAa,CAAC,KAAK,CAAC;YACzB,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,IAAI,iBAAiB,CAAC,MAAuB,CAAC,CAAC;YACxD;gBACE,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,EAAE,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,MAAM,CAAC,eAAe,CAAC,MAAa;QAC1C,OAAO,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC;IAC5D,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,MAAoB;QAClD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;QACzB,kDAAkD;QAClD,IAAI,CAAC;YACH,OAAO,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAAC,WAAM,CAAC;YACP,QAAQ;QACV,CAAC;QAED,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,WAAW,CAAC,MAAM;gBACrB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,+CAA+C,MAAM,EAAE,CAAC,CAAC;gBAC3E,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;oBACjB,MAAM,IAAI,KAAK,CAAC,sDAAsD,MAAM,EAAE,CAAC,CAAC;gBAClF,CAAC;gBAED,gDAAgD;gBAChD,MAAM,MAAM,GAA6B,EAAE,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,wDAAwD,MAAM,EAAE,CAAC,CAAC;gBACpF,CAAC;gBACD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBAClC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACvD,CAAC;gBACD,OAAO,IAAI,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YACjD,KAAK,WAAW,CAAC,IAAI;gBACnB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,+CAA+C,MAAM,EAAE,CAAC,CAAC;gBAC3E,CAAC;gBACD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBACpB,MAAM,IAAI,KAAK,CAAC,yDAAyD,MAAM,EAAE,CAAC,CAAC;gBACrF,CAAC;gBACD,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAC1C,KAAK,WAAW,CAAC,GAAG;gBAClB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,wDAAwD,MAAM,EAAE,CAAC,CAAC;gBACpF,CAAC;gBACD,OAAO,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;YAC7D,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC,eAAe;YACvC,KAAK,WAAW,CAAC,KAAK,CAAC,CAAC,eAAe;YACvC;gBACE,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,OAAO,MAAM,EAAE,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;CACF;AAED,MAAM,iBAAkB,SAAQ,QAAQ;IAGtC,YAAY,SAAwB;QAClC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,sEAAsE;IAC/D,IAAI,CAAC,MAAoB,EAAE,UAAiC,EAAE;QACnE,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;YACxB,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC/B,KAAK,aAAa,CAAC,OAAO;gBACxB,OAAO,UAAU,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YACjD,KAAK,aAAa,CAAC,GAAG;gBACpB,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC7C,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC9C,KAAK,aAAa,CAAC,KAAK;gBACtB,OAAO,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC/C,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAChD,KAAK,aAAa,CAAC,KAAK;gBACtB,OAAO,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC/C,KAAK,aAAa,CAAC,MAAM;gBACvB,OAAO,UAAU,CAAC,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAChD;gBACE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;CACF;AAED,MAAM,YAAa,SAAQ,QAAQ;IAGjC,YAAY,OAAiB;QAC3B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC1B,CAAC;IAED,sEAAsE;IAC/D,KAAK,CAAC,IAAI,CAAC,MAAoB,EAAE,UAAiC,EAAE;QACzE,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;CACF;AAED,MAAM,aAAc,SAAQ,QAAQ;IAGlC,YAAY,KAAiB;QAC3B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAEM,KAAK,CAAC,IAAI,CACf,MAAoB,EACpB,UAAiC,EAAE;QAGnC,MAAM,SAAS,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC5D,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;CACF;AAED,MAAM,WAAY,SAAQ,QAAQ;IAGhC,YAAY,QAAkB;QAC5B,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,sEAAsE;IAC/D,IAAI,CAAC,MAAoB,EAAE,UAAiC,EAAE;QACnE,MAAM,cAAc,GAAG,CACrB,CAAe,EACf,IAA4B,EAEJ,EAAE;YAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC;QACF,OAAO,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC;CACF;AAED,MAAM,cAAe,SAAQ,QAAQ;IAInC,YAAY,MAAgC,EAAE,IAAY;QACxD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAED,sEAAsE;IAC/D,KAAK,CAAC,IAAI,CAAC,MAAoB,EAAE,UAAiC,EAAE;QACzE,sEAAsE;QACtE,MAAM,MAAM,GAAkC,EAAE,CAAC;QACjD,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAC/B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAC/B,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC9D,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n// TODO: Do a review of the Object usage and non-interfaces\n/* eslint-disable @azure/azure-sdk/ts-use-interface-parameters */\n\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { AvroReadable } from \"./AvroReadable\";\nimport type { KeyValuePair } from \"./utils/utils.common\";\n\n/**\n * Options to configure the AvroParser read methods.\n * See {@link AvroParser.readFixedBytes}, {@link AvroParser.readMap} and etc.\n */\ninterface AvroParserReadOptions {\n  /**\n   * An implementation of the `AbortSignalLike` interface to signal the request to cancel the operation.\n   * For example, use the &commat;azure/abort-controller to create an `AbortSignal`.\n   */\n  abortSignal?: AbortSignalLike;\n}\n\nexport class AvroParser {\n  /**\n   * Reads a fixed number of bytes from the stream.\n   *\n   * @param stream -\n   * @param length -\n   * @param options -\n   */\n  public static async readFixedBytes(\n    stream: AvroReadable,\n    length: number,\n    options: AvroParserReadOptions = {},\n  ): Promise<Uint8Array> {\n    const bytes = await stream.read(length, { abortSignal: options.abortSignal });\n    if (bytes.length !== length) {\n      throw new Error(\"Hit stream end.\");\n    }\n    return bytes;\n  }\n\n  /**\n   * Reads a single byte from the stream.\n   *\n   * @param stream -\n   * @param options -\n   */\n  private static async readByte(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<number> {\n    const buf = await AvroParser.readFixedBytes(stream, 1, options);\n    return buf[0];\n  }\n\n  // int and long are stored in variable-length zig-zag coding.\n  // variable-length: https://lucene.apache.org/core/3_5_0/fileformats.html#VInt\n  // zig-zag: https://developers.google.com/protocol-buffers/docs/encoding?csw=1#types\n  private static async readZigZagLong(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<number> {\n    let zigZagEncoded = 0;\n    let significanceInBit = 0;\n    let byte, haveMoreByte, significanceInFloat;\n\n    do {\n      byte = await AvroParser.readByte(stream, options);\n      haveMoreByte = byte & 0x80;\n      zigZagEncoded |= (byte & 0x7f) << significanceInBit;\n      significanceInBit += 7;\n    } while (haveMoreByte && significanceInBit < 28); // bitwise operation only works for 32-bit integers\n\n    if (haveMoreByte) {\n      // Switch to float arithmetic\n      // eslint-disable-next-line no-self-assign\n      zigZagEncoded = zigZagEncoded;\n      significanceInFloat = 268435456; // 2 ** 28.\n      do {\n        byte = await AvroParser.readByte(stream, options);\n        zigZagEncoded += (byte & 0x7f) * significanceInFloat;\n        significanceInFloat *= 128; // 2 ** 7\n      } while (byte & 0x80);\n\n      const res = (zigZagEncoded % 2 ? -(zigZagEncoded + 1) : zigZagEncoded) / 2;\n      if (res < Number.MIN_SAFE_INTEGER || res > Number.MAX_SAFE_INTEGER) {\n        throw new Error(\"Integer overflow.\");\n      }\n      return res;\n    }\n\n    return (zigZagEncoded >> 1) ^ -(zigZagEncoded & 1);\n  }\n\n  public static async readLong(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<number> {\n    return AvroParser.readZigZagLong(stream, options);\n  }\n\n  public static async readInt(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<number> {\n    return AvroParser.readZigZagLong(stream, options);\n  }\n\n  public static async readNull(): Promise<null> {\n    return null;\n  }\n\n  public static async readBoolean(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<boolean> {\n    const b = await AvroParser.readByte(stream, options);\n    if (b === 1) {\n      return true;\n    } else if (b === 0) {\n      return false;\n    } else {\n      throw new Error(\"Byte was not a boolean.\");\n    }\n  }\n\n  public static async readFloat(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<number> {\n    const u8arr = await AvroParser.readFixedBytes(stream, 4, options);\n    const view = new DataView(u8arr.buffer, u8arr.byteOffset, u8arr.byteLength);\n    return view.getFloat32(0, true); // littleEndian = true\n  }\n\n  public static async readDouble(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<number> {\n    const u8arr = await AvroParser.readFixedBytes(stream, 8, options);\n    const view = new DataView(u8arr.buffer, u8arr.byteOffset, u8arr.byteLength);\n    return view.getFloat64(0, true); // littleEndian = true\n  }\n\n  public static async readBytes(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<Uint8Array> {\n    const size = await AvroParser.readLong(stream, options);\n    if (size < 0) {\n      throw new Error(\"Bytes size was negative.\");\n    }\n\n    return stream.read(size, { abortSignal: options.abortSignal });\n  }\n\n  public static async readString(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n  ): Promise<string> {\n    const u8arr = await AvroParser.readBytes(stream, options);\n    const utf8decoder = new TextDecoder();\n    return utf8decoder.decode(u8arr);\n  }\n\n  private static async readMapPair<T>(\n    stream: AvroReadable,\n    readItemMethod: (s: AvroReadable, options?: AvroParserReadOptions) => Promise<T>,\n    options: AvroParserReadOptions = {},\n  ): Promise<KeyValuePair<T>> {\n    const key = await AvroParser.readString(stream, options);\n    // FUTURE: this won't work with readFixed (currently not supported) which needs a length as the parameter.\n    const value = await readItemMethod(stream, options);\n    return { key, value };\n  }\n\n  public static async readMap<T>(\n    stream: AvroReadable,\n    readItemMethod: (s: AvroReadable, options?: AvroParserReadOptions) => Promise<T>,\n    options: AvroParserReadOptions = {},\n  ): Promise<Record<string, T>> {\n    const readPairMethod = (\n      s: AvroReadable,\n      opts: AvroParserReadOptions = {},\n    ): Promise<KeyValuePair<T>> => {\n      return AvroParser.readMapPair(s, readItemMethod, opts);\n    };\n\n    const pairs: KeyValuePair<T>[] = await AvroParser.readArray(stream, readPairMethod, options);\n\n    const dict: Record<string, T> = {};\n    for (const pair of pairs) {\n      dict[pair.key] = pair.value;\n    }\n    return dict;\n  }\n\n  private static async readArray<T>(\n    stream: AvroReadable,\n    readItemMethod: (s: AvroReadable, options?: AvroParserReadOptions) => Promise<T>,\n    options: AvroParserReadOptions = {},\n  ): Promise<T[]> {\n    const items: T[] = [];\n    for (\n      let count = await AvroParser.readLong(stream, options);\n      count !== 0;\n      count = await AvroParser.readLong(stream, options)\n    ) {\n      if (count < 0) {\n        // Ignore block sizes\n        await AvroParser.readLong(stream, options);\n        count = -count;\n      }\n\n      while (count--) {\n        const item: T = await readItemMethod(stream, options);\n        items.push(item);\n      }\n    }\n    return items;\n  }\n}\n\ninterface RecordField {\n  name: string;\n  type: string | ObjectSchema | (string | ObjectSchema)[]; // Unions may not immediately contain other unions.\n}\n\nenum AvroComplex {\n  RECORD = \"record\",\n  ENUM = \"enum\",\n  ARRAY = \"array\",\n  MAP = \"map\",\n  UNION = \"union\",\n  FIXED = \"fixed\",\n}\n\ninterface ObjectSchema {\n  type: Exclude<AvroComplex, AvroComplex.UNION>;\n  name?: string;\n  aliases?: string;\n  fields?: RecordField[];\n  symbols?: string[];\n  values?: string;\n  size?: number;\n}\n\nenum AvroPrimitive {\n  NULL = \"null\",\n  BOOLEAN = \"boolean\",\n  INT = \"int\",\n  LONG = \"long\",\n  FLOAT = \"float\",\n  DOUBLE = \"double\",\n  BYTES = \"bytes\",\n  STRING = \"string\",\n}\n\nexport abstract class AvroType {\n  /**\n   * Reads an object from the stream.\n   */\n  public abstract read(\n    stream: AvroReadable,\n    options?: AvroParserReadOptions,\n  ): Promise<Object | null>; // eslint-disable-line @typescript-eslint/no-wrapper-object-types\n\n  /**\n   * Determines the AvroType from the Avro Schema.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n  public static fromSchema(schema: string | Object): AvroType {\n    if (typeof schema === \"string\") {\n      return AvroType.fromStringSchema(schema);\n    } else if (Array.isArray(schema)) {\n      return AvroType.fromArraySchema(schema);\n    } else {\n      return AvroType.fromObjectSchema(schema as ObjectSchema);\n    }\n  }\n\n  private static fromStringSchema(schema: string): AvroType {\n    switch (schema) {\n      case AvroPrimitive.NULL:\n      case AvroPrimitive.BOOLEAN:\n      case AvroPrimitive.INT:\n      case AvroPrimitive.LONG:\n      case AvroPrimitive.FLOAT:\n      case AvroPrimitive.DOUBLE:\n      case AvroPrimitive.BYTES:\n      case AvroPrimitive.STRING:\n        return new AvroPrimitiveType(schema as AvroPrimitive);\n      default:\n        throw new Error(`Unexpected Avro type ${schema}`);\n    }\n  }\n\n  private static fromArraySchema(schema: any[]): AvroType {\n    return new AvroUnionType(schema.map(AvroType.fromSchema));\n  }\n\n  private static fromObjectSchema(schema: ObjectSchema): AvroType {\n    const type = schema.type;\n    // Primitives can be defined as strings or objects\n    try {\n      return AvroType.fromStringSchema(type);\n    } catch {\n      // no-op\n    }\n\n    switch (type) {\n      case AvroComplex.RECORD:\n        if (schema.aliases) {\n          throw new Error(`aliases currently is not supported, schema: ${schema}`);\n        }\n        if (!schema.name) {\n          throw new Error(`Required attribute 'name' doesn't exist on schema: ${schema}`);\n        }\n\n        // eslint-disable-next-line no-case-declarations\n        const fields: Record<string, AvroType> = {};\n        if (!schema.fields) {\n          throw new Error(`Required attribute 'fields' doesn't exist on schema: ${schema}`);\n        }\n        for (const field of schema.fields) {\n          fields[field.name] = AvroType.fromSchema(field.type);\n        }\n        return new AvroRecordType(fields, schema.name);\n      case AvroComplex.ENUM:\n        if (schema.aliases) {\n          throw new Error(`aliases currently is not supported, schema: ${schema}`);\n        }\n        if (!schema.symbols) {\n          throw new Error(`Required attribute 'symbols' doesn't exist on schema: ${schema}`);\n        }\n        return new AvroEnumType(schema.symbols);\n      case AvroComplex.MAP:\n        if (!schema.values) {\n          throw new Error(`Required attribute 'values' doesn't exist on schema: ${schema}`);\n        }\n        return new AvroMapType(AvroType.fromSchema(schema.values));\n      case AvroComplex.ARRAY: // Unused today\n      case AvroComplex.FIXED: // Unused today\n      default:\n        throw new Error(`Unexpected Avro type ${type} in ${schema}`);\n    }\n  }\n}\n\nclass AvroPrimitiveType extends AvroType {\n  private _primitive: AvroPrimitive;\n\n  constructor(primitive: AvroPrimitive) {\n    super();\n    this._primitive = primitive;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n  public read(stream: AvroReadable, options: AvroParserReadOptions = {}): Promise<Object | null> {\n    switch (this._primitive) {\n      case AvroPrimitive.NULL:\n        return AvroParser.readNull();\n      case AvroPrimitive.BOOLEAN:\n        return AvroParser.readBoolean(stream, options);\n      case AvroPrimitive.INT:\n        return AvroParser.readInt(stream, options);\n      case AvroPrimitive.LONG:\n        return AvroParser.readLong(stream, options);\n      case AvroPrimitive.FLOAT:\n        return AvroParser.readFloat(stream, options);\n      case AvroPrimitive.DOUBLE:\n        return AvroParser.readDouble(stream, options);\n      case AvroPrimitive.BYTES:\n        return AvroParser.readBytes(stream, options);\n      case AvroPrimitive.STRING:\n        return AvroParser.readString(stream, options);\n      default:\n        throw new Error(\"Unknown Avro Primitive\");\n    }\n  }\n}\n\nclass AvroEnumType extends AvroType {\n  private readonly _symbols: string[];\n\n  constructor(symbols: string[]) {\n    super();\n    this._symbols = symbols;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n  public async read(stream: AvroReadable, options: AvroParserReadOptions = {}): Promise<Object> {\n    const value = await AvroParser.readInt(stream, options);\n    return this._symbols[value];\n  }\n}\n\nclass AvroUnionType extends AvroType {\n  private readonly _types: AvroType[];\n\n  constructor(types: AvroType[]) {\n    super();\n    this._types = types;\n  }\n\n  public async read(\n    stream: AvroReadable,\n    options: AvroParserReadOptions = {},\n    // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n  ): Promise<Object | null> {\n    const typeIndex = await AvroParser.readInt(stream, options);\n    return this._types[typeIndex].read(stream, options);\n  }\n}\n\nclass AvroMapType extends AvroType {\n  private readonly _itemType: AvroType;\n\n  constructor(itemType: AvroType) {\n    super();\n    this._itemType = itemType;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n  public read(stream: AvroReadable, options: AvroParserReadOptions = {}): Promise<Object> {\n    const readItemMethod = (\n      s: AvroReadable,\n      opts?: AvroParserReadOptions,\n      // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n    ): Promise<Object | null> => {\n      return this._itemType.read(s, opts);\n    };\n    return AvroParser.readMap(stream, readItemMethod, options);\n  }\n}\n\nclass AvroRecordType extends AvroType {\n  private readonly _name: string;\n  private readonly _fields: Record<string, AvroType>;\n\n  constructor(fields: Record<string, AvroType>, name: string) {\n    super();\n    this._fields = fields;\n    this._name = name;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n  public async read(stream: AvroReadable, options: AvroParserReadOptions = {}): Promise<Object> {\n    // eslint-disable-next-line @typescript-eslint/no-wrapper-object-types\n    const record: Record<string, Object | null> = {};\n    record[\"$schema\"] = this._name;\n    for (const key in this._fields) {\n      if (Object.prototype.hasOwnProperty.call(this._fields, key)) {\n        record[key] = await this._fields[key].read(stream, options);\n      }\n    }\n    return record;\n  }\n}\n"]}