{"version": 3, "file": "OTLPMetricExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPMetricExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,8CAA2D;AAE3D,yEAAsE;AACtE,0EAK2C;AAC3C,sEAGyC;AACzC,2CAAwC;AAExC,MAAM,+BAA+B,GAAG,YAAY,CAAC;AACrD,MAAM,qBAAqB,GAAG,yBAAyB,+BAA+B,EAAE,CAAC;AACzF,MAAM,UAAU,GAAG;IACjB,YAAY,EAAE,iCAAiC,iBAAO,EAAE;CACzD,CAAC;AAEF,MAAM,qBAAsB,SAAQ,yCAGnC;IACC,YAAY,MAA+D;QACzE,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,OAAO,iDACP,IAAI,CAAC,OAAO,GACZ,UAAU,GACV,mBAAY,CAAC,uBAAuB,CACrC,IAAA,aAAM,GAAE,CAAC,kCAAkC,CAC5C,CACF,CAAC;IACJ,CAAC;IAED,OAAO,CAAC,OAA0B;QAChC,OAAO,IAAA,oDAAiC,EAAC,OAAO,CAAC,CAAC;IACpD,CAAC;IAED,aAAa,CAAC,MAAkC;QAC9C,OAAO,OAAO,MAAM,CAAC,GAAG,KAAK,QAAQ;YACnC,CAAC,CAAC,MAAM,CAAC,GAAG;YACZ,CAAC,CAAC,IAAA,aAAM,GAAE,CAAC,mCAAmC,CAAC,MAAM,GAAG,CAAC;gBACzD,CAAC,CAAC,IAAA,gDAA2B,EACzB,IAAA,aAAM,GAAE,CAAC,mCAAmC,CAC7C;gBACH,CAAC,CAAC,IAAA,aAAM,GAAE,CAAC,2BAA2B,CAAC,MAAM,GAAG,CAAC;oBACjD,CAAC,CAAC,IAAA,4CAAuB,EACrB,IAAA,aAAM,GAAE,CAAC,2BAA2B,EACpC,+BAA+B,CAChC;oBACH,CAAC,CAAC,qBAAqB,CAAC;IAC5B,CAAC;CACF;AAED;;GAEG;AACH,MAAa,kBAAmB,SAAQ,+CAA6C;IACnF,YAAY,MAA+D;QACzE,KAAK,CAAC,IAAI,qBAAqB,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;CACF;AAJD,gDAIC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ResourceMetrics } from '@opentelemetry/sdk-metrics';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport { OTLPMetricExporterOptions } from '../../OTLPMetricExporterOptions';\nimport { OTLPMetricExporterBase } from '../../OTLPMetricExporterBase';\nimport {\n  OTLPExporterNodeBase,\n  OTLPExporterNodeConfigBase,\n  appendResourcePathToUrl,\n  appendRootPathToUrlIfNeeded,\n} from '@opentelemetry/otlp-exporter-base';\nimport {\n  createExportMetricsServiceRequest,\n  IExportMetricsServiceRequest,\n} from '@opentelemetry/otlp-transformer';\nimport { VERSION } from '../../version';\n\nconst DEFAULT_COLLECTOR_RESOURCE_PATH = 'v1/metrics';\nconst DEFAULT_COLLECTOR_URL = `http://localhost:4318/${DEFAULT_COLLECTOR_RESOURCE_PATH}`;\nconst USER_AGENT = {\n  'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n};\n\nclass OTLPExporterNodeProxy extends OTLPExporterNodeBase<\n  ResourceMetrics,\n  IExportMetricsServiceRequest\n> {\n  constructor(config?: OTLPExporterNodeConfigBase & OTLPMetricExporterOptions) {\n    super(config);\n    this.headers = {\n      ...this.headers,\n      ...USER_AGENT,\n      ...baggageUtils.parseKeyPairsIntoRecord(\n        getEnv().OTEL_EXPORTER_OTLP_METRICS_HEADERS\n      ),\n    };\n  }\n\n  convert(metrics: ResourceMetrics[]): IExportMetricsServiceRequest {\n    return createExportMetricsServiceRequest(metrics);\n  }\n\n  getDefaultUrl(config: OTLPExporterNodeConfigBase): string {\n    return typeof config.url === 'string'\n      ? config.url\n      : getEnv().OTEL_EXPORTER_OTLP_METRICS_ENDPOINT.length > 0\n      ? appendRootPathToUrlIfNeeded(\n          getEnv().OTEL_EXPORTER_OTLP_METRICS_ENDPOINT\n        )\n      : getEnv().OTEL_EXPORTER_OTLP_ENDPOINT.length > 0\n      ? appendResourcePathToUrl(\n          getEnv().OTEL_EXPORTER_OTLP_ENDPOINT,\n          DEFAULT_COLLECTOR_RESOURCE_PATH\n        )\n      : DEFAULT_COLLECTOR_URL;\n  }\n}\n\n/**\n * Collector Metric Exporter for Node\n */\nexport class OTLPMetricExporter extends OTLPMetricExporterBase<OTLPExporterNodeProxy> {\n  constructor(config?: OTLPExporterNodeConfigBase & OTLPMetricExporterOptions) {\n    super(new OTLPExporterNodeProxy(config), config);\n  }\n}\n"]}