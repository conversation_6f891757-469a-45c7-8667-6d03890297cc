{"version": 3, "file": "messages.js", "sourceRoot": "", "sources": ["../../../../../../src/generated/src/operationsInterfaces/messages.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport {\n  MessagesDequeueOptionalParams,\n  MessagesDequeueResponse,\n  MessagesClearOptionalParams,\n  MessagesClearResponse,\n  QueueMessage,\n  MessagesEnqueueOptionalParams,\n  MessagesEnqueueResponse,\n  MessagesPeekOptionalParams,\n  MessagesPeekResponse\n} from \"../models\";\n\n/** Interface representing a Messages. */\nexport interface Messages {\n  /**\n   * The Dequeue operation retrieves one or more messages from the front of the queue.\n   * @param options The options parameters.\n   */\n  dequeue(\n    options?: MessagesDequeueOptionalParams\n  ): Promise<MessagesDequeueResponse>;\n  /**\n   * The Clear operation deletes all messages from the specified queue.\n   * @param options The options parameters.\n   */\n  clear(options?: MessagesClearOptionalParams): Promise<MessagesClearResponse>;\n  /**\n   * The Enqueue operation adds a new message to the back of the message queue. A visibility timeout can\n   * also be specified to make the message invisible until the visibility timeout expires. A message must\n   * be in a format that can be included in an XML request with UTF-8 encoding. The encoded message can\n   * be up to 64 KB in size for versions 2011-08-18 and newer, or 8 KB in size for previous versions.\n   * @param queueMessage A Message object which can be stored in a Queue\n   * @param options The options parameters.\n   */\n  enqueue(\n    queueMessage: QueueMessage,\n    options?: MessagesEnqueueOptionalParams\n  ): Promise<MessagesEnqueueResponse>;\n  /**\n   * The Peek operation retrieves one or more messages from the front of the queue, but does not alter\n   * the visibility of the message.\n   * @param options The options parameters.\n   */\n  peek(options?: MessagesPeekOptionalParams): Promise<MessagesPeekResponse>;\n}\n"]}