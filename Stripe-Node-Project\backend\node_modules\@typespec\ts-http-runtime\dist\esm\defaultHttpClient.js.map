{"version": 3, "file": "defaultHttpClient.js", "sourceRoot": "", "sources": ["../../src/defaultHttpClient.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAGlC,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAE3D;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,OAAO,oBAAoB,EAAE,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { HttpClient } from \"./interfaces.js\";\nimport { createNodeHttpClient } from \"./nodeHttpClient.js\";\n\n/**\n * Create the correct HttpClient for the current environment.\n */\nexport function createDefaultHttpClient(): HttpClient {\n  return createNodeHttpClient();\n}\n"]}