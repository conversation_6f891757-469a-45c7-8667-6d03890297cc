{"version": 3, "file": "AccountSASServices.js", "sourceRoot": "", "sources": ["../../../src/AccountSASServices.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC;;;;;;;;GAQG;AACH,MAAM,OAAO,kBAAkB;IAA/B;QAgCE;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,SAAI,GAAY,KAAK,CAAC;QAE7B;;WAEG;QACI,UAAK,GAAY,KAAK,CAAC;QAE9B;;WAEG;QACI,UAAK,GAAY,KAAK,CAAC;IAsBhC,CAAC;IAvEC;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,QAAgB;QAClC,MAAM,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;QAEpD,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE,CAAC;YACzB,QAAQ,CAAC,EAAE,CAAC;gBACV,KAAK,GAAG;oBACN,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAC/B,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,IAAI,GAAG,IAAI,CAAC;oBAC/B,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC;oBAChC,MAAM;gBACR,KAAK,GAAG;oBACN,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC;oBAChC,MAAM;gBACR;oBACE,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,EAAE,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAsBD;;;OAGG;IACI,QAAQ;QACb,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;QACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;QACD,OAAO,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3B,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * ONLY AVAILABLE IN NODE.JS RUNTIME.\n *\n * This is a helper class to construct a string representing the services accessible by an AccountSAS. Setting a value\n * to true means that any SAS which uses these permissions will grant access to that service. Once all the\n * values are set, this should be serialized with toString and set as the services field on an\n * {@link AccountSASSignatureValues} object. It is possible to construct the services string without this class, but\n * the order of the services is particular and this class guarantees correctness.\n */\nexport class AccountSASServices {\n  /**\n   * Creates an {@link AccountSASServices} from the specified services string. This method will throw an\n   * Error if it encounters a character that does not correspond to a valid service.\n   *\n   * @param services -\n   */\n  public static parse(services: string): AccountSASServices {\n    const accountSASServices = new AccountSASServices();\n\n    for (const c of services) {\n      switch (c) {\n        case \"b\":\n          accountSASServices.blob = true;\n          break;\n        case \"f\":\n          accountSASServices.file = true;\n          break;\n        case \"q\":\n          accountSASServices.queue = true;\n          break;\n        case \"t\":\n          accountSASServices.table = true;\n          break;\n        default:\n          throw new RangeError(`Invalid service character: ${c}`);\n      }\n    }\n\n    return accountSASServices;\n  }\n\n  /**\n   * Permission to access blob resources granted.\n   */\n  public blob: boolean = false;\n\n  /**\n   * Permission to access file resources granted.\n   */\n  public file: boolean = false;\n\n  /**\n   * Permission to access queue resources granted.\n   */\n  public queue: boolean = false;\n\n  /**\n   * Permission to access table resources granted.\n   */\n  public table: boolean = false;\n\n  /**\n   * Converts the given services to a string.\n   *\n   */\n  public toString(): string {\n    const services: string[] = [];\n    if (this.blob) {\n      services.push(\"b\");\n    }\n    if (this.table) {\n      services.push(\"t\");\n    }\n    if (this.queue) {\n      services.push(\"q\");\n    }\n    if (this.file) {\n      services.push(\"f\");\n    }\n    return services.join(\"\");\n  }\n}\n"]}