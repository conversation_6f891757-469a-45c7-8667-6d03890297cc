// Check what data already exists in the database
require('dotenv').config();
const { supabaseAdmin } = require('./src/config/database');

async function checkExistingData() {
    console.log('🔍 Checking existing data in database...\n');
    
    try {
        // Check users
        console.log('👤 Users:');
        const { data: users, error: userError } = await supabaseAdmin
            .from('users')
            .select('*');
            
        if (userError) {
            console.error('❌ Error fetching users:', userError);
        } else {
            console.log(`   Found ${users.length} user(s):`);
            users.forEach((user, index) => {
                console.log(`   ${index + 1}. ID: ${user.id}`);
                console.log(`      Email: ${user.email}`);
                console.log(`      WordPress Site: ${user.wordpress_site}`);
                console.log(`      Created: ${user.created_at}`);
                console.log('');
            });
        }
        
        // Check vendors
        console.log('🏪 Vendors:');
        const { data: vendors, error: vendorError } = await supabaseAdmin
            .from('vendors')
            .select('*');
            
        if (vendorError) {
            console.error('❌ Error fetching vendors:', vendorError);
        } else {
            console.log(`   Found ${vendors.length} vendor(s):`);
            vendors.forEach((vendor, index) => {
                console.log(`   ${index + 1}. ID: ${vendor.id}`);
                console.log(`      User ID: ${vendor.user_id}`);
                console.log(`      Business Name: ${vendor.business_name}`);
                console.log(`      Business Email: ${vendor.business_email}`);
                console.log(`      Status: ${vendor.status}`);
                console.log('');
            });
        }
        
        // Check API keys
        console.log('🔑 API Keys:');
        const { data: apiKeys, error: apiError } = await supabaseAdmin
            .from('api_keys')
            .select('*');
            
        if (apiError) {
            console.error('❌ Error fetching API keys:', apiError);
        } else {
            console.log(`   Found ${apiKeys.length} API key(s):`);
            apiKeys.forEach((key, index) => {
                console.log(`   ${index + 1}. ID: ${key.id}`);
                console.log(`      User ID: ${key.user_id}`);
                console.log(`      Key Hash: ${key.key_hash ? key.key_hash.substring(0, 20) + '...' : 'NULL'}`);
                console.log(`      WordPress Site: ${key.wordpress_site}`);
                console.log(`      Is Active: ${key.is_active}`);
                console.log(`      Permissions: ${JSON.stringify(key.permissions)}`);
                console.log('');
            });
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

checkExistingData();
