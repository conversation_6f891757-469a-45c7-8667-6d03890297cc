{"version": 3, "file": "storageClient.js", "sourceRoot": "", "sources": ["../../../../../../storage-blob/src/generated/src/storageClient.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,OAAO,KAAK,cAAc,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EACL,WAAW,EACX,aAAa,EACb,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,aAAa,GACd,MAAM,cAAc,CAAC;AAWtB,MAAM,OAAO,aAAc,SAAQ,cAAc,CAAC,qBAAqB;IAIrE;;;;;OAKG;IACH,YAAY,GAAW,EAAE,OAAqC;;QAC5D,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC1C,CAAC;QAED,0CAA0C;QAC1C,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;QACD,MAAM,QAAQ,GAAgC;YAC5C,kBAAkB,EAAE,iCAAiC;SACtD,CAAC;QAEF,MAAM,cAAc,GAAG,qCAAqC,CAAC;QAC7D,MAAM,eAAe,GACnB,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC,eAAe;YAClE,CAAC,CAAC,GAAG,OAAO,CAAC,gBAAgB,CAAC,eAAe,IAAI,cAAc,EAAE;YACjE,CAAC,CAAC,GAAG,cAAc,EAAE,CAAC;QAE1B,MAAM,mBAAmB,iDACpB,QAAQ,GACR,OAAO,KACV,gBAAgB,EAAE;gBAChB,eAAe;aAChB,EACD,QAAQ,EAAE,MAAA,MAAA,OAAO,CAAC,QAAQ,mCAAI,OAAO,CAAC,OAAO,mCAAI,OAAO,GACzD,CAAC;QACF,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC3B,wBAAwB;QACxB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QAEf,0CAA0C;QAC1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;CAQF", "sourcesContent": ["/*\n * Copyright (c) Microsoft Corporation.\n * Licensed under the MIT License.\n *\n * Code generated by Microsoft (R) AutoRest Code Generator.\n * Changes may cause incorrect behavior and will be lost if the code is regenerated.\n */\n\nimport * as coreHttpCompat from \"@azure/core-http-compat\";\nimport {\n  ServiceImpl,\n  ContainerImpl,\n  BlobImpl,\n  PageBlobImpl,\n  AppendBlobImpl,\n  BlockBlobImpl,\n} from \"./operations\";\nimport {\n  Service,\n  Container,\n  Blob,\n  PageBlob,\n  AppendBlob,\n  BlockBlob,\n} from \"./operationsInterfaces\";\nimport { StorageClientOptionalParams } from \"./models\";\n\nexport class StorageClient extends coreHttpCompat.ExtendedServiceClient {\n  url: string;\n  version: string;\n\n  /**\n   * Initializes a new instance of the StorageClient class.\n   * @param url The URL of the service account, container, or blob that is the target of the desired\n   *            operation.\n   * @param options The parameter options\n   */\n  constructor(url: string, options?: StorageClientOptionalParams) {\n    if (url === undefined) {\n      throw new Error(\"'url' cannot be null\");\n    }\n\n    // Initializing default values for options\n    if (!options) {\n      options = {};\n    }\n    const defaults: StorageClientOptionalParams = {\n      requestContentType: \"application/json; charset=utf-8\",\n    };\n\n    const packageDetails = `azsdk-js-azure-storage-blob/12.27.0`;\n    const userAgentPrefix =\n      options.userAgentOptions && options.userAgentOptions.userAgentPrefix\n        ? `${options.userAgentOptions.userAgentPrefix} ${packageDetails}`\n        : `${packageDetails}`;\n\n    const optionsWithDefaults = {\n      ...defaults,\n      ...options,\n      userAgentOptions: {\n        userAgentPrefix,\n      },\n      endpoint: options.endpoint ?? options.baseUri ?? \"{url}\",\n    };\n    super(optionsWithDefaults);\n    // Parameter assignments\n    this.url = url;\n\n    // Assigning values to Constant parameters\n    this.version = options.version || \"2025-05-05\";\n    this.service = new ServiceImpl(this);\n    this.container = new ContainerImpl(this);\n    this.blob = new BlobImpl(this);\n    this.pageBlob = new PageBlobImpl(this);\n    this.appendBlob = new AppendBlobImpl(this);\n    this.blockBlob = new BlockBlobImpl(this);\n  }\n\n  service: Service;\n  container: Container;\n  blob: Blob;\n  pageBlob: PageBlob;\n  appendBlob: AppendBlob;\n  blockBlob: BlockBlob;\n}\n"]}